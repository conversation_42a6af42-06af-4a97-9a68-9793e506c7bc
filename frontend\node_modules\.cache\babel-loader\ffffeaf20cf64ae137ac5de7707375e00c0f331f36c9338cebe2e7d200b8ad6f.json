{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\pages\\\\SettingsPage.js\",\n  _s = $RefreshSig$();\n/**\n * 设置页面\n */\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SettingsPage = () => {\n  _s();\n  const [settings, setSettings] = useState({\n    // API设置\n    apiEndpoint: 'http://localhost:8001',\n    timeout: 30,\n    // 分析设置\n    maxFileSize: 10,\n    supportedFormats: ['png', 'jpg', 'jpeg', 'gif'],\n    autoSave: true,\n    // 智能体设置\n    enableElementDetection: true,\n    enableInteractionAnalysis: true,\n    enableScriptGeneration: true,\n    // 界面设置\n    theme: 'light',\n    language: 'zh-CN',\n    showNotifications: true\n  });\n  const [aiConfig, setAiConfig] = useState({\n    api_key: '',\n    base_url: '',\n    model_name: ''\n  });\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [saved, setSaved] = useState(false);\n  useEffect(() => {\n    // 加载AI配置\n    loadAiConfig();\n  }, []);\n  const loadAiConfig = async () => {\n    try {\n      const response = await fetch('http://localhost:8000/api/v1/config/ai');\n      if (response.ok) {\n        const data = await response.json();\n        setAiConfig(data.config);\n      } else {\n        console.error('加载AI配置失败');\n      }\n    } catch (error) {\n      console.error('加载AI配置失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSettingChange = (key, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n  const handleArraySettingChange = (key, index, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [key]: prev[key].map((item, i) => i === index ? value : item)\n    }));\n  };\n  const handleSaveSettings = () => {\n    // 模拟保存设置\n    localStorage.setItem('ui-automation-settings', JSON.stringify(settings));\n    setSaved(true);\n    setTimeout(() => setSaved(false), 3000);\n  };\n  const handleResetSettings = () => {\n    if (window.confirm('确定要重置所有设置吗？')) {\n      setSettings({\n        apiEndpoint: 'http://localhost:8001',\n        timeout: 30,\n        maxFileSize: 10,\n        supportedFormats: ['png', 'jpg', 'jpeg', 'gif'],\n        autoSave: true,\n        enableElementDetection: true,\n        enableInteractionAnalysis: true,\n        enableScriptGeneration: true,\n        theme: 'light',\n        language: 'zh-CN',\n        showNotifications: true\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"settings-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\u2699\\uFE0F \\u7CFB\\u7EDF\\u8BBE\\u7F6E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u914D\\u7F6E\\u7CFB\\u7EDF\\u53C2\\u6570\\u548C\\u4E2A\\u4EBA\\u504F\\u597D\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDD17 API\\u8BBE\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"API\\u7AEF\\u70B9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: settings.apiEndpoint,\n              onChange: e => handleSettingChange('apiEndpoint', e.target.value),\n              placeholder: \"http://localhost:8001\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u8BF7\\u6C42\\u8D85\\u65F6 (\\u79D2)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: settings.timeout,\n              onChange: e => handleSettingChange('timeout', parseInt(e.target.value)),\n              min: \"5\",\n              max: \"300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDD0D \\u5206\\u6790\\u8BBE\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u6700\\u5927\\u6587\\u4EF6\\u5927\\u5C0F (MB)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: settings.maxFileSize,\n              onChange: e => handleSettingChange('maxFileSize', parseInt(e.target.value)),\n              min: \"1\",\n              max: \"100\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u81EA\\u52A8\\u4FDD\\u5B58\\u7ED3\\u679C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: settings.autoSave,\n              onChange: e => handleSettingChange('autoSave', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83E\\uDD16 \\u667A\\u80FD\\u4F53\\u8BBE\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u542F\\u7528\\u5143\\u7D20\\u8BC6\\u522B\\u667A\\u80FD\\u4F53\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: settings.enableElementDetection,\n              onChange: e => handleSettingChange('enableElementDetection', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u542F\\u7528\\u4EA4\\u4E92\\u5206\\u6790\\u667A\\u80FD\\u4F53\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: settings.enableInteractionAnalysis,\n              onChange: e => handleSettingChange('enableInteractionAnalysis', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u542F\\u7528\\u811A\\u672C\\u751F\\u6210\\u667A\\u80FD\\u4F53\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: settings.enableScriptGeneration,\n              onChange: e => handleSettingChange('enableScriptGeneration', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83C\\uDFA8 \\u754C\\u9762\\u8BBE\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u4E3B\\u9898\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: settings.theme,\n              onChange: e => handleSettingChange('theme', e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"light\",\n                children: \"\\u6D45\\u8272\\u4E3B\\u9898\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"dark\",\n                children: \"\\u6DF1\\u8272\\u4E3B\\u9898\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"auto\",\n                children: \"\\u8DDF\\u968F\\u7CFB\\u7EDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u8BED\\u8A00\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: settings.language,\n              onChange: e => handleSettingChange('language', e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"zh-CN\",\n                children: \"\\u7B80\\u4F53\\u4E2D\\u6587\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"en-US\",\n                children: \"English\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u663E\\u793A\\u901A\\u77E5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: settings.showNotifications,\n              onChange: e => handleSettingChange('showNotifications', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-primary\",\n            onClick: handleSaveSettings,\n            children: saved ? '✅ 已保存' : '💾 保存设置'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            onClick: handleResetSettings,\n            children: \"\\uD83D\\uDD04 \\u91CD\\u7F6E\\u8BBE\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        .settings-page {\n          min-height: 100vh;\n          background: #f8f9fa;\n        }\n\n        .page-header {\n          background: white;\n          border-bottom: 1px solid #e9ecef;\n          padding: 24px 0;\n          margin-bottom: 24px;\n        }\n\n        .page-header h1 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 28px;\n          font-weight: 600;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-header p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-content {\n          max-width: 800px;\n          margin: 0 auto;\n          padding: 0 24px;\n        }\n\n        .settings-container {\n          background: white;\n          border-radius: 12px;\n          padding: 24px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n        }\n\n        .settings-section {\n          margin-bottom: 32px;\n          padding-bottom: 24px;\n          border-bottom: 1px solid #e9ecef;\n        }\n\n        .settings-section:last-of-type {\n          border-bottom: none;\n          margin-bottom: 24px;\n        }\n\n        .settings-section h3 {\n          margin: 0 0 20px 0;\n          color: #333;\n          font-size: 18px;\n          font-weight: 600;\n        }\n\n        .setting-item {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 16px;\n          padding: 12px 0;\n        }\n\n        .setting-item:last-child {\n          margin-bottom: 0;\n        }\n\n        .setting-item label {\n          font-weight: 500;\n          color: #333;\n          flex: 1;\n        }\n\n        .setting-item input,\n        .setting-item select {\n          width: 200px;\n          padding: 8px 12px;\n          border: 1px solid #ddd;\n          border-radius: 6px;\n          font-size: 14px;\n        }\n\n        .setting-item input[type=\"checkbox\"] {\n          width: auto;\n          transform: scale(1.2);\n        }\n\n        .setting-item input:focus,\n        .setting-item select:focus {\n          outline: none;\n          border-color: #007bff;\n          box-shadow: 0 0 0 2px rgba(0,123,255,0.25);\n        }\n\n        .settings-actions {\n          display: flex;\n          gap: 12px;\n          justify-content: center;\n          padding-top: 24px;\n          border-top: 1px solid #e9ecef;\n        }\n\n        .btn-primary,\n        .btn-secondary {\n          border: none;\n          padding: 12px 24px;\n          border-radius: 6px;\n          font-size: 16px;\n          font-weight: 500;\n          cursor: pointer;\n          transition: all 0.2s ease;\n          min-width: 120px;\n        }\n\n        .btn-primary {\n          background: #007bff;\n          color: white;\n        }\n\n        .btn-primary:hover {\n          background: #0056b3;\n        }\n\n        .btn-secondary {\n          background: #6c757d;\n          color: white;\n        }\n\n        .btn-secondary:hover {\n          background: #5a6268;\n        }\n\n        /* 响应式设计 */\n        @media (max-width: 768px) {\n          .page-header h1,\n          .page-header p,\n          .page-content {\n            padding: 0 16px;\n          }\n\n          .settings-container {\n            padding: 16px;\n          }\n\n          .setting-item {\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 8px;\n          }\n\n          .setting-item input,\n          .setting-item select {\n            width: 100%;\n          }\n\n          .settings-actions {\n            flex-direction: column;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 5\n  }, this);\n};\n_s(SettingsPage, \"XTsU9jqMKArdQb8aEPiL8frwSYw=\");\n_c = SettingsPage;\nexport default SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "SettingsPage", "_s", "settings", "setSettings", "apiEndpoint", "timeout", "maxFileSize", "supportedFormats", "autoSave", "enableElementDetection", "enableInteractionAnalysis", "enableScriptGeneration", "theme", "language", "showNotifications", "aiConfig", "setAiConfig", "api_key", "base_url", "model_name", "loading", "setLoading", "saving", "setSaving", "saved", "setSaved", "loadAiConfig", "response", "fetch", "ok", "data", "json", "config", "console", "error", "handleSettingChange", "key", "value", "prev", "handleArraySettingChange", "index", "map", "item", "i", "handleSaveSettings", "localStorage", "setItem", "JSON", "stringify", "setTimeout", "handleResetSettings", "window", "confirm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onChange", "e", "target", "placeholder", "parseInt", "min", "max", "checked", "onClick", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/pages/SettingsPage.js"], "sourcesContent": ["/**\n * 设置页面\n */\nimport React, { useState, useEffect } from 'react';\n\nconst SettingsPage = () => {\n  const [settings, setSettings] = useState({\n    // API设置\n    apiEndpoint: 'http://localhost:8001',\n    timeout: 30,\n\n    // 分析设置\n    maxFileSize: 10,\n    supportedFormats: ['png', 'jpg', 'jpeg', 'gif'],\n    autoSave: true,\n\n    // 智能体设置\n    enableElementDetection: true,\n    enableInteractionAnalysis: true,\n    enableScriptGeneration: true,\n\n    // 界面设置\n    theme: 'light',\n    language: 'zh-CN',\n    showNotifications: true\n  });\n\n  const [aiConfig, setAiConfig] = useState({\n    api_key: '',\n    base_url: '',\n    model_name: ''\n  });\n\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n\n  const [saved, setSaved] = useState(false);\n\n  useEffect(() => {\n    // 加载AI配置\n    loadAiConfig();\n  }, []);\n\n  const loadAiConfig = async () => {\n    try {\n      const response = await fetch('http://localhost:8000/api/v1/config/ai');\n      if (response.ok) {\n        const data = await response.json();\n        setAiConfig(data.config);\n      } else {\n        console.error('加载AI配置失败');\n      }\n    } catch (error) {\n      console.error('加载AI配置失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSettingChange = (key, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n\n  const handleArraySettingChange = (key, index, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [key]: prev[key].map((item, i) => i === index ? value : item)\n    }));\n  };\n\n  const handleSaveSettings = () => {\n    // 模拟保存设置\n    localStorage.setItem('ui-automation-settings', JSON.stringify(settings));\n    setSaved(true);\n    setTimeout(() => setSaved(false), 3000);\n  };\n\n  const handleResetSettings = () => {\n    if (window.confirm('确定要重置所有设置吗？')) {\n      setSettings({\n        apiEndpoint: 'http://localhost:8001',\n        timeout: 30,\n        maxFileSize: 10,\n        supportedFormats: ['png', 'jpg', 'jpeg', 'gif'],\n        autoSave: true,\n        enableElementDetection: true,\n        enableInteractionAnalysis: true,\n        enableScriptGeneration: true,\n        theme: 'light',\n        language: 'zh-CN',\n        showNotifications: true\n      });\n    }\n  };\n\n  return (\n    <div className=\"settings-page\">\n      <div className=\"page-header\">\n        <h1>⚙️ 系统设置</h1>\n        <p>配置系统参数和个人偏好</p>\n      </div>\n\n      <div className=\"page-content\">\n        <div className=\"settings-container\">\n          {/* API设置 */}\n          <div className=\"settings-section\">\n            <h3>🔗 API设置</h3>\n            <div className=\"setting-item\">\n              <label>API端点</label>\n              <input\n                type=\"text\"\n                value={settings.apiEndpoint}\n                onChange={(e) => handleSettingChange('apiEndpoint', e.target.value)}\n                placeholder=\"http://localhost:8001\"\n              />\n            </div>\n            <div className=\"setting-item\">\n              <label>请求超时 (秒)</label>\n              <input\n                type=\"number\"\n                value={settings.timeout}\n                onChange={(e) => handleSettingChange('timeout', parseInt(e.target.value))}\n                min=\"5\"\n                max=\"300\"\n              />\n            </div>\n          </div>\n\n          {/* 分析设置 */}\n          <div className=\"settings-section\">\n            <h3>🔍 分析设置</h3>\n            <div className=\"setting-item\">\n              <label>最大文件大小 (MB)</label>\n              <input\n                type=\"number\"\n                value={settings.maxFileSize}\n                onChange={(e) => handleSettingChange('maxFileSize', parseInt(e.target.value))}\n                min=\"1\"\n                max=\"100\"\n              />\n            </div>\n            <div className=\"setting-item\">\n              <label>自动保存结果</label>\n              <input\n                type=\"checkbox\"\n                checked={settings.autoSave}\n                onChange={(e) => handleSettingChange('autoSave', e.target.checked)}\n              />\n            </div>\n          </div>\n\n          {/* 智能体设置 */}\n          <div className=\"settings-section\">\n            <h3>🤖 智能体设置</h3>\n            <div className=\"setting-item\">\n              <label>启用元素识别智能体</label>\n              <input\n                type=\"checkbox\"\n                checked={settings.enableElementDetection}\n                onChange={(e) => handleSettingChange('enableElementDetection', e.target.checked)}\n              />\n            </div>\n            <div className=\"setting-item\">\n              <label>启用交互分析智能体</label>\n              <input\n                type=\"checkbox\"\n                checked={settings.enableInteractionAnalysis}\n                onChange={(e) => handleSettingChange('enableInteractionAnalysis', e.target.checked)}\n              />\n            </div>\n            <div className=\"setting-item\">\n              <label>启用脚本生成智能体</label>\n              <input\n                type=\"checkbox\"\n                checked={settings.enableScriptGeneration}\n                onChange={(e) => handleSettingChange('enableScriptGeneration', e.target.checked)}\n              />\n            </div>\n          </div>\n\n          {/* 界面设置 */}\n          <div className=\"settings-section\">\n            <h3>🎨 界面设置</h3>\n            <div className=\"setting-item\">\n              <label>主题</label>\n              <select\n                value={settings.theme}\n                onChange={(e) => handleSettingChange('theme', e.target.value)}\n              >\n                <option value=\"light\">浅色主题</option>\n                <option value=\"dark\">深色主题</option>\n                <option value=\"auto\">跟随系统</option>\n              </select>\n            </div>\n            <div className=\"setting-item\">\n              <label>语言</label>\n              <select\n                value={settings.language}\n                onChange={(e) => handleSettingChange('language', e.target.value)}\n              >\n                <option value=\"zh-CN\">简体中文</option>\n                <option value=\"en-US\">English</option>\n              </select>\n            </div>\n            <div className=\"setting-item\">\n              <label>显示通知</label>\n              <input\n                type=\"checkbox\"\n                checked={settings.showNotifications}\n                onChange={(e) => handleSettingChange('showNotifications', e.target.checked)}\n              />\n            </div>\n          </div>\n\n          {/* 操作按钮 */}\n          <div className=\"settings-actions\">\n            <button className=\"btn-primary\" onClick={handleSaveSettings}>\n              {saved ? '✅ 已保存' : '💾 保存设置'}\n            </button>\n            <button className=\"btn-secondary\" onClick={handleResetSettings}>\n              🔄 重置设置\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <style>{`\n        .settings-page {\n          min-height: 100vh;\n          background: #f8f9fa;\n        }\n\n        .page-header {\n          background: white;\n          border-bottom: 1px solid #e9ecef;\n          padding: 24px 0;\n          margin-bottom: 24px;\n        }\n\n        .page-header h1 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 28px;\n          font-weight: 600;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-header p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-content {\n          max-width: 800px;\n          margin: 0 auto;\n          padding: 0 24px;\n        }\n\n        .settings-container {\n          background: white;\n          border-radius: 12px;\n          padding: 24px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n        }\n\n        .settings-section {\n          margin-bottom: 32px;\n          padding-bottom: 24px;\n          border-bottom: 1px solid #e9ecef;\n        }\n\n        .settings-section:last-of-type {\n          border-bottom: none;\n          margin-bottom: 24px;\n        }\n\n        .settings-section h3 {\n          margin: 0 0 20px 0;\n          color: #333;\n          font-size: 18px;\n          font-weight: 600;\n        }\n\n        .setting-item {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 16px;\n          padding: 12px 0;\n        }\n\n        .setting-item:last-child {\n          margin-bottom: 0;\n        }\n\n        .setting-item label {\n          font-weight: 500;\n          color: #333;\n          flex: 1;\n        }\n\n        .setting-item input,\n        .setting-item select {\n          width: 200px;\n          padding: 8px 12px;\n          border: 1px solid #ddd;\n          border-radius: 6px;\n          font-size: 14px;\n        }\n\n        .setting-item input[type=\"checkbox\"] {\n          width: auto;\n          transform: scale(1.2);\n        }\n\n        .setting-item input:focus,\n        .setting-item select:focus {\n          outline: none;\n          border-color: #007bff;\n          box-shadow: 0 0 0 2px rgba(0,123,255,0.25);\n        }\n\n        .settings-actions {\n          display: flex;\n          gap: 12px;\n          justify-content: center;\n          padding-top: 24px;\n          border-top: 1px solid #e9ecef;\n        }\n\n        .btn-primary,\n        .btn-secondary {\n          border: none;\n          padding: 12px 24px;\n          border-radius: 6px;\n          font-size: 16px;\n          font-weight: 500;\n          cursor: pointer;\n          transition: all 0.2s ease;\n          min-width: 120px;\n        }\n\n        .btn-primary {\n          background: #007bff;\n          color: white;\n        }\n\n        .btn-primary:hover {\n          background: #0056b3;\n        }\n\n        .btn-secondary {\n          background: #6c757d;\n          color: white;\n        }\n\n        .btn-secondary:hover {\n          background: #5a6268;\n        }\n\n        /* 响应式设计 */\n        @media (max-width: 768px) {\n          .page-header h1,\n          .page-header p,\n          .page-content {\n            padding: 0 16px;\n          }\n\n          .settings-container {\n            padding: 16px;\n          }\n\n          .setting-item {\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 8px;\n          }\n\n          .setting-item input,\n          .setting-item select {\n            width: 100%;\n          }\n\n          .settings-actions {\n            flex-direction: column;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default SettingsPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGP,QAAQ,CAAC;IACvC;IACAQ,WAAW,EAAE,uBAAuB;IACpCC,OAAO,EAAE,EAAE;IAEX;IACAC,WAAW,EAAE,EAAE;IACfC,gBAAgB,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;IAC/CC,QAAQ,EAAE,IAAI;IAEd;IACAC,sBAAsB,EAAE,IAAI;IAC5BC,yBAAyB,EAAE,IAAI;IAC/BC,sBAAsB,EAAE,IAAI;IAE5B;IACAC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,OAAO;IACjBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC;IACvCqB,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,MAAM,EAAEC,SAAS,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAE3C,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAEzCC,SAAS,CAAC,MAAM;IACd;IACA6B,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,wCAAwC,CAAC;MACtE,IAAID,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClCf,WAAW,CAACc,IAAI,CAACE,MAAM,CAAC;MAC1B,CAAC,MAAM;QACLC,OAAO,CAACC,KAAK,CAAC,UAAU,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,mBAAmB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IAC1ClC,WAAW,CAACmC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACF,GAAG,GAAGC;IACT,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,wBAAwB,GAAGA,CAACH,GAAG,EAAEI,KAAK,EAAEH,KAAK,KAAK;IACtDlC,WAAW,CAACmC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACF,GAAG,GAAGE,IAAI,CAACF,GAAG,CAAC,CAACK,GAAG,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAKA,CAAC,KAAKH,KAAK,GAAGH,KAAK,GAAGK,IAAI;IAC9D,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACAC,YAAY,CAACC,OAAO,CAAC,wBAAwB,EAAEC,IAAI,CAACC,SAAS,CAAC9C,QAAQ,CAAC,CAAC;IACxEuB,QAAQ,CAAC,IAAI,CAAC;IACdwB,UAAU,CAAC,MAAMxB,QAAQ,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;EACzC,CAAC;EAED,MAAMyB,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIC,MAAM,CAACC,OAAO,CAAC,aAAa,CAAC,EAAE;MACjCjD,WAAW,CAAC;QACVC,WAAW,EAAE,uBAAuB;QACpCC,OAAO,EAAE,EAAE;QACXC,WAAW,EAAE,EAAE;QACfC,gBAAgB,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;QAC/CC,QAAQ,EAAE,IAAI;QACdC,sBAAsB,EAAE,IAAI;QAC5BC,yBAAyB,EAAE,IAAI;QAC/BC,sBAAsB,EAAE,IAAI;QAC5BC,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE,OAAO;QACjBC,iBAAiB,EAAE;MACrB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACEf,OAAA;IAAKsD,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BvD,OAAA;MAAKsD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BvD,OAAA;QAAAuD,QAAA,EAAI;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChB3D,OAAA;QAAAuD,QAAA,EAAG;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAEN3D,OAAA;MAAKsD,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BvD,OAAA;QAAKsD,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBAEjCvD,OAAA;UAAKsD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BvD,OAAA;YAAAuD,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjB3D,OAAA;YAAKsD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BvD,OAAA;cAAAuD,QAAA,EAAO;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpB3D,OAAA;cACE4D,IAAI,EAAC,MAAM;cACXtB,KAAK,EAAEnC,QAAQ,CAACE,WAAY;cAC5BwD,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,aAAa,EAAE0B,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAE;cACpE0B,WAAW,EAAC;YAAuB;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BvD,OAAA;cAAAuD,QAAA,EAAO;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvB3D,OAAA;cACE4D,IAAI,EAAC,QAAQ;cACbtB,KAAK,EAAEnC,QAAQ,CAACG,OAAQ;cACxBuD,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,SAAS,EAAE6B,QAAQ,CAACH,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAC,CAAE;cAC1E4B,GAAG,EAAC,GAAG;cACPC,GAAG,EAAC;YAAK;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3D,OAAA;UAAKsD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BvD,OAAA;YAAAuD,QAAA,EAAI;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChB3D,OAAA;YAAKsD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BvD,OAAA;cAAAuD,QAAA,EAAO;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1B3D,OAAA;cACE4D,IAAI,EAAC,QAAQ;cACbtB,KAAK,EAAEnC,QAAQ,CAACI,WAAY;cAC5BsD,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,aAAa,EAAE6B,QAAQ,CAACH,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAC,CAAE;cAC9E4B,GAAG,EAAC,GAAG;cACPC,GAAG,EAAC;YAAK;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BvD,OAAA;cAAAuD,QAAA,EAAO;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrB3D,OAAA;cACE4D,IAAI,EAAC,UAAU;cACfQ,OAAO,EAAEjE,QAAQ,CAACM,QAAS;cAC3BoD,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,UAAU,EAAE0B,CAAC,CAACC,MAAM,CAACK,OAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3D,OAAA;UAAKsD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BvD,OAAA;YAAAuD,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjB3D,OAAA;YAAKsD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BvD,OAAA;cAAAuD,QAAA,EAAO;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxB3D,OAAA;cACE4D,IAAI,EAAC,UAAU;cACfQ,OAAO,EAAEjE,QAAQ,CAACO,sBAAuB;cACzCmD,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,wBAAwB,EAAE0B,CAAC,CAACC,MAAM,CAACK,OAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BvD,OAAA;cAAAuD,QAAA,EAAO;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxB3D,OAAA;cACE4D,IAAI,EAAC,UAAU;cACfQ,OAAO,EAAEjE,QAAQ,CAACQ,yBAA0B;cAC5CkD,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,2BAA2B,EAAE0B,CAAC,CAACC,MAAM,CAACK,OAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BvD,OAAA;cAAAuD,QAAA,EAAO;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxB3D,OAAA;cACE4D,IAAI,EAAC,UAAU;cACfQ,OAAO,EAAEjE,QAAQ,CAACS,sBAAuB;cACzCiD,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,wBAAwB,EAAE0B,CAAC,CAACC,MAAM,CAACK,OAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3D,OAAA;UAAKsD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BvD,OAAA;YAAAuD,QAAA,EAAI;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChB3D,OAAA;YAAKsD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BvD,OAAA;cAAAuD,QAAA,EAAO;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjB3D,OAAA;cACEsC,KAAK,EAAEnC,QAAQ,CAACU,KAAM;cACtBgD,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,OAAO,EAAE0B,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAE;cAAAiB,QAAA,gBAE9DvD,OAAA;gBAAQsC,KAAK,EAAC,OAAO;gBAAAiB,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC3D,OAAA;gBAAQsC,KAAK,EAAC,MAAM;gBAAAiB,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClC3D,OAAA;gBAAQsC,KAAK,EAAC,MAAM;gBAAAiB,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BvD,OAAA;cAAAuD,QAAA,EAAO;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjB3D,OAAA;cACEsC,KAAK,EAAEnC,QAAQ,CAACW,QAAS;cACzB+C,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,UAAU,EAAE0B,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAE;cAAAiB,QAAA,gBAEjEvD,OAAA;gBAAQsC,KAAK,EAAC,OAAO;gBAAAiB,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC3D,OAAA;gBAAQsC,KAAK,EAAC,OAAO;gBAAAiB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN3D,OAAA;YAAKsD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BvD,OAAA;cAAAuD,QAAA,EAAO;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnB3D,OAAA;cACE4D,IAAI,EAAC,UAAU;cACfQ,OAAO,EAAEjE,QAAQ,CAACY,iBAAkB;cACpC8C,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,mBAAmB,EAAE0B,CAAC,CAACC,MAAM,CAACK,OAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3D,OAAA;UAAKsD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BvD,OAAA;YAAQsD,SAAS,EAAC,aAAa;YAACe,OAAO,EAAExB,kBAAmB;YAAAU,QAAA,EACzD9B,KAAK,GAAG,OAAO,GAAG;UAAS;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACT3D,OAAA;YAAQsD,SAAS,EAAC,eAAe;YAACe,OAAO,EAAElB,mBAAoB;YAAAI,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3D,OAAA;MAAAuD,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACzD,EAAA,CA5YID,YAAY;AAAAqE,EAAA,GAAZrE,YAAY;AA8YlB,eAAeA,YAAY;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}