# UI自动化分析平台

基于AI智能体的UI界面分析和自动化测试用例生成平台。

## 项目结构

```
ui-automation/
├── backend/                # FastAPI 后端
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py        # FastAPI 应用入口
│   │   ├── api/           # API 路由
│   │   ├── agents/        # 智能体模块
│   │   ├── core/          # 核心功能
│   │   └── models/        # 数据模型
│   ├── requirements.txt
│   └── Dockerfile
├── frontend/               # React 前端
│   ├── src/
│   │   ├── components/    # React 组件
│   │   ├── pages/         # 页面组件
│   │   ├── services/      # API 服务
│   │   └── utils/         # 工具函数
│   ├── package.json
│   └── public/
├── agents/                 # 智能体配置和提示词
│   ├── element_detection/  # 元素识别智能体
│   ├── interaction_analysis/ # 交互分析智能体
│   └── test_generation/   # 用例生成智能体
├── docker-compose.yml     # 容器编排
└── README.md
```

## 核心功能

1. **文件上传**: 支持图片上传和文字描述
2. **智能体流水线**: 元素识别 → 交互分析 → 用例生成
3. **实时反馈**: SSE流式输出处理进度
4. **结果可视化**: 元素标注、流程图、测试用例

## 技术栈

- **后端**: FastAPI + Celery + Redis
- **前端**: React + TypeScript + Vite
- **AI**: 基于提示词工程的智能体系统
- **可视化**: Fabric.js + Mermaid.js

## 快速开始

### 方式一：一键启动（推荐）
```bash
# Windows
start.bat

# Linux/macOS
./start.sh
```

### 方式二：手动启动

#### 1. 启动后端
```bash
cd backend
pip install -r requirements.txt
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8001
```

#### 2. 启动前端
```bash
cd frontend
npm install
npm start
```

### 访问应用
- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8001
- **API文档**: http://localhost:8001/docs

## API文档

启动后端后访问: http://localhost:8000/docs

## 功能特性

### 🎯 UI元素识别
- 自动识别界面中的交互元素（按钮、输入框、链接等）
- 提供详细的视觉特征描述
- 包含位置信息和功能分析
- 支持置信度评分

### 🔗 交互流程分析
- 分析用户操作路径
- 生成完整的交互流程图
- 识别关键决策点和验证点
- 包含异常场景处理

### 📝 测试用例生成
- 基于MidScene.js规范生成测试用例
- 符合最佳实践的提示词工程
- 包含详细的视觉定位描述
- 支持多种测试场景

### 🚀 实时分析
- 文件上传和处理
- 实时进度反馈
- 结构化结果展示
- 响应式界面设计

## 使用说明

### 1. 上传界面截图
1. 打开前端界面 http://localhost:3000
2. 点击"选择图片文件"上传UI界面截图
3. 在文本框中描述界面的主要功能
4. 点击"🚀 开始分析"按钮

### 2. 查看分析结果
分析完成后，可以查看三个维度的结果：

#### UI元素识别
- 查看识别到的所有交互元素
- 了解每个元素的视觉特征和功能
- 查看置信度评分

#### 交互流程分析
- 查看用户操作的完整流程
- 了解每个步骤的前置条件和预期结果
- 查看异常场景处理

#### 测试用例生成
- 查看生成的MidScene.js测试用例
- 了解每个测试步骤的详细描述
- 查看验证要点和优先级

### 3. 导出和使用
- 复制生成的测试用例代码
- 集成到现有的自动化测试框架
- 根据实际需求调整测试参数

## 项目状态

### ✅ 已完成功能
- [x] 项目架构搭建
- [x] 后端FastAPI服务
- [x] 前端React界面
- [x] 文件上传功能
- [x] 模拟分析流程
- [x] 结果展示界面
- [x] 智能体提示词设计
- [x] Docker容器化配置

### 🚧 演示版本说明
当前版本为演示版本，包含：
- 完整的前后端架构
- 模拟的AI分析结果
- 完整的用户界面流程
- 详细的智能体提示词

### 🔮 后续开发计划
- [ ] 集成真实的AI模型API
- [ ] 实现Redis+Celery异步任务队列
- [ ] 添加SSE实时进度推送
- [ ] 集成图像识别模型
- [ ] 添加用户认证系统
- [ ] 实现结果导出功能
- [ ] 添加批量处理功能
- [ ] 优化UI/UX设计

## 技术架构

### 后端技术栈
- **框架**: FastAPI 0.115+
- **异步**: Uvicorn ASGI服务器
- **任务队列**: Celery + Redis（规划中）
- **文件处理**: Python Pillow
- **API文档**: Swagger/OpenAPI

### 前端技术栈
- **框架**: React 18
- **构建工具**: Create React App
- **HTTP客户端**: Fetch API
- **样式**: CSS3 + Flexbox/Grid
- **组件**: 函数式组件 + Hooks

### 智能体设计
- **元素识别**: 基于视觉特征的UI元素分析
- **交互分析**: 用户行为路径建模
- **用例生成**: MidScene.js规范的测试用例生成

## 贡献指南

欢迎贡献代码！请查看 [DEVELOPMENT.md](DEVELOPMENT.md) 了解详细的开发指南。

### 开发流程
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目地址: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 文档: [项目文档]

---

**注意**: 这是一个演示版本的UI自动化分析平台，展示了完整的技术架构和用户流程。要在生产环境中使用，需要集成真实的AI模型和完善相关功能。


Terminal
$ python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8001