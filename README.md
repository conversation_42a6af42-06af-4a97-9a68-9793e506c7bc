# UI自动化分析平台

基于AI智能体的UI界面分析和自动化测试用例生成平台。

## 项目结构

```
ui-automation/
├── backend/                # FastAPI 后端
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py        # FastAPI 应用入口
│   │   ├── api/           # API 路由
│   │   ├── agents/        # 智能体模块
│   │   ├── core/          # 核心功能
│   │   └── models/        # 数据模型
│   ├── requirements.txt
│   └── Dockerfile
├── frontend/               # React 前端
│   ├── src/
│   │   ├── components/    # React 组件
│   │   ├── pages/         # 页面组件
│   │   ├── services/      # API 服务
│   │   └── utils/         # 工具函数
│   ├── package.json
│   └── public/
├── agents/                 # 智能体配置和提示词
│   ├── element_detection/  # 元素识别智能体
│   ├── interaction_analysis/ # 交互分析智能体
│   └── test_generation/   # 用例生成智能体
├── docker-compose.yml     # 容器编排
└── README.md
```

## 核心功能

1. **文件上传**: 支持图片上传和文字描述
2. **智能体流水线**: 元素识别 → 交互分析 → 用例生成
3. **实时反馈**: SSE流式输出处理进度
4. **结果可视化**: 元素标注、流程图、测试用例

## 技术栈

- **后端**: FastAPI + Celery + Redis
- **前端**: React + TypeScript + Vite
- **AI**: 基于提示词工程的智能体系统
- **可视化**: Fabric.js + Mermaid.js

## 快速开始

### 后端启动
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload
```

### 前端启动
```bash
cd frontend
npm install
npm run dev
```

### Redis启动
```bash
redis-server
```

### Celery Worker启动
```bash
cd backend
celery -A app.core.celery worker --loglevel=info
```

## API文档

启动后端后访问: http://localhost:8000/docs

## 智能体说明

### 1. 元素识别智能体
- 基于UI-TARS技术
- 识别界面中的交互元素
- 输出结构化的元素信息

### 2. 交互分析智能体
- 分析用户操作流程
- 生成交互路径图
- 识别关键决策点

### 3. 用例生成智能体
- 基于MidScene.js规范
- 生成自动化测试用例
- 符合最佳实践的提示词工程
