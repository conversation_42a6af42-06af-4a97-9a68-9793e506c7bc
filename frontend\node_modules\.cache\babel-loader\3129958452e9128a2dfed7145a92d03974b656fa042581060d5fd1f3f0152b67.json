{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\pages\\\\HistoryPage.js\",\n  _s = $RefreshSig$();\n/**\n * 历史记录页面\n */\nimport React, { useState, useEffect } from 'react';\n\n// 生成标准脚本名称格式：年月日+时分+脚本名称+spec.ts\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst generateScriptName = (createdAt, description) => {\n  const date = new Date(createdAt);\n  const year = date.getFullYear();\n  const month = String(date.getMonth() + 1).padStart(2, '0');\n  const day = String(date.getDate()).padStart(2, '0');\n  const hour = String(date.getHours()).padStart(2, '0');\n  const minute = String(date.getMinutes()).padStart(2, '0');\n\n  // 从描述中提取脚本名称，去掉\"界面\"、\"分析\"等后缀\n  const scriptName = description.replace(/界面分析?$/, '').replace(/功能分析?$/, '').replace(/分析$/, '').replace(/界面$/, '').trim();\n  return `${year}${month}${day}-${hour}${minute}-${scriptName}.spec.ts`;\n};\nconst HistoryPage = () => {\n  _s();\n  const [historyData, setHistoryData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedItem, setSelectedItem] = useState(null);\n  useEffect(() => {\n    // 从后端加载真实的脚本数据\n    const loadScriptsData = async () => {\n      try {\n        const response = await fetch('http://localhost:8000/api/v1/scripts');\n        if (response.ok) {\n          const data = await response.json();\n          const scripts = data.scripts || [];\n\n          // 转换数据格式\n          const processedData = scripts.map(script => ({\n            id: script.id,\n            taskId: script.id,\n            fileName: script.original_name || script.script_name,\n            description: script.description,\n            status: 'completed',\n            createdAt: script.created_at || new Date().toISOString(),\n            elementsCount: 0,\n            // 这些数据需要从分析结果中获取\n            flowsCount: 0,\n            scriptsCount: 1,\n            scriptName: script.script_name,\n            filePath: script.file_path,\n            priority: script.priority,\n            runCount: script.run_count || 0\n          }));\n          setHistoryData(processedData);\n        } else {\n          // 如果后端不可用，使用模拟数据\n          const rawData = [{\n            id: '1',\n            taskId: 'task-001',\n            fileName: '登录界面.png',\n            description: '用户登录界面分析',\n            status: 'completed',\n            createdAt: '2024-12-17 10:30:00',\n            elementsCount: 5,\n            flowsCount: 2,\n            scriptsCount: 1\n          }, {\n            id: '2',\n            taskId: 'task-002',\n            fileName: '文件管理界面.png',\n            description: '文件管理界面功能分析',\n            status: 'completed',\n            createdAt: '2024-12-17 14:15:00',\n            elementsCount: 8,\n            flowsCount: 3,\n            scriptsCount: 2\n          }];\n\n          // 为每个项目生成标准脚本名称\n          const processedData = rawData.map(item => ({\n            ...item,\n            scriptName: generateScriptName(item.createdAt, item.description)\n          }));\n          setHistoryData(processedData);\n        }\n      } catch (error) {\n        console.error('加载脚本数据失败:', error);\n        // 使用模拟数据作为后备\n        setHistoryData([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadScriptsData();\n  }, []);\n  const getStatusColor = status => {\n    switch (status) {\n      case 'completed':\n        return '#28a745';\n      case 'failed':\n        return '#dc3545';\n      case 'processing':\n        return '#007bff';\n      default:\n        return '#6c757d';\n    }\n  };\n  const getStatusText = status => {\n    switch (status) {\n      case 'completed':\n        return '已完成';\n      case 'failed':\n        return '失败';\n      case 'processing':\n        return '处理中';\n      default:\n        return '未知';\n    }\n  };\n  const handleViewDetails = item => {\n    setSelectedItem(item);\n  };\n  const handleRunScript = async item => {\n    try {\n      const response = await fetch(`http://localhost:8000/api/v1/scripts/${item.id}/run`, {\n        method: 'POST'\n      });\n      if (response.ok) {\n        const result = await response.json();\n        alert(`脚本开始运行: ${result.message}`);\n        // 可以添加实时状态更新\n      } else {\n        const error = await response.json();\n        alert(`运行失败: ${error.detail}`);\n      }\n    } catch (error) {\n      console.error('运行脚本失败:', error);\n      alert('运行脚本失败，请检查网络连接');\n    }\n  };\n  const handleViewReport = async item => {\n    try {\n      const response = await fetch(`http://localhost:8000/api/v1/scripts/${item.id}/report`);\n      if (response.ok) {\n        const result = await response.json();\n        if (result.exists) {\n          // 打开报告页面\n          window.open(result.report_url, '_blank');\n        } else {\n          alert('报告文件不存在，请先运行脚本');\n        }\n      } else {\n        alert('获取报告失败');\n      }\n    } catch (error) {\n      console.error('查看报告失败:', error);\n      alert('查看报告失败，请检查网络连接');\n    }\n  };\n  const handleViewRunHistory = async item => {\n    try {\n      const response = await fetch(`http://localhost:8000/api/v1/scripts/${item.id}/run-history`);\n      if (response.ok) {\n        const result = await response.json();\n        // 显示运行历史\n        const historyText = result.run_history.map(run => `时间: ${run.timestamp}\\n状态: ${run.status}\\n${run.error ? '错误: ' + run.error : ''}`).join('\\n\\n');\n        alert(`运行历史 (共${result.total_runs}次):\\n\\n${historyText}`);\n      } else {\n        alert('获取运行历史失败');\n      }\n    } catch (error) {\n      console.error('查看运行历史失败:', error);\n      alert('查看运行历史失败，请检查网络连接');\n    }\n  };\n  const handleDownloadScript = item => {\n    // 模拟下载脚本\n    const scriptContent = `# ${item.description}\\n# 生成时间: ${item.createdAt}\\n# 脚本名称: ${item.scriptName}\\n\\nname: ${item.description}\\ndescription: 自动化测试脚本\\n\\nsteps:\\n  - name: 示例步骤\\n    action: aiTap\\n    locate: 示例元素\\n    expect: 预期结果`;\n    const blob = new Blob([scriptContent], {\n      type: 'text/yaml'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    // 使用标准脚本名称格式，但保持.yaml扩展名用于下载\n    a.download = item.scriptName.replace('.spec.ts', '.yaml');\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-page\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83D\\uDCCB \\u5206\\u6790\\u5386\\u53F2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u67E5\\u770B\\u6240\\u6709UI\\u5206\\u6790\\u8BB0\\u5F55\\u548C\\u7ED3\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u52A0\\u8F7D\\u5386\\u53F2\\u8BB0\\u5F55\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"history-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDCCB \\u5206\\u6790\\u5386\\u53F2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u67E5\\u770B\\u6240\\u6709UI\\u5206\\u6790\\u8BB0\\u5F55\\u548C\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-content\",\n      children: historyData.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-icon\",\n          children: \"\\uD83D\\uDCDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u6682\\u65E0\\u5206\\u6790\\u8BB0\\u5F55\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u5F00\\u59CB\\u60A8\\u7684\\u7B2C\\u4E00\\u6B21UI\\u5206\\u6790\\u5427\\uFF01\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-list\",\n        children: historyData.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"history-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"item-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: item.scriptName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '12px',\n                  color: '#999',\n                  margin: '4px 0 0 0'\n                },\n                children: [\"\\u539F\\u59CB\\u6587\\u4EF6: \", item.fileName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-badge\",\n                style: {\n                  backgroundColor: getStatusColor(item.status)\n                },\n                children: getStatusText(item.status)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"item-stats\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-label\",\n                children: \"UI\\u5143\\u7D20\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-value\",\n                children: item.elementsCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-label\",\n                children: \"\\u4EA4\\u4E92\\u6D41\\u7A0B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-value\",\n                children: item.flowsCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-label\",\n                children: \"\\u6D4B\\u8BD5\\u811A\\u672C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-value\",\n                children: item.scriptsCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"item-footer\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"item-time\",\n              children: [\"\\u521B\\u5EFA\\u65F6\\u95F4: \", item.createdAt]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-secondary\",\n                onClick: () => handleViewDetails(item),\n                children: \"\\u67E5\\u770B\\u8BE6\\u60C5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 21\n              }, this), item.status === 'completed' && /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-primary\",\n                onClick: () => handleDownloadScript(item),\n                children: \"\\u4E0B\\u8F7D\\u811A\\u672C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 17\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        .history-page {\n          min-height: 100vh;\n          background: #f8f9fa;\n        }\n\n        .page-header {\n          background: white;\n          border-bottom: 1px solid #e9ecef;\n          padding: 24px 0;\n          margin-bottom: 24px;\n        }\n\n        .page-header h1 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 28px;\n          font-weight: 600;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-header p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-content {\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 0 24px;\n        }\n\n        .loading-container {\n          text-align: center;\n          padding: 60px 20px;\n        }\n\n        .loading-spinner {\n          width: 40px;\n          height: 40px;\n          border: 4px solid #e9ecef;\n          border-top: 4px solid #007bff;\n          border-radius: 50%;\n          animation: spin 1s linear infinite;\n          margin: 0 auto 16px;\n        }\n\n        .empty-state {\n          text-align: center;\n          padding: 60px 20px;\n          background: white;\n          border-radius: 12px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n        }\n\n        .empty-icon {\n          font-size: 48px;\n          margin-bottom: 16px;\n        }\n\n        .empty-state h3 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 20px;\n        }\n\n        .empty-state p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n        }\n\n        .history-list {\n          display: grid;\n          gap: 16px;\n        }\n\n        .history-item {\n          background: white;\n          border-radius: 12px;\n          padding: 20px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n          transition: all 0.2s ease;\n        }\n\n        .history-item:hover {\n          box-shadow: 0 4px 20px rgba(0,0,0,0.15);\n        }\n\n        .item-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 16px;\n        }\n\n        .item-info h3 {\n          margin: 0 0 4px 0;\n          color: #333;\n          font-size: 18px;\n          font-weight: 600;\n        }\n\n        .item-info p {\n          margin: 0;\n          color: #666;\n          font-size: 14px;\n        }\n\n        .status-badge {\n          color: white;\n          padding: 4px 12px;\n          border-radius: 20px;\n          font-size: 12px;\n          font-weight: 500;\n        }\n\n        .item-stats {\n          display: flex;\n          gap: 24px;\n          margin-bottom: 16px;\n          padding: 16px 0;\n          border-top: 1px solid #e9ecef;\n          border-bottom: 1px solid #e9ecef;\n        }\n\n        .stat {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          gap: 4px;\n        }\n\n        .stat-label {\n          font-size: 12px;\n          color: #666;\n        }\n\n        .stat-value {\n          font-size: 18px;\n          font-weight: 600;\n          color: #333;\n        }\n\n        .item-footer {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n        }\n\n        .item-time {\n          font-size: 12px;\n          color: #999;\n        }\n\n        .item-actions {\n          display: flex;\n          gap: 8px;\n        }\n\n        .btn-primary, .btn-secondary {\n          border: none;\n          padding: 8px 16px;\n          border-radius: 6px;\n          font-size: 14px;\n          cursor: pointer;\n          transition: all 0.2s ease;\n        }\n\n        .btn-primary {\n          background: #007bff;\n          color: white;\n        }\n\n        .btn-primary:hover {\n          background: #0056b3;\n        }\n\n        .btn-secondary {\n          background: #6c757d;\n          color: white;\n        }\n\n        .btn-secondary:hover {\n          background: #5a6268;\n        }\n\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n\n        /* 响应式设计 */\n        @media (max-width: 768px) {\n          .page-header h1, .page-header p, .page-content {\n            padding: 0 16px;\n          }\n\n          .item-header {\n            flex-direction: column;\n            gap: 12px;\n          }\n\n          .item-stats {\n            justify-content: space-around;\n          }\n\n          .item-footer {\n            flex-direction: column;\n            gap: 12px;\n            align-items: flex-start;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 226,\n    columnNumber: 5\n  }, this);\n};\n_s(HistoryPage, \"qeDha3f5n1EOPY7s+4jS7jZpbkg=\");\n_c = HistoryPage;\nexport default HistoryPage;\nvar _c;\n$RefreshReg$(_c, \"HistoryPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "generateScriptName", "createdAt", "description", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hour", "getHours", "minute", "getMinutes", "scriptName", "replace", "trim", "HistoryPage", "_s", "historyData", "setHistoryData", "loading", "setLoading", "selectedItem", "setSelectedItem", "loadScriptsData", "response", "fetch", "ok", "data", "json", "scripts", "processedData", "map", "script", "id", "taskId", "fileName", "original_name", "script_name", "status", "created_at", "toISOString", "elementsCount", "flowsCount", "scriptsCount", "filePath", "file_path", "priority", "runCount", "run_count", "rawData", "item", "error", "console", "getStatusColor", "getStatusText", "handleViewDetails", "handleRunScript", "method", "result", "alert", "message", "detail", "handleViewReport", "exists", "window", "open", "report_url", "handleViewRunHistory", "historyText", "run_history", "run", "timestamp", "join", "total_runs", "handleDownloadScript", "scriptContent", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "className", "children", "_jsxFileName", "lineNumber", "columnNumber", "length", "style", "fontSize", "color", "margin", "backgroundColor", "onClick", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/pages/HistoryPage.js"], "sourcesContent": ["/**\n * 历史记录页面\n */\nimport React, { useState, useEffect } from 'react';\n\n// 生成标准脚本名称格式：年月日+时分+脚本名称+spec.ts\nconst generateScriptName = (createdAt, description) => {\n  const date = new Date(createdAt);\n  const year = date.getFullYear();\n  const month = String(date.getMonth() + 1).padStart(2, '0');\n  const day = String(date.getDate()).padStart(2, '0');\n  const hour = String(date.getHours()).padStart(2, '0');\n  const minute = String(date.getMinutes()).padStart(2, '0');\n\n  // 从描述中提取脚本名称，去掉\"界面\"、\"分析\"等后缀\n  const scriptName = description\n    .replace(/界面分析?$/, '')\n    .replace(/功能分析?$/, '')\n    .replace(/分析$/, '')\n    .replace(/界面$/, '')\n    .trim();\n\n  return `${year}${month}${day}-${hour}${minute}-${scriptName}.spec.ts`;\n};\n\nconst HistoryPage = () => {\n  const [historyData, setHistoryData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedItem, setSelectedItem] = useState(null);\n\n  useEffect(() => {\n    // 从后端加载真实的脚本数据\n    const loadScriptsData = async () => {\n      try {\n        const response = await fetch('http://localhost:8000/api/v1/scripts');\n        if (response.ok) {\n          const data = await response.json();\n          const scripts = data.scripts || [];\n\n          // 转换数据格式\n          const processedData = scripts.map(script => ({\n            id: script.id,\n            taskId: script.id,\n            fileName: script.original_name || script.script_name,\n            description: script.description,\n            status: 'completed',\n            createdAt: script.created_at || new Date().toISOString(),\n            elementsCount: 0, // 这些数据需要从分析结果中获取\n            flowsCount: 0,\n            scriptsCount: 1,\n            scriptName: script.script_name,\n            filePath: script.file_path,\n            priority: script.priority,\n            runCount: script.run_count || 0\n          }));\n\n          setHistoryData(processedData);\n        } else {\n          // 如果后端不可用，使用模拟数据\n          const rawData = [\n            {\n              id: '1',\n              taskId: 'task-001',\n              fileName: '登录界面.png',\n              description: '用户登录界面分析',\n              status: 'completed',\n              createdAt: '2024-12-17 10:30:00',\n              elementsCount: 5,\n              flowsCount: 2,\n              scriptsCount: 1\n            },\n            {\n              id: '2',\n              taskId: 'task-002',\n              fileName: '文件管理界面.png',\n              description: '文件管理界面功能分析',\n              status: 'completed',\n              createdAt: '2024-12-17 14:15:00',\n              elementsCount: 8,\n              flowsCount: 3,\n              scriptsCount: 2\n            }\n          ];\n\n          // 为每个项目生成标准脚本名称\n          const processedData = rawData.map(item => ({\n            ...item,\n            scriptName: generateScriptName(item.createdAt, item.description)\n          }));\n\n          setHistoryData(processedData);\n        }\n      } catch (error) {\n        console.error('加载脚本数据失败:', error);\n        // 使用模拟数据作为后备\n        setHistoryData([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadScriptsData();\n  }, []);\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'completed':\n        return '#28a745';\n      case 'failed':\n        return '#dc3545';\n      case 'processing':\n        return '#007bff';\n      default:\n        return '#6c757d';\n    }\n  };\n\n  const getStatusText = (status) => {\n    switch (status) {\n      case 'completed':\n        return '已完成';\n      case 'failed':\n        return '失败';\n      case 'processing':\n        return '处理中';\n      default:\n        return '未知';\n    }\n  };\n\n  const handleViewDetails = (item) => {\n    setSelectedItem(item);\n  };\n\n  const handleRunScript = async (item) => {\n    try {\n      const response = await fetch(`http://localhost:8000/api/v1/scripts/${item.id}/run`, {\n        method: 'POST'\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        alert(`脚本开始运行: ${result.message}`);\n        // 可以添加实时状态更新\n      } else {\n        const error = await response.json();\n        alert(`运行失败: ${error.detail}`);\n      }\n    } catch (error) {\n      console.error('运行脚本失败:', error);\n      alert('运行脚本失败，请检查网络连接');\n    }\n  };\n\n  const handleViewReport = async (item) => {\n    try {\n      const response = await fetch(`http://localhost:8000/api/v1/scripts/${item.id}/report`);\n      if (response.ok) {\n        const result = await response.json();\n        if (result.exists) {\n          // 打开报告页面\n          window.open(result.report_url, '_blank');\n        } else {\n          alert('报告文件不存在，请先运行脚本');\n        }\n      } else {\n        alert('获取报告失败');\n      }\n    } catch (error) {\n      console.error('查看报告失败:', error);\n      alert('查看报告失败，请检查网络连接');\n    }\n  };\n\n  const handleViewRunHistory = async (item) => {\n    try {\n      const response = await fetch(`http://localhost:8000/api/v1/scripts/${item.id}/run-history`);\n      if (response.ok) {\n        const result = await response.json();\n        // 显示运行历史\n        const historyText = result.run_history.map(run =>\n          `时间: ${run.timestamp}\\n状态: ${run.status}\\n${run.error ? '错误: ' + run.error : ''}`\n        ).join('\\n\\n');\n        alert(`运行历史 (共${result.total_runs}次):\\n\\n${historyText}`);\n      } else {\n        alert('获取运行历史失败');\n      }\n    } catch (error) {\n      console.error('查看运行历史失败:', error);\n      alert('查看运行历史失败，请检查网络连接');\n    }\n  };\n\n  const handleDownloadScript = (item) => {\n    // 模拟下载脚本\n    const scriptContent = `# ${item.description}\\n# 生成时间: ${item.createdAt}\\n# 脚本名称: ${item.scriptName}\\n\\nname: ${item.description}\\ndescription: 自动化测试脚本\\n\\nsteps:\\n  - name: 示例步骤\\n    action: aiTap\\n    locate: 示例元素\\n    expect: 预期结果`;\n\n    const blob = new Blob([scriptContent], { type: 'text/yaml' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    // 使用标准脚本名称格式，但保持.yaml扩展名用于下载\n    a.download = item.scriptName.replace('.spec.ts', '.yaml');\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"history-page\">\n        <div className=\"page-header\">\n          <h1>📋 分析历史</h1>\n          <p>查看所有UI分析记录和结果</p>\n        </div>\n        <div className=\"loading-container\">\n          <div className=\"loading-spinner\"></div>\n          <p>加载历史记录中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"history-page\">\n      <div className=\"page-header\">\n        <h1>📋 分析历史</h1>\n        <p>查看所有UI分析记录和结果</p>\n      </div>\n\n      <div className=\"page-content\">\n        {historyData.length === 0 ? (\n          <div className=\"empty-state\">\n            <div className=\"empty-icon\">📝</div>\n            <h3>暂无分析记录</h3>\n            <p>开始您的第一次UI分析吧！</p>\n          </div>\n        ) : (\n          <div className=\"history-list\">\n            {historyData.map((item) => (\n              <div key={item.id} className=\"history-item\">\n                <div className=\"item-header\">\n                  <div className=\"item-info\">\n                    <h3>{item.scriptName}</h3>\n                    <p>{item.description}</p>\n                    <p style={{ fontSize: '12px', color: '#999', margin: '4px 0 0 0' }}>\n                      原始文件: {item.fileName}\n                    </p>\n                  </div>\n                  <div className=\"item-status\">\n                    <span \n                      className=\"status-badge\"\n                      style={{ backgroundColor: getStatusColor(item.status) }}\n                    >\n                      {getStatusText(item.status)}\n                    </span>\n                  </div>\n                </div>\n                \n                <div className=\"item-stats\">\n                  <div className=\"stat\">\n                    <span className=\"stat-label\">UI元素</span>\n                    <span className=\"stat-value\">{item.elementsCount}</span>\n                  </div>\n                  <div className=\"stat\">\n                    <span className=\"stat-label\">交互流程</span>\n                    <span className=\"stat-value\">{item.flowsCount}</span>\n                  </div>\n                  <div className=\"stat\">\n                    <span className=\"stat-label\">测试脚本</span>\n                    <span className=\"stat-value\">{item.scriptsCount}</span>\n                  </div>\n                </div>\n\n                <div className=\"item-footer\">\n                  <span className=\"item-time\">创建时间: {item.createdAt}</span>\n                  <div className=\"item-actions\">\n                    <button \n                      className=\"btn-secondary\"\n                      onClick={() => handleViewDetails(item)}\n                    >\n                      查看详情\n                    </button>\n                    {item.status === 'completed' && (\n                      <button \n                        className=\"btn-primary\"\n                        onClick={() => handleDownloadScript(item)}\n                      >\n                        下载脚本\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      <style>{`\n        .history-page {\n          min-height: 100vh;\n          background: #f8f9fa;\n        }\n\n        .page-header {\n          background: white;\n          border-bottom: 1px solid #e9ecef;\n          padding: 24px 0;\n          margin-bottom: 24px;\n        }\n\n        .page-header h1 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 28px;\n          font-weight: 600;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-header p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-content {\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 0 24px;\n        }\n\n        .loading-container {\n          text-align: center;\n          padding: 60px 20px;\n        }\n\n        .loading-spinner {\n          width: 40px;\n          height: 40px;\n          border: 4px solid #e9ecef;\n          border-top: 4px solid #007bff;\n          border-radius: 50%;\n          animation: spin 1s linear infinite;\n          margin: 0 auto 16px;\n        }\n\n        .empty-state {\n          text-align: center;\n          padding: 60px 20px;\n          background: white;\n          border-radius: 12px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n        }\n\n        .empty-icon {\n          font-size: 48px;\n          margin-bottom: 16px;\n        }\n\n        .empty-state h3 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 20px;\n        }\n\n        .empty-state p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n        }\n\n        .history-list {\n          display: grid;\n          gap: 16px;\n        }\n\n        .history-item {\n          background: white;\n          border-radius: 12px;\n          padding: 20px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n          transition: all 0.2s ease;\n        }\n\n        .history-item:hover {\n          box-shadow: 0 4px 20px rgba(0,0,0,0.15);\n        }\n\n        .item-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 16px;\n        }\n\n        .item-info h3 {\n          margin: 0 0 4px 0;\n          color: #333;\n          font-size: 18px;\n          font-weight: 600;\n        }\n\n        .item-info p {\n          margin: 0;\n          color: #666;\n          font-size: 14px;\n        }\n\n        .status-badge {\n          color: white;\n          padding: 4px 12px;\n          border-radius: 20px;\n          font-size: 12px;\n          font-weight: 500;\n        }\n\n        .item-stats {\n          display: flex;\n          gap: 24px;\n          margin-bottom: 16px;\n          padding: 16px 0;\n          border-top: 1px solid #e9ecef;\n          border-bottom: 1px solid #e9ecef;\n        }\n\n        .stat {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          gap: 4px;\n        }\n\n        .stat-label {\n          font-size: 12px;\n          color: #666;\n        }\n\n        .stat-value {\n          font-size: 18px;\n          font-weight: 600;\n          color: #333;\n        }\n\n        .item-footer {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n        }\n\n        .item-time {\n          font-size: 12px;\n          color: #999;\n        }\n\n        .item-actions {\n          display: flex;\n          gap: 8px;\n        }\n\n        .btn-primary, .btn-secondary {\n          border: none;\n          padding: 8px 16px;\n          border-radius: 6px;\n          font-size: 14px;\n          cursor: pointer;\n          transition: all 0.2s ease;\n        }\n\n        .btn-primary {\n          background: #007bff;\n          color: white;\n        }\n\n        .btn-primary:hover {\n          background: #0056b3;\n        }\n\n        .btn-secondary {\n          background: #6c757d;\n          color: white;\n        }\n\n        .btn-secondary:hover {\n          background: #5a6268;\n        }\n\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n\n        /* 响应式设计 */\n        @media (max-width: 768px) {\n          .page-header h1, .page-header p, .page-content {\n            padding: 0 16px;\n          }\n\n          .item-header {\n            flex-direction: column;\n            gap: 12px;\n          }\n\n          .item-stats {\n            justify-content: space-around;\n          }\n\n          .item-footer {\n            flex-direction: column;\n            gap: 12px;\n            align-items: flex-start;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default HistoryPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;;AAElD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,WAAW,KAAK;EACrD,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACH,SAAS,CAAC;EAChC,MAAMI,IAAI,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;EAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EACnD,MAAMG,IAAI,GAAGL,MAAM,CAACL,IAAI,CAACW,QAAQ,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EACrD,MAAMK,MAAM,GAAGP,MAAM,CAACL,IAAI,CAACa,UAAU,CAAC,CAAC,CAAC,CAACN,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;;EAEzD;EACA,MAAMO,UAAU,GAAGf,WAAW,CAC3BgB,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CACrBA,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBC,IAAI,CAAC,CAAC;EAET,OAAO,GAAGd,IAAI,GAAGE,KAAK,GAAGI,GAAG,IAAIE,IAAI,GAAGE,MAAM,IAAIE,UAAU,UAAU;AACvE,CAAC;AAED,MAAMG,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAEtDC,SAAS,CAAC,MAAM;IACd;IACA,MAAM+B,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,sCAAsC,CAAC;QACpE,IAAID,QAAQ,CAACE,EAAE,EAAE;UACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;UAClC,MAAMC,OAAO,GAAGF,IAAI,CAACE,OAAO,IAAI,EAAE;;UAElC;UACA,MAAMC,aAAa,GAAGD,OAAO,CAACE,GAAG,CAACC,MAAM,KAAK;YAC3CC,EAAE,EAAED,MAAM,CAACC,EAAE;YACbC,MAAM,EAAEF,MAAM,CAACC,EAAE;YACjBE,QAAQ,EAAEH,MAAM,CAACI,aAAa,IAAIJ,MAAM,CAACK,WAAW;YACpDxC,WAAW,EAAEmC,MAAM,CAACnC,WAAW;YAC/ByC,MAAM,EAAE,WAAW;YACnB1C,SAAS,EAAEoC,MAAM,CAACO,UAAU,IAAI,IAAIxC,IAAI,CAAC,CAAC,CAACyC,WAAW,CAAC,CAAC;YACxDC,aAAa,EAAE,CAAC;YAAE;YAClBC,UAAU,EAAE,CAAC;YACbC,YAAY,EAAE,CAAC;YACf/B,UAAU,EAAEoB,MAAM,CAACK,WAAW;YAC9BO,QAAQ,EAAEZ,MAAM,CAACa,SAAS;YAC1BC,QAAQ,EAAEd,MAAM,CAACc,QAAQ;YACzBC,QAAQ,EAAEf,MAAM,CAACgB,SAAS,IAAI;UAChC,CAAC,CAAC,CAAC;UAEH9B,cAAc,CAACY,aAAa,CAAC;QAC/B,CAAC,MAAM;UACL;UACA,MAAMmB,OAAO,GAAG,CACd;YACEhB,EAAE,EAAE,GAAG;YACPC,MAAM,EAAE,UAAU;YAClBC,QAAQ,EAAE,UAAU;YACpBtC,WAAW,EAAE,UAAU;YACvByC,MAAM,EAAE,WAAW;YACnB1C,SAAS,EAAE,qBAAqB;YAChC6C,aAAa,EAAE,CAAC;YAChBC,UAAU,EAAE,CAAC;YACbC,YAAY,EAAE;UAChB,CAAC,EACD;YACEV,EAAE,EAAE,GAAG;YACPC,MAAM,EAAE,UAAU;YAClBC,QAAQ,EAAE,YAAY;YACtBtC,WAAW,EAAE,YAAY;YACzByC,MAAM,EAAE,WAAW;YACnB1C,SAAS,EAAE,qBAAqB;YAChC6C,aAAa,EAAE,CAAC;YAChBC,UAAU,EAAE,CAAC;YACbC,YAAY,EAAE;UAChB,CAAC,CACF;;UAED;UACA,MAAMb,aAAa,GAAGmB,OAAO,CAAClB,GAAG,CAACmB,IAAI,KAAK;YACzC,GAAGA,IAAI;YACPtC,UAAU,EAAEjB,kBAAkB,CAACuD,IAAI,CAACtD,SAAS,EAAEsD,IAAI,CAACrD,WAAW;UACjE,CAAC,CAAC,CAAC;UAEHqB,cAAc,CAACY,aAAa,CAAC;QAC/B;MACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC;QACAjC,cAAc,CAAC,EAAE,CAAC;MACpB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM8B,cAAc,GAAIf,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,YAAY;QACf,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMgB,aAAa,GAAIhB,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,KAAK;MACd,KAAK,QAAQ;QACX,OAAO,IAAI;MACb,KAAK,YAAY;QACf,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMiB,iBAAiB,GAAIL,IAAI,IAAK;IAClC5B,eAAe,CAAC4B,IAAI,CAAC;EACvB,CAAC;EAED,MAAMM,eAAe,GAAG,MAAON,IAAI,IAAK;IACtC,IAAI;MACF,MAAM1B,QAAQ,GAAG,MAAMC,KAAK,CAAC,wCAAwCyB,IAAI,CAACjB,EAAE,MAAM,EAAE;QAClFwB,MAAM,EAAE;MACV,CAAC,CAAC;MAEF,IAAIjC,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMgC,MAAM,GAAG,MAAMlC,QAAQ,CAACI,IAAI,CAAC,CAAC;QACpC+B,KAAK,CAAC,WAAWD,MAAM,CAACE,OAAO,EAAE,CAAC;QAClC;MACF,CAAC,MAAM;QACL,MAAMT,KAAK,GAAG,MAAM3B,QAAQ,CAACI,IAAI,CAAC,CAAC;QACnC+B,KAAK,CAAC,SAASR,KAAK,CAACU,MAAM,EAAE,CAAC;MAChC;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BQ,KAAK,CAAC,gBAAgB,CAAC;IACzB;EACF,CAAC;EAED,MAAMG,gBAAgB,GAAG,MAAOZ,IAAI,IAAK;IACvC,IAAI;MACF,MAAM1B,QAAQ,GAAG,MAAMC,KAAK,CAAC,wCAAwCyB,IAAI,CAACjB,EAAE,SAAS,CAAC;MACtF,IAAIT,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMgC,MAAM,GAAG,MAAMlC,QAAQ,CAACI,IAAI,CAAC,CAAC;QACpC,IAAI8B,MAAM,CAACK,MAAM,EAAE;UACjB;UACAC,MAAM,CAACC,IAAI,CAACP,MAAM,CAACQ,UAAU,EAAE,QAAQ,CAAC;QAC1C,CAAC,MAAM;UACLP,KAAK,CAAC,gBAAgB,CAAC;QACzB;MACF,CAAC,MAAM;QACLA,KAAK,CAAC,QAAQ,CAAC;MACjB;IACF,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BQ,KAAK,CAAC,gBAAgB,CAAC;IACzB;EACF,CAAC;EAED,MAAMQ,oBAAoB,GAAG,MAAOjB,IAAI,IAAK;IAC3C,IAAI;MACF,MAAM1B,QAAQ,GAAG,MAAMC,KAAK,CAAC,wCAAwCyB,IAAI,CAACjB,EAAE,cAAc,CAAC;MAC3F,IAAIT,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMgC,MAAM,GAAG,MAAMlC,QAAQ,CAACI,IAAI,CAAC,CAAC;QACpC;QACA,MAAMwC,WAAW,GAAGV,MAAM,CAACW,WAAW,CAACtC,GAAG,CAACuC,GAAG,IAC5C,OAAOA,GAAG,CAACC,SAAS,SAASD,GAAG,CAAChC,MAAM,KAAKgC,GAAG,CAACnB,KAAK,GAAG,MAAM,GAAGmB,GAAG,CAACnB,KAAK,GAAG,EAAE,EACjF,CAAC,CAACqB,IAAI,CAAC,MAAM,CAAC;QACdb,KAAK,CAAC,UAAUD,MAAM,CAACe,UAAU,UAAUL,WAAW,EAAE,CAAC;MAC3D,CAAC,MAAM;QACLT,KAAK,CAAC,UAAU,CAAC;MACnB;IACF,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCQ,KAAK,CAAC,kBAAkB,CAAC;IAC3B;EACF,CAAC;EAED,MAAMe,oBAAoB,GAAIxB,IAAI,IAAK;IACrC;IACA,MAAMyB,aAAa,GAAG,KAAKzB,IAAI,CAACrD,WAAW,aAAaqD,IAAI,CAACtD,SAAS,aAAasD,IAAI,CAACtC,UAAU,aAAasC,IAAI,CAACrD,WAAW,yGAAyG;IAExO,MAAM+E,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,aAAa,CAAC,EAAE;MAAEG,IAAI,EAAE;IAAY,CAAC,CAAC;IAC7D,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IACrC,MAAMM,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;IACZ;IACAG,CAAC,CAACI,QAAQ,GAAGpC,IAAI,CAACtC,UAAU,CAACC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC;IACzDsE,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;IAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;IACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC;IAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;EAC1B,CAAC;EAED,IAAI5D,OAAO,EAAE;IACX,oBACEzB,OAAA;MAAKkG,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BnG,OAAA;QAAKkG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BnG,OAAA;UAAAmG,QAAA,EAAI;QAAO;UAAA1D,QAAA,EAAA2D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChBtG,OAAA;UAAAmG,QAAA,EAAG;QAAa;UAAA1D,QAAA,EAAA2D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAA7D,QAAA,EAAA2D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eACNtG,OAAA;QAAKkG,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCnG,OAAA;UAAKkG,SAAS,EAAC;QAAiB;UAAAzD,QAAA,EAAA2D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCtG,OAAA;UAAAmG,QAAA,EAAG;QAAU;UAAA1D,QAAA,EAAA2D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAA7D,QAAA,EAAA2D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAA7D,QAAA,EAAA2D,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEtG,OAAA;IAAKkG,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BnG,OAAA;MAAKkG,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BnG,OAAA;QAAAmG,QAAA,EAAI;MAAO;QAAA1D,QAAA,EAAA2D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChBtG,OAAA;QAAAmG,QAAA,EAAG;MAAa;QAAA1D,QAAA,EAAA2D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAA7D,QAAA,EAAA2D,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eAENtG,OAAA;MAAKkG,SAAS,EAAC,cAAc;MAAAC,QAAA,EAC1B5E,WAAW,CAACgF,MAAM,KAAK,CAAC,gBACvBvG,OAAA;QAAKkG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BnG,OAAA;UAAKkG,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAE;UAAA1D,QAAA,EAAA2D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpCtG,OAAA;UAAAmG,QAAA,EAAI;QAAM;UAAA1D,QAAA,EAAA2D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACftG,OAAA;UAAAmG,QAAA,EAAG;QAAa;UAAA1D,QAAA,EAAA2D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAA7D,QAAA,EAAA2D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,gBAENtG,OAAA;QAAKkG,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1B5E,WAAW,CAACc,GAAG,CAAEmB,IAAI,iBACpBxD,OAAA;UAAmBkG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzCnG,OAAA;YAAKkG,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BnG,OAAA;cAAKkG,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBnG,OAAA;gBAAAmG,QAAA,EAAK3C,IAAI,CAACtC;cAAU;gBAAAuB,QAAA,EAAA2D,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1BtG,OAAA;gBAAAmG,QAAA,EAAI3C,IAAI,CAACrD;cAAW;gBAAAsC,QAAA,EAAA2D,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBtG,OAAA;gBAAGwG,KAAK,EAAE;kBAAEC,QAAQ,EAAE,MAAM;kBAAEC,KAAK,EAAE,MAAM;kBAAEC,MAAM,EAAE;gBAAY,CAAE;gBAAAR,QAAA,GAAC,4BAC5D,EAAC3C,IAAI,CAACf,QAAQ;cAAA;gBAAAA,QAAA,EAAA2D,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAA7D,QAAA,EAAA2D,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNtG,OAAA;cAAKkG,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BnG,OAAA;gBACEkG,SAAS,EAAC,cAAc;gBACxBM,KAAK,EAAE;kBAAEI,eAAe,EAAEjD,cAAc,CAACH,IAAI,CAACZ,MAAM;gBAAE,CAAE;gBAAAuD,QAAA,EAEvDvC,aAAa,CAACJ,IAAI,CAACZ,MAAM;cAAC;gBAAAH,QAAA,EAAA2D,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAA7D,QAAA,EAAA2D,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAA7D,QAAA,EAAA2D,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtG,OAAA;YAAKkG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBnG,OAAA;cAAKkG,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBnG,OAAA;gBAAMkG,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAI;gBAAA1D,QAAA,EAAA2D,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxCtG,OAAA;gBAAMkG,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE3C,IAAI,CAACT;cAAa;gBAAAN,QAAA,EAAA2D,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAA7D,QAAA,EAAA2D,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACNtG,OAAA;cAAKkG,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBnG,OAAA;gBAAMkG,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAI;gBAAA1D,QAAA,EAAA2D,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxCtG,OAAA;gBAAMkG,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE3C,IAAI,CAACR;cAAU;gBAAAP,QAAA,EAAA2D,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAA7D,QAAA,EAAA2D,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACNtG,OAAA;cAAKkG,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBnG,OAAA;gBAAMkG,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAI;gBAAA1D,QAAA,EAAA2D,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxCtG,OAAA;gBAAMkG,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE3C,IAAI,CAACP;cAAY;gBAAAR,QAAA,EAAA2D,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAA7D,QAAA,EAAA2D,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAA7D,QAAA,EAAA2D,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtG,OAAA;YAAKkG,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BnG,OAAA;cAAMkG,SAAS,EAAC,WAAW;cAAAC,QAAA,GAAC,4BAAM,EAAC3C,IAAI,CAACtD,SAAS;YAAA;cAAAuC,QAAA,EAAA2D,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzDtG,OAAA;cAAKkG,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BnG,OAAA;gBACEkG,SAAS,EAAC,eAAe;gBACzBW,OAAO,EAAEA,CAAA,KAAMhD,iBAAiB,CAACL,IAAI,CAAE;gBAAA2C,QAAA,EACxC;cAED;gBAAA1D,QAAA,EAAA2D,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACR9C,IAAI,CAACZ,MAAM,KAAK,WAAW,iBAC1B5C,OAAA;gBACEkG,SAAS,EAAC,aAAa;gBACvBW,OAAO,EAAEA,CAAA,KAAM7B,oBAAoB,CAACxB,IAAI,CAAE;gBAAA2C,QAAA,EAC3C;cAED;gBAAA1D,QAAA,EAAA2D,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAA7D,QAAA,EAAA2D,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAA7D,QAAA,EAAA2D,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GApDE9C,IAAI,CAACjB,EAAE;UAAAE,QAAA,EAAA2D,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqDZ,CACN;MAAC;QAAA7D,QAAA,EAAA2D,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAA7D,QAAA,EAAA2D,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENtG,OAAA;MAAAmG,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAA1D,QAAA,EAAA2D,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAA7D,QAAA,EAAA2D,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAChF,EAAA,CAnfID,WAAW;AAAAyF,EAAA,GAAXzF,WAAW;AAqfjB,eAAeA,WAAW;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}