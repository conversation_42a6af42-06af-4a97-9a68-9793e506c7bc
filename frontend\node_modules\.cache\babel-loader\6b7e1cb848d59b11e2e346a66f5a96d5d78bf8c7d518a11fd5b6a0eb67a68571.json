{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\components\\\\SimpleUpload.js\",\n  _s = $RefreshSig$();\n/**\n * 简化的上传组件 - 移除项目信息字段，添加实时分析界面\n */\nimport React, { useState, useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SimpleUpload = ({\n  onUploadSuccess,\n  onUploadError\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const [showAnalysis, setShowAnalysis] = useState(false);\n  const [analysisProgress, setAnalysisProgress] = useState({\n    overall: 0,\n    currentStep: '准备开始分析...',\n    steps: [{\n      name: '初始化',\n      status: 'pending',\n      progress: 0\n    }, {\n      name: '元素分析和智能识别',\n      status: 'pending',\n      progress: 0\n    }, {\n      name: '生成自动化测试脚本',\n      status: 'pending',\n      progress: 0\n    }]\n  });\n  const [analysisLogs, setAnalysisLogs] = useState([]);\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n    setIsUploading(true);\n    setShowAnalysis(true);\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n\n      // 开始模拟分析进度\n      simulateAnalysisProgress();\n      const response = await fetch('http://localhost:8001/api/v1/upload', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n      const result = await response.json();\n      onUploadSuccess(result);\n    } catch (error) {\n      console.error('Upload error:', error);\n      setShowAnalysis(false);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  const addLog = (message, type = 'info') => {\n    const timestamp = new Date().toLocaleTimeString();\n    setAnalysisLogs(prev => [...prev, {\n      id: Date.now() + Math.random(),\n      timestamp,\n      message,\n      type\n    }]);\n  };\n  const simulateAnalysisProgress = () => {\n    let step = 0;\n    const steps = ['初始化', '元素分析和智能识别', '生成自动化测试脚本'];\n\n    // 清空之前的日志\n    setAnalysisLogs([]);\n\n    // 详细的步骤日志\n    const stepLogs = {\n      0: [\n      // 初始化\n      '🚀 开始分析流程...', '📋 检查上传文件格式和大小', '🔧 初始化AI分析引擎', '📊 加载UI元素识别模型', '✅ 初始化完成'],\n      1: [\n      // 元素分析和智能识别\n      '🔍 开始图像预处理...', '🎯 检测UI界面元素', '📝 识别文本内容和标签', '🔘 分析按钮和交互元素', '📋 识别输入框和表单元素', '🎨 分析布局和样式信息', '🧠 AI智能分类UI组件', '✅ 元素分析完成'],\n      2: [\n      // 生成自动化测试脚本\n      '📝 开始生成测试脚本...', '🔧 构建MidScene.js测试框架', '📋 生成元素定位策略', '⚡ 创建交互操作脚本', '🧪 添加断言和验证逻辑', '📄 格式化YAML输出', '✅ 测试脚本生成完成']\n    };\n    const updateProgress = () => {\n      if (step < steps.length) {\n        // 添加步骤开始日志\n        addLog(`开始执行: ${steps[step]}`, 'step');\n\n        // 更新进度状态\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: Math.round((step + 1) / steps.length * 100),\n          currentStep: `正在执行: ${steps[step]}`,\n          steps: prev.steps.map((s, index) => ({\n            ...s,\n            status: index < step ? 'completed' : index === step ? 'processing' : 'pending',\n            progress: index < step ? 100 : index === step ? 0 : 0\n          }))\n        }));\n\n        // 模拟步骤内的详细日志\n        const currentStepLogs = stepLogs[step];\n        let logIndex = 0;\n        const addStepLogs = () => {\n          if (logIndex < currentStepLogs.length) {\n            addLog(currentStepLogs[logIndex], 'info');\n\n            // 更新当前步骤进度\n            const progress = Math.round((logIndex + 1) / currentStepLogs.length * 100);\n            setAnalysisProgress(prev => ({\n              ...prev,\n              steps: prev.steps.map((s, index) => index === step ? {\n                ...s,\n                progress\n              } : s)\n            }));\n            logIndex++;\n            setTimeout(addStepLogs, 800 + Math.random() * 1200);\n          } else {\n            // 当前步骤完成\n            setAnalysisProgress(prev => ({\n              ...prev,\n              steps: prev.steps.map((s, index) => index === step ? {\n                ...s,\n                status: 'completed',\n                progress: 100\n              } : s)\n            }));\n            step++;\n            setTimeout(updateProgress, 1000);\n          }\n        };\n        setTimeout(addStepLogs, 500);\n      } else {\n        // 所有步骤完成\n        addLog('🎉 所有分析步骤已完成！', 'success');\n        addLog('📄 测试脚本已生成，可以下载使用', 'success');\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: 100,\n          currentStep: '分析完成',\n          steps: prev.steps.map(s => ({\n            ...s,\n            status: 'completed',\n            progress: 100\n          }))\n        }));\n      }\n    };\n\n    // 开始分析\n    addLog('🔥 启动UI自动化测试脚本分析...', 'start');\n    setTimeout(updateProgress, 1000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '24px',\n      height: '100vh',\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: showAnalysis ? '0 0 400px' : '1',\n        background: 'white',\n        padding: '24px',\n        borderRadius: '12px',\n        boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n        transition: 'all 0.3s ease'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '24px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              marginBottom: '12px',\n              fontWeight: '600',\n              color: '#333',\n              fontSize: '16px'\n            },\n            children: \"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"file-upload-area\",\n            onClick: () => document.getElementById('file-input').click(),\n            style: {\n              height: '240px',\n              border: '2px dashed #667eea',\n              borderRadius: '12px',\n              background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.3s ease',\n              position: 'relative',\n              overflow: 'hidden'\n            },\n            onDragOver: e => {\n              e.preventDefault();\n              e.currentTarget.style.borderColor = '#4f46e5';\n              e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';\n            },\n            onDragLeave: e => {\n              e.currentTarget.style.borderColor = '#667eea';\n              e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';\n            },\n            onDrop: e => {\n              e.preventDefault();\n              const files = e.dataTransfer.files;\n              if (files.length > 0) {\n                setSelectedFile(files[0]);\n              }\n              e.currentTarget.style.borderColor = '#667eea';\n              e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"file-input\",\n              type: \"file\",\n              accept: \"image/*\",\n              onChange: handleFileSelect,\n              style: {\n                display: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), selectedFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: '20px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '48px',\n                  marginBottom: '16px',\n                  color: '#667eea'\n                },\n                children: \"\\u2705\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 8px 0',\n                  color: '#333',\n                  fontSize: '18px'\n                },\n                children: \"\\u6587\\u4EF6\\u5DF2\\u9009\\u62E9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0 0 8px 0',\n                  color: '#666',\n                  fontSize: '14px',\n                  fontWeight: '500'\n                },\n                children: selectedFile.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0',\n                  color: '#999',\n                  fontSize: '12px'\n                },\n                children: [\"\\u5927\\u5C0F: \", (selectedFile.size / 1024 / 1024).toFixed(2), \" MB\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: e => {\n                  e.stopPropagation();\n                  setSelectedFile(null);\n                  document.getElementById('file-input').value = '';\n                },\n                style: {\n                  marginTop: '12px',\n                  background: 'rgba(239, 68, 68, 0.1)',\n                  color: '#ef4444',\n                  border: '1px solid rgba(239, 68, 68, 0.3)',\n                  padding: '6px 12px',\n                  borderRadius: '6px',\n                  fontSize: '12px',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                },\n                children: \"\\u91CD\\u65B0\\u9009\\u62E9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: '20px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '48px',\n                  marginBottom: '16px',\n                  color: '#667eea'\n                },\n                children: \"\\uD83D\\uDCE4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 8px 0',\n                  color: '#333',\n                  fontSize: '18px'\n                },\n                children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u4E0A\\u4F20\\u56FE\\u7247\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0 0 4px 0',\n                  color: '#666',\n                  fontSize: '14px'\n                },\n                children: \"\\u652F\\u6301 PNG\\u3001JPG\\u3001JPEG\\u3001GIF \\u683C\\u5F0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0',\n                  color: '#999',\n                  fontSize: '12px'\n                },\n                children: \"\\u6587\\u4EF6\\u5927\\u5C0F\\u4E0D\\u8D85\\u8FC7 10MB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: '16px',\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  color: 'white',\n                  padding: '8px 20px',\n                  borderRadius: '20px',\n                  fontSize: '14px',\n                  fontWeight: '500',\n                  display: 'inline-block'\n                },\n                children: \"\\u9009\\u62E9\\u6587\\u4EF6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"description\",\n            style: {\n              display: 'block',\n              marginBottom: '8px',\n              fontWeight: '600',\n              color: '#333',\n              fontSize: '16px'\n            },\n            children: \"\\uD83C\\uDFAF \\u754C\\u9762\\u529F\\u80FD\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"description\",\n            value: description,\n            onChange: e => setDescription(e.target.value),\n            placeholder: \"\\u8BF7\\u8BE6\\u7EC6\\u63CF\\u8FF0\\u8FD9\\u4E2A\\u754C\\u9762\\u7684\\u4E3B\\u8981\\u529F\\u80FD\\uFF0C\\u4F8B\\u5982\\uFF1A\\u8FD9\\u662F\\u4E00\\u4E2A\\u767B\\u5F55\\u9875\\u9762\\uFF0C\\u5305\\u542B\\u7528\\u6237\\u540D\\u5BC6\\u7801\\u8F93\\u5165\\u6846\\u548C\\u767B\\u5F55\\u6309\\u94AE...\",\n            rows: 4,\n            style: {\n              width: '100%',\n              padding: '12px',\n              border: '2px solid #e5e7eb',\n              borderRadius: '8px',\n              resize: 'vertical',\n              fontFamily: 'inherit',\n              fontSize: '14px',\n              lineHeight: '1.5',\n              transition: 'border-color 0.2s ease',\n              background: '#fafafa'\n            },\n            onFocus: e => {\n              e.target.style.borderColor = '#667eea';\n              e.target.style.background = '#ffffff';\n            },\n            onBlur: e => {\n              e.target.style.borderColor = '#e5e7eb';\n              e.target.style.background = '#fafafa';\n            },\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'right',\n              fontSize: '12px',\n              color: '#666',\n              marginTop: '4px'\n            },\n            children: [description.length, \"/500\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: isUploading || !selectedFile || !description.trim(),\n          style: {\n            background: isUploading ? '#6c757d' : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '8px',\n            fontSize: '16px',\n            fontWeight: '500',\n            cursor: isUploading ? 'not-allowed' : 'pointer',\n            transition: 'all 0.3s ease',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px',\n            width: '100%',\n            justifyContent: 'center'\n          },\n          children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                width: '16px',\n                height: '16px',\n                border: '2px solid transparent',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this), \"\\u5F00\\u59CB\\u5206\\u6790\\u4E2D...\"]\n          }, void 0, true) : '🚀 开始分析'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), showAnalysis && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: '1',\n        background: 'white',\n        padding: '24px',\n        borderRadius: '12px',\n        boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n        overflow: 'hidden',\n        display: 'flex',\n        flexDirection: 'column'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          marginBottom: '24px',\n          paddingBottom: '16px',\n          borderBottom: '2px solid #f1f3f4'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: 0,\n            color: '#333',\n            fontSize: '20px',\n            fontWeight: '600'\n          },\n          children: \"\\uD83D\\uDD0D \\u5B9E\\u65F6\\u5206\\u6790\\u8FDB\\u5EA6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: analysisProgress.overall === 100 ? '#d4edda' : '#f8f9fa',\n            color: analysisProgress.overall === 100 ? '#155724' : '#666',\n            padding: '8px 16px',\n            borderRadius: '20px',\n            fontSize: '14px',\n            fontWeight: '500',\n            border: analysisProgress.overall === 100 ? '1px solid #c3e6cb' : 'none'\n          },\n          children: [analysisProgress.overall, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: '#f8f9ff',\n          border: '1px solid #e5e7eb',\n          borderRadius: '8px',\n          padding: '16px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '14px',\n            color: '#666',\n            marginBottom: '8px'\n          },\n          children: \"\\u5206\\u6790\\u72B6\\u6001\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '16px',\n            fontWeight: '600',\n            color: '#333'\n          },\n          children: analysisProgress.currentStep\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '16px',\n            fontWeight: '600',\n            color: '#333',\n            marginBottom: '12px'\n          },\n          children: \"\\uD83D\\uDCCA \\u5206\\u6790\\u6B65\\u9AA4\\u6982\\u89C8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '8px'\n          },\n          children: analysisProgress.steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1,\n              padding: '12px 8px',\n              background: step.status === 'completed' ? '#d4edda' : step.status === 'processing' ? '#e3f2fd' : '#f8f9fa',\n              border: `1px solid ${step.status === 'completed' ? '#c3e6cb' : step.status === 'processing' ? '#bbdefb' : '#e9ecef'}`,\n              borderRadius: '6px',\n              textAlign: 'center',\n              fontSize: '12px',\n              transition: 'all 0.3s ease'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '16px',\n                marginBottom: '4px',\n                color: step.status === 'completed' ? '#28a745' : step.status === 'processing' ? '#1976d2' : '#6c757d'\n              },\n              children: step.status === 'completed' ? '✅' : step.status === 'processing' ? '⚡' : '⏳'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: '500',\n                color: '#333',\n                fontSize: '11px'\n              },\n              children: step.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '100%',\n                height: '3px',\n                background: '#e9ecef',\n                borderRadius: '2px',\n                overflow: 'hidden',\n                marginTop: '6px'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  height: '100%',\n                  width: `${step.progress}%`,\n                  background: step.status === 'completed' ? '#28a745' : step.status === 'processing' ? '#1976d2' : '#e9ecef',\n                  transition: 'width 0.5s ease',\n                  borderRadius: '2px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '16px',\n              fontWeight: '600',\n              color: '#333'\n            },\n            children: \"\\uD83D\\uDCDD \\u5B9E\\u65F6\\u5206\\u6790\\u65E5\\u5FD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              color: '#666',\n              background: '#f8f9fa',\n              padding: '4px 8px',\n              borderRadius: '12px'\n            },\n            children: [analysisLogs.length, \" \\u6761\\u65E5\\u5FD7\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            background: '#1a1a1a',\n            borderRadius: '8px',\n            padding: '16px',\n            overflow: 'auto',\n            fontFamily: 'Monaco, Consolas, \"Courier New\", monospace',\n            fontSize: '13px',\n            lineHeight: '1.5',\n            maxHeight: '400px'\n          },\n          children: analysisLogs.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#666',\n              textAlign: 'center',\n              padding: '20px'\n            },\n            children: \"\\u7B49\\u5F85\\u5206\\u6790\\u5F00\\u59CB...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 17\n          }, this) : analysisLogs.map(log => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '8px',\n              padding: '6px 0',\n              borderBottom: '1px solid #333'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'flex-start',\n                gap: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#888',\n                  fontSize: '11px',\n                  minWidth: '60px',\n                  fontWeight: '500'\n                },\n                children: log.timestamp\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: log.type === 'start' ? '#4fc3f7' : log.type === 'step' ? '#ffb74d' : log.type === 'success' ? '#81c784' : log.type === 'error' ? '#e57373' : '#e0e0e0',\n                  fontWeight: log.type === 'step' || log.type === 'start' || log.type === 'success' ? '600' : '400'\n                },\n                children: log.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 21\n            }, this)\n          }, log.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 570,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 194,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleUpload, \"d0s4pX12mQmXWGqYjiS5FNXTCbA=\");\n_c = SimpleUpload;\nexport default SimpleUpload;\nvar _c;\n$RefreshReg$(_c, \"SimpleUpload\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SimpleUpload", "onUploadSuccess", "onUploadError", "_s", "selectedFile", "setSelectedFile", "description", "setDescription", "isUploading", "setIsUploading", "showAnalysis", "setShowAnalysis", "analysisProgress", "setAnalysisProgress", "overall", "currentStep", "steps", "name", "status", "progress", "analysisLogs", "setAnalysisLogs", "handleFileSelect", "event", "file", "target", "files", "handleSubmit", "preventDefault", "trim", "formData", "FormData", "append", "simulateAnalysisProgress", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "result", "error", "console", "message", "addLog", "type", "timestamp", "Date", "toLocaleTimeString", "prev", "id", "now", "Math", "random", "step", "stepLogs", "updateProgress", "length", "round", "map", "s", "index", "currentStepLogs", "logIndex", "addStepLogs", "setTimeout", "style", "display", "gap", "height", "padding", "children", "flex", "background", "borderRadius", "boxShadow", "transition", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "marginBottom", "fontWeight", "color", "fontSize", "className", "onClick", "document", "getElementById", "click", "border", "flexDirection", "alignItems", "justifyContent", "cursor", "position", "overflow", "onDragOver", "e", "currentTarget", "borderColor", "onDragLeave", "onDrop", "dataTransfer", "accept", "onChange", "textAlign", "margin", "size", "toFixed", "stopPropagation", "value", "marginTop", "htmlFor", "placeholder", "rows", "width", "resize", "fontFamily", "lineHeight", "onFocus", "onBlur", "required", "disabled", "borderTop", "animation", "paddingBottom", "borderBottom", "maxHeight", "log", "min<PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/components/SimpleUpload.js"], "sourcesContent": ["/**\n * 简化的上传组件 - 移除项目信息字段，添加实时分析界面\n */\nimport React, { useState, useEffect, useRef } from 'react';\n\nconst SimpleUpload = ({ onUploadSuccess, onUploadError }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const [showAnalysis, setShowAnalysis] = useState(false);\n  const [analysisProgress, setAnalysisProgress] = useState({\n    overall: 0,\n    currentStep: '准备开始分析...',\n    steps: [\n      { name: '初始化', status: 'pending', progress: 0 },\n      { name: '元素分析和智能识别', status: 'pending', progress: 0 },\n      { name: '生成自动化测试脚本', status: 'pending', progress: 0 }\n    ]\n  });\n  const [analysisLogs, setAnalysisLogs] = useState([]);\n\n  const handleFileSelect = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n\n  const handleSubmit = async (event) => {\n    event.preventDefault();\n\n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n\n    setIsUploading(true);\n    setShowAnalysis(true);\n\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n\n      // 开始模拟分析进度\n      simulateAnalysisProgress();\n\n      const response = await fetch('http://localhost:8001/api/v1/upload', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      onUploadSuccess(result);\n\n    } catch (error) {\n      console.error('Upload error:', error);\n      setShowAnalysis(false);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const addLog = (message, type = 'info') => {\n    const timestamp = new Date().toLocaleTimeString();\n    setAnalysisLogs(prev => [...prev, {\n      id: Date.now() + Math.random(),\n      timestamp,\n      message,\n      type\n    }]);\n  };\n\n  const simulateAnalysisProgress = () => {\n    let step = 0;\n    const steps = ['初始化', '元素分析和智能识别', '生成自动化测试脚本'];\n\n    // 清空之前的日志\n    setAnalysisLogs([]);\n\n    // 详细的步骤日志\n    const stepLogs = {\n      0: [ // 初始化\n        '🚀 开始分析流程...',\n        '📋 检查上传文件格式和大小',\n        '🔧 初始化AI分析引擎',\n        '📊 加载UI元素识别模型',\n        '✅ 初始化完成'\n      ],\n      1: [ // 元素分析和智能识别\n        '🔍 开始图像预处理...',\n        '🎯 检测UI界面元素',\n        '📝 识别文本内容和标签',\n        '🔘 分析按钮和交互元素',\n        '📋 识别输入框和表单元素',\n        '🎨 分析布局和样式信息',\n        '🧠 AI智能分类UI组件',\n        '✅ 元素分析完成'\n      ],\n      2: [ // 生成自动化测试脚本\n        '📝 开始生成测试脚本...',\n        '🔧 构建MidScene.js测试框架',\n        '📋 生成元素定位策略',\n        '⚡ 创建交互操作脚本',\n        '🧪 添加断言和验证逻辑',\n        '📄 格式化YAML输出',\n        '✅ 测试脚本生成完成'\n      ]\n    };\n\n    const updateProgress = () => {\n      if (step < steps.length) {\n        // 添加步骤开始日志\n        addLog(`开始执行: ${steps[step]}`, 'step');\n\n        // 更新进度状态\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: Math.round((step + 1) / steps.length * 100),\n          currentStep: `正在执行: ${steps[step]}`,\n          steps: prev.steps.map((s, index) => ({\n            ...s,\n            status: index < step ? 'completed' : index === step ? 'processing' : 'pending',\n            progress: index < step ? 100 : index === step ? 0 : 0\n          }))\n        }));\n\n        // 模拟步骤内的详细日志\n        const currentStepLogs = stepLogs[step];\n        let logIndex = 0;\n\n        const addStepLogs = () => {\n          if (logIndex < currentStepLogs.length) {\n            addLog(currentStepLogs[logIndex], 'info');\n\n            // 更新当前步骤进度\n            const progress = Math.round(((logIndex + 1) / currentStepLogs.length) * 100);\n            setAnalysisProgress(prev => ({\n              ...prev,\n              steps: prev.steps.map((s, index) =>\n                index === step ? { ...s, progress } : s\n              )\n            }));\n\n            logIndex++;\n            setTimeout(addStepLogs, 800 + Math.random() * 1200);\n          } else {\n            // 当前步骤完成\n            setAnalysisProgress(prev => ({\n              ...prev,\n              steps: prev.steps.map((s, index) =>\n                index === step ? { ...s, status: 'completed', progress: 100 } : s\n              )\n            }));\n\n            step++;\n            setTimeout(updateProgress, 1000);\n          }\n        };\n\n        setTimeout(addStepLogs, 500);\n\n      } else {\n        // 所有步骤完成\n        addLog('🎉 所有分析步骤已完成！', 'success');\n        addLog('📄 测试脚本已生成，可以下载使用', 'success');\n\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: 100,\n          currentStep: '分析完成',\n          steps: prev.steps.map(s => ({ ...s, status: 'completed', progress: 100 }))\n        }));\n      }\n    };\n\n    // 开始分析\n    addLog('🔥 启动UI自动化测试脚本分析...', 'start');\n    setTimeout(updateProgress, 1000);\n  };\n\n  return (\n    <div style={{ display: 'flex', gap: '24px', height: '100vh', padding: '24px' }}>\n      {/* 左侧上传区域 */}\n      <div style={{ \n        flex: showAnalysis ? '0 0 400px' : '1',\n        background: 'white', \n        padding: '24px', \n        borderRadius: '12px', \n        boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n        transition: 'all 0.3s ease'\n      }}>\n        <h3>📁 上传UI界面截图</h3>\n        <form onSubmit={handleSubmit}>\n          <div style={{ marginBottom: '24px' }}>\n            <label style={{ display: 'block', marginBottom: '12px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n              📁 上传UI界面截图\n            </label>\n            <div\n              className=\"file-upload-area\"\n              onClick={() => document.getElementById('file-input').click()}\n              style={{\n                height: '240px',\n                border: '2px dashed #667eea',\n                borderRadius: '12px',\n                background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                justifyContent: 'center',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease',\n                position: 'relative',\n                overflow: 'hidden'\n              }}\n              onDragOver={(e) => {\n                e.preventDefault();\n                e.currentTarget.style.borderColor = '#4f46e5';\n                e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';\n              }}\n              onDragLeave={(e) => {\n                e.currentTarget.style.borderColor = '#667eea';\n                e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';\n              }}\n              onDrop={(e) => {\n                e.preventDefault();\n                const files = e.dataTransfer.files;\n                if (files.length > 0) {\n                  setSelectedFile(files[0]);\n                }\n                e.currentTarget.style.borderColor = '#667eea';\n                e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';\n              }}\n            >\n              <input\n                id=\"file-input\"\n                type=\"file\"\n                accept=\"image/*\"\n                onChange={handleFileSelect}\n                style={{ display: 'none' }}\n              />\n\n              {selectedFile ? (\n                <div style={{ textAlign: 'center', padding: '20px' }}>\n                  <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>✅</div>\n                  <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>文件已选择</h3>\n                  <p style={{ margin: '0 0 8px 0', color: '#666', fontSize: '14px', fontWeight: '500' }}>\n                    {selectedFile.name}\n                  </p>\n                  <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>\n                    大小: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB\n                  </p>\n                  <button\n                    type=\"button\"\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      setSelectedFile(null);\n                      document.getElementById('file-input').value = '';\n                    }}\n                    style={{\n                      marginTop: '12px',\n                      background: 'rgba(239, 68, 68, 0.1)',\n                      color: '#ef4444',\n                      border: '1px solid rgba(239, 68, 68, 0.3)',\n                      padding: '6px 12px',\n                      borderRadius: '6px',\n                      fontSize: '12px',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    }}\n                  >\n                    重新选择\n                  </button>\n                </div>\n              ) : (\n                <div style={{ textAlign: 'center', padding: '20px' }}>\n                  <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>📤</div>\n                  <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>点击或拖拽上传图片</h3>\n                  <p style={{ margin: '0 0 4px 0', color: '#666', fontSize: '14px' }}>\n                    支持 PNG、JPG、JPEG、GIF 格式\n                  </p>\n                  <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>\n                    文件大小不超过 10MB\n                  </p>\n                  <div style={{\n                    marginTop: '16px',\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    color: 'white',\n                    padding: '8px 20px',\n                    borderRadius: '20px',\n                    fontSize: '14px',\n                    fontWeight: '500',\n                    display: 'inline-block'\n                  }}>\n                    选择文件\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* 界面功能描述 */}\n          <div style={{ marginBottom: '20px' }}>\n            <label htmlFor=\"description\" style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n              🎯 界面功能描述\n            </label>\n            <textarea\n              id=\"description\"\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              placeholder=\"请详细描述这个界面的主要功能，例如：这是一个登录页面，包含用户名密码输入框和登录按钮...\"\n              rows={4}\n              style={{\n                width: '100%',\n                padding: '12px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '8px',\n                resize: 'vertical',\n                fontFamily: 'inherit',\n                fontSize: '14px',\n                lineHeight: '1.5',\n                transition: 'border-color 0.2s ease',\n                background: '#fafafa'\n              }}\n              onFocus={(e) => {\n                e.target.style.borderColor = '#667eea';\n                e.target.style.background = '#ffffff';\n              }}\n              onBlur={(e) => {\n                e.target.style.borderColor = '#e5e7eb';\n                e.target.style.background = '#fafafa';\n              }}\n              required\n            />\n            <div style={{ textAlign: 'right', fontSize: '12px', color: '#666', marginTop: '4px' }}>\n              {description.length}/500\n            </div>\n          </div>\n\n          <button\n            type=\"submit\"\n            disabled={isUploading || !selectedFile || !description.trim()}\n            style={{\n              background: isUploading ? '#6c757d' : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '8px',\n              fontSize: '16px',\n              fontWeight: '500',\n              cursor: isUploading ? 'not-allowed' : 'pointer',\n              transition: 'all 0.3s ease',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px',\n              width: '100%',\n              justifyContent: 'center'\n            }}\n          >\n            {isUploading ? (\n              <>\n                <span style={{\n                  width: '16px',\n                  height: '16px',\n                  border: '2px solid transparent',\n                  borderTop: '2px solid white',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}></span>\n                开始分析中...\n              </>\n            ) : (\n              '🚀 开始分析'\n            )}\n          </button>\n        </form>\n      </div>\n\n      {/* 右侧实时分析界面 */}\n      {showAnalysis && (\n        <div style={{\n          flex: '1',\n          background: 'white',\n          padding: '24px',\n          borderRadius: '12px',\n          boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n          overflow: 'hidden',\n          display: 'flex',\n          flexDirection: 'column'\n        }}>\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            marginBottom: '24px',\n            paddingBottom: '16px',\n            borderBottom: '2px solid #f1f3f4'\n          }}>\n            <h3 style={{ margin: 0, color: '#333', fontSize: '20px', fontWeight: '600' }}>\n              🔍 实时分析进度\n            </h3>\n            <div style={{\n              background: analysisProgress.overall === 100 ? '#d4edda' : '#f8f9fa',\n              color: analysisProgress.overall === 100 ? '#155724' : '#666',\n              padding: '8px 16px',\n              borderRadius: '20px',\n              fontSize: '14px',\n              fontWeight: '500',\n              border: analysisProgress.overall === 100 ? '1px solid #c3e6cb' : 'none'\n            }}>\n              {analysisProgress.overall}%\n            </div>\n          </div>\n\n          {/* 当前步骤 */}\n          <div style={{\n            background: '#f8f9ff',\n            border: '1px solid #e5e7eb',\n            borderRadius: '8px',\n            padding: '16px',\n            marginBottom: '20px'\n          }}>\n            <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>\n              分析状态\n            </div>\n            <div style={{ fontSize: '16px', fontWeight: '600', color: '#333' }}>\n              {analysisProgress.currentStep}\n            </div>\n          </div>\n\n          {/* 分析步骤概览 */}\n          <div style={{ marginBottom: '20px' }}>\n            <div style={{ fontSize: '16px', fontWeight: '600', color: '#333', marginBottom: '12px' }}>\n              📊 分析步骤概览\n            </div>\n            <div style={{ display: 'flex', gap: '8px' }}>\n              {analysisProgress.steps.map((step, index) => (\n                <div key={index} style={{\n                  flex: 1,\n                  padding: '12px 8px',\n                  background: step.status === 'completed' ? '#d4edda' :\n                             step.status === 'processing' ? '#e3f2fd' : '#f8f9fa',\n                  border: `1px solid ${step.status === 'completed' ? '#c3e6cb' :\n                                      step.status === 'processing' ? '#bbdefb' : '#e9ecef'}`,\n                  borderRadius: '6px',\n                  textAlign: 'center',\n                  fontSize: '12px',\n                  transition: 'all 0.3s ease'\n                }}>\n                  <div style={{\n                    fontSize: '16px',\n                    marginBottom: '4px',\n                    color: step.status === 'completed' ? '#28a745' :\n                           step.status === 'processing' ? '#1976d2' : '#6c757d'\n                  }}>\n                    {step.status === 'completed' ? '✅' :\n                     step.status === 'processing' ? '⚡' : '⏳'}\n                  </div>\n                  <div style={{ fontWeight: '500', color: '#333', fontSize: '11px' }}>\n                    {step.name}\n                  </div>\n                  <div style={{\n                    width: '100%',\n                    height: '3px',\n                    background: '#e9ecef',\n                    borderRadius: '2px',\n                    overflow: 'hidden',\n                    marginTop: '6px'\n                  }}>\n                    <div style={{\n                      height: '100%',\n                      width: `${step.progress}%`,\n                      background: step.status === 'completed' ? '#28a745' :\n                                 step.status === 'processing' ? '#1976d2' : '#e9ecef',\n                      transition: 'width 0.5s ease',\n                      borderRadius: '2px'\n                    }}></div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* 实时日志区域 */}\n          <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              marginBottom: '12px'\n            }}>\n              <div style={{ fontSize: '16px', fontWeight: '600', color: '#333' }}>\n                📝 实时分析日志\n              </div>\n              <div style={{\n                fontSize: '12px',\n                color: '#666',\n                background: '#f8f9fa',\n                padding: '4px 8px',\n                borderRadius: '12px'\n              }}>\n                {analysisLogs.length} 条日志\n              </div>\n            </div>\n\n            <div style={{\n              flex: 1,\n              background: '#1a1a1a',\n              borderRadius: '8px',\n              padding: '16px',\n              overflow: 'auto',\n              fontFamily: 'Monaco, Consolas, \"Courier New\", monospace',\n              fontSize: '13px',\n              lineHeight: '1.5',\n              maxHeight: '400px'\n            }}>\n              {analysisLogs.length === 0 ? (\n                <div style={{ color: '#666', textAlign: 'center', padding: '20px' }}>\n                  等待分析开始...\n                </div>\n              ) : (\n                analysisLogs.map((log) => (\n                  <div key={log.id} style={{\n                    marginBottom: '8px',\n                    padding: '6px 0',\n                    borderBottom: '1px solid #333'\n                  }}>\n                    <div style={{\n                      display: 'flex',\n                      alignItems: 'flex-start',\n                      gap: '8px'\n                    }}>\n                      <span style={{\n                        color: '#888',\n                        fontSize: '11px',\n                        minWidth: '60px',\n                        fontWeight: '500'\n                      }}>\n                        {log.timestamp}\n                      </span>\n                      <span style={{\n                        color: log.type === 'start' ? '#4fc3f7' :\n                               log.type === 'step' ? '#ffb74d' :\n                               log.type === 'success' ? '#81c784' :\n                               log.type === 'error' ? '#e57373' : '#e0e0e0',\n                        fontWeight: log.type === 'step' || log.type === 'start' || log.type === 'success' ? '600' : '400'\n                      }}>\n                        {log.message}\n                      </span>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n\n      <style>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default SimpleUpload;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3D,MAAMC,YAAY,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpB,QAAQ,CAAC;IACvDqB,OAAO,EAAE,CAAC;IACVC,WAAW,EAAE,WAAW;IACxBC,KAAK,EAAE,CACL;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAE,CAAC,EAC/C;MAAEF,IAAI,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAE,CAAC,EACrD;MAAEF,IAAI,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAE,CAAC;EAEzD,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM6B,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACRnB,eAAe,CAACmB,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOJ,KAAK,IAAK;IACpCA,KAAK,CAACK,cAAc,CAAC,CAAC;IAEtB,IAAI,CAACxB,YAAY,EAAE;MACjBF,aAAa,CAAC,SAAS,CAAC;MACxB;IACF;IAEA,IAAI,CAACI,WAAW,CAACuB,IAAI,CAAC,CAAC,EAAE;MACvB3B,aAAa,CAAC,WAAW,CAAC;MAC1B;IACF;IAEAO,cAAc,CAAC,IAAI,CAAC;IACpBE,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF,MAAMmB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE5B,YAAY,CAAC;MAC3C0B,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE1B,WAAW,CAACuB,IAAI,CAAC,CAAC,CAAC;;MAElD;MACAI,wBAAwB,CAAC,CAAC;MAE1B,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,qCAAqC,EAAE;QAClEC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEP;MACR,CAAC,CAAC;MAEF,IAAI,CAACI,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,uBAAuBR,QAAQ,CAAChB,MAAM,EAAE,CAAC;MAC/E;MAEA,MAAMyB,MAAM,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MACpCvC,eAAe,CAAC0C,MAAM,CAAC;IAEzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCjC,eAAe,CAAC,KAAK,CAAC;MACtBT,aAAa,CAAC0C,KAAK,CAACE,OAAO,IAAI,UAAU,CAAC;IAC5C,CAAC,SAAS;MACRrC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMsC,MAAM,GAAGA,CAACD,OAAO,EAAEE,IAAI,GAAG,MAAM,KAAK;IACzC,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;IACjD9B,eAAe,CAAC+B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAChCC,EAAE,EAAEH,IAAI,CAACI,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC;MAC9BP,SAAS;MACTH,OAAO;MACPE;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMf,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAIwB,IAAI,GAAG,CAAC;IACZ,MAAMzC,KAAK,GAAG,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,CAAC;;IAE/C;IACAK,eAAe,CAAC,EAAE,CAAC;;IAEnB;IACA,MAAMqC,QAAQ,GAAG;MACf,CAAC,EAAE;MAAE;MACH,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,eAAe,EACf,SAAS,CACV;MACD,CAAC,EAAE;MAAE;MACH,eAAe,EACf,aAAa,EACb,cAAc,EACd,cAAc,EACd,eAAe,EACf,cAAc,EACd,eAAe,EACf,UAAU,CACX;MACD,CAAC,EAAE;MAAE;MACH,gBAAgB,EAChB,sBAAsB,EACtB,aAAa,EACb,YAAY,EACZ,cAAc,EACd,cAAc,EACd,YAAY;IAEhB,CAAC;IAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAIF,IAAI,GAAGzC,KAAK,CAAC4C,MAAM,EAAE;QACvB;QACAb,MAAM,CAAC,SAAS/B,KAAK,CAACyC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC;;QAEtC;QACA5C,mBAAmB,CAACuC,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACPtC,OAAO,EAAEyC,IAAI,CAACM,KAAK,CAAC,CAACJ,IAAI,GAAG,CAAC,IAAIzC,KAAK,CAAC4C,MAAM,GAAG,GAAG,CAAC;UACpD7C,WAAW,EAAE,SAASC,KAAK,CAACyC,IAAI,CAAC,EAAE;UACnCzC,KAAK,EAAEoC,IAAI,CAACpC,KAAK,CAAC8C,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,MAAM;YACnC,GAAGD,CAAC;YACJ7C,MAAM,EAAE8C,KAAK,GAAGP,IAAI,GAAG,WAAW,GAAGO,KAAK,KAAKP,IAAI,GAAG,YAAY,GAAG,SAAS;YAC9EtC,QAAQ,EAAE6C,KAAK,GAAGP,IAAI,GAAG,GAAG,GAAGO,KAAK,KAAKP,IAAI,GAAG,CAAC,GAAG;UACtD,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMQ,eAAe,GAAGP,QAAQ,CAACD,IAAI,CAAC;QACtC,IAAIS,QAAQ,GAAG,CAAC;QAEhB,MAAMC,WAAW,GAAGA,CAAA,KAAM;UACxB,IAAID,QAAQ,GAAGD,eAAe,CAACL,MAAM,EAAE;YACrCb,MAAM,CAACkB,eAAe,CAACC,QAAQ,CAAC,EAAE,MAAM,CAAC;;YAEzC;YACA,MAAM/C,QAAQ,GAAGoC,IAAI,CAACM,KAAK,CAAE,CAACK,QAAQ,GAAG,CAAC,IAAID,eAAe,CAACL,MAAM,GAAI,GAAG,CAAC;YAC5E/C,mBAAmB,CAACuC,IAAI,KAAK;cAC3B,GAAGA,IAAI;cACPpC,KAAK,EAAEoC,IAAI,CAACpC,KAAK,CAAC8C,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAC7BA,KAAK,KAAKP,IAAI,GAAG;gBAAE,GAAGM,CAAC;gBAAE5C;cAAS,CAAC,GAAG4C,CACxC;YACF,CAAC,CAAC,CAAC;YAEHG,QAAQ,EAAE;YACVE,UAAU,CAACD,WAAW,EAAE,GAAG,GAAGZ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;UACrD,CAAC,MAAM;YACL;YACA3C,mBAAmB,CAACuC,IAAI,KAAK;cAC3B,GAAGA,IAAI;cACPpC,KAAK,EAAEoC,IAAI,CAACpC,KAAK,CAAC8C,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAC7BA,KAAK,KAAKP,IAAI,GAAG;gBAAE,GAAGM,CAAC;gBAAE7C,MAAM,EAAE,WAAW;gBAAEC,QAAQ,EAAE;cAAI,CAAC,GAAG4C,CAClE;YACF,CAAC,CAAC,CAAC;YAEHN,IAAI,EAAE;YACNW,UAAU,CAACT,cAAc,EAAE,IAAI,CAAC;UAClC;QACF,CAAC;QAEDS,UAAU,CAACD,WAAW,EAAE,GAAG,CAAC;MAE9B,CAAC,MAAM;QACL;QACApB,MAAM,CAAC,eAAe,EAAE,SAAS,CAAC;QAClCA,MAAM,CAAC,mBAAmB,EAAE,SAAS,CAAC;QAEtClC,mBAAmB,CAACuC,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACPtC,OAAO,EAAE,GAAG;UACZC,WAAW,EAAE,MAAM;UACnBC,KAAK,EAAEoC,IAAI,CAACpC,KAAK,CAAC8C,GAAG,CAACC,CAAC,KAAK;YAAE,GAAGA,CAAC;YAAE7C,MAAM,EAAE,WAAW;YAAEC,QAAQ,EAAE;UAAI,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;MACL;IACF,CAAC;;IAED;IACA4B,MAAM,CAAC,qBAAqB,EAAE,OAAO,CAAC;IACtCqB,UAAU,CAACT,cAAc,EAAE,IAAI,CAAC;EAClC,CAAC;EAED,oBACE9D,OAAA;IAAKwE,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,GAAG,EAAE,MAAM;MAAEC,MAAM,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAE7E7E,OAAA;MAAKwE,KAAK,EAAE;QACVM,IAAI,EAAEjE,YAAY,GAAG,WAAW,GAAG,GAAG;QACtCkE,UAAU,EAAE,OAAO;QACnBH,OAAO,EAAE,MAAM;QACfI,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE,4BAA4B;QACvCC,UAAU,EAAE;MACd,CAAE;MAAAL,QAAA,gBACA7E,OAAA;QAAA6E,QAAA,EAAI;MAAW;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpBtF,OAAA;QAAMuF,QAAQ,EAAEzD,YAAa;QAAA+C,QAAA,gBAC3B7E,OAAA;UAAKwE,KAAK,EAAE;YAAEgB,YAAY,EAAE;UAAO,CAAE;UAAAX,QAAA,gBACnC7E,OAAA;YAAOwE,KAAK,EAAE;cAAEC,OAAO,EAAE,OAAO;cAAEe,YAAY,EAAE,MAAM;cAAEC,UAAU,EAAE,KAAK;cAAEC,KAAK,EAAE,MAAM;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAE9G;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtF,OAAA;YACE4F,SAAS,EAAC,kBAAkB;YAC5BC,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAACC,KAAK,CAAC,CAAE;YAC7DxB,KAAK,EAAE;cACLG,MAAM,EAAE,OAAO;cACfsB,MAAM,EAAE,oBAAoB;cAC5BjB,YAAY,EAAE,MAAM;cACpBD,UAAU,EAAExE,YAAY,GAAG,mDAAmD,GAAG,mDAAmD;cACpIkE,OAAO,EAAE,MAAM;cACfyB,aAAa,EAAE,QAAQ;cACvBC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,MAAM,EAAE,SAAS;cACjBnB,UAAU,EAAE,eAAe;cAC3BoB,QAAQ,EAAE,UAAU;cACpBC,QAAQ,EAAE;YACZ,CAAE;YACFC,UAAU,EAAGC,CAAC,IAAK;cACjBA,CAAC,CAAC1E,cAAc,CAAC,CAAC;cAClB0E,CAAC,CAACC,aAAa,CAAClC,KAAK,CAACmC,WAAW,GAAG,SAAS;cAC7CF,CAAC,CAACC,aAAa,CAAClC,KAAK,CAACO,UAAU,GAAG,mDAAmD;YACxF,CAAE;YACF6B,WAAW,EAAGH,CAAC,IAAK;cAClBA,CAAC,CAACC,aAAa,CAAClC,KAAK,CAACmC,WAAW,GAAG,SAAS;cAC7CF,CAAC,CAACC,aAAa,CAAClC,KAAK,CAACO,UAAU,GAAGxE,YAAY,GAAG,mDAAmD,GAAG,mDAAmD;YAC7J,CAAE;YACFsG,MAAM,EAAGJ,CAAC,IAAK;cACbA,CAAC,CAAC1E,cAAc,CAAC,CAAC;cAClB,MAAMF,KAAK,GAAG4E,CAAC,CAACK,YAAY,CAACjF,KAAK;cAClC,IAAIA,KAAK,CAACkC,MAAM,GAAG,CAAC,EAAE;gBACpBvD,eAAe,CAACqB,KAAK,CAAC,CAAC,CAAC,CAAC;cAC3B;cACA4E,CAAC,CAACC,aAAa,CAAClC,KAAK,CAACmC,WAAW,GAAG,SAAS;cAC7CF,CAAC,CAACC,aAAa,CAAClC,KAAK,CAACO,UAAU,GAAG,mDAAmD;YACxF,CAAE;YAAAF,QAAA,gBAEF7E,OAAA;cACEwD,EAAE,EAAC,YAAY;cACfL,IAAI,EAAC,MAAM;cACX4D,MAAM,EAAC,SAAS;cAChBC,QAAQ,EAAEvF,gBAAiB;cAC3B+C,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAO;YAAE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,EAED/E,YAAY,gBACXP,OAAA;cAAKwE,KAAK,EAAE;gBAAEyC,SAAS,EAAE,QAAQ;gBAAErC,OAAO,EAAE;cAAO,CAAE;cAAAC,QAAA,gBACnD7E,OAAA;gBAAKwE,KAAK,EAAE;kBAAEmB,QAAQ,EAAE,MAAM;kBAAEH,YAAY,EAAE,MAAM;kBAAEE,KAAK,EAAE;gBAAU,CAAE;gBAAAb,QAAA,EAAC;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjFtF,OAAA;gBAAIwE,KAAK,EAAE;kBAAE0C,MAAM,EAAE,WAAW;kBAAExB,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAd,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/EtF,OAAA;gBAAGwE,KAAK,EAAE;kBAAE0C,MAAM,EAAE,WAAW;kBAAExB,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,MAAM;kBAAEF,UAAU,EAAE;gBAAM,CAAE;gBAAAZ,QAAA,EACnFtE,YAAY,CAACa;cAAI;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACJtF,OAAA;gBAAGwE,KAAK,EAAE;kBAAE0C,MAAM,EAAE,GAAG;kBAAExB,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAd,QAAA,GAAC,gBACtD,EAAC,CAACtE,YAAY,CAAC4G,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KACpD;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJtF,OAAA;gBACEmD,IAAI,EAAC,QAAQ;gBACb0C,OAAO,EAAGY,CAAC,IAAK;kBACdA,CAAC,CAACY,eAAe,CAAC,CAAC;kBACnB7G,eAAe,CAAC,IAAI,CAAC;kBACrBsF,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAACuB,KAAK,GAAG,EAAE;gBAClD,CAAE;gBACF9C,KAAK,EAAE;kBACL+C,SAAS,EAAE,MAAM;kBACjBxC,UAAU,EAAE,wBAAwB;kBACpCW,KAAK,EAAE,SAAS;kBAChBO,MAAM,EAAE,kCAAkC;kBAC1CrB,OAAO,EAAE,UAAU;kBACnBI,YAAY,EAAE,KAAK;kBACnBW,QAAQ,EAAE,MAAM;kBAChBU,MAAM,EAAE,SAAS;kBACjBnB,UAAU,EAAE;gBACd,CAAE;gBAAAL,QAAA,EACH;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,gBAENtF,OAAA;cAAKwE,KAAK,EAAE;gBAAEyC,SAAS,EAAE,QAAQ;gBAAErC,OAAO,EAAE;cAAO,CAAE;cAAAC,QAAA,gBACnD7E,OAAA;gBAAKwE,KAAK,EAAE;kBAAEmB,QAAQ,EAAE,MAAM;kBAAEH,YAAY,EAAE,MAAM;kBAAEE,KAAK,EAAE;gBAAU,CAAE;gBAAAb,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClFtF,OAAA;gBAAIwE,KAAK,EAAE;kBAAE0C,MAAM,EAAE,WAAW;kBAAExB,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAd,QAAA,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnFtF,OAAA;gBAAGwE,KAAK,EAAE;kBAAE0C,MAAM,EAAE,WAAW;kBAAExB,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAd,QAAA,EAAC;cAEpE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJtF,OAAA;gBAAGwE,KAAK,EAAE;kBAAE0C,MAAM,EAAE,GAAG;kBAAExB,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAd,QAAA,EAAC;cAE5D;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJtF,OAAA;gBAAKwE,KAAK,EAAE;kBACV+C,SAAS,EAAE,MAAM;kBACjBxC,UAAU,EAAE,mDAAmD;kBAC/DW,KAAK,EAAE,OAAO;kBACdd,OAAO,EAAE,UAAU;kBACnBI,YAAY,EAAE,MAAM;kBACpBW,QAAQ,EAAE,MAAM;kBAChBF,UAAU,EAAE,KAAK;kBACjBhB,OAAO,EAAE;gBACX,CAAE;gBAAAI,QAAA,EAAC;cAEH;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtF,OAAA;UAAKwE,KAAK,EAAE;YAAEgB,YAAY,EAAE;UAAO,CAAE;UAAAX,QAAA,gBACnC7E,OAAA;YAAOwH,OAAO,EAAC,aAAa;YAAChD,KAAK,EAAE;cAAEC,OAAO,EAAE,OAAO;cAAEe,YAAY,EAAE,KAAK;cAAEC,UAAU,EAAE,KAAK;cAAEC,KAAK,EAAE,MAAM;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAEnI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtF,OAAA;YACEwD,EAAE,EAAC,aAAa;YAChB8D,KAAK,EAAE7G,WAAY;YACnBuG,QAAQ,EAAGP,CAAC,IAAK/F,cAAc,CAAC+F,CAAC,CAAC7E,MAAM,CAAC0F,KAAK,CAAE;YAChDG,WAAW,EAAC,iQAA+C;YAC3DC,IAAI,EAAE,CAAE;YACRlD,KAAK,EAAE;cACLmD,KAAK,EAAE,MAAM;cACb/C,OAAO,EAAE,MAAM;cACfqB,MAAM,EAAE,mBAAmB;cAC3BjB,YAAY,EAAE,KAAK;cACnB4C,MAAM,EAAE,UAAU;cAClBC,UAAU,EAAE,SAAS;cACrBlC,QAAQ,EAAE,MAAM;cAChBmC,UAAU,EAAE,KAAK;cACjB5C,UAAU,EAAE,wBAAwB;cACpCH,UAAU,EAAE;YACd,CAAE;YACFgD,OAAO,EAAGtB,CAAC,IAAK;cACdA,CAAC,CAAC7E,MAAM,CAAC4C,KAAK,CAACmC,WAAW,GAAG,SAAS;cACtCF,CAAC,CAAC7E,MAAM,CAAC4C,KAAK,CAACO,UAAU,GAAG,SAAS;YACvC,CAAE;YACFiD,MAAM,EAAGvB,CAAC,IAAK;cACbA,CAAC,CAAC7E,MAAM,CAAC4C,KAAK,CAACmC,WAAW,GAAG,SAAS;cACtCF,CAAC,CAAC7E,MAAM,CAAC4C,KAAK,CAACO,UAAU,GAAG,SAAS;YACvC,CAAE;YACFkD,QAAQ;UAAA;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFtF,OAAA;YAAKwE,KAAK,EAAE;cAAEyC,SAAS,EAAE,OAAO;cAAEtB,QAAQ,EAAE,MAAM;cAAED,KAAK,EAAE,MAAM;cAAE6B,SAAS,EAAE;YAAM,CAAE;YAAA1C,QAAA,GACnFpE,WAAW,CAACsD,MAAM,EAAC,MACtB;UAAA;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtF,OAAA;UACEmD,IAAI,EAAC,QAAQ;UACb+E,QAAQ,EAAEvH,WAAW,IAAI,CAACJ,YAAY,IAAI,CAACE,WAAW,CAACuB,IAAI,CAAC,CAAE;UAC9DwC,KAAK,EAAE;YACLO,UAAU,EAAEpE,WAAW,GAAG,SAAS,GAAG,mDAAmD;YACzF+E,KAAK,EAAE,OAAO;YACdO,MAAM,EAAE,MAAM;YACdrB,OAAO,EAAE,WAAW;YACpBI,YAAY,EAAE,KAAK;YACnBW,QAAQ,EAAE,MAAM;YAChBF,UAAU,EAAE,KAAK;YACjBY,MAAM,EAAE1F,WAAW,GAAG,aAAa,GAAG,SAAS;YAC/CuE,UAAU,EAAE,eAAe;YAC3BT,OAAO,EAAE,MAAM;YACf0B,UAAU,EAAE,QAAQ;YACpBzB,GAAG,EAAE,KAAK;YACViD,KAAK,EAAE,MAAM;YACbvB,cAAc,EAAE;UAClB,CAAE;UAAAvB,QAAA,EAEDlE,WAAW,gBACVX,OAAA,CAAAE,SAAA;YAAA2E,QAAA,gBACE7E,OAAA;cAAMwE,KAAK,EAAE;gBACXmD,KAAK,EAAE,MAAM;gBACbhD,MAAM,EAAE,MAAM;gBACdsB,MAAM,EAAE,uBAAuB;gBAC/BkC,SAAS,EAAE,iBAAiB;gBAC5BnD,YAAY,EAAE,KAAK;gBACnBoD,SAAS,EAAE;cACb;YAAE;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,qCAEZ;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGLzE,YAAY,iBACXb,OAAA;MAAKwE,KAAK,EAAE;QACVM,IAAI,EAAE,GAAG;QACTC,UAAU,EAAE,OAAO;QACnBH,OAAO,EAAE,MAAM;QACfI,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE,4BAA4B;QACvCsB,QAAQ,EAAE,QAAQ;QAClB9B,OAAO,EAAE,MAAM;QACfyB,aAAa,EAAE;MACjB,CAAE;MAAArB,QAAA,gBACA7E,OAAA;QAAKwE,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACf0B,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,eAAe;UAC/BZ,YAAY,EAAE,MAAM;UACpB6C,aAAa,EAAE,MAAM;UACrBC,YAAY,EAAE;QAChB,CAAE;QAAAzD,QAAA,gBACA7E,OAAA;UAAIwE,KAAK,EAAE;YAAE0C,MAAM,EAAE,CAAC;YAAExB,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE,MAAM;YAAEF,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EAAC;QAE9E;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtF,OAAA;UAAKwE,KAAK,EAAE;YACVO,UAAU,EAAEhE,gBAAgB,CAACE,OAAO,KAAK,GAAG,GAAG,SAAS,GAAG,SAAS;YACpEyE,KAAK,EAAE3E,gBAAgB,CAACE,OAAO,KAAK,GAAG,GAAG,SAAS,GAAG,MAAM;YAC5D2D,OAAO,EAAE,UAAU;YACnBI,YAAY,EAAE,MAAM;YACpBW,QAAQ,EAAE,MAAM;YAChBF,UAAU,EAAE,KAAK;YACjBQ,MAAM,EAAElF,gBAAgB,CAACE,OAAO,KAAK,GAAG,GAAG,mBAAmB,GAAG;UACnE,CAAE;UAAA4D,QAAA,GACC9D,gBAAgB,CAACE,OAAO,EAAC,GAC5B;QAAA;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtF,OAAA;QAAKwE,KAAK,EAAE;UACVO,UAAU,EAAE,SAAS;UACrBkB,MAAM,EAAE,mBAAmB;UAC3BjB,YAAY,EAAE,KAAK;UACnBJ,OAAO,EAAE,MAAM;UACfY,YAAY,EAAE;QAChB,CAAE;QAAAX,QAAA,gBACA7E,OAAA;UAAKwE,KAAK,EAAE;YAAEmB,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE,MAAM;YAAEF,YAAY,EAAE;UAAM,CAAE;UAAAX,QAAA,EAAC;QAEtE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNtF,OAAA;UAAKwE,KAAK,EAAE;YAAEmB,QAAQ,EAAE,MAAM;YAAEF,UAAU,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAb,QAAA,EAChE9D,gBAAgB,CAACG;QAAW;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtF,OAAA;QAAKwE,KAAK,EAAE;UAAEgB,YAAY,EAAE;QAAO,CAAE;QAAAX,QAAA,gBACnC7E,OAAA;UAAKwE,KAAK,EAAE;YAAEmB,QAAQ,EAAE,MAAM;YAAEF,UAAU,EAAE,KAAK;YAAEC,KAAK,EAAE,MAAM;YAAEF,YAAY,EAAE;UAAO,CAAE;UAAAX,QAAA,EAAC;QAE1F;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNtF,OAAA;UAAKwE,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAM,CAAE;UAAAG,QAAA,EACzC9D,gBAAgB,CAACI,KAAK,CAAC8C,GAAG,CAAC,CAACL,IAAI,EAAEO,KAAK,kBACtCnE,OAAA;YAAiBwE,KAAK,EAAE;cACtBM,IAAI,EAAE,CAAC;cACPF,OAAO,EAAE,UAAU;cACnBG,UAAU,EAAEnB,IAAI,CAACvC,MAAM,KAAK,WAAW,GAAG,SAAS,GACxCuC,IAAI,CAACvC,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG,SAAS;cAC/D4E,MAAM,EAAE,aAAarC,IAAI,CAACvC,MAAM,KAAK,WAAW,GAAG,SAAS,GACxCuC,IAAI,CAACvC,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG,SAAS,EAAE;cAC1E2D,YAAY,EAAE,KAAK;cACnBiC,SAAS,EAAE,QAAQ;cACnBtB,QAAQ,EAAE,MAAM;cAChBT,UAAU,EAAE;YACd,CAAE;YAAAL,QAAA,gBACA7E,OAAA;cAAKwE,KAAK,EAAE;gBACVmB,QAAQ,EAAE,MAAM;gBAChBH,YAAY,EAAE,KAAK;gBACnBE,KAAK,EAAE9B,IAAI,CAACvC,MAAM,KAAK,WAAW,GAAG,SAAS,GACvCuC,IAAI,CAACvC,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG;cACpD,CAAE;cAAAwD,QAAA,EACCjB,IAAI,CAACvC,MAAM,KAAK,WAAW,GAAG,GAAG,GACjCuC,IAAI,CAACvC,MAAM,KAAK,YAAY,GAAG,GAAG,GAAG;YAAG;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNtF,OAAA;cAAKwE,KAAK,EAAE;gBAAEiB,UAAU,EAAE,KAAK;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAd,QAAA,EAChEjB,IAAI,CAACxC;YAAI;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACNtF,OAAA;cAAKwE,KAAK,EAAE;gBACVmD,KAAK,EAAE,MAAM;gBACbhD,MAAM,EAAE,KAAK;gBACbI,UAAU,EAAE,SAAS;gBACrBC,YAAY,EAAE,KAAK;gBACnBuB,QAAQ,EAAE,QAAQ;gBAClBgB,SAAS,EAAE;cACb,CAAE;cAAA1C,QAAA,eACA7E,OAAA;gBAAKwE,KAAK,EAAE;kBACVG,MAAM,EAAE,MAAM;kBACdgD,KAAK,EAAE,GAAG/D,IAAI,CAACtC,QAAQ,GAAG;kBAC1ByD,UAAU,EAAEnB,IAAI,CAACvC,MAAM,KAAK,WAAW,GAAG,SAAS,GACxCuC,IAAI,CAACvC,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG,SAAS;kBAC/D6D,UAAU,EAAE,iBAAiB;kBAC7BF,YAAY,EAAE;gBAChB;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA,GAxCEnB,KAAK;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyCV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtF,OAAA;QAAKwE,KAAK,EAAE;UAAEM,IAAI,EAAE,CAAC;UAAEL,OAAO,EAAE,MAAM;UAAEyB,aAAa,EAAE;QAAS,CAAE;QAAArB,QAAA,gBAChE7E,OAAA;UAAKwE,KAAK,EAAE;YACVC,OAAO,EAAE,MAAM;YACf0B,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,eAAe;YAC/BZ,YAAY,EAAE;UAChB,CAAE;UAAAX,QAAA,gBACA7E,OAAA;YAAKwE,KAAK,EAAE;cAAEmB,QAAQ,EAAE,MAAM;cAAEF,UAAU,EAAE,KAAK;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAb,QAAA,EAAC;UAEpE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtF,OAAA;YAAKwE,KAAK,EAAE;cACVmB,QAAQ,EAAE,MAAM;cAChBD,KAAK,EAAE,MAAM;cACbX,UAAU,EAAE,SAAS;cACrBH,OAAO,EAAE,SAAS;cAClBI,YAAY,EAAE;YAChB,CAAE;YAAAH,QAAA,GACCtD,YAAY,CAACwC,MAAM,EAAC,qBACvB;UAAA;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtF,OAAA;UAAKwE,KAAK,EAAE;YACVM,IAAI,EAAE,CAAC;YACPC,UAAU,EAAE,SAAS;YACrBC,YAAY,EAAE,KAAK;YACnBJ,OAAO,EAAE,MAAM;YACf2B,QAAQ,EAAE,MAAM;YAChBsB,UAAU,EAAE,4CAA4C;YACxDlC,QAAQ,EAAE,MAAM;YAChBmC,UAAU,EAAE,KAAK;YACjBS,SAAS,EAAE;UACb,CAAE;UAAA1D,QAAA,EACCtD,YAAY,CAACwC,MAAM,KAAK,CAAC,gBACxB/D,OAAA;YAAKwE,KAAK,EAAE;cAAEkB,KAAK,EAAE,MAAM;cAAEuB,SAAS,EAAE,QAAQ;cAAErC,OAAO,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAErE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAEN/D,YAAY,CAAC0C,GAAG,CAAEuE,GAAG,iBACnBxI,OAAA;YAAkBwE,KAAK,EAAE;cACvBgB,YAAY,EAAE,KAAK;cACnBZ,OAAO,EAAE,OAAO;cAChB0D,YAAY,EAAE;YAChB,CAAE;YAAAzD,QAAA,eACA7E,OAAA;cAAKwE,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACf0B,UAAU,EAAE,YAAY;gBACxBzB,GAAG,EAAE;cACP,CAAE;cAAAG,QAAA,gBACA7E,OAAA;gBAAMwE,KAAK,EAAE;kBACXkB,KAAK,EAAE,MAAM;kBACbC,QAAQ,EAAE,MAAM;kBAChB8C,QAAQ,EAAE,MAAM;kBAChBhD,UAAU,EAAE;gBACd,CAAE;gBAAAZ,QAAA,EACC2D,GAAG,CAACpF;cAAS;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACPtF,OAAA;gBAAMwE,KAAK,EAAE;kBACXkB,KAAK,EAAE8C,GAAG,CAACrF,IAAI,KAAK,OAAO,GAAG,SAAS,GAChCqF,GAAG,CAACrF,IAAI,KAAK,MAAM,GAAG,SAAS,GAC/BqF,GAAG,CAACrF,IAAI,KAAK,SAAS,GAAG,SAAS,GAClCqF,GAAG,CAACrF,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;kBACnDsC,UAAU,EAAE+C,GAAG,CAACrF,IAAI,KAAK,MAAM,IAAIqF,GAAG,CAACrF,IAAI,KAAK,OAAO,IAAIqF,GAAG,CAACrF,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG;gBAC9F,CAAE;gBAAA0B,QAAA,EACC2D,GAAG,CAACvF;cAAO;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC,GA3BEkD,GAAG,CAAChF,EAAE;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4BX,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDtF,OAAA;MAAA6E,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;IAAO;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAChF,EAAA,CA5jBIH,YAAY;AAAAuI,EAAA,GAAZvI,YAAY;AA8jBlB,eAAeA,YAAY;AAAC,IAAAuI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}