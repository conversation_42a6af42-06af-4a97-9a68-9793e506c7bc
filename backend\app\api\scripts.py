"""
脚本管理API路由
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks, Form
import subprocess
import os
import asyncio
import logging
from typing import List, Dict, Any
from app.core.script_manager import script_manager
from app.core.config import config

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/scripts")
async def get_scripts_list():
    """
    获取所有脚本列表
    
    Returns:
        脚本列表
    """
    try:
        scripts = script_manager.get_scripts_list()
        return {
            "scripts": scripts,
            "count": len(scripts)
        }
    except Exception as e:
        logger.error(f"获取脚本列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取脚本列表失败: {str(e)}")

@router.get("/scripts/{script_id}")
async def get_script_details(script_id: str):
    """
    获取脚本详细信息
    
    Args:
        script_id: 脚本ID
        
    Returns:
        脚本详细信息
    """
    try:
        script = script_manager.get_script_by_id(script_id)
        if not script:
            raise HTTPException(status_code=404, detail="脚本不存在")
        
        # 获取运行历史
        run_history = script_manager.get_script_run_history(script_id)
        
        return {
            "script": script,
            "run_history": run_history
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取脚本详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取脚本详情失败: {str(e)}")

@router.post("/scripts/{script_id}/run")
async def run_script(script_id: str, background_tasks: BackgroundTasks):
    """
    运行脚本
    
    Args:
        script_id: 脚本ID
        background_tasks: 后台任务
        
    Returns:
        运行结果
    """
    try:
        script = script_manager.get_script_by_id(script_id)
        if not script:
            raise HTTPException(status_code=404, detail="脚本不存在")
        
        # 启动后台运行任务
        background_tasks.add_task(execute_script, script_id, script)
        
        return {
            "message": f"脚本 {script.get('script_name', '')} 开始运行",
            "script_id": script_id,
            "status": "running"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动脚本运行失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动脚本运行失败: {str(e)}")

async def execute_script(script_id: str, script_info: Dict[str, Any]):
    """执行脚本（后台任务）"""
    try:
        script_name = script_info.get('script_name', '')
        file_path = script_info.get('file_path', '')
        
        if not os.path.exists(file_path):
            raise Exception(f"脚本文件不存在: {file_path}")
        
        # 构建运行命令
        cmd = f"npx playwright test --headed {file_path}"
        
        # 执行脚本
        logger.info(f"执行脚本: {cmd}")
        
        process = await asyncio.create_subprocess_shell(
            cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd=os.path.dirname(os.path.dirname(os.path.dirname(__file__)))  # 项目根目录
        )
        
        stdout, stderr = await process.communicate()
        
        # 处理结果
        output = stdout.decode('utf-8') if stdout else ''
        error = stderr.decode('utf-8') if stderr else ''
        
        if process.returncode == 0:
            status = "success"
            logger.info(f"脚本 {script_name} 运行成功")
        else:
            status = "failed"
            logger.error(f"脚本 {script_name} 运行失败: {error}")
        
        # 记录运行历史
        script_manager.record_script_run(script_id, status, output, error)
        
    except Exception as e:
        error_msg = str(e)
        logger.error(f"脚本执行异常: {error_msg}")
        script_manager.record_script_run(script_id, "error", "", error_msg)

@router.get("/scripts/{script_id}/run-history")
async def get_script_run_history(script_id: str):
    """
    获取脚本运行历史
    
    Args:
        script_id: 脚本ID
        
    Returns:
        运行历史列表
    """
    try:
        script = script_manager.get_script_by_id(script_id)
        if not script:
            raise HTTPException(status_code=404, detail="脚本不存在")
        
        run_history = script_manager.get_script_run_history(script_id)
        
        return {
            "script_id": script_id,
            "script_name": script.get('script_name', ''),
            "run_history": run_history,
            "total_runs": len(run_history)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取运行历史失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取运行历史失败: {str(e)}")

@router.get("/scripts/{script_id}/report")
async def get_script_report(script_id: str):
    """
    获取脚本运行报告
    
    Args:
        script_id: 脚本ID
        
    Returns:
        报告信息
    """
    try:
        script = script_manager.get_script_by_id(script_id)
        if not script:
            raise HTTPException(status_code=404, detail="脚本不存在")
        
        # 查找对应的报告文件
        report_dir = config.REPORTS_FOLDER
        script_name = script.get('script_name', '').replace('.spec.ts', '')
        
        # 可能的报告文件路径
        possible_reports = [
            os.path.join(report_dir, f"{script_name}.html"),
            os.path.join(report_dir, "index.html"),
            os.path.join(report_dir, "report.html")
        ]
        
        report_path = None
        for path in possible_reports:
            if os.path.exists(path):
                report_path = path
                break
        
        if report_path:
            return {
                "script_id": script_id,
                "script_name": script.get('script_name', ''),
                "report_path": report_path,
                "report_url": f"/reports/{os.path.basename(report_path)}",
                "exists": True
            }
        else:
            return {
                "script_id": script_id,
                "script_name": script.get('script_name', ''),
                "report_path": None,
                "report_url": None,
                "exists": False,
                "message": "报告文件不存在，请先运行脚本"
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取脚本报告失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取脚本报告失败: {str(e)}")

@router.post("/config/ai")
async def update_ai_config(
    api_key: str = Form(..., description="API密钥"),
    base_url: str = Form(..., description="API基础URL"),
    model_name: str = Form(..., description="模型名称")
):
    """
    更新AI模型配置
    
    Args:
        api_key: API密钥
        base_url: API基础URL
        model_name: 模型名称
        
    Returns:
        更新结果
    """
    try:
        # 更新配置
        config.update_ai_config(api_key, base_url, model_name)
        
        return {
            "message": "AI模型配置更新成功",
            "config": config.get_ai_config()
        }
        
    except Exception as e:
        logger.error(f"更新AI配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新AI配置失败: {str(e)}")

@router.get("/config/ai")
async def get_ai_config():
    """
    获取当前AI模型配置
    
    Returns:
        AI模型配置
    """
    try:
        ai_config = config.get_ai_config()
        # 隐藏API密钥的敏感部分
        if ai_config.get('api_key'):
            api_key = ai_config['api_key']
            if len(api_key) > 8:
                ai_config['api_key'] = api_key[:4] + '*' * (len(api_key) - 8) + api_key[-4:]
        
        return {
            "config": ai_config,
            "status": "configured" if config.OPENAI_API_KEY else "not_configured"
        }
        
    except Exception as e:
        logger.error(f"获取AI配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取AI配置失败: {str(e)}")
