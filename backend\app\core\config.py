"""
配置管理模块
"""
import os
from typing import Dict, Any

class Config:
    """配置管理类"""
    
    def __init__(self):
        # AI模型配置
        self.OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', 'sk-3b9cb1dbb58a421082ba5c9f0d6c07d6')
        self.OPENAI_BASE_URL = os.getenv('OPENAI_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1')
        self.MIDSCENE_MODEL_NAME = os.getenv('MIDSCENE_MODEL_NAME', 'qwen-vl-max-latest')
        
        # 文件存储配置
        self.UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads')
        self.STATIC_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static')
        self.REPORTS_FOLDER = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'midscene_run', 'report')
        
        # 确保目录存在
        os.makedirs(self.UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(self.STATIC_FOLDER, exist_ok=True)
        os.makedirs(self.REPORTS_FOLDER, exist_ok=True)
        
        # Redis配置
        self.REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
        
        # Celery配置
        self.CELERY_BROKER_URL = self.REDIS_URL
        self.CELERY_RESULT_BACKEND = self.REDIS_URL
    
    def update_ai_config(self, api_key: str = None, base_url: str = None, model_name: str = None):
        """更新AI模型配置"""
        if api_key:
            self.OPENAI_API_KEY = api_key
        if base_url:
            self.OPENAI_BASE_URL = base_url
        if model_name:
            self.MIDSCENE_MODEL_NAME = model_name
    
    def get_ai_config(self) -> Dict[str, Any]:
        """获取AI模型配置"""
        return {
            'api_key': self.OPENAI_API_KEY,
            'base_url': self.OPENAI_BASE_URL,
            'model_name': self.MIDSCENE_MODEL_NAME
        }

# 全局配置实例
config = Config()
