#!/usr/bin/env python3
"""
快速功能测试脚本
"""
import requests
import json
import sys
import os

# 添加后端路径
sys.path.append('backend')

def test_backend_apis():
    """测试后端API"""
    print("🔧 测试后端API...")
    
    base_url = "http://localhost:8000/api/v1"
    
    # 1. 测试AI配置
    try:
        response = requests.get(f"{base_url}/config/ai")
        if response.status_code == 200:
            print("  ✅ AI配置API正常")
        else:
            print(f"  ❌ AI配置API异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ AI配置API错误: {e}")
        return False
    
    # 2. 测试脚本管理
    try:
        response = requests.get(f"{base_url}/scripts")
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ 脚本管理API正常，当前有 {data['count']} 个脚本")
        else:
            print(f"  ❌ 脚本管理API异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 脚本管理API错误: {e}")
        return False
    
    return True

def test_agents_mock():
    """测试智能体（模拟模式）"""
    print("🤖 测试智能体（模拟模式）...")
    
    try:
        from backend.app.agents.element_detection.agent import ElementDetectionAgent
        from backend.app.agents.interaction_analysis.agent import InteractionAnalysisAgent
        from backend.app.agents.test_generation.agent import TestGenerationAgent
        
        # 创建测试图片
        from PIL import Image
        test_image = "test.png"
        img = Image.new('RGB', (800, 600), color='white')
        img.save(test_image)
        
        # 1. 元素识别（模拟模式）
        element_agent = ElementDetectionAgent(api_key="")  # 空密钥使用模拟模式
        elements = element_agent.analyze(test_image, "测试界面")
        print(f"  ✅ 元素识别: {len(elements)} 个元素")
        
        # 2. 交互分析（模拟模式）
        interaction_agent = InteractionAnalysisAgent(api_key="")
        flows = interaction_agent.analyze(elements, "测试界面")
        print(f"  ✅ 交互分析: {len(flows)} 个流程")
        
        # 3. 脚本生成（模拟模式）
        script_agent = TestGenerationAgent(api_key="")
        scripts = script_agent.analyze(elements, flows, "测试界面")
        print(f"  ✅ 脚本生成: {len(scripts)} 个脚本")
        
        # 清理
        os.remove(test_image)
        return True
        
    except Exception as e:
        print(f"  ❌ 智能体测试失败: {e}")
        return False

def test_script_manager():
    """测试脚本管理器"""
    print("📝 测试脚本管理器...")
    
    try:
        from backend.app.core.script_manager import script_manager
        
        # 创建测试脚本
        test_script = {
            'script_name': '20241218-1500-测试脚本.spec.ts',
            'original_name': '测试脚本',
            'description': '快速测试脚本',
            'priority': 'medium',
            'test_steps': [
                {
                    'action_type': 'aiTap',
                    'action_description': '点击按钮',
                    'visual_target': '测试按钮'
                }
            ]
        }
        
        # 保存脚本
        task_id = "quick-test-001"
        script_path = script_manager.save_script(test_script, task_id)
        
        if script_path and os.path.exists(script_path):
            print(f"  ✅ 脚本保存成功: {os.path.basename(script_path)}")
            
            # 测试获取脚本列表
            scripts = script_manager.get_scripts_list()
            print(f"  ✅ 脚本列表: {len(scripts)} 个脚本")
            
            # 测试运行记录
            script_manager.record_script_run(task_id, "success", "测试成功")
            history = script_manager.get_script_run_history(task_id)
            print(f"  ✅ 运行历史: {len(history)} 条记录")
            
            return True
        else:
            print("  ❌ 脚本保存失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 脚本管理器测试失败: {e}")
        return False

def test_environment():
    """测试环境配置"""
    print("🌍 测试环境配置...")
    
    checks = [
        ("package.json", os.path.exists("package.json")),
        ("playwright.config.ts", os.path.exists("playwright.config.ts")),
        ("tests目录", os.path.exists("tests")),
        ("报告目录", os.path.exists("midscene_run/report")),
        ("后端静态目录", os.path.exists("backend/static")),
        ("前端源码", os.path.exists("frontend/src")),
    ]
    
    all_good = True
    for name, exists in checks:
        status = "✅" if exists else "❌"
        print(f"  {status} {name}")
        if not exists:
            all_good = False
    
    return all_good

def main():
    """主测试函数"""
    print("🚀 UI自动化测试平台快速验证")
    print("=" * 50)
    
    tests = [
        ("后端API", test_backend_apis),
        ("智能体模拟", test_agents_mock),
        ("脚本管理", test_script_manager),
        ("环境配置", test_environment),
    ]
    
    results = []
    for name, test_func in tests:
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} 测试异常: {e}")
            results.append((name, False))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
    
    print(f"\n总计: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 系统核心功能正常！")
        print("\n📋 使用指南:")
        print("1. 启动前端: cd frontend && npm start")
        print("2. 访问: http://localhost:3000")
        print("3. 在设置页面配置AI模型参数")
        print("4. 上传UI截图开始分析")
        print("5. 查看历史记录和运行脚本")
    else:
        print("\n⚠️ 部分功能异常，请检查配置")

if __name__ == "__main__":
    main()
