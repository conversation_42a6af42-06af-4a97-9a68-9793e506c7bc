{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\pages\\\\HistoryPage.js\",\n  _s = $RefreshSig$();\n/**\n * 历史记录页面\n */\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HistoryPage = () => {\n  _s();\n  const [historyData, setHistoryData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedItem, setSelectedItem] = useState(null);\n  useEffect(() => {\n    // 模拟加载历史数据\n    setTimeout(() => {\n      setHistoryData([{\n        id: '1',\n        taskId: 'task-001',\n        fileName: '登录界面.png',\n        description: '用户登录界面分析',\n        status: 'completed',\n        createdAt: '2024-12-17 10:30:00',\n        elementsCount: 5,\n        flowsCount: 2,\n        scriptsCount: 1\n      }, {\n        id: '2',\n        taskId: 'task-002',\n        fileName: '文件管理界面.png',\n        description: '文件管理界面功能分析',\n        status: 'completed',\n        createdAt: '2024-12-17 14:15:00',\n        elementsCount: 8,\n        flowsCount: 3,\n        scriptsCount: 2\n      }, {\n        id: '3',\n        taskId: 'task-003',\n        fileName: '购物车页面.png',\n        description: '电商购物车界面分析',\n        status: 'failed',\n        createdAt: '2024-12-17 16:45:00',\n        elementsCount: 0,\n        flowsCount: 0,\n        scriptsCount: 0\n      }]);\n      setLoading(false);\n    }, 1000);\n  }, []);\n  const getStatusColor = status => {\n    switch (status) {\n      case 'completed':\n        return '#28a745';\n      case 'failed':\n        return '#dc3545';\n      case 'processing':\n        return '#007bff';\n      default:\n        return '#6c757d';\n    }\n  };\n  const getStatusText = status => {\n    switch (status) {\n      case 'completed':\n        return '已完成';\n      case 'failed':\n        return '失败';\n      case 'processing':\n        return '处理中';\n      default:\n        return '未知';\n    }\n  };\n  const handleViewDetails = item => {\n    setSelectedItem(item);\n  };\n  const handleDownloadScript = item => {\n    // 模拟下载脚本\n    const scriptContent = `# ${item.description}\\n# 生成时间: ${item.createdAt}\\n\\nname: ${item.description}\\ndescription: 自动化测试脚本\\n\\nsteps:\\n  - name: 示例步骤\\n    action: aiTap\\n    locate: 示例元素\\n    expect: 预期结果`;\n    const blob = new Blob([scriptContent], {\n      type: 'text/yaml'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `${item.fileName.replace('.png', '')}_script.yaml`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-page\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83D\\uDCCB \\u5206\\u6790\\u5386\\u53F2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u67E5\\u770B\\u6240\\u6709UI\\u5206\\u6790\\u8BB0\\u5F55\\u548C\\u7ED3\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u52A0\\u8F7D\\u5386\\u53F2\\u8BB0\\u5F55\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"history-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDCCB \\u5206\\u6790\\u5386\\u53F2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u67E5\\u770B\\u6240\\u6709UI\\u5206\\u6790\\u8BB0\\u5F55\\u548C\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-content\",\n      children: historyData.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-icon\",\n          children: \"\\uD83D\\uDCDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u6682\\u65E0\\u5206\\u6790\\u8BB0\\u5F55\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u5F00\\u59CB\\u60A8\\u7684\\u7B2C\\u4E00\\u6B21UI\\u5206\\u6790\\u5427\\uFF01\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-list\",\n        children: historyData.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"history-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"item-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: item.fileName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-badge\",\n                style: {\n                  backgroundColor: getStatusColor(item.status)\n                },\n                children: getStatusText(item.status)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"item-stats\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-label\",\n                children: \"UI\\u5143\\u7D20\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-value\",\n                children: item.elementsCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-label\",\n                children: \"\\u4EA4\\u4E92\\u6D41\\u7A0B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-value\",\n                children: item.flowsCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-label\",\n                children: \"\\u6D4B\\u8BD5\\u811A\\u672C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-value\",\n                children: item.scriptsCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"item-footer\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"item-time\",\n              children: [\"\\u521B\\u5EFA\\u65F6\\u95F4: \", item.createdAt]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-secondary\",\n                onClick: () => handleViewDetails(item),\n                children: \"\\u67E5\\u770B\\u8BE6\\u60C5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 21\n              }, this), item.status === 'completed' && /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-primary\",\n                onClick: () => handleDownloadScript(item),\n                children: \"\\u4E0B\\u8F7D\\u811A\\u672C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 17\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .history-page {\n          min-height: 100vh;\n          background: #f8f9fa;\n        }\n\n        .page-header {\n          background: white;\n          border-bottom: 1px solid #e9ecef;\n          padding: 24px 0;\n          margin-bottom: 24px;\n        }\n\n        .page-header h1 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 28px;\n          font-weight: 600;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-header p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-content {\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 0 24px;\n        }\n\n        .loading-container {\n          text-align: center;\n          padding: 60px 20px;\n        }\n\n        .loading-spinner {\n          width: 40px;\n          height: 40px;\n          border: 4px solid #e9ecef;\n          border-top: 4px solid #007bff;\n          border-radius: 50%;\n          animation: spin 1s linear infinite;\n          margin: 0 auto 16px;\n        }\n\n        .empty-state {\n          text-align: center;\n          padding: 60px 20px;\n          background: white;\n          border-radius: 12px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n        }\n\n        .empty-icon {\n          font-size: 48px;\n          margin-bottom: 16px;\n        }\n\n        .empty-state h3 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 20px;\n        }\n\n        .empty-state p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n        }\n\n        .history-list {\n          display: grid;\n          gap: 16px;\n        }\n\n        .history-item {\n          background: white;\n          border-radius: 12px;\n          padding: 20px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n          transition: all 0.2s ease;\n        }\n\n        .history-item:hover {\n          box-shadow: 0 4px 20px rgba(0,0,0,0.15);\n        }\n\n        .item-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 16px;\n        }\n\n        .item-info h3 {\n          margin: 0 0 4px 0;\n          color: #333;\n          font-size: 18px;\n          font-weight: 600;\n        }\n\n        .item-info p {\n          margin: 0;\n          color: #666;\n          font-size: 14px;\n        }\n\n        .status-badge {\n          color: white;\n          padding: 4px 12px;\n          border-radius: 20px;\n          font-size: 12px;\n          font-weight: 500;\n        }\n\n        .item-stats {\n          display: flex;\n          gap: 24px;\n          margin-bottom: 16px;\n          padding: 16px 0;\n          border-top: 1px solid #e9ecef;\n          border-bottom: 1px solid #e9ecef;\n        }\n\n        .stat {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          gap: 4px;\n        }\n\n        .stat-label {\n          font-size: 12px;\n          color: #666;\n        }\n\n        .stat-value {\n          font-size: 18px;\n          font-weight: 600;\n          color: #333;\n        }\n\n        .item-footer {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n        }\n\n        .item-time {\n          font-size: 12px;\n          color: #999;\n        }\n\n        .item-actions {\n          display: flex;\n          gap: 8px;\n        }\n\n        .btn-primary, .btn-secondary {\n          border: none;\n          padding: 8px 16px;\n          border-radius: 6px;\n          font-size: 14px;\n          cursor: pointer;\n          transition: all 0.2s ease;\n        }\n\n        .btn-primary {\n          background: #007bff;\n          color: white;\n        }\n\n        .btn-primary:hover {\n          background: #0056b3;\n        }\n\n        .btn-secondary {\n          background: #6c757d;\n          color: white;\n        }\n\n        .btn-secondary:hover {\n          background: #5a6268;\n        }\n\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n\n        /* 响应式设计 */\n        @media (max-width: 768px) {\n          .page-header h1, .page-header p, .page-content {\n            padding: 0 16px;\n          }\n\n          .item-header {\n            flex-direction: column;\n            gap: 12px;\n          }\n\n          .item-stats {\n            justify-content: space-around;\n          }\n\n          .item-footer {\n            flex-direction: column;\n            gap: 12px;\n            align-items: flex-start;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 5\n  }, this);\n};\n_s(HistoryPage, \"qeDha3f5n1EOPY7s+4jS7jZpbkg=\");\n_c = HistoryPage;\nexport default HistoryPage;\nvar _c;\n$RefreshReg$(_c, \"HistoryPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "HistoryPage", "_s", "historyData", "setHistoryData", "loading", "setLoading", "selectedItem", "setSelectedItem", "setTimeout", "id", "taskId", "fileName", "description", "status", "createdAt", "elementsCount", "flowsCount", "scriptsCount", "getStatusColor", "getStatusText", "handleViewDetails", "item", "handleDownloadScript", "scriptContent", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "replace", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "className", "children", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "style", "backgroundColor", "onClick", "jsx", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/pages/HistoryPage.js"], "sourcesContent": ["/**\n * 历史记录页面\n */\nimport React, { useState, useEffect } from 'react';\n\nconst HistoryPage = () => {\n  const [historyData, setHistoryData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedItem, setSelectedItem] = useState(null);\n\n  useEffect(() => {\n    // 模拟加载历史数据\n    setTimeout(() => {\n      setHistoryData([\n        {\n          id: '1',\n          taskId: 'task-001',\n          fileName: '登录界面.png',\n          description: '用户登录界面分析',\n          status: 'completed',\n          createdAt: '2024-12-17 10:30:00',\n          elementsCount: 5,\n          flowsCount: 2,\n          scriptsCount: 1\n        },\n        {\n          id: '2',\n          taskId: 'task-002',\n          fileName: '文件管理界面.png',\n          description: '文件管理界面功能分析',\n          status: 'completed',\n          createdAt: '2024-12-17 14:15:00',\n          elementsCount: 8,\n          flowsCount: 3,\n          scriptsCount: 2\n        },\n        {\n          id: '3',\n          taskId: 'task-003',\n          fileName: '购物车页面.png',\n          description: '电商购物车界面分析',\n          status: 'failed',\n          createdAt: '2024-12-17 16:45:00',\n          elementsCount: 0,\n          flowsCount: 0,\n          scriptsCount: 0\n        }\n      ]);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'completed':\n        return '#28a745';\n      case 'failed':\n        return '#dc3545';\n      case 'processing':\n        return '#007bff';\n      default:\n        return '#6c757d';\n    }\n  };\n\n  const getStatusText = (status) => {\n    switch (status) {\n      case 'completed':\n        return '已完成';\n      case 'failed':\n        return '失败';\n      case 'processing':\n        return '处理中';\n      default:\n        return '未知';\n    }\n  };\n\n  const handleViewDetails = (item) => {\n    setSelectedItem(item);\n  };\n\n  const handleDownloadScript = (item) => {\n    // 模拟下载脚本\n    const scriptContent = `# ${item.description}\\n# 生成时间: ${item.createdAt}\\n\\nname: ${item.description}\\ndescription: 自动化测试脚本\\n\\nsteps:\\n  - name: 示例步骤\\n    action: aiTap\\n    locate: 示例元素\\n    expect: 预期结果`;\n    \n    const blob = new Blob([scriptContent], { type: 'text/yaml' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `${item.fileName.replace('.png', '')}_script.yaml`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"history-page\">\n        <div className=\"page-header\">\n          <h1>📋 分析历史</h1>\n          <p>查看所有UI分析记录和结果</p>\n        </div>\n        <div className=\"loading-container\">\n          <div className=\"loading-spinner\"></div>\n          <p>加载历史记录中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"history-page\">\n      <div className=\"page-header\">\n        <h1>📋 分析历史</h1>\n        <p>查看所有UI分析记录和结果</p>\n      </div>\n\n      <div className=\"page-content\">\n        {historyData.length === 0 ? (\n          <div className=\"empty-state\">\n            <div className=\"empty-icon\">📝</div>\n            <h3>暂无分析记录</h3>\n            <p>开始您的第一次UI分析吧！</p>\n          </div>\n        ) : (\n          <div className=\"history-list\">\n            {historyData.map((item) => (\n              <div key={item.id} className=\"history-item\">\n                <div className=\"item-header\">\n                  <div className=\"item-info\">\n                    <h3>{item.fileName}</h3>\n                    <p>{item.description}</p>\n                  </div>\n                  <div className=\"item-status\">\n                    <span \n                      className=\"status-badge\"\n                      style={{ backgroundColor: getStatusColor(item.status) }}\n                    >\n                      {getStatusText(item.status)}\n                    </span>\n                  </div>\n                </div>\n                \n                <div className=\"item-stats\">\n                  <div className=\"stat\">\n                    <span className=\"stat-label\">UI元素</span>\n                    <span className=\"stat-value\">{item.elementsCount}</span>\n                  </div>\n                  <div className=\"stat\">\n                    <span className=\"stat-label\">交互流程</span>\n                    <span className=\"stat-value\">{item.flowsCount}</span>\n                  </div>\n                  <div className=\"stat\">\n                    <span className=\"stat-label\">测试脚本</span>\n                    <span className=\"stat-value\">{item.scriptsCount}</span>\n                  </div>\n                </div>\n\n                <div className=\"item-footer\">\n                  <span className=\"item-time\">创建时间: {item.createdAt}</span>\n                  <div className=\"item-actions\">\n                    <button \n                      className=\"btn-secondary\"\n                      onClick={() => handleViewDetails(item)}\n                    >\n                      查看详情\n                    </button>\n                    {item.status === 'completed' && (\n                      <button \n                        className=\"btn-primary\"\n                        onClick={() => handleDownloadScript(item)}\n                      >\n                        下载脚本\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      <style jsx>{`\n        .history-page {\n          min-height: 100vh;\n          background: #f8f9fa;\n        }\n\n        .page-header {\n          background: white;\n          border-bottom: 1px solid #e9ecef;\n          padding: 24px 0;\n          margin-bottom: 24px;\n        }\n\n        .page-header h1 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 28px;\n          font-weight: 600;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-header p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-content {\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 0 24px;\n        }\n\n        .loading-container {\n          text-align: center;\n          padding: 60px 20px;\n        }\n\n        .loading-spinner {\n          width: 40px;\n          height: 40px;\n          border: 4px solid #e9ecef;\n          border-top: 4px solid #007bff;\n          border-radius: 50%;\n          animation: spin 1s linear infinite;\n          margin: 0 auto 16px;\n        }\n\n        .empty-state {\n          text-align: center;\n          padding: 60px 20px;\n          background: white;\n          border-radius: 12px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n        }\n\n        .empty-icon {\n          font-size: 48px;\n          margin-bottom: 16px;\n        }\n\n        .empty-state h3 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 20px;\n        }\n\n        .empty-state p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n        }\n\n        .history-list {\n          display: grid;\n          gap: 16px;\n        }\n\n        .history-item {\n          background: white;\n          border-radius: 12px;\n          padding: 20px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n          transition: all 0.2s ease;\n        }\n\n        .history-item:hover {\n          box-shadow: 0 4px 20px rgba(0,0,0,0.15);\n        }\n\n        .item-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 16px;\n        }\n\n        .item-info h3 {\n          margin: 0 0 4px 0;\n          color: #333;\n          font-size: 18px;\n          font-weight: 600;\n        }\n\n        .item-info p {\n          margin: 0;\n          color: #666;\n          font-size: 14px;\n        }\n\n        .status-badge {\n          color: white;\n          padding: 4px 12px;\n          border-radius: 20px;\n          font-size: 12px;\n          font-weight: 500;\n        }\n\n        .item-stats {\n          display: flex;\n          gap: 24px;\n          margin-bottom: 16px;\n          padding: 16px 0;\n          border-top: 1px solid #e9ecef;\n          border-bottom: 1px solid #e9ecef;\n        }\n\n        .stat {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          gap: 4px;\n        }\n\n        .stat-label {\n          font-size: 12px;\n          color: #666;\n        }\n\n        .stat-value {\n          font-size: 18px;\n          font-weight: 600;\n          color: #333;\n        }\n\n        .item-footer {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n        }\n\n        .item-time {\n          font-size: 12px;\n          color: #999;\n        }\n\n        .item-actions {\n          display: flex;\n          gap: 8px;\n        }\n\n        .btn-primary, .btn-secondary {\n          border: none;\n          padding: 8px 16px;\n          border-radius: 6px;\n          font-size: 14px;\n          cursor: pointer;\n          transition: all 0.2s ease;\n        }\n\n        .btn-primary {\n          background: #007bff;\n          color: white;\n        }\n\n        .btn-primary:hover {\n          background: #0056b3;\n        }\n\n        .btn-secondary {\n          background: #6c757d;\n          color: white;\n        }\n\n        .btn-secondary:hover {\n          background: #5a6268;\n        }\n\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n\n        /* 响应式设计 */\n        @media (max-width: 768px) {\n          .page-header h1, .page-header p, .page-content {\n            padding: 0 16px;\n          }\n\n          .item-header {\n            flex-direction: column;\n            gap: 12px;\n          }\n\n          .item-stats {\n            justify-content: space-around;\n          }\n\n          .item-footer {\n            flex-direction: column;\n            gap: 12px;\n            align-items: flex-start;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default HistoryPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAEtDC,SAAS,CAAC,MAAM;IACd;IACAW,UAAU,CAAC,MAAM;MACfL,cAAc,CAAC,CACb;QACEM,EAAE,EAAE,GAAG;QACPC,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE,UAAU;QACpBC,WAAW,EAAE,UAAU;QACvBC,MAAM,EAAE,WAAW;QACnBC,SAAS,EAAE,qBAAqB;QAChCC,aAAa,EAAE,CAAC;QAChBC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE;MAChB,CAAC,EACD;QACER,EAAE,EAAE,GAAG;QACPC,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE,YAAY;QACtBC,WAAW,EAAE,YAAY;QACzBC,MAAM,EAAE,WAAW;QACnBC,SAAS,EAAE,qBAAqB;QAChCC,aAAa,EAAE,CAAC;QAChBC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE;MAChB,CAAC,EACD;QACER,EAAE,EAAE,GAAG;QACPC,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE,WAAW;QACrBC,WAAW,EAAE,WAAW;QACxBC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,qBAAqB;QAChCC,aAAa,EAAE,CAAC;QAChBC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE;MAChB,CAAC,CACF,CAAC;MACFZ,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMa,cAAc,GAAIL,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,YAAY;QACf,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMM,aAAa,GAAIN,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,KAAK;MACd,KAAK,QAAQ;QACX,OAAO,IAAI;MACb,KAAK,YAAY;QACf,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMO,iBAAiB,GAAIC,IAAI,IAAK;IAClCd,eAAe,CAACc,IAAI,CAAC;EACvB,CAAC;EAED,MAAMC,oBAAoB,GAAID,IAAI,IAAK;IACrC;IACA,MAAME,aAAa,GAAG,KAAKF,IAAI,CAACT,WAAW,aAAaS,IAAI,CAACP,SAAS,aAAaO,IAAI,CAACT,WAAW,yGAAyG;IAE5M,MAAMY,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,aAAa,CAAC,EAAE;MAAEG,IAAI,EAAE;IAAY,CAAC,CAAC;IAC7D,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IACrC,MAAMM,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;IACZG,CAAC,CAACI,QAAQ,GAAG,GAAGb,IAAI,CAACV,QAAQ,CAACwB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,cAAc;IAC/DJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,CAAC,CAAC;IAC5BA,CAAC,CAACQ,KAAK,CAAC,CAAC;IACTP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,CAAC,CAAC;IAC5BF,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC;EAC1B,CAAC;EAED,IAAIvB,OAAO,EAAE;IACX,oBACEL,OAAA;MAAK0C,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B3C,OAAA;QAAK0C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B3C,OAAA;UAAA2C,QAAA,EAAI;QAAO;UAAA/B,QAAA,EAAAgC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChB9C,OAAA;UAAA2C,QAAA,EAAG;QAAa;UAAA/B,QAAA,EAAAgC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAlC,QAAA,EAAAgC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eACN9C,OAAA;QAAK0C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC3C,OAAA;UAAK0C,SAAS,EAAC;QAAiB;UAAA9B,QAAA,EAAAgC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvC9C,OAAA;UAAA2C,QAAA,EAAG;QAAU;UAAA/B,QAAA,EAAAgC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAlC,QAAA,EAAAgC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAlC,QAAA,EAAAgC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE9C,OAAA;IAAK0C,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B3C,OAAA;MAAK0C,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B3C,OAAA;QAAA2C,QAAA,EAAI;MAAO;QAAA/B,QAAA,EAAAgC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChB9C,OAAA;QAAA2C,QAAA,EAAG;MAAa;QAAA/B,QAAA,EAAAgC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAlC,QAAA,EAAAgC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eAEN9C,OAAA;MAAK0C,SAAS,EAAC,cAAc;MAAAC,QAAA,EAC1BxC,WAAW,CAAC4C,MAAM,KAAK,CAAC,gBACvB/C,OAAA;QAAK0C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B3C,OAAA;UAAK0C,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAE;UAAA/B,QAAA,EAAAgC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpC9C,OAAA;UAAA2C,QAAA,EAAI;QAAM;UAAA/B,QAAA,EAAAgC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACf9C,OAAA;UAAA2C,QAAA,EAAG;QAAa;UAAA/B,QAAA,EAAAgC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAlC,QAAA,EAAAgC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,gBAEN9C,OAAA;QAAK0C,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1BxC,WAAW,CAAC6C,GAAG,CAAE1B,IAAI,iBACpBtB,OAAA;UAAmB0C,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzC3C,OAAA;YAAK0C,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B3C,OAAA;cAAK0C,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB3C,OAAA;gBAAA2C,QAAA,EAAKrB,IAAI,CAACV;cAAQ;gBAAAA,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxB9C,OAAA;gBAAA2C,QAAA,EAAIrB,IAAI,CAACT;cAAW;gBAAAD,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAlC,QAAA,EAAAgC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACN9C,OAAA;cAAK0C,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1B3C,OAAA;gBACE0C,SAAS,EAAC,cAAc;gBACxBO,KAAK,EAAE;kBAAEC,eAAe,EAAE/B,cAAc,CAACG,IAAI,CAACR,MAAM;gBAAE,CAAE;gBAAA6B,QAAA,EAEvDvB,aAAa,CAACE,IAAI,CAACR,MAAM;cAAC;gBAAAF,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAlC,QAAA,EAAAgC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAlC,QAAA,EAAAgC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9C,OAAA;YAAK0C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB3C,OAAA;cAAK0C,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB3C,OAAA;gBAAM0C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAI;gBAAA/B,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxC9C,OAAA;gBAAM0C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAErB,IAAI,CAACN;cAAa;gBAAAJ,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAlC,QAAA,EAAAgC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACN9C,OAAA;cAAK0C,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB3C,OAAA;gBAAM0C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAI;gBAAA/B,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxC9C,OAAA;gBAAM0C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAErB,IAAI,CAACL;cAAU;gBAAAL,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAlC,QAAA,EAAAgC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACN9C,OAAA;cAAK0C,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB3C,OAAA;gBAAM0C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAI;gBAAA/B,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxC9C,OAAA;gBAAM0C,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAErB,IAAI,CAACJ;cAAY;gBAAAN,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAlC,QAAA,EAAAgC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAAlC,QAAA,EAAAgC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9C,OAAA;YAAK0C,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B3C,OAAA;cAAM0C,SAAS,EAAC,WAAW;cAAAC,QAAA,GAAC,4BAAM,EAACrB,IAAI,CAACP,SAAS;YAAA;cAAAH,QAAA,EAAAgC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzD9C,OAAA;cAAK0C,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3C,OAAA;gBACE0C,SAAS,EAAC,eAAe;gBACzBS,OAAO,EAAEA,CAAA,KAAM9B,iBAAiB,CAACC,IAAI,CAAE;gBAAAqB,QAAA,EACxC;cAED;gBAAA/B,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACRxB,IAAI,CAACR,MAAM,KAAK,WAAW,iBAC1Bd,OAAA;gBACE0C,SAAS,EAAC,aAAa;gBACvBS,OAAO,EAAEA,CAAA,KAAM5B,oBAAoB,CAACD,IAAI,CAAE;gBAAAqB,QAAA,EAC3C;cAED;gBAAA/B,QAAA,EAAAgC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAlC,QAAA,EAAAgC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAlC,QAAA,EAAAgC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAjDExB,IAAI,CAACZ,EAAE;UAAAE,QAAA,EAAAgC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkDZ,CACN;MAAC;QAAAlC,QAAA,EAAAgC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAAlC,QAAA,EAAAgC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN9C,OAAA;MAAOoD,GAAG;MAAAT,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAA/B,QAAA,EAAAgC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAlC,QAAA,EAAAgC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC5C,EAAA,CApZID,WAAW;AAAAoD,EAAA,GAAXpD,WAAW;AAsZjB,eAAeA,WAAW;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}