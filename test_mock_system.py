#!/usr/bin/env python3
"""
UI自动化测试平台模拟测试脚本
"""
import requests
import json
import time
import os
from pathlib import Path

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def test_basic_apis():
    """测试基础API接口"""
    print("🔧 测试基础API接口...")
    
    # 测试健康检查
    try:
        response = requests.get(f"{BASE_URL}/docs")
        if response.status_code == 200:
            print("✅ 后端服务运行正常")
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接后端服务: {e}")
        return False
    
    # 测试AI配置接口
    try:
        response = requests.get(f"{API_BASE}/config/ai")
        if response.status_code == 200:
            config = response.json()
            print(f"✅ AI配置接口正常: {config['status']}")
        else:
            print(f"❌ AI配置接口异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ AI配置接口错误: {e}")
        return False
    
    # 测试脚本管理接口
    try:
        response = requests.get(f"{API_BASE}/scripts")
        if response.status_code == 200:
            scripts = response.json()
            print(f"✅ 脚本管理接口正常，共 {scripts['count']} 个脚本")
        else:
            print(f"❌ 脚本管理接口异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 脚本管理接口错误: {e}")
        return False
    
    return True

def test_mock_agents():
    """测试智能体（模拟模式）"""
    print("🤖 测试智能体（模拟模式）...")
    
    try:
        # 导入智能体（强制使用模拟模式）
        import sys
        sys.path.append('backend')
        
        from backend.app.agents.element_detection.agent import ElementDetectionAgent
        from backend.app.agents.interaction_analysis.agent import InteractionAnalysisAgent
        from backend.app.agents.test_generation.agent import TestGenerationAgent
        
        # 创建测试图片
        test_image_path = "test_ui.png"
        if not os.path.exists(test_image_path):
            from PIL import Image
            img = Image.new('RGB', (800, 600), color='lightblue')
            img.save(test_image_path)
        
        # 1. 测试元素识别智能体（模拟模式）
        print("  🔍 测试元素识别智能体...")
        element_agent = ElementDetectionAgent(api_key="")  # 空API密钥强制使用模拟模式
        elements = element_agent.analyze(test_image_path, "测试登录界面")
        
        if elements and len(elements) > 0:
            print(f"    ✅ 识别到 {len(elements)} 个UI元素")
            for i, element in enumerate(elements[:3]):
                print(f"      {i+1}. {element.get('type', 'unknown')}: {element.get('description', 'N/A')}")
        else:
            print("    ❌ 元素识别失败")
            return False
        
        # 2. 测试交互分析智能体（模拟模式）
        print("  🔄 测试交互分析智能体...")
        interaction_agent = InteractionAnalysisAgent(api_key="")  # 空API密钥强制使用模拟模式
        flows = interaction_agent.analyze(elements, "测试登录界面")
        
        if flows and len(flows) > 0:
            print(f"    ✅ 分析出 {len(flows)} 个交互流程")
            for i, flow in enumerate(flows[:2]):
                print(f"      {i+1}. {flow.get('name', 'unknown')}: {flow.get('description', 'N/A')}")
        else:
            print("    ❌ 交互分析失败")
            return False
        
        # 3. 测试脚本生成智能体（模拟模式）
        print("  📜 测试脚本生成智能体...")
        script_agent = TestGenerationAgent(api_key="")  # 空API密钥强制使用模拟模式
        scripts = script_agent.analyze(elements, flows, "测试登录界面")
        
        if scripts and len(scripts) > 0:
            print(f"    ✅ 生成了 {len(scripts)} 个测试脚本")
            for i, script in enumerate(scripts[:2]):
                script_name = script.get('script_name', 'unknown')
                print(f"      {i+1}. {script_name}")
        else:
            print("    ❌ 脚本生成失败")
            return False
        
        # 清理测试文件
        if os.path.exists(test_image_path):
            os.remove(test_image_path)
        
        return True
        
    except Exception as e:
        print(f"❌ 智能体测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_script_management():
    """测试脚本管理功能"""
    print("📝 测试脚本管理功能...")
    
    try:
        # 导入脚本管理器
        import sys
        sys.path.append('backend')
        
        from backend.app.core.script_manager import script_manager
        
        # 创建测试脚本数据
        test_script = {
            'script_name': '20241218-1200-测试脚本.spec.ts',
            'original_name': '测试脚本',
            'description': '自动化测试脚本示例',
            'priority': 'high',
            'estimated_duration': '30秒',
            'test_steps': [
                {
                    'action_type': 'aiTap',
                    'action_description': '点击登录按钮',
                    'visual_target': '登录按钮',
                    'expected_result': '页面跳转到主页'
                }
            ],
            'yaml_content': 'name: 测试脚本\ndescription: 示例脚本\nsteps:\n  - action: aiTap\n    locate: 登录按钮'
        }
        
        # 保存脚本
        task_id = "test-task-001"
        script_path = script_manager.save_script(test_script, task_id)
        
        if script_path and os.path.exists(script_path):
            print(f"    ✅ 脚本保存成功: {script_path}")
        else:
            print("    ❌ 脚本保存失败")
            return False
        
        # 获取脚本列表
        scripts = script_manager.get_scripts_list()
        if scripts and len(scripts) > 0:
            print(f"    ✅ 脚本列表获取成功，共 {len(scripts)} 个脚本")
        else:
            print("    ❌ 脚本列表为空")
            return False
        
        # 记录运行历史
        script_manager.record_script_run(task_id, "success", "测试运行成功", "")
        run_history = script_manager.get_script_run_history(task_id)
        
        if run_history and len(run_history) > 0:
            print(f"    ✅ 运行历史记录成功，共 {len(run_history)} 条记录")
        else:
            print("    ❌ 运行历史记录失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 脚本管理测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_playwright_setup():
    """测试Playwright环境"""
    print("🎭 测试Playwright环境...")
    
    try:
        # 检查package.json
        if os.path.exists("package.json"):
            print("    ✅ package.json 存在")
        else:
            print("    ❌ package.json 不存在")
            return False
        
        # 检查playwright.config.ts
        if os.path.exists("playwright.config.ts"):
            print("    ✅ playwright.config.ts 存在")
        else:
            print("    ❌ playwright.config.ts 不存在")
            return False
        
        # 检查tests目录
        if os.path.exists("tests"):
            print("    ✅ tests 目录存在")
        else:
            print("    ❌ tests 目录不存在")
            return False
        
        # 检查报告目录
        if os.path.exists("midscene_run/report"):
            print("    ✅ 报告目录存在")
        else:
            print("    ❌ 报告目录不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Playwright环境检查异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始UI自动化测试平台模拟测试")
    print("=" * 60)
    
    # 测试结果统计
    results = []
    
    # 1. 测试基础API
    results.append(("基础API接口", test_basic_apis()))
    
    # 2. 测试智能体（模拟模式）
    results.append(("智能体模拟测试", test_mock_agents()))
    
    # 3. 测试脚本管理
    results.append(("脚本管理功能", test_script_management()))
    
    # 4. 测试Playwright环境
    results.append(("Playwright环境", test_playwright_setup()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统基础功能正常。")
        print("\n📋 下一步操作建议:")
        print("  1. 启动前端服务: cd frontend && npm start")
        print("  2. 访问 http://localhost:3000 测试完整功能")
        print("  3. 在设置页面配置真实的AI模型参数")
        print("  4. 上传UI截图进行真实分析测试")
    else:
        print("⚠️ 部分测试失败，请检查系统配置。")

if __name__ == "__main__":
    main()
