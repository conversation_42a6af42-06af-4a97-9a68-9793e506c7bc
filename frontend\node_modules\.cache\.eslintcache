[{"E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\index.js": "1", "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\App.js": "2", "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\reportWebVitals.js": "3", "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\components\\SimpleUpload.js": "4", "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\components\\SimpleResults.js": "5", "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\components\\RealTimeAnalysis.js": "6", "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\components\\Sidebar.js": "7", "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\pages\\AnalysisPage.js": "8", "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\pages\\HistoryPage.js": "9", "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\pages\\SettingsPage.js": "10", "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\pages\\AboutPage.js": "11"}, {"size": 535, "mtime": 1750178451214, "results": "12", "hashOfConfig": "13"}, {"size": 1300, "mtime": 1750183162081, "results": "14", "hashOfConfig": "13"}, {"size": 362, "mtime": 1750178451321, "results": "15", "hashOfConfig": "13"}, {"size": 24791, "mtime": 1750227716725, "results": "16", "hashOfConfig": "13"}, {"size": 14265, "mtime": 1750213155919, "results": "17", "hashOfConfig": "13"}, {"size": 14646, "mtime": 1750216534821, "results": "18", "hashOfConfig": "13"}, {"size": 2471, "mtime": 1750183583333, "results": "19", "hashOfConfig": "13"}, {"size": 5836, "mtime": 1750212605674, "results": "20", "hashOfConfig": "13"}, {"size": 16235, "mtime": 1750216235695, "results": "21", "hashOfConfig": "13"}, {"size": 14003, "mtime": 1750216457955, "results": "22", "hashOfConfig": "13"}, {"size": 10997, "mtime": 1750183689649, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1j4iww2", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\index.js", [], [], "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\App.js", [], [], "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\reportWebVitals.js", [], [], "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\components\\SimpleUpload.js", [], [], "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\components\\SimpleResults.js", [], [], "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\components\\RealTimeAnalysis.js", ["57"], [], "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\components\\Sidebar.js", [], [], "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\pages\\AnalysisPage.js", [], [], "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\pages\\HistoryPage.js", ["58"], [], "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\pages\\SettingsPage.js", ["59", "60"], [], "E:\\WorkFile\\HuiCe\\testing-2025-06-14\\003demo\\ui-automation\\frontend\\src\\pages\\AboutPage.js", [], [], {"ruleId": "61", "severity": 1, "message": "62", "line": 138, "column": 6, "nodeType": "63", "endLine": 138, "endColumn": 51, "suggestions": "64"}, {"ruleId": "65", "severity": 1, "message": "66", "line": 29, "column": 10, "nodeType": "67", "messageId": "68", "endLine": 29, "endColumn": 22}, {"ruleId": "65", "severity": 1, "message": "69", "line": 34, "column": 10, "nodeType": "67", "messageId": "68", "endLine": 34, "endColumn": 17}, {"ruleId": "65", "severity": 1, "message": "70", "line": 67, "column": 9, "nodeType": "67", "messageId": "68", "endLine": 67, "endColumn": 33}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'startTime'. Either include it or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setAgents' needs the current value of 'startTime'.", "ArrayExpression", ["71"], "no-unused-vars", "'selectedItem' is assigned a value but never used.", "Identifier", "unusedVar", "'loading' is assigned a value but never used.", "'handleArraySettingChange' is assigned a value but never used.", {"desc": "72", "fix": "73"}, "Update the dependencies array to be: [taskId, onAnalysisComplete, onAnalysisError, startTime]", {"range": "74", "text": "75"}, [3598, 3643], "[taskId, onAnalysisComplete, onAnalysisError, startTime]"]