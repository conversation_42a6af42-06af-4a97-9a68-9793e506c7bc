"""
MidScene.js脚本生成器
严格按照MidScene.js官方YAML格式生成测试脚本
"""
from typing import List, Dict, Any
import yaml
from datetime import datetime

class MidSceneScriptGenerator:
    """MidScene.js脚本生成器"""
    
    def __init__(self):
        self.supported_actions = [
            'ai', 'aiAction', 'aiTap', 'aiInput', 'aiHover', 
            'aiScroll', 'aiKeyboardPress', 'aiQuery', 
            'aiBoolean', 'aiNumber', 'aiString', 'aiAssert', 
            'aiWaitFor', 'sleep'
        ]
    
    def generate_script(self, 
                       script_name: str,
                       description: str,
                       elements: List[Dict[str, Any]],
                       flows: List[Dict[str, Any]],
                       priority: str = "medium") -> str:
        """
        生成MidScene.js YAML格式脚本
        
        Args:
            script_name: 脚本名称
            description: 脚本描述
            elements: UI元素列表
            flows: 交互流程列表
            priority: 优先级
            
        Returns:
            YAML格式的MidScene.js脚本
        """
        # 构建脚本结构
        script_data = {
            'name': script_name,
            'description': description,
            'meta': {
                'priority': priority,
                'generated_at': datetime.now().isoformat(),
                'generator': 'UI自动化分析平台',
                'version': '1.0.0'
            },
            'steps': []
        }
        
        # 基于交互流程生成步骤
        for flow in flows:
            flow_steps = self._convert_flow_to_steps(flow, elements)
            script_data['steps'].extend(flow_steps)
        
        # 如果没有流程，基于元素生成基础步骤
        if not flows and elements:
            basic_steps = self._generate_basic_steps(elements)
            script_data['steps'].extend(basic_steps)
        
        # 转换为YAML格式
        yaml_content = self._to_yaml(script_data)
        return yaml_content
    
    def _convert_flow_to_steps(self, flow: Dict[str, Any], elements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """将交互流程转换为MidScene.js步骤"""
        steps = []
        
        # 添加流程注释
        steps.append({
            'name': f"# {flow.get('flow_name', '未命名流程')}",
            'description': flow.get('description', '')
        })
        
        for step in flow.get('steps', []):
            midscene_step = self._convert_step_to_midscene(step, elements)
            if midscene_step:
                steps.append(midscene_step)
        
        return steps
    
    def _convert_step_to_midscene(self, step: Dict[str, Any], elements: List[Dict[str, Any]]) -> Dict[str, Any]:
        """将单个步骤转换为MidScene.js格式"""
        action = step.get('action', '').lower()
        target_element = step.get('target_element', '')
        expected_result = step.get('expected_result', '')
        
        # 查找对应的UI元素
        element = self._find_element_by_name(target_element, elements)
        
        # 生成详细的视觉定位描述
        locate_description = self._generate_locate_description(element, target_element)
        
        # 确定MidScene.js动作类型
        midscene_action = self._determine_midscene_action(action, element)
        
        # 构建步骤
        midscene_step = {
            'name': step.get('action', '执行操作'),
            'action': midscene_action,
            'locate': locate_description,
            'expect': expected_result
        }
        
        # 添加特定动作的参数
        if midscene_action == 'aiInput':
            midscene_step['value'] = self._generate_input_value(element)
        elif midscene_action == 'aiScroll':
            midscene_step['direction'] = 'down'
            midscene_step['distance'] = 300
        elif midscene_action == 'sleep':
            midscene_step['duration'] = 1000
        
        return midscene_step
    
    def _generate_basic_steps(self, elements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """基于UI元素生成基础测试步骤"""
        steps = []
        
        # 为输入框生成输入步骤
        input_elements = [e for e in elements if e.get('element_type') == 'input']
        for element in input_elements:
            steps.append({
                'name': f"输入{element.get('name', '内容')}",
                'action': 'aiInput',
                'locate': self._generate_locate_description(element),
                'value': self._generate_input_value(element),
                'expect': f"{element.get('name', '输入框')}显示输入内容"
            })
        
        # 为按钮生成点击步骤
        button_elements = [e for e in elements if e.get('element_type') == 'button']
        for element in button_elements:
            steps.append({
                'name': f"点击{element.get('name', '按钮')}",
                'action': 'aiTap',
                'locate': self._generate_locate_description(element),
                'expect': f"{element.get('name', '按钮')}点击响应正常"
            })
        
        # 添加最终验证步骤
        if steps:
            steps.append({
                'name': '验证操作结果',
                'action': 'aiAssert',
                'locate': '页面显示操作成功的反馈信息',
                'expect': '所有操作执行成功，页面状态正确'
            })
        
        return steps
    
    def _find_element_by_name(self, target_name: str, elements: List[Dict[str, Any]]) -> Dict[str, Any]:
        """根据名称查找UI元素"""
        for element in elements:
            if (target_name in element.get('name', '') or 
                target_name in element.get('description', '')):
                return element
        return {}
    
    def _generate_locate_description(self, element: Dict[str, Any], fallback: str = '') -> str:
        """生成详细的视觉定位描述"""
        if not element:
            return fallback or '目标元素'
        
        name = element.get('name', '元素')
        description = element.get('description', '')
        visual_features = element.get('visual_features', {})
        position = element.get('position', {})
        text_content = element.get('text_content', '')
        
        # 构建详细描述
        locate_parts = []
        
        # 基础描述
        if description:
            locate_parts.append(description)
        else:
            locate_parts.append(name)
        
        # 添加文字内容
        if text_content:
            locate_parts.append(f"显示'{text_content}'文字")
        
        # 添加视觉特征
        if visual_features.get('color'):
            locate_parts.append(visual_features['color'])
        
        if visual_features.get('shape'):
            locate_parts.append(visual_features['shape'])
        
        # 添加位置信息
        if position.get('area'):
            locate_parts.append(f"位于{position['area']}")
        
        if position.get('relative_to'):
            locate_parts.append(f"相对于{position['relative_to']}")
        
        return '，'.join(locate_parts)
    
    def _determine_midscene_action(self, action: str, element: Dict[str, Any]) -> str:
        """确定MidScene.js动作类型"""
        action_lower = action.lower()
        element_type = element.get('element_type', '') if element else ''
        
        # 动作映射
        if '点击' in action or 'click' in action_lower or 'tap' in action_lower:
            return 'aiTap'
        elif '输入' in action or 'input' in action_lower or element_type == 'input':
            return 'aiInput'
        elif '悬停' in action or 'hover' in action_lower:
            return 'aiHover'
        elif '滚动' in action or 'scroll' in action_lower:
            return 'aiScroll'
        elif '等待' in action or 'wait' in action_lower:
            return 'aiWaitFor'
        elif '验证' in action or '检查' in action or 'assert' in action_lower:
            return 'aiAssert'
        elif '查询' in action or 'query' in action_lower:
            return 'aiQuery'
        elif '按键' in action or 'key' in action_lower:
            return 'aiKeyboardPress'
        else:
            return 'ai'  # 复合操作
    
    def _generate_input_value(self, element: Dict[str, Any]) -> str:
        """生成输入值"""
        if not element:
            return 'test_value'
        
        name = element.get('name', '').lower()
        
        # 根据元素名称生成合适的测试数据
        if '邮箱' in name or 'email' in name:
            return '<EMAIL>'
        elif '密码' in name or 'password' in name:
            return 'Test123456'
        elif '手机' in name or 'phone' in name:
            return '13800138000'
        elif '姓名' in name or 'name' in name:
            return '测试用户'
        elif '年龄' in name or 'age' in name:
            return '25'
        else:
            return 'test_input'
    
    def _to_yaml(self, data: Dict[str, Any]) -> str:
        """转换为YAML格式"""
        # 自定义YAML输出格式
        yaml_lines = []
        
        # 添加头部信息
        yaml_lines.append(f"# {data['name']}")
        yaml_lines.append(f"# {data['description']}")
        yaml_lines.append(f"# 生成时间: {data['meta']['generated_at']}")
        yaml_lines.append(f"# 生成器: {data['meta']['generator']}")
        yaml_lines.append("")
        
        # 添加基本信息
        yaml_lines.append(f"name: {data['name']}")
        yaml_lines.append(f"description: {data['description']}")
        yaml_lines.append("")
        
        # 添加步骤
        yaml_lines.append("steps:")
        for i, step in enumerate(data['steps']):
            if step.get('name', '').startswith('#'):
                # 注释步骤
                yaml_lines.append(f"  {step['name']}")
                if step.get('description'):
                    yaml_lines.append(f"  # {step['description']}")
                yaml_lines.append("")
            else:
                # 正常步骤
                yaml_lines.append(f"  - name: {step['name']}")
                yaml_lines.append(f"    action: {step['action']}")
                yaml_lines.append(f"    locate: {step['locate']}")
                
                if 'value' in step:
                    yaml_lines.append(f"    value: {step['value']}")
                if 'direction' in step:
                    yaml_lines.append(f"    direction: {step['direction']}")
                if 'distance' in step:
                    yaml_lines.append(f"    distance: {step['distance']}")
                if 'duration' in step:
                    yaml_lines.append(f"    duration: {step['duration']}")
                
                yaml_lines.append(f"    expect: {step['expect']}")
                yaml_lines.append("")
        
        return '\n'.join(yaml_lines)

# 全局实例
midscene_generator = MidSceneScriptGenerator()
