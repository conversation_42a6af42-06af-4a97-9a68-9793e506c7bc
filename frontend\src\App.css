/* UI自动化分析平台样式 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f7fa;
  color: #333;
  line-height: 1.6;
}

.app {
  min-height: 100vh;
  display: flex;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header */
.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px 0;
  text-align: center;
  position: relative;
}

.app-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 20px;
}

.reset-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  position: absolute;
  top: 20px;
  left: 20px;
}

.reset-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* Main */
.app-main {
  flex: 1;
  margin-left: 280px;
  transition: margin-left 0.3s ease;
  min-height: 100vh;
}

.app-main.sidebar-collapsed {
  margin-left: 70px;
}

/* 页面容器 */
.page-container {
  width: 100%;
  min-height: 100vh;
}

/* Error Banner */
.error-banner {
  background: #f8d7da;
  color: #721c24;
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  border: 1px solid #f5c6cb;
}

.error-icon {
  font-size: 20px;
}

.error-text {
  flex: 1;
}

.error-close {
  background: none;
  border: none;
  color: #721c24;
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.error-close:hover {
  background: rgba(114, 28, 36, 0.1);
}

/* Temporary Content */
.temp-content {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.temp-content h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.8rem;
}

.upload-placeholder,
.analysis-placeholder,
.results-placeholder {
  padding: 40px 20px;
  border: 2px dashed #e9ecef;
  border-radius: 12px;
  margin: 20px 0;
}

.upload-placeholder h3,
.analysis-placeholder h3,
.results-placeholder h3 {
  font-size: 1.5rem;
  margin-bottom: 16px;
  color: #495057;
}

.upload-placeholder p,
.analysis-placeholder p,
.results-placeholder p {
  color: #6c757d;
  margin-bottom: 20px;
}

.demo-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.demo-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
}

/* Demo Progress */
.demo-progress {
  margin: 24px 0;
  text-align: left;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.progress-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #e9ecef;
}

.progress-item span:first-child {
  font-weight: 500;
}

.status {
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.status.processing {
  background: #cce5ff;
  color: #0066cc;
}

.status.pending {
  background: #f0f0f0;
  color: #666;
}

.status.completed {
  background: #d4edda;
  color: #155724;
}

/* Demo Results */
.demo-results {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 24px 0;
}

.result-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.result-section h4 {
  color: #333;
  margin-bottom: 8px;
  font-size: 1.1rem;
}

.result-section p {
  color: #666;
  font-size: 14px;
}

/* Footer */
.app-footer {
  background: #343a40;
  color: #adb5bd;
  padding: 20px 0;
  text-align: center;
  margin-top: auto;
}

.app-footer p {
  margin: 0;
  font-size: 14px;
}

/* Responsive */
@media (max-width: 768px) {
  .app-main {
    margin-left: 0;
  }

  .app-main.sidebar-collapsed {
    margin-left: 0;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .app-header p {
    font-size: 1rem;
  }

  .reset-button {
    position: static;
    margin-top: 16px;
  }

  .temp-content {
    padding: 20px;
  }

  .demo-results {
    grid-template-columns: 1fr;
  }
}
