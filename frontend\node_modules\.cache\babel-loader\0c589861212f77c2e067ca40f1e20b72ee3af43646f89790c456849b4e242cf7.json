{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\n/**\n * UI自动化分析平台 - 主应用组件\n */\nimport React, { useState } from 'react';\nimport Sidebar from './components/Sidebar';\nimport AnalysisPage from './pages/AnalysisPage';\nimport HistoryPage from './pages/HistoryPage';\nimport SettingsPage from './pages/SettingsPage';\nimport AboutPage from './pages/AboutPage';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [currentPage, setCurrentPage] = useState('analysis');\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const renderCurrentPage = () => {\n    switch (currentPage) {\n      case 'analysis':\n        return /*#__PURE__*/_jsxDEV(AnalysisPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 16\n        }, this);\n      case 'history':\n        return /*#__PURE__*/_jsxDEV(HistoryPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 16\n        }, this);\n      case 'settings':\n        return /*#__PURE__*/_jsxDEV(SettingsPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 16\n        }, this);\n      case 'about':\n        return /*#__PURE__*/_jsxDEV(AboutPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(AnalysisPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"app-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83E\\uDD16 UI\\u81EA\\u52A8\\u5316\\u5206\\u6790\\u5E73\\u53F0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u57FA\\u4E8EAI\\u667A\\u80FD\\u4F53\\u7684UI\\u754C\\u9762\\u5206\\u6790\\u548C\\u81EA\\u52A8\\u5316\\u6D4B\\u8BD5\\u811A\\u672C\\u751F\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), appState !== 'upload' && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"reset-button\",\n          onClick: handleReset,\n          children: \"\\u2190 \\u91CD\\u65B0\\u5F00\\u59CB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"app-main\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-banner\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"error-icon\",\n            children: \"\\u26A0\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"error-text\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"error-close\",\n            onClick: () => setError(''),\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 13\n        }, this), appState === 'upload' && /*#__PURE__*/_jsxDEV(SimpleUpload, {\n          onUploadSuccess: handleUploadSuccess,\n          onUploadError: handleUploadError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 13\n        }, this), appState === 'analyzing' && currentTaskId && /*#__PURE__*/_jsxDEV(RealTimeAnalysis, {\n          taskId: currentTaskId,\n          onAnalysisComplete: handleAnalysisComplete,\n          onAnalysisError: handleAnalysisError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this), appState === 'results' && analysisResult && /*#__PURE__*/_jsxDEV(SimpleResults, {\n          result: analysisResult\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"app-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\xA9 2024 UI\\u81EA\\u52A8\\u5316\\u5206\\u6790\\u5E73\\u53F0 - \\u57FA\\u4E8EFastAPI + React + AI\\u667A\\u80FD\\u4F53\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"zRr3cXc7LdArnNe3kpAVd6tJ9Nk=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "Sidebar", "AnalysisPage", "HistoryPage", "SettingsPage", "AboutPage", "jsxDEV", "_jsxDEV", "App", "_s", "currentPage", "setCurrentPage", "sidebarCollapsed", "setSidebarCollapsed", "renderCurrentPage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "appState", "onClick", "handleReset", "error", "setError", "SimpleUpload", "onUploadSuccess", "handleUploadSuccess", "onUploadError", "handleUploadError", "currentTaskId", "RealTimeAnalysis", "taskId", "onAnalysisComplete", "handleAnalysisComplete", "onAnalysisError", "handleAnalysisError", "analysisResult", "SimpleResults", "result", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/App.js"], "sourcesContent": ["/**\n * UI自动化分析平台 - 主应用组件\n */\nimport React, { useState } from 'react';\nimport Sidebar from './components/Sidebar';\nimport AnalysisPage from './pages/AnalysisPage';\nimport HistoryPage from './pages/HistoryPage';\nimport SettingsPage from './pages/SettingsPage';\nimport AboutPage from './pages/AboutPage';\nimport './App.css';\n\nfunction App() {\n  const [currentPage, setCurrentPage] = useState('analysis');\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n\n  const renderCurrentPage = () => {\n    switch (currentPage) {\n      case 'analysis':\n        return <AnalysisPage />;\n      case 'history':\n        return <HistoryPage />;\n      case 'settings':\n        return <SettingsPage />;\n      case 'about':\n        return <AboutPage />;\n      default:\n        return <AnalysisPage />;\n    }\n  };\n\n  return (\n    <div className=\"app\">\n      <header className=\"app-header\">\n        <div className=\"container\">\n          <h1>🤖 UI自动化分析平台</h1>\n          <p>基于AI智能体的UI界面分析和自动化测试脚本生成</p>\n          {appState !== 'upload' && (\n            <button className=\"reset-button\" onClick={handleReset}>\n              ← 重新开始\n            </button>\n          )}\n        </div>\n      </header>\n\n      <main className=\"app-main\">\n        <div className=\"container\">\n          {error && (\n            <div className=\"error-banner\">\n              <span className=\"error-icon\">⚠️</span>\n              <span className=\"error-text\">{error}</span>\n              <button className=\"error-close\" onClick={() => setError('')}>\n                ✕\n              </button>\n            </div>\n          )}\n\n          {/* 实际组件 */}\n          {appState === 'upload' && (\n            <SimpleUpload\n              onUploadSuccess={handleUploadSuccess}\n              onUploadError={handleUploadError}\n            />\n          )}\n\n          {appState === 'analyzing' && currentTaskId && (\n            <RealTimeAnalysis\n              taskId={currentTaskId}\n              onAnalysisComplete={handleAnalysisComplete}\n              onAnalysisError={handleAnalysisError}\n            />\n          )}\n\n          {appState === 'results' && analysisResult && (\n            <SimpleResults result={analysisResult} />\n          )}\n        </div>\n      </main>\n\n      <footer className=\"app-footer\">\n        <div className=\"container\">\n          <p>© 2024 UI自动化分析平台 - 基于FastAPI + React + AI智能体</p>\n        </div>\n      </footer>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAC,UAAU,CAAC;EAC1D,MAAM,CAACY,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAMc,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQJ,WAAW;MACjB,KAAK,UAAU;QACb,oBAAOH,OAAA,CAACL,YAAY;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB,KAAK,SAAS;QACZ,oBAAOX,OAAA,CAACJ,WAAW;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxB,KAAK,UAAU;QACb,oBAAOX,OAAA,CAACH,YAAY;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB,KAAK,OAAO;QACV,oBAAOX,OAAA,CAACF,SAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtB;QACE,oBAAOX,OAAA,CAACL,YAAY;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC3B;EACF,CAAC;EAED,oBACEX,OAAA;IAAKY,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBb,OAAA;MAAQY,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC5Bb,OAAA;QAAKY,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBb,OAAA;UAAAa,QAAA,EAAI;QAAY;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBX,OAAA;UAAAa,QAAA,EAAG;QAAwB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAC9BG,QAAQ,KAAK,QAAQ,iBACpBd,OAAA;UAAQY,SAAS,EAAC,cAAc;UAACG,OAAO,EAAEC,WAAY;UAAAH,QAAA,EAAC;QAEvD;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETX,OAAA;MAAMY,SAAS,EAAC,UAAU;MAAAC,QAAA,eACxBb,OAAA;QAAKY,SAAS,EAAC,WAAW;QAAAC,QAAA,GACvBI,KAAK,iBACJjB,OAAA;UAAKY,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3Bb,OAAA;YAAMY,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtCX,OAAA;YAAMY,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEI;UAAK;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3CX,OAAA;YAAQY,SAAS,EAAC,aAAa;YAACG,OAAO,EAAEA,CAAA,KAAMG,QAAQ,CAAC,EAAE,CAAE;YAAAL,QAAA,EAAC;UAE7D;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,EAGAG,QAAQ,KAAK,QAAQ,iBACpBd,OAAA,CAACmB,YAAY;UACXC,eAAe,EAAEC,mBAAoB;UACrCC,aAAa,EAAEC;QAAkB;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CACF,EAEAG,QAAQ,KAAK,WAAW,IAAIU,aAAa,iBACxCxB,OAAA,CAACyB,gBAAgB;UACfC,MAAM,EAAEF,aAAc;UACtBG,kBAAkB,EAAEC,sBAAuB;UAC3CC,eAAe,EAAEC;QAAoB;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CACF,EAEAG,QAAQ,KAAK,SAAS,IAAIiB,cAAc,iBACvC/B,OAAA,CAACgC,aAAa;UAACC,MAAM,EAAEF;QAAe;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACzC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEPX,OAAA;MAAQY,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC5Bb,OAAA;QAAKY,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBb,OAAA;UAAAa,QAAA,EAAG;QAA4C;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACT,EAAA,CA1EQD,GAAG;AAAAiC,EAAA,GAAHjC,GAAG;AA4EZ,eAAeA,GAAG;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}