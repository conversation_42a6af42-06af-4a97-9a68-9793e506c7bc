# 开发环境搭建指南

## 环境要求

### 基础环境
- Python 3.11+
- Node.js 18+
- Redis 6+
- Git

### 开发工具（推荐）
- VS Code
- Docker Desktop
- Postman（API测试）

## 快速启动

### 方式一：Docker Compose（推荐）

1. **启动所有服务**
   ```bash
   # Windows
   start.bat
   
   # Linux/macOS
   ./start.sh
   ```

2. **访问应用**
   - 前端：http://localhost:3000
   - 后端API：http://localhost:8000
   - API文档：http://localhost:8000/docs

### 方式二：本地开发

#### 1. 启动 Redis
```bash
# 使用 Docker
docker run -d -p 6379:6379 redis:7-alpine

# 或本地安装的 Redis
redis-server
```

#### 2. 启动后端
```bash
cd backend

# 安装依赖
pip install -r requirements.txt

# 启动 FastAPI
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 新终端启动 Celery Worker
celery -A app.core.celery_app worker --loglevel=info
```

#### 3. 启动前端
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm start
```

## 项目结构

```
ui-automation/
├── backend/                 # FastAPI 后端
│   ├── app/
│   │   ├── main.py         # 应用入口
│   │   ├── api/            # API 路由
│   │   ├── agents/         # 智能体模块
│   │   ├── core/           # 核心功能
│   │   └── models/         # 数据模型
│   ├── requirements.txt    # Python 依赖
│   └── Dockerfile
├── frontend/               # React 前端
│   ├── src/
│   │   ├── components/     # React 组件
│   │   ├── services/       # API 服务
│   │   └── App.js         # 主应用
│   ├── package.json       # Node.js 依赖
│   └── Dockerfile
├── agents/                 # 智能体配置
├── docker-compose.yml     # 容器编排
├── start.bat              # Windows 启动脚本
├── start.sh               # Linux/macOS 启动脚本
└── README.md
```

## 开发流程

### 1. 后端开发

#### 添加新的API端点
1. 在 `backend/app/api/` 下创建新的路由文件
2. 在 `backend/app/main.py` 中注册路由
3. 更新数据模型（如需要）

#### 添加新的智能体
1. 在 `backend/app/agents/` 下创建新目录
2. 实现智能体类和分析方法
3. 在分析流水线中集成

### 2. 前端开发

#### 添加新组件
1. 在 `frontend/src/components/` 下创建组件
2. 更新主应用路由
3. 添加相应的样式

#### API 集成
1. 在 `frontend/src/services/api.js` 中添加API方法
2. 在组件中使用API服务
3. 处理错误和加载状态

### 3. 测试

#### 后端测试
```bash
cd backend
pytest
```

#### 前端测试
```bash
cd frontend
npm test
```

#### 集成测试
1. 启动所有服务
2. 使用 Postman 测试 API
3. 在浏览器中测试完整流程

## 常见问题

### 1. Redis 连接失败
- 检查 Redis 是否启动
- 确认端口 6379 未被占用
- 检查防火墙设置

### 2. Celery Worker 无法启动
- 确认 Redis 连接正常
- 检查 Python 路径配置
- 查看错误日志

### 3. 前端无法连接后端
- 确认后端服务运行在 8000 端口
- 检查 CORS 配置
- 确认 API 基础 URL 正确

### 4. Docker 相关问题
- 确认 Docker Desktop 已启动
- 检查端口占用情况
- 清理 Docker 缓存：`docker system prune`

## 调试技巧

### 1. 查看日志
```bash
# Docker 环境
docker-compose logs -f [service-name]

# 本地环境
# 后端日志在终端输出
# 前端日志在浏览器控制台
```

### 2. 进入容器调试
```bash
docker exec -it ui-automation-backend bash
docker exec -it ui-automation-frontend sh
```

### 3. 数据库调试
```bash
# 连接 Redis
docker exec -it ui-automation-redis redis-cli
```

## 部署说明

### 生产环境部署
1. 修改环境变量配置
2. 使用生产级数据库
3. 配置反向代理（Nginx）
4. 设置 HTTPS
5. 配置监控和日志

### 环境变量
- `REDIS_URL`: Redis 连接地址
- `PYTHONPATH`: Python 模块路径
- `REACT_APP_API_URL`: 前端 API 地址

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request
5. 代码审查
6. 合并到主分支
