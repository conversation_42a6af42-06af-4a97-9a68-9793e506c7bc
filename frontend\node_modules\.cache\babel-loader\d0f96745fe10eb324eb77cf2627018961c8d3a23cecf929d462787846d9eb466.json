{"ast": null, "code": "/**\n * UI分析页面 - 主要的分析功能页面\n */import React,{useState}from'react';import SimpleUpload from'../components/SimpleUpload';import SimpleResults from'../components/SimpleResults';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AnalysisPage=()=>{const[analysisResult,setAnalysisResult]=useState(null);const[error,setError]=useState('');const handleUploadSuccess=result=>{setAnalysisResult(result);setError('');};const handleUploadError=errorMessage=>{setError(errorMessage);};return/*#__PURE__*/_jsxs(\"div\",{style:{height:'100vh',overflow:'hidden'},children:[/*#__PURE__*/_jsx(SimpleUpload,{onUploadSuccess:handleUploadSuccess,onUploadError:handleUploadError}),analysisResult&&/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:'0',left:'0',right:'0',bottom:'0',background:'rgba(0,0,0,0.5)',display:'flex',alignItems:'center',justifyContent:'center',zIndex:1000},children:/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',padding:'24px',borderRadius:'12px',boxShadow:'0 4px 20px rgba(0,0,0,0.15)',maxWidth:'800px',maxHeight:'80vh',overflow:'auto',position:'relative'},children:[/*#__PURE__*/_jsx(SimpleResults,{result:analysisResult}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setAnalysisResult(null),style:{position:'absolute',top:'16px',right:'16px',background:'none',border:'none',fontSize:'20px',cursor:'pointer',color:'#666'},children:\"\\u2715\"})]})}),error&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'fixed',top:'20px',right:'20px',background:'#fee',border:'1px solid #fcc',borderRadius:'8px',padding:'16px',color:'#c33',zIndex:1001,maxWidth:'400px'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u9519\\u8BEF\\uFF1A\"}),\" \",error,/*#__PURE__*/_jsx(\"button\",{onClick:()=>setError(''),style:{marginLeft:'12px',background:'none',border:'none',color:'#c33',cursor:'pointer',fontSize:'16px'},children:\"\\u2715\"})]})]});};export default AnalysisPage;", "map": {"version": 3, "names": ["React", "useState", "SimpleUpload", "SimpleResults", "jsx", "_jsx", "jsxs", "_jsxs", "AnalysisPage", "analysisResult", "setAnalysisResult", "error", "setError", "handleUploadSuccess", "result", "handleUploadError", "errorMessage", "style", "height", "overflow", "children", "onUploadSuccess", "onUploadError", "position", "top", "left", "right", "bottom", "background", "display", "alignItems", "justifyContent", "zIndex", "padding", "borderRadius", "boxShadow", "max<PERSON><PERSON><PERSON>", "maxHeight", "onClick", "border", "fontSize", "cursor", "color", "marginLeft"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/pages/AnalysisPage.js"], "sourcesContent": ["/**\n * UI分析页面 - 主要的分析功能页面\n */\nimport React, { useState } from 'react';\nimport SimpleUpload from '../components/SimpleUpload';\nimport SimpleResults from '../components/SimpleResults';\n\nconst AnalysisPage = () => {\n  const [analysisResult, setAnalysisResult] = useState(null);\n  const [error, setError] = useState('');\n\n  const handleUploadSuccess = (result) => {\n    setAnalysisResult(result);\n    setError('');\n  };\n\n  const handleUploadError = (errorMessage) => {\n    setError(errorMessage);\n  };\n\n  return (\n    <div style={{ height: '100vh', overflow: 'hidden' }}>\n      <SimpleUpload\n        onUploadSuccess={handleUploadSuccess}\n        onUploadError={handleUploadError}\n      />\n\n      {/* 分析结果弹窗 */}\n      {analysisResult && (\n        <div style={{\n          position: 'fixed',\n          top: '0',\n          left: '0',\n          right: '0',\n          bottom: '0',\n          background: 'rgba(0,0,0,0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            padding: '24px',\n            borderRadius: '12px',\n            boxShadow: '0 4px 20px rgba(0,0,0,0.15)',\n            maxWidth: '800px',\n            maxHeight: '80vh',\n            overflow: 'auto',\n            position: 'relative'\n          }}>\n            <SimpleResults result={analysisResult} />\n            <button\n              onClick={() => setAnalysisResult(null)}\n              style={{\n                position: 'absolute',\n                top: '16px',\n                right: '16px',\n                background: 'none',\n                border: 'none',\n                fontSize: '20px',\n                cursor: 'pointer',\n                color: '#666'\n              }}\n            >\n              ✕\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* 错误提示 */}\n      {error && (\n        <div style={{\n          position: 'fixed',\n          top: '20px',\n          right: '20px',\n          background: '#fee',\n          border: '1px solid #fcc',\n          borderRadius: '8px',\n          padding: '16px',\n          color: '#c33',\n          zIndex: 1001,\n          maxWidth: '400px'\n        }}>\n          <strong>错误：</strong> {error}\n          <button\n            onClick={() => setError('')}\n            style={{\n              marginLeft: '12px',\n              background: 'none',\n              border: 'none',\n              color: '#c33',\n              cursor: 'pointer',\n              fontSize: '16px'\n            }}\n          >\n            ✕\n          </button>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AnalysisPage;\n"], "mappings": "AAAA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,YAAY,KAAM,4BAA4B,CACrD,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExD,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAGT,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACU,KAAK,CAAEC,QAAQ,CAAC,CAAGX,QAAQ,CAAC,EAAE,CAAC,CAEtC,KAAM,CAAAY,mBAAmB,CAAIC,MAAM,EAAK,CACtCJ,iBAAiB,CAACI,MAAM,CAAC,CACzBF,QAAQ,CAAC,EAAE,CAAC,CACd,CAAC,CAED,KAAM,CAAAG,iBAAiB,CAAIC,YAAY,EAAK,CAC1CJ,QAAQ,CAACI,YAAY,CAAC,CACxB,CAAC,CAED,mBACET,KAAA,QAAKU,KAAK,CAAE,CAAEC,MAAM,CAAE,OAAO,CAAEC,QAAQ,CAAE,QAAS,CAAE,CAAAC,QAAA,eAClDf,IAAA,CAACH,YAAY,EACXmB,eAAe,CAAER,mBAAoB,CACrCS,aAAa,CAAEP,iBAAkB,CAClC,CAAC,CAGDN,cAAc,eACbJ,IAAA,QAAKY,KAAK,CAAE,CACVM,QAAQ,CAAE,OAAO,CACjBC,GAAG,CAAE,GAAG,CACRC,IAAI,CAAE,GAAG,CACTC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXC,UAAU,CAAE,iBAAiB,CAC7BC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBC,MAAM,CAAE,IACV,CAAE,CAAAZ,QAAA,cACAb,KAAA,QAAKU,KAAK,CAAE,CACVW,UAAU,CAAE,OAAO,CACnBK,OAAO,CAAE,MAAM,CACfC,YAAY,CAAE,MAAM,CACpBC,SAAS,CAAE,6BAA6B,CACxCC,QAAQ,CAAE,OAAO,CACjBC,SAAS,CAAE,MAAM,CACjBlB,QAAQ,CAAE,MAAM,CAChBI,QAAQ,CAAE,UACZ,CAAE,CAAAH,QAAA,eACAf,IAAA,CAACF,aAAa,EAACW,MAAM,CAAEL,cAAe,CAAE,CAAC,cACzCJ,IAAA,WACEiC,OAAO,CAAEA,CAAA,GAAM5B,iBAAiB,CAAC,IAAI,CAAE,CACvCO,KAAK,CAAE,CACLM,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,MAAM,CACXE,KAAK,CAAE,MAAM,CACbE,UAAU,CAAE,MAAM,CAClBW,MAAM,CAAE,MAAM,CACdC,QAAQ,CAAE,MAAM,CAChBC,MAAM,CAAE,SAAS,CACjBC,KAAK,CAAE,MACT,CAAE,CAAAtB,QAAA,CACH,QAED,CAAQ,CAAC,EACN,CAAC,CACH,CACN,CAGAT,KAAK,eACJJ,KAAA,QAAKU,KAAK,CAAE,CACVM,QAAQ,CAAE,OAAO,CACjBC,GAAG,CAAE,MAAM,CACXE,KAAK,CAAE,MAAM,CACbE,UAAU,CAAE,MAAM,CAClBW,MAAM,CAAE,gBAAgB,CACxBL,YAAY,CAAE,KAAK,CACnBD,OAAO,CAAE,MAAM,CACfS,KAAK,CAAE,MAAM,CACbV,MAAM,CAAE,IAAI,CACZI,QAAQ,CAAE,OACZ,CAAE,CAAAhB,QAAA,eACAf,IAAA,WAAAe,QAAA,CAAQ,oBAAG,CAAQ,CAAC,IAAC,CAACT,KAAK,cAC3BN,IAAA,WACEiC,OAAO,CAAEA,CAAA,GAAM1B,QAAQ,CAAC,EAAE,CAAE,CAC5BK,KAAK,CAAE,CACL0B,UAAU,CAAE,MAAM,CAClBf,UAAU,CAAE,MAAM,CAClBW,MAAM,CAAE,MAAM,CACdG,KAAK,CAAE,MAAM,CACbD,MAAM,CAAE,SAAS,CACjBD,QAAQ,CAAE,MACZ,CAAE,CAAApB,QAAA,CACH,QAED,CAAQ,CAAC,EACN,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAZ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}