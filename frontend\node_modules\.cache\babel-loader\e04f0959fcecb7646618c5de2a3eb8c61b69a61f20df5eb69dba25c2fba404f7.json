{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\pages\\\\HistoryPage.js\",\n  _s = $RefreshSig$();\n/**\n * 历史记录页面\n */\nimport React, { useState, useEffect } from 'react';\n\n// 生成标准脚本名称格式：年月日+时分+脚本名称+spec.ts\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst generateScriptName = (createdAt, description) => {\n  const date = new Date(createdAt);\n  const year = date.getFullYear();\n  const month = String(date.getMonth() + 1).padStart(2, '0');\n  const day = String(date.getDate()).padStart(2, '0');\n  const hour = String(date.getHours()).padStart(2, '0');\n  const minute = String(date.getMinutes()).padStart(2, '0');\n\n  // 从描述中提取脚本名称，去掉\"界面\"、\"分析\"等后缀\n  const scriptName = description.replace(/界面分析?$/, '').replace(/功能分析?$/, '').replace(/分析$/, '').replace(/界面$/, '').trim();\n  return `${year}${month}${day}-${hour}${minute}-${scriptName}.spec.ts`;\n};\nconst HistoryPage = () => {\n  _s();\n  const [historyData, setHistoryData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedItem, setSelectedItem] = useState(null);\n  useEffect(() => {\n    // 模拟加载历史数据\n    setTimeout(() => {\n      setHistoryData([{\n        id: '1',\n        taskId: 'task-001',\n        fileName: '登录界面.png',\n        description: '用户登录界面分析',\n        status: 'completed',\n        createdAt: '2024-12-17 10:30:00',\n        elementsCount: 5,\n        flowsCount: 2,\n        scriptsCount: 1\n      }, {\n        id: '2',\n        taskId: 'task-002',\n        fileName: '文件管理界面.png',\n        description: '文件管理界面功能分析',\n        status: 'completed',\n        createdAt: '2024-12-17 14:15:00',\n        elementsCount: 8,\n        flowsCount: 3,\n        scriptsCount: 2\n      }, {\n        id: '3',\n        taskId: 'task-003',\n        fileName: '购物车页面.png',\n        description: '电商购物车界面分析',\n        status: 'failed',\n        createdAt: '2024-12-17 16:45:00',\n        elementsCount: 0,\n        flowsCount: 0,\n        scriptsCount: 0\n      }]);\n      setLoading(false);\n    }, 1000);\n  }, []);\n  const getStatusColor = status => {\n    switch (status) {\n      case 'completed':\n        return '#28a745';\n      case 'failed':\n        return '#dc3545';\n      case 'processing':\n        return '#007bff';\n      default:\n        return '#6c757d';\n    }\n  };\n  const getStatusText = status => {\n    switch (status) {\n      case 'completed':\n        return '已完成';\n      case 'failed':\n        return '失败';\n      case 'processing':\n        return '处理中';\n      default:\n        return '未知';\n    }\n  };\n  const handleViewDetails = item => {\n    setSelectedItem(item);\n  };\n  const handleDownloadScript = item => {\n    // 模拟下载脚本\n    const scriptContent = `# ${item.description}\\n# 生成时间: ${item.createdAt}\\n\\nname: ${item.description}\\ndescription: 自动化测试脚本\\n\\nsteps:\\n  - name: 示例步骤\\n    action: aiTap\\n    locate: 示例元素\\n    expect: 预期结果`;\n    const blob = new Blob([scriptContent], {\n      type: 'text/yaml'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `${item.fileName.replace('.png', '')}_script.yaml`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"history-page\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83D\\uDCCB \\u5206\\u6790\\u5386\\u53F2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u67E5\\u770B\\u6240\\u6709UI\\u5206\\u6790\\u8BB0\\u5F55\\u548C\\u7ED3\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u52A0\\u8F7D\\u5386\\u53F2\\u8BB0\\u5F55\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"history-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDCCB \\u5206\\u6790\\u5386\\u53F2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u67E5\\u770B\\u6240\\u6709UI\\u5206\\u6790\\u8BB0\\u5F55\\u548C\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-content\",\n      children: historyData.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-icon\",\n          children: \"\\uD83D\\uDCDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u6682\\u65E0\\u5206\\u6790\\u8BB0\\u5F55\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u5F00\\u59CB\\u60A8\\u7684\\u7B2C\\u4E00\\u6B21UI\\u5206\\u6790\\u5427\\uFF01\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-list\",\n        children: historyData.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"history-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"item-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: item.fileName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-badge\",\n                style: {\n                  backgroundColor: getStatusColor(item.status)\n                },\n                children: getStatusText(item.status)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"item-stats\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-label\",\n                children: \"UI\\u5143\\u7D20\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-value\",\n                children: item.elementsCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-label\",\n                children: \"\\u4EA4\\u4E92\\u6D41\\u7A0B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-value\",\n                children: item.flowsCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-label\",\n                children: \"\\u6D4B\\u8BD5\\u811A\\u672C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-value\",\n                children: item.scriptsCount\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"item-footer\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"item-time\",\n              children: [\"\\u521B\\u5EFA\\u65F6\\u95F4: \", item.createdAt]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"item-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-secondary\",\n                onClick: () => handleViewDetails(item),\n                children: \"\\u67E5\\u770B\\u8BE6\\u60C5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 21\n              }, this), item.status === 'completed' && /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn-primary\",\n                onClick: () => handleDownloadScript(item),\n                children: \"\\u4E0B\\u8F7D\\u811A\\u672C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 17\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        .history-page {\n          min-height: 100vh;\n          background: #f8f9fa;\n        }\n\n        .page-header {\n          background: white;\n          border-bottom: 1px solid #e9ecef;\n          padding: 24px 0;\n          margin-bottom: 24px;\n        }\n\n        .page-header h1 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 28px;\n          font-weight: 600;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-header p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-content {\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 0 24px;\n        }\n\n        .loading-container {\n          text-align: center;\n          padding: 60px 20px;\n        }\n\n        .loading-spinner {\n          width: 40px;\n          height: 40px;\n          border: 4px solid #e9ecef;\n          border-top: 4px solid #007bff;\n          border-radius: 50%;\n          animation: spin 1s linear infinite;\n          margin: 0 auto 16px;\n        }\n\n        .empty-state {\n          text-align: center;\n          padding: 60px 20px;\n          background: white;\n          border-radius: 12px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n        }\n\n        .empty-icon {\n          font-size: 48px;\n          margin-bottom: 16px;\n        }\n\n        .empty-state h3 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 20px;\n        }\n\n        .empty-state p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n        }\n\n        .history-list {\n          display: grid;\n          gap: 16px;\n        }\n\n        .history-item {\n          background: white;\n          border-radius: 12px;\n          padding: 20px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n          transition: all 0.2s ease;\n        }\n\n        .history-item:hover {\n          box-shadow: 0 4px 20px rgba(0,0,0,0.15);\n        }\n\n        .item-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 16px;\n        }\n\n        .item-info h3 {\n          margin: 0 0 4px 0;\n          color: #333;\n          font-size: 18px;\n          font-weight: 600;\n        }\n\n        .item-info p {\n          margin: 0;\n          color: #666;\n          font-size: 14px;\n        }\n\n        .status-badge {\n          color: white;\n          padding: 4px 12px;\n          border-radius: 20px;\n          font-size: 12px;\n          font-weight: 500;\n        }\n\n        .item-stats {\n          display: flex;\n          gap: 24px;\n          margin-bottom: 16px;\n          padding: 16px 0;\n          border-top: 1px solid #e9ecef;\n          border-bottom: 1px solid #e9ecef;\n        }\n\n        .stat {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          gap: 4px;\n        }\n\n        .stat-label {\n          font-size: 12px;\n          color: #666;\n        }\n\n        .stat-value {\n          font-size: 18px;\n          font-weight: 600;\n          color: #333;\n        }\n\n        .item-footer {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n        }\n\n        .item-time {\n          font-size: 12px;\n          color: #999;\n        }\n\n        .item-actions {\n          display: flex;\n          gap: 8px;\n        }\n\n        .btn-primary, .btn-secondary {\n          border: none;\n          padding: 8px 16px;\n          border-radius: 6px;\n          font-size: 14px;\n          cursor: pointer;\n          transition: all 0.2s ease;\n        }\n\n        .btn-primary {\n          background: #007bff;\n          color: white;\n        }\n\n        .btn-primary:hover {\n          background: #0056b3;\n        }\n\n        .btn-secondary {\n          background: #6c757d;\n          color: white;\n        }\n\n        .btn-secondary:hover {\n          background: #5a6268;\n        }\n\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n\n        /* 响应式设计 */\n        @media (max-width: 768px) {\n          .page-header h1, .page-header p, .page-content {\n            padding: 0 16px;\n          }\n\n          .item-header {\n            flex-direction: column;\n            gap: 12px;\n          }\n\n          .item-stats {\n            justify-content: space-around;\n          }\n\n          .item-footer {\n            flex-direction: column;\n            gap: 12px;\n            align-items: flex-start;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n};\n_s(HistoryPage, \"qeDha3f5n1EOPY7s+4jS7jZpbkg=\");\n_c = HistoryPage;\nexport default HistoryPage;\nvar _c;\n$RefreshReg$(_c, \"HistoryPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "generateScriptName", "createdAt", "description", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hour", "getHours", "minute", "getMinutes", "scriptName", "replace", "trim", "HistoryPage", "_s", "historyData", "setHistoryData", "loading", "setLoading", "selectedItem", "setSelectedItem", "setTimeout", "id", "taskId", "fileName", "status", "elementsCount", "flowsCount", "scriptsCount", "getStatusColor", "getStatusText", "handleViewDetails", "item", "handleDownloadScript", "scriptContent", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "className", "children", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "style", "backgroundColor", "onClick", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/pages/HistoryPage.js"], "sourcesContent": ["/**\n * 历史记录页面\n */\nimport React, { useState, useEffect } from 'react';\n\n// 生成标准脚本名称格式：年月日+时分+脚本名称+spec.ts\nconst generateScriptName = (createdAt, description) => {\n  const date = new Date(createdAt);\n  const year = date.getFullYear();\n  const month = String(date.getMonth() + 1).padStart(2, '0');\n  const day = String(date.getDate()).padStart(2, '0');\n  const hour = String(date.getHours()).padStart(2, '0');\n  const minute = String(date.getMinutes()).padStart(2, '0');\n\n  // 从描述中提取脚本名称，去掉\"界面\"、\"分析\"等后缀\n  const scriptName = description\n    .replace(/界面分析?$/, '')\n    .replace(/功能分析?$/, '')\n    .replace(/分析$/, '')\n    .replace(/界面$/, '')\n    .trim();\n\n  return `${year}${month}${day}-${hour}${minute}-${scriptName}.spec.ts`;\n};\n\nconst HistoryPage = () => {\n  const [historyData, setHistoryData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedItem, setSelectedItem] = useState(null);\n\n  useEffect(() => {\n    // 模拟加载历史数据\n    setTimeout(() => {\n      setHistoryData([\n        {\n          id: '1',\n          taskId: 'task-001',\n          fileName: '登录界面.png',\n          description: '用户登录界面分析',\n          status: 'completed',\n          createdAt: '2024-12-17 10:30:00',\n          elementsCount: 5,\n          flowsCount: 2,\n          scriptsCount: 1\n        },\n        {\n          id: '2',\n          taskId: 'task-002',\n          fileName: '文件管理界面.png',\n          description: '文件管理界面功能分析',\n          status: 'completed',\n          createdAt: '2024-12-17 14:15:00',\n          elementsCount: 8,\n          flowsCount: 3,\n          scriptsCount: 2\n        },\n        {\n          id: '3',\n          taskId: 'task-003',\n          fileName: '购物车页面.png',\n          description: '电商购物车界面分析',\n          status: 'failed',\n          createdAt: '2024-12-17 16:45:00',\n          elementsCount: 0,\n          flowsCount: 0,\n          scriptsCount: 0\n        }\n      ]);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'completed':\n        return '#28a745';\n      case 'failed':\n        return '#dc3545';\n      case 'processing':\n        return '#007bff';\n      default:\n        return '#6c757d';\n    }\n  };\n\n  const getStatusText = (status) => {\n    switch (status) {\n      case 'completed':\n        return '已完成';\n      case 'failed':\n        return '失败';\n      case 'processing':\n        return '处理中';\n      default:\n        return '未知';\n    }\n  };\n\n  const handleViewDetails = (item) => {\n    setSelectedItem(item);\n  };\n\n  const handleDownloadScript = (item) => {\n    // 模拟下载脚本\n    const scriptContent = `# ${item.description}\\n# 生成时间: ${item.createdAt}\\n\\nname: ${item.description}\\ndescription: 自动化测试脚本\\n\\nsteps:\\n  - name: 示例步骤\\n    action: aiTap\\n    locate: 示例元素\\n    expect: 预期结果`;\n    \n    const blob = new Blob([scriptContent], { type: 'text/yaml' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `${item.fileName.replace('.png', '')}_script.yaml`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"history-page\">\n        <div className=\"page-header\">\n          <h1>📋 分析历史</h1>\n          <p>查看所有UI分析记录和结果</p>\n        </div>\n        <div className=\"loading-container\">\n          <div className=\"loading-spinner\"></div>\n          <p>加载历史记录中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"history-page\">\n      <div className=\"page-header\">\n        <h1>📋 分析历史</h1>\n        <p>查看所有UI分析记录和结果</p>\n      </div>\n\n      <div className=\"page-content\">\n        {historyData.length === 0 ? (\n          <div className=\"empty-state\">\n            <div className=\"empty-icon\">📝</div>\n            <h3>暂无分析记录</h3>\n            <p>开始您的第一次UI分析吧！</p>\n          </div>\n        ) : (\n          <div className=\"history-list\">\n            {historyData.map((item) => (\n              <div key={item.id} className=\"history-item\">\n                <div className=\"item-header\">\n                  <div className=\"item-info\">\n                    <h3>{item.fileName}</h3>\n                    <p>{item.description}</p>\n                  </div>\n                  <div className=\"item-status\">\n                    <span \n                      className=\"status-badge\"\n                      style={{ backgroundColor: getStatusColor(item.status) }}\n                    >\n                      {getStatusText(item.status)}\n                    </span>\n                  </div>\n                </div>\n                \n                <div className=\"item-stats\">\n                  <div className=\"stat\">\n                    <span className=\"stat-label\">UI元素</span>\n                    <span className=\"stat-value\">{item.elementsCount}</span>\n                  </div>\n                  <div className=\"stat\">\n                    <span className=\"stat-label\">交互流程</span>\n                    <span className=\"stat-value\">{item.flowsCount}</span>\n                  </div>\n                  <div className=\"stat\">\n                    <span className=\"stat-label\">测试脚本</span>\n                    <span className=\"stat-value\">{item.scriptsCount}</span>\n                  </div>\n                </div>\n\n                <div className=\"item-footer\">\n                  <span className=\"item-time\">创建时间: {item.createdAt}</span>\n                  <div className=\"item-actions\">\n                    <button \n                      className=\"btn-secondary\"\n                      onClick={() => handleViewDetails(item)}\n                    >\n                      查看详情\n                    </button>\n                    {item.status === 'completed' && (\n                      <button \n                        className=\"btn-primary\"\n                        onClick={() => handleDownloadScript(item)}\n                      >\n                        下载脚本\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      <style>{`\n        .history-page {\n          min-height: 100vh;\n          background: #f8f9fa;\n        }\n\n        .page-header {\n          background: white;\n          border-bottom: 1px solid #e9ecef;\n          padding: 24px 0;\n          margin-bottom: 24px;\n        }\n\n        .page-header h1 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 28px;\n          font-weight: 600;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-header p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-content {\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 0 24px;\n        }\n\n        .loading-container {\n          text-align: center;\n          padding: 60px 20px;\n        }\n\n        .loading-spinner {\n          width: 40px;\n          height: 40px;\n          border: 4px solid #e9ecef;\n          border-top: 4px solid #007bff;\n          border-radius: 50%;\n          animation: spin 1s linear infinite;\n          margin: 0 auto 16px;\n        }\n\n        .empty-state {\n          text-align: center;\n          padding: 60px 20px;\n          background: white;\n          border-radius: 12px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n        }\n\n        .empty-icon {\n          font-size: 48px;\n          margin-bottom: 16px;\n        }\n\n        .empty-state h3 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 20px;\n        }\n\n        .empty-state p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n        }\n\n        .history-list {\n          display: grid;\n          gap: 16px;\n        }\n\n        .history-item {\n          background: white;\n          border-radius: 12px;\n          padding: 20px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n          transition: all 0.2s ease;\n        }\n\n        .history-item:hover {\n          box-shadow: 0 4px 20px rgba(0,0,0,0.15);\n        }\n\n        .item-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 16px;\n        }\n\n        .item-info h3 {\n          margin: 0 0 4px 0;\n          color: #333;\n          font-size: 18px;\n          font-weight: 600;\n        }\n\n        .item-info p {\n          margin: 0;\n          color: #666;\n          font-size: 14px;\n        }\n\n        .status-badge {\n          color: white;\n          padding: 4px 12px;\n          border-radius: 20px;\n          font-size: 12px;\n          font-weight: 500;\n        }\n\n        .item-stats {\n          display: flex;\n          gap: 24px;\n          margin-bottom: 16px;\n          padding: 16px 0;\n          border-top: 1px solid #e9ecef;\n          border-bottom: 1px solid #e9ecef;\n        }\n\n        .stat {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          gap: 4px;\n        }\n\n        .stat-label {\n          font-size: 12px;\n          color: #666;\n        }\n\n        .stat-value {\n          font-size: 18px;\n          font-weight: 600;\n          color: #333;\n        }\n\n        .item-footer {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n        }\n\n        .item-time {\n          font-size: 12px;\n          color: #999;\n        }\n\n        .item-actions {\n          display: flex;\n          gap: 8px;\n        }\n\n        .btn-primary, .btn-secondary {\n          border: none;\n          padding: 8px 16px;\n          border-radius: 6px;\n          font-size: 14px;\n          cursor: pointer;\n          transition: all 0.2s ease;\n        }\n\n        .btn-primary {\n          background: #007bff;\n          color: white;\n        }\n\n        .btn-primary:hover {\n          background: #0056b3;\n        }\n\n        .btn-secondary {\n          background: #6c757d;\n          color: white;\n        }\n\n        .btn-secondary:hover {\n          background: #5a6268;\n        }\n\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n\n        /* 响应式设计 */\n        @media (max-width: 768px) {\n          .page-header h1, .page-header p, .page-content {\n            padding: 0 16px;\n          }\n\n          .item-header {\n            flex-direction: column;\n            gap: 12px;\n          }\n\n          .item-stats {\n            justify-content: space-around;\n          }\n\n          .item-footer {\n            flex-direction: column;\n            gap: 12px;\n            align-items: flex-start;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default HistoryPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;;AAElD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,WAAW,KAAK;EACrD,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACH,SAAS,CAAC;EAChC,MAAMI,IAAI,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;EAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EACnD,MAAMG,IAAI,GAAGL,MAAM,CAACL,IAAI,CAACW,QAAQ,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EACrD,MAAMK,MAAM,GAAGP,MAAM,CAACL,IAAI,CAACa,UAAU,CAAC,CAAC,CAAC,CAACN,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;;EAEzD;EACA,MAAMO,UAAU,GAAGf,WAAW,CAC3BgB,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CACrBA,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBC,IAAI,CAAC,CAAC;EAET,OAAO,GAAGd,IAAI,GAAGE,KAAK,GAAGI,GAAG,IAAIE,IAAI,GAAGE,MAAM,IAAIE,UAAU,UAAU;AACvE,CAAC;AAED,MAAMG,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAEtDC,SAAS,CAAC,MAAM;IACd;IACA+B,UAAU,CAAC,MAAM;MACfL,cAAc,CAAC,CACb;QACEM,EAAE,EAAE,GAAG;QACPC,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE,UAAU;QACpB7B,WAAW,EAAE,UAAU;QACvB8B,MAAM,EAAE,WAAW;QACnB/B,SAAS,EAAE,qBAAqB;QAChCgC,aAAa,EAAE,CAAC;QAChBC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE;MAChB,CAAC,EACD;QACEN,EAAE,EAAE,GAAG;QACPC,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE,YAAY;QACtB7B,WAAW,EAAE,YAAY;QACzB8B,MAAM,EAAE,WAAW;QACnB/B,SAAS,EAAE,qBAAqB;QAChCgC,aAAa,EAAE,CAAC;QAChBC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE;MAChB,CAAC,EACD;QACEN,EAAE,EAAE,GAAG;QACPC,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE,WAAW;QACrB7B,WAAW,EAAE,WAAW;QACxB8B,MAAM,EAAE,QAAQ;QAChB/B,SAAS,EAAE,qBAAqB;QAChCgC,aAAa,EAAE,CAAC;QAChBC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE;MAChB,CAAC,CACF,CAAC;MACFV,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,cAAc,GAAIJ,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,YAAY;QACf,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMK,aAAa,GAAIL,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,KAAK;MACd,KAAK,QAAQ;QACX,OAAO,IAAI;MACb,KAAK,YAAY;QACf,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMM,iBAAiB,GAAIC,IAAI,IAAK;IAClCZ,eAAe,CAACY,IAAI,CAAC;EACvB,CAAC;EAED,MAAMC,oBAAoB,GAAID,IAAI,IAAK;IACrC;IACA,MAAME,aAAa,GAAG,KAAKF,IAAI,CAACrC,WAAW,aAAaqC,IAAI,CAACtC,SAAS,aAAasC,IAAI,CAACrC,WAAW,yGAAyG;IAE5M,MAAMwC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,aAAa,CAAC,EAAE;MAAEG,IAAI,EAAE;IAAY,CAAC,CAAC;IAC7D,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IACrC,MAAMM,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;IACZG,CAAC,CAACI,QAAQ,GAAG,GAAGb,IAAI,CAACR,QAAQ,CAACb,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,cAAc;IAC/D+B,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;IAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;IACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC;IAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;EAC1B,CAAC;EAED,IAAIrB,OAAO,EAAE;IACX,oBACEzB,OAAA;MAAK2D,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B5D,OAAA;QAAK2D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B5D,OAAA;UAAA4D,QAAA,EAAI;QAAO;UAAA5B,QAAA,EAAA6B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChB/D,OAAA;UAAA4D,QAAA,EAAG;QAAa;UAAA5B,QAAA,EAAA6B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAA/B,QAAA,EAAA6B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eACN/D,OAAA;QAAK2D,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC5D,OAAA;UAAK2D,SAAS,EAAC;QAAiB;UAAA3B,QAAA,EAAA6B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvC/D,OAAA;UAAA4D,QAAA,EAAG;QAAU;UAAA5B,QAAA,EAAA6B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAA/B,QAAA,EAAA6B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAA/B,QAAA,EAAA6B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE/D,OAAA;IAAK2D,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B5D,OAAA;MAAK2D,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B5D,OAAA;QAAA4D,QAAA,EAAI;MAAO;QAAA5B,QAAA,EAAA6B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChB/D,OAAA;QAAA4D,QAAA,EAAG;MAAa;QAAA5B,QAAA,EAAA6B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAA/B,QAAA,EAAA6B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eAEN/D,OAAA;MAAK2D,SAAS,EAAC,cAAc;MAAAC,QAAA,EAC1BrC,WAAW,CAACyC,MAAM,KAAK,CAAC,gBACvBhE,OAAA;QAAK2D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B5D,OAAA;UAAK2D,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAE;UAAA5B,QAAA,EAAA6B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACpC/D,OAAA;UAAA4D,QAAA,EAAI;QAAM;UAAA5B,QAAA,EAAA6B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACf/D,OAAA;UAAA4D,QAAA,EAAG;QAAa;UAAA5B,QAAA,EAAA6B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAA/B,QAAA,EAAA6B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,gBAEN/D,OAAA;QAAK2D,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1BrC,WAAW,CAAC0C,GAAG,CAAEzB,IAAI,iBACpBxC,OAAA;UAAmB2D,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzC5D,OAAA;YAAK2D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B5D,OAAA;cAAK2D,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB5D,OAAA;gBAAA4D,QAAA,EAAKpB,IAAI,CAACR;cAAQ;gBAAAA,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxB/D,OAAA;gBAAA4D,QAAA,EAAIpB,IAAI,CAACrC;cAAW;gBAAA6B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAA/B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACN/D,OAAA;cAAK2D,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1B5D,OAAA;gBACE2D,SAAS,EAAC,cAAc;gBACxBO,KAAK,EAAE;kBAAEC,eAAe,EAAE9B,cAAc,CAACG,IAAI,CAACP,MAAM;gBAAE,CAAE;gBAAA2B,QAAA,EAEvDtB,aAAa,CAACE,IAAI,CAACP,MAAM;cAAC;gBAAAD,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAA/B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAA/B,QAAA,EAAA6B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/D,OAAA;YAAK2D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5D,OAAA;cAAK2D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5D,OAAA;gBAAM2D,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAI;gBAAA5B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxC/D,OAAA;gBAAM2D,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEpB,IAAI,CAACN;cAAa;gBAAAF,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAA/B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACN/D,OAAA;cAAK2D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5D,OAAA;gBAAM2D,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAI;gBAAA5B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxC/D,OAAA;gBAAM2D,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEpB,IAAI,CAACL;cAAU;gBAAAH,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAA/B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACN/D,OAAA;cAAK2D,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5D,OAAA;gBAAM2D,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAI;gBAAA5B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxC/D,OAAA;gBAAM2D,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEpB,IAAI,CAACJ;cAAY;gBAAAJ,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAA/B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA;YAAA/B,QAAA,EAAA6B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/D,OAAA;YAAK2D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B5D,OAAA;cAAM2D,SAAS,EAAC,WAAW;cAAAC,QAAA,GAAC,4BAAM,EAACpB,IAAI,CAACtC,SAAS;YAAA;cAAA8B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzD/D,OAAA;cAAK2D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B5D,OAAA;gBACE2D,SAAS,EAAC,eAAe;gBACzBS,OAAO,EAAEA,CAAA,KAAM7B,iBAAiB,CAACC,IAAI,CAAE;gBAAAoB,QAAA,EACxC;cAED;gBAAA5B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACRvB,IAAI,CAACP,MAAM,KAAK,WAAW,iBAC1BjC,OAAA;gBACE2D,SAAS,EAAC,aAAa;gBACvBS,OAAO,EAAEA,CAAA,KAAM3B,oBAAoB,CAACD,IAAI,CAAE;gBAAAoB,QAAA,EAC3C;cAED;gBAAA5B,QAAA,EAAA6B,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAA/B,QAAA,EAAA6B,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAA/B,QAAA,EAAA6B,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAjDEvB,IAAI,CAACV,EAAE;UAAAE,QAAA,EAAA6B,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkDZ,CACN;MAAC;QAAA/B,QAAA,EAAA6B,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAA/B,QAAA,EAAA6B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN/D,OAAA;MAAA4D,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAA5B,QAAA,EAAA6B,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAA/B,QAAA,EAAA6B,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACzC,EAAA,CApZID,WAAW;AAAAgD,EAAA,GAAXhD,WAAW;AAsZjB,eAAeA,WAAW;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}