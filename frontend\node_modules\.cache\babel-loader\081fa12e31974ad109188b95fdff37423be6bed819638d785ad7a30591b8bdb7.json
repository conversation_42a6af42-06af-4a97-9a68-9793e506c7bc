{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\components\\\\SimpleUpload.js\",\n  _s = $RefreshSig$();\n/**\n * 简化的上传组件\n */\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SimpleUpload = ({\n  onUploadSuccess,\n  onUploadError\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n    setIsUploading(true);\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n      const response = await fetch('http://localhost:8001/api/v1/upload', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n      const result = await response.json();\n      onUploadSuccess(result);\n\n      // 重置表单\n      setSelectedFile(null);\n      setDescription('');\n      event.target.reset();\n    } catch (error) {\n      console.error('Upload error:', error);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  const handleFileManagementDemo = async () => {\n    setIsUploading(true);\n    try {\n      const formData = new FormData();\n      formData.append('description', '文件管理界面功能分析 - 基于提供的截图进行专项分析');\n      const response = await fetch('http://localhost:8001/api/v1/analyze/file-management', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n      const result = await response.json();\n      onUploadSuccess(result);\n    } catch (error) {\n      console.error('File management demo error:', error);\n      onUploadError(error.message || '文件管理界面分析失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'white',\n      padding: '24px',\n      borderRadius: '12px',\n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n      marginBottom: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"file-input\",\n          style: {\n            display: 'block',\n            marginBottom: '8px',\n            fontWeight: '500'\n          },\n          children: \"\\u9009\\u62E9\\u56FE\\u7247\\u6587\\u4EF6:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          id: \"file-input\",\n          type: \"file\",\n          accept: \"image/*\",\n          onChange: handleFileSelect,\n          style: {\n            width: '100%',\n            padding: '8px',\n            border: '1px solid #ddd',\n            borderRadius: '4px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), selectedFile && /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            marginTop: '8px',\n            color: '#666',\n            fontSize: '14px'\n          },\n          children: [\"\\u5DF2\\u9009\\u62E9: \", selectedFile.name, \" (\", (selectedFile.size / 1024 / 1024).toFixed(2), \" MB)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"description\",\n          style: {\n            display: 'block',\n            marginBottom: '8px',\n            fontWeight: '500'\n          },\n          children: \"\\u754C\\u9762\\u529F\\u80FD\\u63CF\\u8FF0:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          id: \"description\",\n          value: description,\n          onChange: e => setDescription(e.target.value),\n          placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u8FD9\\u4E2A\\u754C\\u9762\\u7684\\u4E3B\\u8981\\u529F\\u80FD\\uFF0C\\u4F8B\\u5982\\uFF1A\\u8FD9\\u662F\\u4E00\\u4E2A\\u767B\\u5F55\\u9875\\u9762\\uFF0C\\u5305\\u542B\\u7528\\u6237\\u540D\\u5BC6\\u7801\\u8F93\\u5165\\u6846\\u548C\\u767B\\u5F55\\u6309\\u94AE...\",\n          rows: 4,\n          style: {\n            width: '100%',\n            padding: '12px',\n            border: '1px solid #ddd',\n            borderRadius: '4px',\n            resize: 'vertical',\n            fontFamily: 'inherit'\n          },\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'right',\n            fontSize: '12px',\n            color: '#666',\n            marginTop: '4px'\n          },\n          children: [description.length, \"/500\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: isUploading || !selectedFile || !description.trim(),\n        style: {\n          background: isUploading ? '#6c757d' : '#007bff',\n          color: 'white',\n          border: 'none',\n          padding: '12px 24px',\n          borderRadius: '6px',\n          fontSize: '16px',\n          fontWeight: '500',\n          cursor: isUploading ? 'not-allowed' : 'pointer',\n          transition: 'all 0.3s ease',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        },\n        children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: '16px',\n              height: '16px',\n              border: '2px solid transparent',\n              borderTop: '2px solid white',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this), \"\\u5206\\u6790\\u4E2D...\"]\n        }, void 0, true) : '🚀 开始分析'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        margin: '24px 0',\n        color: '#666',\n        fontSize: '14px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          height: '1px',\n          background: '#e9ecef'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          padding: '0 16px'\n        },\n        children: \"\\u6216\\u8005\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          height: '1px',\n          background: '#e9ecef'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          margin: '0 0 12px 0',\n          color: '#333',\n          fontSize: '16px'\n        },\n        children: \"\\uD83D\\uDCC1 \\u4E13\\u9879\\u5206\\u6790\\u6F14\\u793A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: '0 0 16px 0',\n          color: '#666',\n          fontSize: '14px'\n        },\n        children: \"\\u57FA\\u4E8E\\u60A8\\u63D0\\u4F9B\\u7684\\u6587\\u4EF6\\u7BA1\\u7406\\u754C\\u9762\\u622A\\u56FE\\uFF0C\\u751F\\u6210\\u4E13\\u95E8\\u7684UI\\u81EA\\u52A8\\u5316\\u6D4B\\u8BD5\\u811A\\u672C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: handleFileManagementDemo,\n        disabled: isUploading,\n        style: {\n          background: isUploading ? '#6c757d' : '#28a745',\n          color: 'white',\n          border: 'none',\n          padding: '12px 32px',\n          borderRadius: '6px',\n          fontSize: '16px',\n          fontWeight: '500',\n          cursor: isUploading ? 'not-allowed' : 'pointer',\n          transition: 'all 0.3s ease',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px',\n          margin: '0 auto'\n        },\n        children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: '16px',\n              height: '16px',\n              border: '2px solid transparent',\n              borderTop: '2px solid white',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this), \"\\u5206\\u6790\\u4E2D...\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: \"\\uD83D\\uDCCA \\u5206\\u6790\\u6587\\u4EF6\\u7BA1\\u7406\\u754C\\u9762\"\n        }, void 0, false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleUpload, \"rg49S67Niskg9326cNUOfz9PB/M=\");\n_c = SimpleUpload;\nexport default SimpleUpload;\nvar _c;\n$RefreshReg$(_c, \"SimpleUpload\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SimpleUpload", "onUploadSuccess", "onUploadError", "_s", "selectedFile", "setSelectedFile", "description", "setDescription", "isUploading", "setIsUploading", "handleFileSelect", "event", "file", "target", "files", "handleSubmit", "preventDefault", "trim", "formData", "FormData", "append", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "status", "result", "reset", "error", "console", "message", "handleFileManagementDemo", "style", "background", "padding", "borderRadius", "boxShadow", "marginBottom", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "display", "fontWeight", "id", "type", "accept", "onChange", "width", "border", "marginTop", "color", "fontSize", "name", "size", "toFixed", "value", "e", "placeholder", "rows", "resize", "fontFamily", "required", "textAlign", "length", "disabled", "cursor", "transition", "alignItems", "gap", "height", "borderTop", "animation", "margin", "flex", "onClick", "jsx", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/components/SimpleUpload.js"], "sourcesContent": ["/**\n * 简化的上传组件\n */\nimport React, { useState } from 'react';\n\nconst SimpleUpload = ({ onUploadSuccess, onUploadError }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n\n  const handleFileSelect = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n\n  const handleSubmit = async (event) => {\n    event.preventDefault();\n\n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n\n    setIsUploading(true);\n\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n\n      const response = await fetch('http://localhost:8001/api/v1/upload', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      onUploadSuccess(result);\n\n      // 重置表单\n      setSelectedFile(null);\n      setDescription('');\n      event.target.reset();\n    } catch (error) {\n      console.error('Upload error:', error);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const handleFileManagementDemo = async () => {\n    setIsUploading(true);\n\n    try {\n      const formData = new FormData();\n      formData.append('description', '文件管理界面功能分析 - 基于提供的截图进行专项分析');\n\n      const response = await fetch('http://localhost:8001/api/v1/analyze/file-management', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      onUploadSuccess(result);\n\n    } catch (error) {\n      console.error('File management demo error:', error);\n      onUploadError(error.message || '文件管理界面分析失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  return (\n    <div style={{ \n      background: 'white', \n      padding: '24px', \n      borderRadius: '12px', \n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n      marginBottom: '24px'\n    }}>\n      <h3>📁 上传UI界面截图</h3>\n      <form onSubmit={handleSubmit}>\n        <div style={{ marginBottom: '16px' }}>\n          <label htmlFor=\"file-input\" style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>\n            选择图片文件:\n          </label>\n          <input\n            id=\"file-input\"\n            type=\"file\"\n            accept=\"image/*\"\n            onChange={handleFileSelect}\n            style={{\n              width: '100%',\n              padding: '8px',\n              border: '1px solid #ddd',\n              borderRadius: '4px'\n            }}\n          />\n          {selectedFile && (\n            <p style={{ marginTop: '8px', color: '#666', fontSize: '14px' }}>\n              已选择: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)\n            </p>\n          )}\n        </div>\n\n        <div style={{ marginBottom: '16px' }}>\n          <label htmlFor=\"description\" style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>\n            界面功能描述:\n          </label>\n          <textarea\n            id=\"description\"\n            value={description}\n            onChange={(e) => setDescription(e.target.value)}\n            placeholder=\"请描述这个界面的主要功能，例如：这是一个登录页面，包含用户名密码输入框和登录按钮...\"\n            rows={4}\n            style={{\n              width: '100%',\n              padding: '12px',\n              border: '1px solid #ddd',\n              borderRadius: '4px',\n              resize: 'vertical',\n              fontFamily: 'inherit'\n            }}\n            required\n          />\n          <div style={{ textAlign: 'right', fontSize: '12px', color: '#666', marginTop: '4px' }}>\n            {description.length}/500\n          </div>\n        </div>\n\n        <button\n          type=\"submit\"\n          disabled={isUploading || !selectedFile || !description.trim()}\n          style={{\n            background: isUploading ? '#6c757d' : '#007bff',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            fontSize: '16px',\n            fontWeight: '500',\n            cursor: isUploading ? 'not-allowed' : 'pointer',\n            transition: 'all 0.3s ease',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          }}\n        >\n          {isUploading ? (\n            <>\n              <span style={{\n                width: '16px',\n                height: '16px',\n                border: '2px solid transparent',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }}></span>\n              分析中...\n            </>\n          ) : (\n            '🚀 开始分析'\n          )}\n        </button>\n      </form>\n\n      {/* 分隔线 */}\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        margin: '24px 0',\n        color: '#666',\n        fontSize: '14px'\n      }}>\n        <div style={{ flex: 1, height: '1px', background: '#e9ecef' }}></div>\n        <span style={{ padding: '0 16px' }}>或者</span>\n        <div style={{ flex: 1, height: '1px', background: '#e9ecef' }}></div>\n      </div>\n\n      {/* 文件管理界面演示按钮 */}\n      <div style={{ textAlign: 'center' }}>\n        <h4 style={{ margin: '0 0 12px 0', color: '#333', fontSize: '16px' }}>\n          📁 专项分析演示\n        </h4>\n        <p style={{ margin: '0 0 16px 0', color: '#666', fontSize: '14px' }}>\n          基于您提供的文件管理界面截图，生成专门的UI自动化测试脚本\n        </p>\n        <button\n          type=\"button\"\n          onClick={handleFileManagementDemo}\n          disabled={isUploading}\n          style={{\n            background: isUploading ? '#6c757d' : '#28a745',\n            color: 'white',\n            border: 'none',\n            padding: '12px 32px',\n            borderRadius: '6px',\n            fontSize: '16px',\n            fontWeight: '500',\n            cursor: isUploading ? 'not-allowed' : 'pointer',\n            transition: 'all 0.3s ease',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px',\n            margin: '0 auto'\n          }}\n        >\n          {isUploading ? (\n            <>\n              <span style={{\n                width: '16px',\n                height: '16px',\n                border: '2px solid transparent',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }}></span>\n              分析中...\n            </>\n          ) : (\n            <>\n              📊 分析文件管理界面\n            </>\n          )}\n        </button>\n      </div>\n\n      <style jsx>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default SimpleUpload;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,YAAY,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMe,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACRP,eAAe,CAACO,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOJ,KAAK,IAAK;IACpCA,KAAK,CAACK,cAAc,CAAC,CAAC;IAEtB,IAAI,CAACZ,YAAY,EAAE;MACjBF,aAAa,CAAC,SAAS,CAAC;MACxB;IACF;IAEA,IAAI,CAACI,WAAW,CAACW,IAAI,CAAC,CAAC,EAAE;MACvBf,aAAa,CAAC,WAAW,CAAC;MAC1B;IACF;IAEAO,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,MAAMS,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEhB,YAAY,CAAC;MAC3Cc,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEd,WAAW,CAACW,IAAI,CAAC,CAAC,CAAC;MAElD,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,qCAAqC,EAAE;QAClEC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEN;MACR,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,uBAAuBR,QAAQ,CAACS,MAAM,EAAE,CAAC;MAC/E;MAEA,MAAMC,MAAM,GAAG,MAAMV,QAAQ,CAACM,IAAI,CAAC,CAAC;MACpC1B,eAAe,CAAC8B,MAAM,CAAC;;MAEvB;MACA1B,eAAe,CAAC,IAAI,CAAC;MACrBE,cAAc,CAAC,EAAE,CAAC;MAClBI,KAAK,CAACE,MAAM,CAACmB,KAAK,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC/B,aAAa,CAAC+B,KAAK,CAACE,OAAO,IAAI,UAAU,CAAC;IAC5C,CAAC,SAAS;MACR1B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM2B,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C3B,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,MAAMS,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE,4BAA4B,CAAC;MAE5D,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,sDAAsD,EAAE;QACnFC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEN;MACR,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,uBAAuBR,QAAQ,CAACS,MAAM,EAAE,CAAC;MAC/E;MAEA,MAAMC,MAAM,GAAG,MAAMV,QAAQ,CAACM,IAAI,CAAC,CAAC;MACpC1B,eAAe,CAAC8B,MAAM,CAAC;IAEzB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD/B,aAAa,CAAC+B,KAAK,CAACE,OAAO,IAAI,gBAAgB,CAAC;IAClD,CAAC,SAAS;MACR1B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,oBACEZ,OAAA;IAAKwC,KAAK,EAAE;MACVC,UAAU,EAAE,OAAO;MACnBC,OAAO,EAAE,MAAM;MACfC,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,4BAA4B;MACvCC,YAAY,EAAE;IAChB,CAAE;IAAAC,QAAA,gBACA9C,OAAA;MAAA8C,QAAA,EAAI;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpBlD,OAAA;MAAMmD,QAAQ,EAAEjC,YAAa;MAAA4B,QAAA,gBAC3B9C,OAAA;QAAKwC,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnC9C,OAAA;UAAOoD,OAAO,EAAC,YAAY;UAACZ,KAAK,EAAE;YAAEa,OAAO,EAAE,OAAO;YAAER,YAAY,EAAE,KAAK;YAAES,UAAU,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAEjG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRlD,OAAA;UACEuD,EAAE,EAAC,YAAY;UACfC,IAAI,EAAC,MAAM;UACXC,MAAM,EAAC,SAAS;UAChBC,QAAQ,EAAE7C,gBAAiB;UAC3B2B,KAAK,EAAE;YACLmB,KAAK,EAAE,MAAM;YACbjB,OAAO,EAAE,KAAK;YACdkB,MAAM,EAAE,gBAAgB;YACxBjB,YAAY,EAAE;UAChB;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACD3C,YAAY,iBACXP,OAAA;UAAGwC,KAAK,EAAE;YAAEqB,SAAS,EAAE,KAAK;YAAEC,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAjB,QAAA,GAAC,sBAC1D,EAACvC,YAAY,CAACyD,IAAI,EAAC,IAAE,EAAC,CAACzD,YAAY,CAAC0D,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,MAC1E;QAAA;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENlD,OAAA;QAAKwC,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnC9C,OAAA;UAAOoD,OAAO,EAAC,aAAa;UAACZ,KAAK,EAAE;YAAEa,OAAO,EAAE,OAAO;YAAER,YAAY,EAAE,KAAK;YAAES,UAAU,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAElG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRlD,OAAA;UACEuD,EAAE,EAAC,aAAa;UAChBY,KAAK,EAAE1D,WAAY;UACnBiD,QAAQ,EAAGU,CAAC,IAAK1D,cAAc,CAAC0D,CAAC,CAACpD,MAAM,CAACmD,KAAK,CAAE;UAChDE,WAAW,EAAC,qPAA6C;UACzDC,IAAI,EAAE,CAAE;UACR9B,KAAK,EAAE;YACLmB,KAAK,EAAE,MAAM;YACbjB,OAAO,EAAE,MAAM;YACfkB,MAAM,EAAE,gBAAgB;YACxBjB,YAAY,EAAE,KAAK;YACnB4B,MAAM,EAAE,UAAU;YAClBC,UAAU,EAAE;UACd,CAAE;UACFC,QAAQ;QAAA;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFlD,OAAA;UAAKwC,KAAK,EAAE;YAAEkC,SAAS,EAAE,OAAO;YAAEX,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE,MAAM;YAAED,SAAS,EAAE;UAAM,CAAE;UAAAf,QAAA,GACnFrC,WAAW,CAACkE,MAAM,EAAC,MACtB;QAAA;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlD,OAAA;QACEwD,IAAI,EAAC,QAAQ;QACboB,QAAQ,EAAEjE,WAAW,IAAI,CAACJ,YAAY,IAAI,CAACE,WAAW,CAACW,IAAI,CAAC,CAAE;QAC9DoB,KAAK,EAAE;UACLC,UAAU,EAAE9B,WAAW,GAAG,SAAS,GAAG,SAAS;UAC/CmD,KAAK,EAAE,OAAO;UACdF,MAAM,EAAE,MAAM;UACdlB,OAAO,EAAE,WAAW;UACpBC,YAAY,EAAE,KAAK;UACnBoB,QAAQ,EAAE,MAAM;UAChBT,UAAU,EAAE,KAAK;UACjBuB,MAAM,EAAElE,WAAW,GAAG,aAAa,GAAG,SAAS;UAC/CmE,UAAU,EAAE,eAAe;UAC3BzB,OAAO,EAAE,MAAM;UACf0B,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE;QACP,CAAE;QAAAlC,QAAA,EAEDnC,WAAW,gBACVX,OAAA,CAAAE,SAAA;UAAA4C,QAAA,gBACE9C,OAAA;YAAMwC,KAAK,EAAE;cACXmB,KAAK,EAAE,MAAM;cACbsB,MAAM,EAAE,MAAM;cACdrB,MAAM,EAAE,uBAAuB;cAC/BsB,SAAS,EAAE,iBAAiB;cAC5BvC,YAAY,EAAE,KAAK;cACnBwC,SAAS,EAAE;YACb;UAAE;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,yBAEZ;QAAA,eAAE,CAAC,GAEH;MACD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGPlD,OAAA;MAAKwC,KAAK,EAAE;QACVa,OAAO,EAAE,MAAM;QACf0B,UAAU,EAAE,QAAQ;QACpBK,MAAM,EAAE,QAAQ;QAChBtB,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE;MACZ,CAAE;MAAAjB,QAAA,gBACA9C,OAAA;QAAKwC,KAAK,EAAE;UAAE6C,IAAI,EAAE,CAAC;UAAEJ,MAAM,EAAE,KAAK;UAAExC,UAAU,EAAE;QAAU;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrElD,OAAA;QAAMwC,KAAK,EAAE;UAAEE,OAAO,EAAE;QAAS,CAAE;QAAAI,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7ClD,OAAA;QAAKwC,KAAK,EAAE;UAAE6C,IAAI,EAAE,CAAC;UAAEJ,MAAM,EAAE,KAAK;UAAExC,UAAU,EAAE;QAAU;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE,CAAC,eAGNlD,OAAA;MAAKwC,KAAK,EAAE;QAAEkC,SAAS,EAAE;MAAS,CAAE;MAAA5B,QAAA,gBAClC9C,OAAA;QAAIwC,KAAK,EAAE;UAAE4C,MAAM,EAAE,YAAY;UAAEtB,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAjB,QAAA,EAAC;MAEtE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLlD,OAAA;QAAGwC,KAAK,EAAE;UAAE4C,MAAM,EAAE,YAAY;UAAEtB,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAjB,QAAA,EAAC;MAErE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJlD,OAAA;QACEwD,IAAI,EAAC,QAAQ;QACb8B,OAAO,EAAE/C,wBAAyB;QAClCqC,QAAQ,EAAEjE,WAAY;QACtB6B,KAAK,EAAE;UACLC,UAAU,EAAE9B,WAAW,GAAG,SAAS,GAAG,SAAS;UAC/CmD,KAAK,EAAE,OAAO;UACdF,MAAM,EAAE,MAAM;UACdlB,OAAO,EAAE,WAAW;UACpBC,YAAY,EAAE,KAAK;UACnBoB,QAAQ,EAAE,MAAM;UAChBT,UAAU,EAAE,KAAK;UACjBuB,MAAM,EAAElE,WAAW,GAAG,aAAa,GAAG,SAAS;UAC/CmE,UAAU,EAAE,eAAe;UAC3BzB,OAAO,EAAE,MAAM;UACf0B,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE,KAAK;UACVI,MAAM,EAAE;QACV,CAAE;QAAAtC,QAAA,EAEDnC,WAAW,gBACVX,OAAA,CAAAE,SAAA;UAAA4C,QAAA,gBACE9C,OAAA;YAAMwC,KAAK,EAAE;cACXmB,KAAK,EAAE,MAAM;cACbsB,MAAM,EAAE,MAAM;cACdrB,MAAM,EAAE,uBAAuB;cAC/BsB,SAAS,EAAE,iBAAiB;cAC5BvC,YAAY,EAAE,KAAK;cACnBwC,SAAS,EAAE;YACb;UAAE;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,yBAEZ;QAAA,eAAE,CAAC,gBAEHlD,OAAA,CAAAE,SAAA;UAAA4C,QAAA,EAAE;QAEF,gBAAE;MACH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENlD,OAAA;MAAOuF,GAAG;MAAAzC,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC5C,EAAA,CAxPIH,YAAY;AAAAqF,EAAA,GAAZrF,YAAY;AA0PlB,eAAeA,YAAY;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}