{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\components\\\\SimpleResults.js\",\n  _s = $RefreshSig$();\n/**\n * 简化的结果展示组件\n */\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleResults = ({\n  result\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState('elements');\n\n  // 下载脚本功能\n  const downloadScript = (content, filename) => {\n    const blob = new Blob([content], {\n      type: 'text/yaml'\n    });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `${filename.replace(/[^a-zA-Z0-9\\u4e00-\\u9fa5]/g, '_')}.yaml`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n  if (!result || !result.result) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        padding: '24px',\n        borderRadius: '12px',\n        boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u6CA1\\u6709\\u5206\\u6790\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this);\n  }\n  const {\n    task_id,\n    result: analysisResult\n  } = result;\n  const {\n    elements = [],\n    flows = [],\n    automation_scripts = []\n  } = analysisResult;\n  const renderElements = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n      children: [\"\\uD83C\\uDFAF \\u8BC6\\u522B\\u5230\\u7684UI\\u5143\\u7D20 (\", elements.length, \"\\u4E2A)\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gap: '16px',\n        gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))'\n      },\n      children: elements.map(element => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          border: '1px solid #e9ecef',\n          borderRadius: '8px',\n          padding: '16px',\n          background: '#f8f9fa'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontWeight: '600',\n              color: '#333'\n            },\n            children: element.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              background: '#007bff',\n              color: 'white',\n              padding: '2px 8px',\n              borderRadius: '12px',\n              fontSize: '12px'\n            },\n            children: element.element_type\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#666',\n            marginBottom: '8px',\n            fontSize: '14px'\n          },\n          children: element.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this), element.text_content && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#e3f2fd',\n            padding: '8px',\n            borderRadius: '4px',\n            fontSize: '13px',\n            marginBottom: '8px'\n          },\n          children: [\"\\u6587\\u5B57\\u5185\\u5BB9: \\\"\", element.text_content, \"\\\"\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '13px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '4px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u4F4D\\u7F6E:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 17\n            }, this), \" \", element.position.area]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '4px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u89C6\\u89C9\\u7279\\u5F81:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 17\n            }, this), \" \", element.visual_features.color, \", \", element.visual_features.shape]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '4px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u529F\\u80FD:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 17\n            }, this), \" \", element.functionality]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'right',\n              fontWeight: '500',\n              color: '#28a745',\n              marginTop: '8px'\n            },\n            children: [\"\\u7F6E\\u4FE1\\u5EA6: \", (element.confidence_score * 100).toFixed(1), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 13\n        }, this)]\n      }, element.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n  const renderFlows = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n      children: [\"\\uD83D\\uDD17 \\u4EA4\\u4E92\\u6D41\\u7A0B (\", flows.length, \"\\u4E2A)\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '20px'\n      },\n      children: flows.map((flow, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          border: '1px solid #e9ecef',\n          borderRadius: '8px',\n          padding: '20px',\n          background: '#f8f9fa'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          style: {\n            margin: '0 0 8px 0',\n            color: '#333'\n          },\n          children: flow.flow_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#666',\n            marginBottom: '16px'\n          },\n          children: flow.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            style: {\n              margin: '0 0 8px 0',\n              color: '#333'\n            },\n            children: \"\\u64CD\\u4F5C\\u6B65\\u9AA4:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n            style: {\n              margin: '0',\n              paddingLeft: '20px'\n            },\n            children: flow.steps.map((step, stepIndex) => /*#__PURE__*/_jsxDEV(\"li\", {\n              style: {\n                marginBottom: '12px',\n                padding: '8px',\n                background: 'white',\n                borderRadius: '4px',\n                borderLeft: '3px solid #007bff'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: '500',\n                  color: '#333',\n                  marginBottom: '4px'\n                },\n                children: step.action\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '13px',\n                  color: '#666',\n                  marginBottom: '2px'\n                },\n                children: [\"\\u76EE\\u6807: \", step.target_element]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '13px',\n                  color: '#666'\n                },\n                children: [\"\\u9884\\u671F\\u7ED3\\u679C: \", step.expected_result]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 21\n              }, this)]\n            }, stepIndex, true, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '13px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6210\\u529F\\u6807\\u51C6:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this), \" \", flow.success_criteria]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 15\n          }, this), flow.error_scenarios.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u5F02\\u5E38\\u573A\\u666F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 19\n            }, this), \" \", flow.error_scenarios.join(', ')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n  const renderScripts = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n      children: [\"\\uD83D\\uDCDD UI\\u81EA\\u52A8\\u5316\\u6D4B\\u8BD5\\u811A\\u672C (\", automation_scripts.length, \"\\u4E2A)\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '20px'\n      },\n      children: automation_scripts.map((script, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          border: '1px solid #e9ecef',\n          borderRadius: '8px',\n          padding: '20px',\n          background: '#f8f9fa'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'flex-start',\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            style: {\n              margin: '0',\n              color: '#333'\n            },\n            children: script.script_name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '8px',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                padding: '2px 8px',\n                borderRadius: '12px',\n                fontSize: '12px',\n                fontWeight: '500',\n                background: script.priority === 'high' ? '#dc3545' : '#ffc107',\n                color: script.priority === 'high' ? 'white' : '#212529'\n              },\n              children: script.priority\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '12px',\n                color: '#666'\n              },\n              children: script.estimated_duration\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#666',\n            marginBottom: '16px'\n          },\n          children: script.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this), script.preconditions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u524D\\u7F6E\\u6761\\u4EF6:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              margin: '4px 0 0 0',\n              paddingLeft: '20px'\n            },\n            children: script.preconditions.map((condition, i) => /*#__PURE__*/_jsxDEV(\"li\", {\n              style: {\n                fontSize: '13px',\n                color: '#666',\n                marginBottom: '2px'\n              },\n              children: condition\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u811A\\u672C\\u6B65\\u9AA4:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n            style: {\n              margin: '4px 0 0 0',\n              paddingLeft: '20px'\n            },\n            children: script.test_steps.map((step, stepIndex) => /*#__PURE__*/_jsxDEV(\"li\", {\n              style: {\n                marginBottom: '12px',\n                padding: '8px',\n                background: 'white',\n                borderRadius: '4px',\n                borderLeft: '3px solid #007bff'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '4px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    background: '#6c757d',\n                    color: 'white',\n                    padding: '2px 6px',\n                    borderRadius: '4px',\n                    fontSize: '11px',\n                    marginRight: '8px'\n                  },\n                  children: step.action_type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: '500',\n                    color: '#333'\n                  },\n                  children: step.action_description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '13px',\n                  color: '#666',\n                  marginBottom: '2px'\n                },\n                children: [\"\\u76EE\\u6807: \", step.visual_target]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '13px',\n                  color: '#666',\n                  marginBottom: '2px'\n                },\n                children: [\"\\u9884\\u671F: \", step.expected_result]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '13px',\n                  color: '#666'\n                },\n                children: [\"\\u9A8C\\u8BC1: \", step.validation_step]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 21\n              }, this)]\n            }, stepIndex, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u9A8C\\u8BC1\\u8981\\u70B9:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              margin: '4px 0 0 0',\n              paddingLeft: '20px'\n            },\n            children: script.validation_points.map((point, i) => /*#__PURE__*/_jsxDEV(\"li\", {\n              style: {\n                fontSize: '13px',\n                color: '#666',\n                marginBottom: '2px'\n              },\n              children: point\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this), script.yaml_content && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"MidScene.js YAML\\u811A\\u672C:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => downloadScript(script.yaml_content, script.script_name),\n              style: {\n                background: '#28a745',\n                color: 'white',\n                border: 'none',\n                padding: '4px 8px',\n                borderRadius: '4px',\n                fontSize: '12px',\n                cursor: 'pointer'\n              },\n              children: \"\\uD83D\\uDCE5 \\u4E0B\\u8F7D\\u811A\\u672C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n            style: {\n              background: '#f8f9fa',\n              border: '1px solid #e9ecef',\n              borderRadius: '4px',\n              padding: '12px',\n              fontSize: '12px',\n              overflow: 'auto',\n              maxHeight: '300px',\n              fontFamily: 'Monaco, Consolas, \"Courier New\", monospace'\n            },\n            children: script.yaml_content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 15\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'white',\n      padding: '24px',\n      borderRadius: '12px',\n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'flex-start',\n        marginBottom: '24px',\n        flexWrap: 'wrap',\n        gap: '16px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0',\n          color: '#333',\n          fontSize: '24px',\n          fontWeight: '600'\n        },\n        children: \"\\uD83D\\uDCCA \\u5206\\u6790\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '4px',\n          fontSize: '14px',\n          color: '#666',\n          textAlign: 'right'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\u4EFB\\u52A1ID: \", task_id]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\u72B6\\u6001: \", analysisResult.status]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        border: '1px solid #e9ecef',\n        borderRadius: '8px',\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          background: '#f8f9fa',\n          borderBottom: '1px solid #e9ecef'\n        },\n        children: [{\n          key: 'elements',\n          label: `UI元素 (${elements.length})`\n        }, {\n          key: 'flows',\n          label: `交互流程 (${flows.length})`\n        }, {\n          key: 'scripts',\n          label: `自动化脚本 (${automation_scripts.length})`\n        }].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab.key),\n          style: {\n            flex: 1,\n            padding: '12px 16px',\n            border: 'none',\n            background: activeTab === tab.key ? 'white' : 'transparent',\n            cursor: 'pointer',\n            fontSize: '14px',\n            fontWeight: '500',\n            color: activeTab === tab.key ? '#007bff' : '#666',\n            transition: 'all 0.3s ease',\n            borderBottom: activeTab === tab.key ? '2px solid #007bff' : 'none'\n          },\n          children: tab.label\n        }, tab.key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '24px',\n          minHeight: '400px'\n        },\n        children: [activeTab === 'elements' && renderElements(), activeTab === 'flows' && renderFlows(), activeTab === 'scripts' && renderScripts()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 287,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleResults, \"mM0FuaXHVMsFFVYqcToeSUTnM1o=\");\n_c = SimpleResults;\nexport default SimpleResults;\nvar _c;\n$RefreshReg$(_c, \"SimpleResults\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "SimpleResults", "result", "_s", "activeTab", "setActiveTab", "downloadScript", "content", "filename", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "replace", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "style", "background", "padding", "borderRadius", "boxShadow", "textAlign", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "task_id", "analysisResult", "elements", "flows", "automation_scripts", "renderElements", "length", "display", "gap", "gridTemplateColumns", "map", "element", "border", "justifyContent", "alignItems", "marginBottom", "fontWeight", "color", "name", "fontSize", "element_type", "description", "text_content", "position", "area", "visual_features", "shape", "functionality", "marginTop", "confidence_score", "toFixed", "id", "renderFlows", "flexDirection", "flow", "index", "margin", "flow_name", "paddingLeft", "steps", "step", "stepIndex", "borderLeft", "action", "target_element", "expected_result", "success_criteria", "error_scenarios", "join", "renderScripts", "script", "script_name", "priority", "estimated_duration", "preconditions", "condition", "i", "test_steps", "marginRight", "action_type", "action_description", "visual_target", "validation_step", "validation_points", "point", "yaml_content", "onClick", "cursor", "overflow", "maxHeight", "fontFamily", "flexWrap", "status", "borderBottom", "key", "label", "tab", "flex", "transition", "minHeight", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/components/SimpleResults.js"], "sourcesContent": ["/**\n * 简化的结果展示组件\n */\nimport React, { useState } from 'react';\n\nconst SimpleResults = ({ result }) => {\n  const [activeTab, setActiveTab] = useState('elements');\n\n  // 下载脚本功能\n  const downloadScript = (content, filename) => {\n    const blob = new Blob([content], { type: 'text/yaml' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `${filename.replace(/[^a-zA-Z0-9\\u4e00-\\u9fa5]/g, '_')}.yaml`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  if (!result || !result.result) {\n    return (\n      <div style={{\n        background: 'white',\n        padding: '24px',\n        borderRadius: '12px',\n        boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n        textAlign: 'center'\n      }}>\n        <p>没有分析结果</p>\n      </div>\n    );\n  }\n\n  const { task_id, result: analysisResult } = result;\n  const { elements = [], flows = [], automation_scripts = [] } = analysisResult;\n\n  const renderElements = () => (\n    <div>\n      <h4>🎯 识别到的UI元素 ({elements.length}个)</h4>\n      <div style={{ display: 'grid', gap: '16px', gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))' }}>\n        {elements.map((element) => (\n          <div key={element.id} style={{\n            border: '1px solid #e9ecef',\n            borderRadius: '8px',\n            padding: '16px',\n            background: '#f8f9fa'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>\n              <span style={{ fontWeight: '600', color: '#333' }}>{element.name}</span>\n              <span style={{\n                background: '#007bff',\n                color: 'white',\n                padding: '2px 8px',\n                borderRadius: '12px',\n                fontSize: '12px'\n              }}>\n                {element.element_type}\n              </span>\n            </div>\n            <div style={{ color: '#666', marginBottom: '8px', fontSize: '14px' }}>\n              {element.description}\n            </div>\n            {element.text_content && (\n              <div style={{\n                background: '#e3f2fd',\n                padding: '8px',\n                borderRadius: '4px',\n                fontSize: '13px',\n                marginBottom: '8px'\n              }}>\n                文字内容: \"{element.text_content}\"\n              </div>\n            )}\n            <div style={{ fontSize: '13px' }}>\n              <div style={{ marginBottom: '4px' }}>\n                <strong>位置:</strong> {element.position.area}\n              </div>\n              <div style={{ marginBottom: '4px' }}>\n                <strong>视觉特征:</strong> {element.visual_features.color}, {element.visual_features.shape}\n              </div>\n              <div style={{ marginBottom: '4px' }}>\n                <strong>功能:</strong> {element.functionality}\n              </div>\n              <div style={{ textAlign: 'right', fontWeight: '500', color: '#28a745', marginTop: '8px' }}>\n                置信度: {(element.confidence_score * 100).toFixed(1)}%\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n\n  const renderFlows = () => (\n    <div>\n      <h4>🔗 交互流程 ({flows.length}个)</h4>\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>\n        {flows.map((flow, index) => (\n          <div key={index} style={{\n            border: '1px solid #e9ecef',\n            borderRadius: '8px',\n            padding: '20px',\n            background: '#f8f9fa'\n          }}>\n            <h5 style={{ margin: '0 0 8px 0', color: '#333' }}>{flow.flow_name}</h5>\n            <p style={{ color: '#666', marginBottom: '16px' }}>{flow.description}</p>\n            \n            <div style={{ marginBottom: '16px' }}>\n              <h6 style={{ margin: '0 0 8px 0', color: '#333' }}>操作步骤:</h6>\n              <ol style={{ margin: '0', paddingLeft: '20px' }}>\n                {flow.steps.map((step, stepIndex) => (\n                  <li key={stepIndex} style={{\n                    marginBottom: '12px',\n                    padding: '8px',\n                    background: 'white',\n                    borderRadius: '4px',\n                    borderLeft: '3px solid #007bff'\n                  }}>\n                    <div style={{ fontWeight: '500', color: '#333', marginBottom: '4px' }}>\n                      {step.action}\n                    </div>\n                    <div style={{ fontSize: '13px', color: '#666', marginBottom: '2px' }}>\n                      目标: {step.target_element}\n                    </div>\n                    <div style={{ fontSize: '13px', color: '#666' }}>\n                      预期结果: {step.expected_result}\n                    </div>\n                  </li>\n                ))}\n              </ol>\n            </div>\n            \n            <div style={{ fontSize: '13px' }}>\n              <div style={{ marginBottom: '8px' }}>\n                <strong>成功标准:</strong> {flow.success_criteria}\n              </div>\n              {flow.error_scenarios.length > 0 && (\n                <div>\n                  <strong>异常场景:</strong> {flow.error_scenarios.join(', ')}\n                </div>\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n\n  const renderScripts = () => (\n    <div>\n      <h4>📝 UI自动化测试脚本 ({automation_scripts.length}个)</h4>\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>\n        {automation_scripts.map((script, index) => (\n          <div key={index} style={{\n            border: '1px solid #e9ecef',\n            borderRadius: '8px',\n            padding: '20px',\n            background: '#f8f9fa'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '12px' }}>\n              <h5 style={{ margin: '0', color: '#333' }}>{script.script_name}</h5>\n              <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>\n                <span style={{\n                  padding: '2px 8px',\n                  borderRadius: '12px',\n                  fontSize: '12px',\n                  fontWeight: '500',\n                  background: script.priority === 'high' ? '#dc3545' : '#ffc107',\n                  color: script.priority === 'high' ? 'white' : '#212529'\n                }}>\n                  {script.priority}\n                </span>\n                <span style={{ fontSize: '12px', color: '#666' }}>\n                  {script.estimated_duration}\n                </span>\n              </div>\n            </div>\n\n            <p style={{ color: '#666', marginBottom: '16px' }}>{script.description}</p>\n            \n            {script.preconditions.length > 0 && (\n              <div style={{ marginBottom: '12px' }}>\n                <strong>前置条件:</strong>\n                <ul style={{ margin: '4px 0 0 0', paddingLeft: '20px' }}>\n                  {script.preconditions.map((condition, i) => (\n                    <li key={i} style={{ fontSize: '13px', color: '#666', marginBottom: '2px' }}>\n                      {condition}\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            )}\n\n            <div style={{ marginBottom: '12px' }}>\n              <strong>脚本步骤:</strong>\n              <ol style={{ margin: '4px 0 0 0', paddingLeft: '20px' }}>\n                {script.test_steps.map((step, stepIndex) => (\n                  <li key={stepIndex} style={{\n                    marginBottom: '12px',\n                    padding: '8px',\n                    background: 'white',\n                    borderRadius: '4px',\n                    borderLeft: '3px solid #007bff'\n                  }}>\n                    <div style={{ marginBottom: '4px' }}>\n                      <span style={{\n                        background: '#6c757d',\n                        color: 'white',\n                        padding: '2px 6px',\n                        borderRadius: '4px',\n                        fontSize: '11px',\n                        marginRight: '8px'\n                      }}>\n                        {step.action_type}\n                      </span>\n                      <span style={{ fontWeight: '500', color: '#333' }}>\n                        {step.action_description}\n                      </span>\n                    </div>\n                    <div style={{ fontSize: '13px', color: '#666', marginBottom: '2px' }}>\n                      目标: {step.visual_target}\n                    </div>\n                    <div style={{ fontSize: '13px', color: '#666', marginBottom: '2px' }}>\n                      预期: {step.expected_result}\n                    </div>\n                    <div style={{ fontSize: '13px', color: '#666' }}>\n                      验证: {step.validation_step}\n                    </div>\n                  </li>\n                ))}\n              </ol>\n            </div>\n\n            <div style={{ marginBottom: '16px' }}>\n              <strong>验证要点:</strong>\n              <ul style={{ margin: '4px 0 0 0', paddingLeft: '20px' }}>\n                {script.validation_points.map((point, i) => (\n                  <li key={i} style={{ fontSize: '13px', color: '#666', marginBottom: '2px' }}>\n                    {point}\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {script.yaml_content && (\n              <div>\n                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>\n                  <strong>MidScene.js YAML脚本:</strong>\n                  <button\n                    onClick={() => downloadScript(script.yaml_content, script.script_name)}\n                    style={{\n                      background: '#28a745',\n                      color: 'white',\n                      border: 'none',\n                      padding: '4px 8px',\n                      borderRadius: '4px',\n                      fontSize: '12px',\n                      cursor: 'pointer'\n                    }}\n                  >\n                    📥 下载脚本\n                  </button>\n                </div>\n                <pre style={{\n                  background: '#f8f9fa',\n                  border: '1px solid #e9ecef',\n                  borderRadius: '4px',\n                  padding: '12px',\n                  fontSize: '12px',\n                  overflow: 'auto',\n                  maxHeight: '300px',\n                  fontFamily: 'Monaco, Consolas, \"Courier New\", monospace'\n                }}>\n                  {script.yaml_content}\n                </pre>\n              </div>\n            )}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n\n  return (\n    <div style={{ \n      background: 'white', \n      padding: '24px', \n      borderRadius: '12px', \n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)'\n    }}>\n      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '24px', flexWrap: 'wrap', gap: '16px' }}>\n        <h3 style={{ margin: '0', color: '#333', fontSize: '24px', fontWeight: '600' }}>\n          📊 分析结果\n        </h3>\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', fontSize: '14px', color: '#666', textAlign: 'right' }}>\n          <span>任务ID: {task_id}</span>\n          <span>状态: {analysisResult.status}</span>\n        </div>\n      </div>\n\n      <div style={{ border: '1px solid #e9ecef', borderRadius: '8px', overflow: 'hidden' }}>\n        <div style={{ display: 'flex', background: '#f8f9fa', borderBottom: '1px solid #e9ecef' }}>\n          {[\n            { key: 'elements', label: `UI元素 (${elements.length})` },\n            { key: 'flows', label: `交互流程 (${flows.length})` },\n            { key: 'scripts', label: `自动化脚本 (${automation_scripts.length})` }\n          ].map(tab => (\n            <button\n              key={tab.key}\n              onClick={() => setActiveTab(tab.key)}\n              style={{\n                flex: 1,\n                padding: '12px 16px',\n                border: 'none',\n                background: activeTab === tab.key ? 'white' : 'transparent',\n                cursor: 'pointer',\n                fontSize: '14px',\n                fontWeight: '500',\n                color: activeTab === tab.key ? '#007bff' : '#666',\n                transition: 'all 0.3s ease',\n                borderBottom: activeTab === tab.key ? '2px solid #007bff' : 'none'\n              }}\n            >\n              {tab.label}\n            </button>\n          ))}\n        </div>\n\n        <div style={{ padding: '24px', minHeight: '400px' }}>\n          {activeTab === 'elements' && renderElements()}\n          {activeTab === 'flows' && renderFlows()}\n          {activeTab === 'scripts' && renderScripts()}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleResults;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EACpC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGP,QAAQ,CAAC,UAAU,CAAC;;EAEtD;EACA,MAAMQ,cAAc,GAAGA,CAACC,OAAO,EAAEC,QAAQ,KAAK;IAC5C,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,OAAO,CAAC,EAAE;MAAEI,IAAI,EAAE;IAAY,CAAC,CAAC;IACvD,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IACrC,MAAMM,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;IACZG,CAAC,CAACI,QAAQ,GAAG,GAAGX,QAAQ,CAACY,OAAO,CAAC,4BAA4B,EAAE,GAAG,CAAC,OAAO;IAC1EJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,CAAC,CAAC;IAC5BA,CAAC,CAACQ,KAAK,CAAC,CAAC;IACTP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,CAAC,CAAC;IAC5BF,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC;EAC1B,CAAC;EAED,IAAI,CAACV,MAAM,IAAI,CAACA,MAAM,CAACA,MAAM,EAAE;IAC7B,oBACEF,OAAA;MAAK0B,KAAK,EAAE;QACVC,UAAU,EAAE,OAAO;QACnBC,OAAO,EAAE,MAAM;QACfC,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE,4BAA4B;QACvCC,SAAS,EAAE;MACb,CAAE;MAAAC,QAAA,eACAhC,OAAA;QAAAgC,QAAA,EAAG;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,MAAM;IAAEC,OAAO;IAAEnC,MAAM,EAAEoC;EAAe,CAAC,GAAGpC,MAAM;EAClD,MAAM;IAAEqC,QAAQ,GAAG,EAAE;IAAEC,KAAK,GAAG,EAAE;IAAEC,kBAAkB,GAAG;EAAG,CAAC,GAAGH,cAAc;EAE7E,MAAMI,cAAc,GAAGA,CAAA,kBACrB1C,OAAA;IAAAgC,QAAA,gBACEhC,OAAA;MAAAgC,QAAA,GAAI,uDAAa,EAACO,QAAQ,CAACI,MAAM,EAAC,SAAE;IAAA;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACzCpC,OAAA;MAAK0B,KAAK,EAAE;QAAEkB,OAAO,EAAE,MAAM;QAAEC,GAAG,EAAE,MAAM;QAAEC,mBAAmB,EAAE;MAAwC,CAAE;MAAAd,QAAA,EACxGO,QAAQ,CAACQ,GAAG,CAAEC,OAAO,iBACpBhD,OAAA;QAAsB0B,KAAK,EAAE;UAC3BuB,MAAM,EAAE,mBAAmB;UAC3BpB,YAAY,EAAE,KAAK;UACnBD,OAAO,EAAE,MAAM;UACfD,UAAU,EAAE;QACd,CAAE;QAAAK,QAAA,gBACAhC,OAAA;UAAK0B,KAAK,EAAE;YAAEkB,OAAO,EAAE,MAAM;YAAEM,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAApB,QAAA,gBAC1GhC,OAAA;YAAM0B,KAAK,EAAE;cAAE2B,UAAU,EAAE,KAAK;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAtB,QAAA,EAAEgB,OAAO,CAACO;UAAI;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxEpC,OAAA;YAAM0B,KAAK,EAAE;cACXC,UAAU,EAAE,SAAS;cACrB2B,KAAK,EAAE,OAAO;cACd1B,OAAO,EAAE,SAAS;cAClBC,YAAY,EAAE,MAAM;cACpB2B,QAAQ,EAAE;YACZ,CAAE;YAAAxB,QAAA,EACCgB,OAAO,CAACS;UAAY;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNpC,OAAA;UAAK0B,KAAK,EAAE;YAAE4B,KAAK,EAAE,MAAM;YAAEF,YAAY,EAAE,KAAK;YAAEI,QAAQ,EAAE;UAAO,CAAE;UAAAxB,QAAA,EAClEgB,OAAO,CAACU;QAAW;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,EACLY,OAAO,CAACW,YAAY,iBACnB3D,OAAA;UAAK0B,KAAK,EAAE;YACVC,UAAU,EAAE,SAAS;YACrBC,OAAO,EAAE,KAAK;YACdC,YAAY,EAAE,KAAK;YACnB2B,QAAQ,EAAE,MAAM;YAChBJ,YAAY,EAAE;UAChB,CAAE;UAAApB,QAAA,GAAC,8BACM,EAACgB,OAAO,CAACW,YAAY,EAAC,IAC/B;QAAA;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,eACDpC,OAAA;UAAK0B,KAAK,EAAE;YAAE8B,QAAQ,EAAE;UAAO,CAAE;UAAAxB,QAAA,gBAC/BhC,OAAA;YAAK0B,KAAK,EAAE;cAAE0B,YAAY,EAAE;YAAM,CAAE;YAAApB,QAAA,gBAClChC,OAAA;cAAAgC,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACY,OAAO,CAACY,QAAQ,CAACC,IAAI;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACNpC,OAAA;YAAK0B,KAAK,EAAE;cAAE0B,YAAY,EAAE;YAAM,CAAE;YAAApB,QAAA,gBAClChC,OAAA;cAAAgC,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACY,OAAO,CAACc,eAAe,CAACR,KAAK,EAAC,IAAE,EAACN,OAAO,CAACc,eAAe,CAACC,KAAK;UAAA;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC,eACNpC,OAAA;YAAK0B,KAAK,EAAE;cAAE0B,YAAY,EAAE;YAAM,CAAE;YAAApB,QAAA,gBAClChC,OAAA;cAAAgC,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACY,OAAO,CAACgB,aAAa;UAAA;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACNpC,OAAA;YAAK0B,KAAK,EAAE;cAAEK,SAAS,EAAE,OAAO;cAAEsB,UAAU,EAAE,KAAK;cAAEC,KAAK,EAAE,SAAS;cAAEW,SAAS,EAAE;YAAM,CAAE;YAAAjC,QAAA,GAAC,sBACpF,EAAC,CAACgB,OAAO,CAACkB,gBAAgB,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GACpD;UAAA;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GA7CEY,OAAO,CAACoB,EAAE;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA8Cf,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMiC,WAAW,GAAGA,CAAA,kBAClBrE,OAAA;IAAAgC,QAAA,gBACEhC,OAAA;MAAAgC,QAAA,GAAI,yCAAS,EAACQ,KAAK,CAACG,MAAM,EAAC,SAAE;IAAA;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAClCpC,OAAA;MAAK0B,KAAK,EAAE;QAAEkB,OAAO,EAAE,MAAM;QAAE0B,aAAa,EAAE,QAAQ;QAAEzB,GAAG,EAAE;MAAO,CAAE;MAAAb,QAAA,EACnEQ,KAAK,CAACO,GAAG,CAAC,CAACwB,IAAI,EAAEC,KAAK,kBACrBxE,OAAA;QAAiB0B,KAAK,EAAE;UACtBuB,MAAM,EAAE,mBAAmB;UAC3BpB,YAAY,EAAE,KAAK;UACnBD,OAAO,EAAE,MAAM;UACfD,UAAU,EAAE;QACd,CAAE;QAAAK,QAAA,gBACAhC,OAAA;UAAI0B,KAAK,EAAE;YAAE+C,MAAM,EAAE,WAAW;YAAEnB,KAAK,EAAE;UAAO,CAAE;UAAAtB,QAAA,EAAEuC,IAAI,CAACG;QAAS;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxEpC,OAAA;UAAG0B,KAAK,EAAE;YAAE4B,KAAK,EAAE,MAAM;YAAEF,YAAY,EAAE;UAAO,CAAE;UAAApB,QAAA,EAAEuC,IAAI,CAACb;QAAW;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEzEpC,OAAA;UAAK0B,KAAK,EAAE;YAAE0B,YAAY,EAAE;UAAO,CAAE;UAAApB,QAAA,gBACnChC,OAAA;YAAI0B,KAAK,EAAE;cAAE+C,MAAM,EAAE,WAAW;cAAEnB,KAAK,EAAE;YAAO,CAAE;YAAAtB,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7DpC,OAAA;YAAI0B,KAAK,EAAE;cAAE+C,MAAM,EAAE,GAAG;cAAEE,WAAW,EAAE;YAAO,CAAE;YAAA3C,QAAA,EAC7CuC,IAAI,CAACK,KAAK,CAAC7B,GAAG,CAAC,CAAC8B,IAAI,EAAEC,SAAS,kBAC9B9E,OAAA;cAAoB0B,KAAK,EAAE;gBACzB0B,YAAY,EAAE,MAAM;gBACpBxB,OAAO,EAAE,KAAK;gBACdD,UAAU,EAAE,OAAO;gBACnBE,YAAY,EAAE,KAAK;gBACnBkD,UAAU,EAAE;cACd,CAAE;cAAA/C,QAAA,gBACAhC,OAAA;gBAAK0B,KAAK,EAAE;kBAAE2B,UAAU,EAAE,KAAK;kBAAEC,KAAK,EAAE,MAAM;kBAAEF,YAAY,EAAE;gBAAM,CAAE;gBAAApB,QAAA,EACnE6C,IAAI,CAACG;cAAM;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNpC,OAAA;gBAAK0B,KAAK,EAAE;kBAAE8B,QAAQ,EAAE,MAAM;kBAAEF,KAAK,EAAE,MAAM;kBAAEF,YAAY,EAAE;gBAAM,CAAE;gBAAApB,QAAA,GAAC,gBAChE,EAAC6C,IAAI,CAACI,cAAc;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eACNpC,OAAA;gBAAK0B,KAAK,EAAE;kBAAE8B,QAAQ,EAAE,MAAM;kBAAEF,KAAK,EAAE;gBAAO,CAAE;gBAAAtB,QAAA,GAAC,4BACzC,EAAC6C,IAAI,CAACK,eAAe;cAAA;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA,GAfC0C,SAAS;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBd,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENpC,OAAA;UAAK0B,KAAK,EAAE;YAAE8B,QAAQ,EAAE;UAAO,CAAE;UAAAxB,QAAA,gBAC/BhC,OAAA;YAAK0B,KAAK,EAAE;cAAE0B,YAAY,EAAE;YAAM,CAAE;YAAApB,QAAA,gBAClChC,OAAA;cAAAgC,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACmC,IAAI,CAACY,gBAAgB;UAAA;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,EACLmC,IAAI,CAACa,eAAe,CAACzC,MAAM,GAAG,CAAC,iBAC9B3C,OAAA;YAAAgC,QAAA,gBACEhC,OAAA;cAAAgC,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACmC,IAAI,CAACa,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC;UAAA;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA,GA3CEoC,KAAK;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4CV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMkD,aAAa,GAAGA,CAAA,kBACpBtF,OAAA;IAAAgC,QAAA,gBACEhC,OAAA;MAAAgC,QAAA,GAAI,6DAAc,EAACS,kBAAkB,CAACE,MAAM,EAAC,SAAE;IAAA;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpDpC,OAAA;MAAK0B,KAAK,EAAE;QAAEkB,OAAO,EAAE,MAAM;QAAE0B,aAAa,EAAE,QAAQ;QAAEzB,GAAG,EAAE;MAAO,CAAE;MAAAb,QAAA,EACnES,kBAAkB,CAACM,GAAG,CAAC,CAACwC,MAAM,EAAEf,KAAK,kBACpCxE,OAAA;QAAiB0B,KAAK,EAAE;UACtBuB,MAAM,EAAE,mBAAmB;UAC3BpB,YAAY,EAAE,KAAK;UACnBD,OAAO,EAAE,MAAM;UACfD,UAAU,EAAE;QACd,CAAE;QAAAK,QAAA,gBACAhC,OAAA;UAAK0B,KAAK,EAAE;YAAEkB,OAAO,EAAE,MAAM;YAAEM,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,YAAY;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAApB,QAAA,gBAC/GhC,OAAA;YAAI0B,KAAK,EAAE;cAAE+C,MAAM,EAAE,GAAG;cAAEnB,KAAK,EAAE;YAAO,CAAE;YAAAtB,QAAA,EAAEuD,MAAM,CAACC;UAAW;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpEpC,OAAA;YAAK0B,KAAK,EAAE;cAAEkB,OAAO,EAAE,MAAM;cAAEC,GAAG,EAAE,KAAK;cAAEM,UAAU,EAAE;YAAS,CAAE;YAAAnB,QAAA,gBAChEhC,OAAA;cAAM0B,KAAK,EAAE;gBACXE,OAAO,EAAE,SAAS;gBAClBC,YAAY,EAAE,MAAM;gBACpB2B,QAAQ,EAAE,MAAM;gBAChBH,UAAU,EAAE,KAAK;gBACjB1B,UAAU,EAAE4D,MAAM,CAACE,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;gBAC9DnC,KAAK,EAAEiC,MAAM,CAACE,QAAQ,KAAK,MAAM,GAAG,OAAO,GAAG;cAChD,CAAE;cAAAzD,QAAA,EACCuD,MAAM,CAACE;YAAQ;cAAAxD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACPpC,OAAA;cAAM0B,KAAK,EAAE;gBAAE8B,QAAQ,EAAE,MAAM;gBAAEF,KAAK,EAAE;cAAO,CAAE;cAAAtB,QAAA,EAC9CuD,MAAM,CAACG;YAAkB;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpC,OAAA;UAAG0B,KAAK,EAAE;YAAE4B,KAAK,EAAE,MAAM;YAAEF,YAAY,EAAE;UAAO,CAAE;UAAApB,QAAA,EAAEuD,MAAM,CAAC7B;QAAW;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAE1EmD,MAAM,CAACI,aAAa,CAAChD,MAAM,GAAG,CAAC,iBAC9B3C,OAAA;UAAK0B,KAAK,EAAE;YAAE0B,YAAY,EAAE;UAAO,CAAE;UAAApB,QAAA,gBACnChC,OAAA;YAAAgC,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtBpC,OAAA;YAAI0B,KAAK,EAAE;cAAE+C,MAAM,EAAE,WAAW;cAAEE,WAAW,EAAE;YAAO,CAAE;YAAA3C,QAAA,EACrDuD,MAAM,CAACI,aAAa,CAAC5C,GAAG,CAAC,CAAC6C,SAAS,EAAEC,CAAC,kBACrC7F,OAAA;cAAY0B,KAAK,EAAE;gBAAE8B,QAAQ,EAAE,MAAM;gBAAEF,KAAK,EAAE,MAAM;gBAAEF,YAAY,EAAE;cAAM,CAAE;cAAApB,QAAA,EACzE4D;YAAS,GADHC,CAAC;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEN,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACN,eAEDpC,OAAA;UAAK0B,KAAK,EAAE;YAAE0B,YAAY,EAAE;UAAO,CAAE;UAAApB,QAAA,gBACnChC,OAAA;YAAAgC,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtBpC,OAAA;YAAI0B,KAAK,EAAE;cAAE+C,MAAM,EAAE,WAAW;cAAEE,WAAW,EAAE;YAAO,CAAE;YAAA3C,QAAA,EACrDuD,MAAM,CAACO,UAAU,CAAC/C,GAAG,CAAC,CAAC8B,IAAI,EAAEC,SAAS,kBACrC9E,OAAA;cAAoB0B,KAAK,EAAE;gBACzB0B,YAAY,EAAE,MAAM;gBACpBxB,OAAO,EAAE,KAAK;gBACdD,UAAU,EAAE,OAAO;gBACnBE,YAAY,EAAE,KAAK;gBACnBkD,UAAU,EAAE;cACd,CAAE;cAAA/C,QAAA,gBACAhC,OAAA;gBAAK0B,KAAK,EAAE;kBAAE0B,YAAY,EAAE;gBAAM,CAAE;gBAAApB,QAAA,gBAClChC,OAAA;kBAAM0B,KAAK,EAAE;oBACXC,UAAU,EAAE,SAAS;oBACrB2B,KAAK,EAAE,OAAO;oBACd1B,OAAO,EAAE,SAAS;oBAClBC,YAAY,EAAE,KAAK;oBACnB2B,QAAQ,EAAE,MAAM;oBAChBuC,WAAW,EAAE;kBACf,CAAE;kBAAA/D,QAAA,EACC6C,IAAI,CAACmB;gBAAW;kBAAA/D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACPpC,OAAA;kBAAM0B,KAAK,EAAE;oBAAE2B,UAAU,EAAE,KAAK;oBAAEC,KAAK,EAAE;kBAAO,CAAE;kBAAAtB,QAAA,EAC/C6C,IAAI,CAACoB;gBAAkB;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNpC,OAAA;gBAAK0B,KAAK,EAAE;kBAAE8B,QAAQ,EAAE,MAAM;kBAAEF,KAAK,EAAE,MAAM;kBAAEF,YAAY,EAAE;gBAAM,CAAE;gBAAApB,QAAA,GAAC,gBAChE,EAAC6C,IAAI,CAACqB,aAAa;cAAA;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACNpC,OAAA;gBAAK0B,KAAK,EAAE;kBAAE8B,QAAQ,EAAE,MAAM;kBAAEF,KAAK,EAAE,MAAM;kBAAEF,YAAY,EAAE;gBAAM,CAAE;gBAAApB,QAAA,GAAC,gBAChE,EAAC6C,IAAI,CAACK,eAAe;cAAA;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACNpC,OAAA;gBAAK0B,KAAK,EAAE;kBAAE8B,QAAQ,EAAE,MAAM;kBAAEF,KAAK,EAAE;gBAAO,CAAE;gBAAAtB,QAAA,GAAC,gBAC3C,EAAC6C,IAAI,CAACsB,eAAe;cAAA;gBAAAlE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC;YAAA,GA9BC0C,SAAS;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+Bd,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENpC,OAAA;UAAK0B,KAAK,EAAE;YAAE0B,YAAY,EAAE;UAAO,CAAE;UAAApB,QAAA,gBACnChC,OAAA;YAAAgC,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtBpC,OAAA;YAAI0B,KAAK,EAAE;cAAE+C,MAAM,EAAE,WAAW;cAAEE,WAAW,EAAE;YAAO,CAAE;YAAA3C,QAAA,EACrDuD,MAAM,CAACa,iBAAiB,CAACrD,GAAG,CAAC,CAACsD,KAAK,EAAER,CAAC,kBACrC7F,OAAA;cAAY0B,KAAK,EAAE;gBAAE8B,QAAQ,EAAE,MAAM;gBAAEF,KAAK,EAAE,MAAM;gBAAEF,YAAY,EAAE;cAAM,CAAE;cAAApB,QAAA,EACzEqE;YAAK,GADCR,CAAC;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEN,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EAELmD,MAAM,CAACe,YAAY,iBAClBtG,OAAA;UAAAgC,QAAA,gBACEhC,OAAA;YAAK0B,KAAK,EAAE;cAAEkB,OAAO,EAAE,MAAM;cAAEM,cAAc,EAAE,eAAe;cAAEC,UAAU,EAAE,QAAQ;cAAEC,YAAY,EAAE;YAAM,CAAE;YAAApB,QAAA,gBAC1GhC,OAAA;cAAAgC,QAAA,EAAQ;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpCpC,OAAA;cACEuG,OAAO,EAAEA,CAAA,KAAMjG,cAAc,CAACiF,MAAM,CAACe,YAAY,EAAEf,MAAM,CAACC,WAAW,CAAE;cACvE9D,KAAK,EAAE;gBACLC,UAAU,EAAE,SAAS;gBACrB2B,KAAK,EAAE,OAAO;gBACdL,MAAM,EAAE,MAAM;gBACdrB,OAAO,EAAE,SAAS;gBAClBC,YAAY,EAAE,KAAK;gBACnB2B,QAAQ,EAAE,MAAM;gBAChBgD,MAAM,EAAE;cACV,CAAE;cAAAxE,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNpC,OAAA;YAAK0B,KAAK,EAAE;cACVC,UAAU,EAAE,SAAS;cACrBsB,MAAM,EAAE,mBAAmB;cAC3BpB,YAAY,EAAE,KAAK;cACnBD,OAAO,EAAE,MAAM;cACf4B,QAAQ,EAAE,MAAM;cAChBiD,QAAQ,EAAE,MAAM;cAChBC,SAAS,EAAE,OAAO;cAClBC,UAAU,EAAE;YACd,CAAE;YAAA3E,QAAA,EACCuD,MAAM,CAACe;UAAY;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA,GA3HOoC,KAAK;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4HV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACEpC,OAAA;IAAK0B,KAAK,EAAE;MACVC,UAAU,EAAE,OAAO;MACnBC,OAAO,EAAE,MAAM;MACfC,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE;IACb,CAAE;IAAAE,QAAA,gBACAhC,OAAA;MAAK0B,KAAK,EAAE;QAAEkB,OAAO,EAAE,MAAM;QAAEM,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,YAAY;QAAEC,YAAY,EAAE,MAAM;QAAEwD,QAAQ,EAAE,MAAM;QAAE/D,GAAG,EAAE;MAAO,CAAE;MAAAb,QAAA,gBAC9IhC,OAAA;QAAI0B,KAAK,EAAE;UAAE+C,MAAM,EAAE,GAAG;UAAEnB,KAAK,EAAE,MAAM;UAAEE,QAAQ,EAAE,MAAM;UAAEH,UAAU,EAAE;QAAM,CAAE;QAAArB,QAAA,EAAC;MAEhF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLpC,OAAA;QAAK0B,KAAK,EAAE;UAAEkB,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,GAAG,EAAE,KAAK;UAAEW,QAAQ,EAAE,MAAM;UAAEF,KAAK,EAAE,MAAM;UAAEvB,SAAS,EAAE;QAAQ,CAAE;QAAAC,QAAA,gBACxHhC,OAAA;UAAAgC,QAAA,GAAM,kBAAM,EAACK,OAAO;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5BpC,OAAA;UAAAgC,QAAA,GAAM,gBAAI,EAACM,cAAc,CAACuE,MAAM;QAAA;UAAA5E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpC,OAAA;MAAK0B,KAAK,EAAE;QAAEuB,MAAM,EAAE,mBAAmB;QAAEpB,YAAY,EAAE,KAAK;QAAE4E,QAAQ,EAAE;MAAS,CAAE;MAAAzE,QAAA,gBACnFhC,OAAA;QAAK0B,KAAK,EAAE;UAAEkB,OAAO,EAAE,MAAM;UAAEjB,UAAU,EAAE,SAAS;UAAEmF,YAAY,EAAE;QAAoB,CAAE;QAAA9E,QAAA,EACvF,CACC;UAAE+E,GAAG,EAAE,UAAU;UAAEC,KAAK,EAAE,SAASzE,QAAQ,CAACI,MAAM;QAAI,CAAC,EACvD;UAAEoE,GAAG,EAAE,OAAO;UAAEC,KAAK,EAAE,SAASxE,KAAK,CAACG,MAAM;QAAI,CAAC,EACjD;UAAEoE,GAAG,EAAE,SAAS;UAAEC,KAAK,EAAE,UAAUvE,kBAAkB,CAACE,MAAM;QAAI,CAAC,CAClE,CAACI,GAAG,CAACkE,GAAG,iBACPjH,OAAA;UAEEuG,OAAO,EAAEA,CAAA,KAAMlG,YAAY,CAAC4G,GAAG,CAACF,GAAG,CAAE;UACrCrF,KAAK,EAAE;YACLwF,IAAI,EAAE,CAAC;YACPtF,OAAO,EAAE,WAAW;YACpBqB,MAAM,EAAE,MAAM;YACdtB,UAAU,EAAEvB,SAAS,KAAK6G,GAAG,CAACF,GAAG,GAAG,OAAO,GAAG,aAAa;YAC3DP,MAAM,EAAE,SAAS;YACjBhD,QAAQ,EAAE,MAAM;YAChBH,UAAU,EAAE,KAAK;YACjBC,KAAK,EAAElD,SAAS,KAAK6G,GAAG,CAACF,GAAG,GAAG,SAAS,GAAG,MAAM;YACjDI,UAAU,EAAE,eAAe;YAC3BL,YAAY,EAAE1G,SAAS,KAAK6G,GAAG,CAACF,GAAG,GAAG,mBAAmB,GAAG;UAC9D,CAAE;UAAA/E,QAAA,EAEDiF,GAAG,CAACD;QAAK,GAfLC,GAAG,CAACF,GAAG;UAAA9E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBN,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENpC,OAAA;QAAK0B,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEwF,SAAS,EAAE;QAAQ,CAAE;QAAApF,QAAA,GACjD5B,SAAS,KAAK,UAAU,IAAIsC,cAAc,CAAC,CAAC,EAC5CtC,SAAS,KAAK,OAAO,IAAIiE,WAAW,CAAC,CAAC,EACtCjE,SAAS,KAAK,SAAS,IAAIkF,aAAa,CAAC,CAAC;MAAA;QAAArD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CA7UIF,aAAa;AAAAoH,EAAA,GAAbpH,aAAa;AA+UnB,eAAeA,aAAa;AAAC,IAAAoH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}