{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\n/**\n * UI自动化分析平台 - 主应用组件\n */\nimport React, { useState } from 'react';\nimport Sidebar from './components/Sidebar';\nimport AnalysisPage from './pages/AnalysisPage';\nimport HistoryPage from './pages/HistoryPage';\nimport SettingsPage from './pages/SettingsPage';\nimport AboutPage from './pages/AboutPage';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [currentPage, setCurrentPage] = useState('analysis');\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const renderCurrentPage = () => {\n    switch (currentPage) {\n      case 'analysis':\n        return /*#__PURE__*/_jsxDEV(AnalysisPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 16\n        }, this);\n      case 'history':\n        return /*#__PURE__*/_jsxDEV(HistoryPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 16\n        }, this);\n      case 'settings':\n        return /*#__PURE__*/_jsxDEV(SettingsPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 16\n        }, this);\n      case 'about':\n        return /*#__PURE__*/_jsxDEV(AboutPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(AnalysisPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      currentPage: currentPage,\n      onPageChange: setCurrentPage,\n      collapsed: sidebarCollapsed,\n      onToggleCollapse: () => setSidebarCollapsed(!sidebarCollapsed)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `app-main ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-container\",\n        children: renderCurrentPage()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"zRr3cXc7LdArnNe3kpAVd6tJ9Nk=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "Sidebar", "AnalysisPage", "HistoryPage", "SettingsPage", "AboutPage", "jsxDEV", "_jsxDEV", "App", "_s", "currentPage", "setCurrentPage", "sidebarCollapsed", "setSidebarCollapsed", "renderCurrentPage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "onPageChange", "collapsed", "onToggleCollapse", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/App.js"], "sourcesContent": ["/**\n * UI自动化分析平台 - 主应用组件\n */\nimport React, { useState } from 'react';\nimport Sidebar from './components/Sidebar';\nimport AnalysisPage from './pages/AnalysisPage';\nimport HistoryPage from './pages/HistoryPage';\nimport SettingsPage from './pages/SettingsPage';\nimport AboutPage from './pages/AboutPage';\nimport './App.css';\n\nfunction App() {\n  const [currentPage, setCurrentPage] = useState('analysis');\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n\n  const renderCurrentPage = () => {\n    switch (currentPage) {\n      case 'analysis':\n        return <AnalysisPage />;\n      case 'history':\n        return <HistoryPage />;\n      case 'settings':\n        return <SettingsPage />;\n      case 'about':\n        return <AboutPage />;\n      default:\n        return <AnalysisPage />;\n    }\n  };\n\n  return (\n    <div className=\"app\">\n      <Sidebar\n        currentPage={currentPage}\n        onPageChange={setCurrentPage}\n        collapsed={sidebarCollapsed}\n        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}\n      />\n\n      <main className={`app-main ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>\n        <div className=\"page-container\">\n          {renderCurrentPage()}\n        </div>\n      </main>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAC,UAAU,CAAC;EAC1D,MAAM,CAACY,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAMc,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQJ,WAAW;MACjB,KAAK,UAAU;QACb,oBAAOH,OAAA,CAACL,YAAY;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB,KAAK,SAAS;QACZ,oBAAOX,OAAA,CAACJ,WAAW;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxB,KAAK,UAAU;QACb,oBAAOX,OAAA,CAACH,YAAY;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB,KAAK,OAAO;QACV,oBAAOX,OAAA,CAACF,SAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtB;QACE,oBAAOX,OAAA,CAACL,YAAY;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC3B;EACF,CAAC;EAED,oBACEX,OAAA;IAAKY,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBb,OAAA,CAACN,OAAO;MACNS,WAAW,EAAEA,WAAY;MACzBW,YAAY,EAAEV,cAAe;MAC7BW,SAAS,EAAEV,gBAAiB;MAC5BW,gBAAgB,EAAEA,CAAA,KAAMV,mBAAmB,CAAC,CAACD,gBAAgB;IAAE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CAAC,eAEFX,OAAA;MAAMY,SAAS,EAAE,YAAYP,gBAAgB,GAAG,mBAAmB,GAAG,EAAE,EAAG;MAAAQ,QAAA,eACzEb,OAAA;QAAKY,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BN,iBAAiB,CAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACT,EAAA,CAnCQD,GAAG;AAAAgB,EAAA,GAAHhB,GAAG;AAqCZ,eAAeA,GAAG;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}