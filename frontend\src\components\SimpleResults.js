/**
 * 简化的结果展示组件
 */
import React, { useState } from 'react';

const SimpleResults = ({ result }) => {
  const [activeTab, setActiveTab] = useState('elements');

  // 下载脚本功能
  const downloadScript = (content, filename) => {
    const blob = new Blob([content], { type: 'text/yaml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${filename.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')}.yaml`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (!result || !result.result) {
    return (
      <div style={{
        background: 'white',
        padding: '24px',
        borderRadius: '12px',
        boxShadow: '0 2px 12px rgba(0,0,0,0.1)',
        textAlign: 'center'
      }}>
        <p>没有分析结果</p>
      </div>
    );
  }

  const { task_id, result: analysisResult } = result;
  const { elements = [], flows = [], automation_scripts = [] } = analysisResult;

  const renderElements = () => (
    <div>
      <h4>🎯 识别到的UI元素 ({elements.length}个)</h4>
      <div style={{ display: 'grid', gap: '16px', gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))' }}>
        {elements.map((element) => (
          <div key={element.id} style={{
            border: '1px solid #e9ecef',
            borderRadius: '8px',
            padding: '16px',
            background: '#f8f9fa'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
              <span style={{ fontWeight: '600', color: '#333' }}>{element.name}</span>
              <span style={{
                background: '#007bff',
                color: 'white',
                padding: '2px 8px',
                borderRadius: '12px',
                fontSize: '12px'
              }}>
                {element.element_type}
              </span>
            </div>
            <div style={{ color: '#666', marginBottom: '8px', fontSize: '14px' }}>
              {element.description}
            </div>
            {element.text_content && (
              <div style={{
                background: '#e3f2fd',
                padding: '8px',
                borderRadius: '4px',
                fontSize: '13px',
                marginBottom: '8px'
              }}>
                文字内容: "{element.text_content}"
              </div>
            )}
            <div style={{ fontSize: '13px' }}>
              <div style={{ marginBottom: '4px' }}>
                <strong>位置:</strong> {element.position.area}
              </div>
              <div style={{ marginBottom: '4px' }}>
                <strong>视觉特征:</strong> {element.visual_features.color}, {element.visual_features.shape}
              </div>
              <div style={{ marginBottom: '4px' }}>
                <strong>功能:</strong> {element.functionality}
              </div>
              <div style={{ textAlign: 'right', fontWeight: '500', color: '#28a745', marginTop: '8px' }}>
                置信度: {(element.confidence_score * 100).toFixed(1)}%
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderFlows = () => (
    <div>
      <h4>🔗 交互流程 ({flows.length}个)</h4>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
        {flows.map((flow, index) => (
          <div key={index} style={{
            border: '1px solid #e9ecef',
            borderRadius: '8px',
            padding: '20px',
            background: '#f8f9fa'
          }}>
            <h5 style={{ margin: '0 0 8px 0', color: '#333' }}>{flow.flow_name}</h5>
            <p style={{ color: '#666', marginBottom: '16px' }}>{flow.description}</p>
            
            <div style={{ marginBottom: '16px' }}>
              <h6 style={{ margin: '0 0 8px 0', color: '#333' }}>操作步骤:</h6>
              <ol style={{ margin: '0', paddingLeft: '20px' }}>
                {flow.steps.map((step, stepIndex) => (
                  <li key={stepIndex} style={{
                    marginBottom: '12px',
                    padding: '8px',
                    background: 'white',
                    borderRadius: '4px',
                    borderLeft: '3px solid #007bff'
                  }}>
                    <div style={{ fontWeight: '500', color: '#333', marginBottom: '4px' }}>
                      {step.action}
                    </div>
                    <div style={{ fontSize: '13px', color: '#666', marginBottom: '2px' }}>
                      目标: {step.target_element}
                    </div>
                    <div style={{ fontSize: '13px', color: '#666' }}>
                      预期结果: {step.expected_result}
                    </div>
                  </li>
                ))}
              </ol>
            </div>
            
            <div style={{ fontSize: '13px' }}>
              <div style={{ marginBottom: '8px' }}>
                <strong>成功标准:</strong> {flow.success_criteria}
              </div>
              {flow.error_scenarios.length > 0 && (
                <div>
                  <strong>异常场景:</strong> {flow.error_scenarios.join(', ')}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderScripts = () => (
    <div>
      <h4>📝 UI自动化测试脚本 ({automation_scripts.length}个)</h4>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
        {automation_scripts.map((script, index) => (
          <div key={index} style={{
            border: '1px solid #e9ecef',
            borderRadius: '8px',
            padding: '20px',
            background: '#f8f9fa'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '12px' }}>
              <h5 style={{ margin: '0', color: '#333' }}>{script.script_name}</h5>
              <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                <span style={{
                  padding: '2px 8px',
                  borderRadius: '12px',
                  fontSize: '12px',
                  fontWeight: '500',
                  background: script.priority === 'high' ? '#dc3545' : '#ffc107',
                  color: script.priority === 'high' ? 'white' : '#212529'
                }}>
                  {script.priority}
                </span>
                <span style={{ fontSize: '12px', color: '#666' }}>
                  {script.estimated_duration}
                </span>
              </div>
            </div>

            <p style={{ color: '#666', marginBottom: '16px' }}>{script.description}</p>
            
            {script.preconditions.length > 0 && (
              <div style={{ marginBottom: '12px' }}>
                <strong>前置条件:</strong>
                <ul style={{ margin: '4px 0 0 0', paddingLeft: '20px' }}>
                  {script.preconditions.map((condition, i) => (
                    <li key={i} style={{ fontSize: '13px', color: '#666', marginBottom: '2px' }}>
                      {condition}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            <div style={{ marginBottom: '12px' }}>
              <strong>脚本步骤:</strong>
              <ol style={{ margin: '4px 0 0 0', paddingLeft: '20px' }}>
                {script.test_steps.map((step, stepIndex) => (
                  <li key={stepIndex} style={{
                    marginBottom: '12px',
                    padding: '8px',
                    background: 'white',
                    borderRadius: '4px',
                    borderLeft: '3px solid #007bff'
                  }}>
                    <div style={{ marginBottom: '4px' }}>
                      <span style={{
                        background: '#6c757d',
                        color: 'white',
                        padding: '2px 6px',
                        borderRadius: '4px',
                        fontSize: '11px',
                        marginRight: '8px'
                      }}>
                        {step.action_type}
                      </span>
                      <span style={{ fontWeight: '500', color: '#333' }}>
                        {step.action_description}
                      </span>
                    </div>
                    <div style={{ fontSize: '13px', color: '#666', marginBottom: '2px' }}>
                      目标: {step.visual_target}
                    </div>
                    <div style={{ fontSize: '13px', color: '#666', marginBottom: '2px' }}>
                      预期: {step.expected_result}
                    </div>
                    <div style={{ fontSize: '13px', color: '#666' }}>
                      验证: {step.validation_step}
                    </div>
                  </li>
                ))}
              </ol>
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>验证要点:</strong>
              <ul style={{ margin: '4px 0 0 0', paddingLeft: '20px' }}>
                {script.validation_points.map((point, i) => (
                  <li key={i} style={{ fontSize: '13px', color: '#666', marginBottom: '2px' }}>
                    {point}
                  </li>
                ))}
              </ul>
            </div>

            {script.yaml_content && (
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                  <strong>MidScene.js YAML脚本:</strong>
                  <button
                    onClick={() => downloadScript(script.yaml_content, script.script_name)}
                    style={{
                      background: '#28a745',
                      color: 'white',
                      border: 'none',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      cursor: 'pointer'
                    }}
                  >
                    📥 下载脚本
                  </button>
                </div>
                <pre style={{
                  background: '#f8f9fa',
                  border: '1px solid #e9ecef',
                  borderRadius: '4px',
                  padding: '12px',
                  fontSize: '12px',
                  overflow: 'auto',
                  maxHeight: '300px',
                  fontFamily: 'Monaco, Consolas, "Courier New", monospace'
                }}>
                  {script.yaml_content}
                </pre>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div style={{ 
      background: 'white', 
      padding: '24px', 
      borderRadius: '12px', 
      boxShadow: '0 2px 12px rgba(0,0,0,0.1)'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '24px', flexWrap: 'wrap', gap: '16px' }}>
        <h3 style={{ margin: '0', color: '#333', fontSize: '24px', fontWeight: '600' }}>
          📊 分析结果
        </h3>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', fontSize: '14px', color: '#666', textAlign: 'right' }}>
          <span>任务ID: {task_id}</span>
          <span>状态: {analysisResult.status}</span>
        </div>
      </div>

      <div style={{ border: '1px solid #e9ecef', borderRadius: '8px', overflow: 'hidden' }}>
        <div style={{ display: 'flex', background: '#f8f9fa', borderBottom: '1px solid #e9ecef' }}>
          {[
            { key: 'elements', label: `UI元素 (${elements.length})` },
            { key: 'flows', label: `交互流程 (${flows.length})` },
            { key: 'scripts', label: `自动化脚本 (${automation_scripts.length})` }
          ].map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              style={{
                flex: 1,
                padding: '12px 16px',
                border: 'none',
                background: activeTab === tab.key ? 'white' : 'transparent',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                color: activeTab === tab.key ? '#007bff' : '#666',
                transition: 'all 0.3s ease',
                borderBottom: activeTab === tab.key ? '2px solid #007bff' : 'none'
              }}
            >
              {tab.label}
            </button>
          ))}
        </div>

        <div style={{ padding: '24px', minHeight: '400px' }}>
          {activeTab === 'elements' && renderElements()}
          {activeTab === 'flows' && renderFlows()}
          {activeTab === 'scripts' && renderScripts()}
        </div>
      </div>
    </div>
  );
};

export default SimpleResults;
