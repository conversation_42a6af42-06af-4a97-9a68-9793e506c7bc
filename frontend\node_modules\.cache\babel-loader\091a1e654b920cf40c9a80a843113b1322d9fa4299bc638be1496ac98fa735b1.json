{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\pages\\\\AnalysisPage.js\",\n  _s = $RefreshSig$();\n/**\n * UI分析页面 - 主要的分析功能页面\n */\nimport React, { useState } from 'react';\nimport SimpleUpload from '../components/SimpleUpload';\nimport SimpleResults from '../components/SimpleResults';\nimport RealTimeAnalysis from '../components/RealTimeAnalysis';\nimport './AnalysisPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnalysisPage = () => {\n  _s();\n  const [appState, setAppState] = useState('upload'); // 'upload' | 'analyzing' | 'results'\n  const [currentTaskId, setCurrentTaskId] = useState('');\n  const [analysisResult, setAnalysisResult] = useState(null);\n  const [error, setError] = useState('');\n  const handleUploadSuccess = result => {\n    if (result.task_id) {\n      // 如果有任务ID，进入实时分析模式\n      setCurrentTaskId(result.task_id);\n      setAppState('analyzing');\n    } else {\n      // 如果直接返回结果，进入结果展示模式\n      setAnalysisResult(result);\n      setAppState('results');\n    }\n    setError('');\n  };\n  const handleUploadError = errorMessage => {\n    setError(errorMessage);\n  };\n  const handleAnalysisComplete = (taskData = null) => {\n    // 实时分析完成，设置最终结果\n    setAppState('results');\n    if (taskData) {\n      // 使用从API获取的真实数据\n      setAnalysisResult({\n        task_id: currentTaskId,\n        message: \"分析完成\",\n        result: taskData\n      });\n    } else {\n      // 使用默认数据\n      setAnalysisResult({\n        task_id: currentTaskId,\n        message: \"分析完成\",\n        result: {\n          status: \"completed\",\n          elements: [],\n          flows: [],\n          automation_scripts: []\n        }\n      });\n    }\n  };\n  const handleAnalysisError = errorMessage => {\n    setError(errorMessage);\n    setAppState('upload');\n  };\n  const handleReset = () => {\n    setAppState('upload');\n    setCurrentTaskId('');\n    setAnalysisResult(null);\n    setError('');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"analysis-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83D\\uDD0D UI\\u754C\\u9762\\u5206\\u6790\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\\uFF0C\\u4F7F\\u7528AI\\u667A\\u80FD\\u4F53\\u8FDB\\u884C\\u81EA\\u52A8\\u5316\\u5206\\u6790\\u548C\\u6D4B\\u8BD5\\u811A\\u672C\\u751F\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), appState !== 'upload' && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"reset-button\",\n          onClick: handleReset,\n          children: \"\\u2190 \\u91CD\\u65B0\\u5F00\\u59CB\\u5206\\u6790\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-banner\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"error-icon\",\n        children: \"\\u26A0\\uFE0F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"error-text\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"error-close\",\n        onClick: () => setError(''),\n        children: \"\\u2715\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-content\",\n      children: [appState === 'upload' && /*#__PURE__*/_jsxDEV(SimpleUpload, {\n        onUploadSuccess: handleUploadSuccess,\n        onUploadError: handleUploadError\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 11\n      }, this), appState === 'analyzing' && currentTaskId && /*#__PURE__*/_jsxDEV(RealTimeAnalysis, {\n        taskId: currentTaskId,\n        onAnalysisComplete: handleAnalysisComplete,\n        onAnalysisError: handleAnalysisError\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 11\n      }, this), appState === 'results' && analysisResult && /*#__PURE__*/_jsxDEV(SimpleResults, {\n        result: analysisResult\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_s(AnalysisPage, \"3TI9Vtr+WUqebxz+MEMIiBjCZ08=\");\n_c = AnalysisPage;\nexport default AnalysisPage;\nvar _c;\n$RefreshReg$(_c, \"AnalysisPage\");", "map": {"version": 3, "names": ["React", "useState", "SimpleUpload", "SimpleResults", "RealTimeAnalysis", "jsxDEV", "_jsxDEV", "AnalysisPage", "_s", "appState", "setAppState", "currentTaskId", "setCurrentTaskId", "analysisResult", "setAnalysisResult", "error", "setError", "handleUploadSuccess", "result", "task_id", "handleUploadError", "errorMessage", "handleAnalysisComplete", "taskData", "message", "status", "elements", "flows", "automation_scripts", "handleAnalysisError", "handleReset", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onUploadSuccess", "onUploadError", "taskId", "onAnalysisComplete", "onAnalysisError", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/pages/AnalysisPage.js"], "sourcesContent": ["/**\n * UI分析页面 - 主要的分析功能页面\n */\nimport React, { useState } from 'react';\nimport SimpleUpload from '../components/SimpleUpload';\nimport SimpleResults from '../components/SimpleResults';\nimport RealTimeAnalysis from '../components/RealTimeAnalysis';\nimport './AnalysisPage.css';\n\nconst AnalysisPage = () => {\n  const [appState, setAppState] = useState('upload'); // 'upload' | 'analyzing' | 'results'\n  const [currentTaskId, setCurrentTaskId] = useState('');\n  const [analysisResult, setAnalysisResult] = useState(null);\n  const [error, setError] = useState('');\n\n  const handleUploadSuccess = (result) => {\n    if (result.task_id) {\n      // 如果有任务ID，进入实时分析模式\n      setCurrentTaskId(result.task_id);\n      setAppState('analyzing');\n    } else {\n      // 如果直接返回结果，进入结果展示模式\n      setAnalysisResult(result);\n      setAppState('results');\n    }\n    setError('');\n  };\n\n  const handleUploadError = (errorMessage) => {\n    setError(errorMessage);\n  };\n\n  const handleAnalysisComplete = (taskData = null) => {\n    // 实时分析完成，设置最终结果\n    setAppState('results');\n\n    if (taskData) {\n      // 使用从API获取的真实数据\n      setAnalysisResult({\n        task_id: currentTaskId,\n        message: \"分析完成\",\n        result: taskData\n      });\n    } else {\n      // 使用默认数据\n      setAnalysisResult({\n        task_id: currentTaskId,\n        message: \"分析完成\",\n        result: {\n          status: \"completed\",\n          elements: [],\n          flows: [],\n          automation_scripts: []\n        }\n      });\n    }\n  };\n\n  const handleAnalysisError = (errorMessage) => {\n    setError(errorMessage);\n    setAppState('upload');\n  };\n\n  const handleReset = () => {\n    setAppState('upload');\n    setCurrentTaskId('');\n    setAnalysisResult(null);\n    setError('');\n  };\n\n  return (\n    <div className=\"analysis-page\">\n      {/* 页面头部 */}\n      <div className=\"page-header\">\n        <div className=\"header-content\">\n          <h1>🔍 UI界面分析</h1>\n          <p>上传UI界面截图，使用AI智能体进行自动化分析和测试脚本生成</p>\n          {appState !== 'upload' && (\n            <button className=\"reset-button\" onClick={handleReset}>\n              ← 重新开始分析\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* 错误提示 */}\n      {error && (\n        <div className=\"error-banner\">\n          <span className=\"error-icon\">⚠️</span>\n          <span className=\"error-text\">{error}</span>\n          <button className=\"error-close\" onClick={() => setError('')}>\n            ✕\n          </button>\n        </div>\n      )}\n\n      {/* 主要内容区域 */}\n      <div className=\"page-content\">\n        {appState === 'upload' && (\n          <SimpleUpload\n            onUploadSuccess={handleUploadSuccess}\n            onUploadError={handleUploadError}\n          />\n        )}\n\n        {appState === 'analyzing' && currentTaskId && (\n          <RealTimeAnalysis\n            taskId={currentTaskId}\n            onAnalysisComplete={handleAnalysisComplete}\n            onAnalysisError={handleAnalysisError}\n          />\n        )}\n\n        {appState === 'results' && analysisResult && (\n          <SimpleResults result={analysisResult} />\n        )}\n      </div>\n\n\n    </div>\n  );\n};\n\nexport default AnalysisPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACpD,MAAM,CAACU,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACY,cAAc,EAAEC,iBAAiB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMgB,mBAAmB,GAAIC,MAAM,IAAK;IACtC,IAAIA,MAAM,CAACC,OAAO,EAAE;MAClB;MACAP,gBAAgB,CAACM,MAAM,CAACC,OAAO,CAAC;MAChCT,WAAW,CAAC,WAAW,CAAC;IAC1B,CAAC,MAAM;MACL;MACAI,iBAAiB,CAACI,MAAM,CAAC;MACzBR,WAAW,CAAC,SAAS,CAAC;IACxB;IACAM,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMI,iBAAiB,GAAIC,YAAY,IAAK;IAC1CL,QAAQ,CAACK,YAAY,CAAC;EACxB,CAAC;EAED,MAAMC,sBAAsB,GAAGA,CAACC,QAAQ,GAAG,IAAI,KAAK;IAClD;IACAb,WAAW,CAAC,SAAS,CAAC;IAEtB,IAAIa,QAAQ,EAAE;MACZ;MACAT,iBAAiB,CAAC;QAChBK,OAAO,EAAER,aAAa;QACtBa,OAAO,EAAE,MAAM;QACfN,MAAM,EAAEK;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAT,iBAAiB,CAAC;QAChBK,OAAO,EAAER,aAAa;QACtBa,OAAO,EAAE,MAAM;QACfN,MAAM,EAAE;UACNO,MAAM,EAAE,WAAW;UACnBC,QAAQ,EAAE,EAAE;UACZC,KAAK,EAAE,EAAE;UACTC,kBAAkB,EAAE;QACtB;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAIR,YAAY,IAAK;IAC5CL,QAAQ,CAACK,YAAY,CAAC;IACtBX,WAAW,CAAC,QAAQ,CAAC;EACvB,CAAC;EAED,MAAMoB,WAAW,GAAGA,CAAA,KAAM;IACxBpB,WAAW,CAAC,QAAQ,CAAC;IACrBE,gBAAgB,CAAC,EAAE,CAAC;IACpBE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,oBACEV,OAAA;IAAKyB,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5B1B,OAAA;MAAKyB,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1B1B,OAAA;QAAKyB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B1B,OAAA;UAAA0B,QAAA,EAAI;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClB9B,OAAA;UAAA0B,QAAA,EAAG;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EACpC3B,QAAQ,KAAK,QAAQ,iBACpBH,OAAA;UAAQyB,SAAS,EAAC,cAAc;UAACM,OAAO,EAAEP,WAAY;UAAAE,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLrB,KAAK,iBACJT,OAAA;MAAKyB,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B1B,OAAA;QAAMyB,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtC9B,OAAA;QAAMyB,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAEjB;MAAK;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC3C9B,OAAA;QAAQyB,SAAS,EAAC,aAAa;QAACM,OAAO,EAAEA,CAAA,KAAMrB,QAAQ,CAAC,EAAE,CAAE;QAAAgB,QAAA,EAAC;MAE7D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAGD9B,OAAA;MAAKyB,SAAS,EAAC,cAAc;MAAAC,QAAA,GAC1BvB,QAAQ,KAAK,QAAQ,iBACpBH,OAAA,CAACJ,YAAY;QACXoC,eAAe,EAAErB,mBAAoB;QACrCsB,aAAa,EAAEnB;MAAkB;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CACF,EAEA3B,QAAQ,KAAK,WAAW,IAAIE,aAAa,iBACxCL,OAAA,CAACF,gBAAgB;QACfoC,MAAM,EAAE7B,aAAc;QACtB8B,kBAAkB,EAAEnB,sBAAuB;QAC3CoB,eAAe,EAAEb;MAAoB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CACF,EAEA3B,QAAQ,KAAK,SAAS,IAAII,cAAc,iBACvCP,OAAA,CAACH,aAAa;QAACe,MAAM,EAAEL;MAAe;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACzC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGH,CAAC;AAEV,CAAC;AAAC5B,EAAA,CAhHID,YAAY;AAAAoC,EAAA,GAAZpC,YAAY;AAkHlB,eAAeA,YAAY;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}