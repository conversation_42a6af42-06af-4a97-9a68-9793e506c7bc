"""
UI自动化分析平台 - FastAPI 主应用
"""
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
import asyncio
import json
import uuid
from typing import Optional
import os
from pathlib import Path

from app.core.celery_app import celery_app
from app.core.task_manager import TaskManager
from app.api.upload import router as upload_router
from app.api.stream import router as stream_router

# 创建FastAPI应用
app = FastAPI(
    title="UI自动化分析平台",
    description="基于AI智能体的UI界面分析和自动化测试用例生成平台",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],  # React开发服务器
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建上传目录
UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)

# 任务管理器实例
task_manager = TaskManager()

# 注册路由
app.include_router(upload_router, prefix="/api/v1", tags=["upload"])
app.include_router(stream_router, prefix="/api/v1", tags=["stream"])

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "UI自动化分析平台 API",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "celery_active": celery_app.control.inspect().active() is not None
    }

@app.post("/api/v1/analyze")
async def analyze_ui(
    image_file: UploadFile = File(...),
    description: str = Form(...)
):
    """
    UI分析接口
    接收图片和描述，返回任务ID
    """
    # 验证文件类型
    if not image_file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="只支持图片文件")
    
    # 生成任务ID
    task_id = str(uuid.uuid4())
    
    # 保存上传的文件
    file_path = UPLOAD_DIR / f"{task_id}_{image_file.filename}"
    with open(file_path, "wb") as buffer:
        content = await image_file.read()
        buffer.write(content)
    
    # 启动异步分析任务
    from app.core.analysis_pipeline import start_analysis_pipeline
    celery_app.send_task(
        'app.core.analysis_pipeline.start_analysis_pipeline',
        args=[task_id, str(file_path), description]
    )
    
    return {
        "task_id": task_id,
        "message": "分析任务已启动",
        "stream_url": f"/api/v1/stream/{task_id}"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
