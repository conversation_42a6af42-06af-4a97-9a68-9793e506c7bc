"""
UI自动化分析平台 - FastAPI 主应用
"""
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
import asyncio
import json
import uuid
from typing import Optional
import os
from pathlib import Path

from app.core.sse_manager import sse_manager
from app.api.stream import router as stream_router
from app.api.upload import router as upload_router
from app.api.scripts import router as scripts_router

# 创建FastAPI应用
app = FastAPI(
    title="UI自动化分析平台",
    description="基于AI智能体的UI界面分析和自动化测试用例生成平台",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],  # React开发服务器
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建上传目录
UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)

# 注册路由
app.include_router(upload_router, prefix="/api/v1", tags=["upload"])
app.include_router(stream_router, prefix="/api/v1", tags=["stream"])
app.include_router(scripts_router, prefix="/api/v1", tags=["scripts"])

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "UI自动化分析平台 API",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "message": "UI自动化分析平台运行正常"
    }

@app.post("/api/v1/analyze")
async def analyze_ui(
    image_file: UploadFile = File(...),
    description: str = Form(...)
):
    """
    UI分析接口（演示版本）
    接收图片和描述，返回任务ID
    """
    # 验证文件类型
    if not image_file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="只支持图片文件")

    # 生成任务ID
    task_id = str(uuid.uuid4())

    # 保存上传的文件
    file_path = UPLOAD_DIR / f"{task_id}_{image_file.filename}"
    with open(file_path, "wb") as buffer:
        content = await image_file.read()
        buffer.write(content)

    # 模拟分析结果
    mock_result = {
        "task_id": task_id,
        "status": "completed",
        "elements": [
            {
                "id": "element_001",
                "name": "登录按钮",
                "element_type": "button",
                "description": "页面右上角的蓝色登录按钮",
                "text_content": "登录",
                "position": {"area": "页面右上角", "relative_to": "导航栏"},
                "visual_features": {"color": "蓝色背景，白色文字", "size": "中等", "shape": "圆角矩形"},
                "functionality": "用户登录入口",
                "interaction_state": "可点击",
                "confidence_score": 0.95
            }
        ],
        "flows": [
            {
                "flow_name": "用户登录流程",
                "description": "用户通过用户名密码登录系统",
                "steps": [
                    {
                        "step_id": 1,
                        "action": "点击登录按钮",
                        "target_element": "登录按钮",
                        "expected_result": "显示登录表单"
                    }
                ],
                "success_criteria": "成功登录并跳转到主页",
                "error_scenarios": ["用户名密码错误"]
            }
        ],
        "automation_scripts": [
            {
                "script_name": "登录功能自动化脚本",
                "description": "验证用户登录功能的UI自动化测试脚本",
                "priority": "high",
                "estimated_duration": "30秒",
                "preconditions": ["用户未登录"],
                "test_steps": [
                    {
                        "step_id": 1,
                        "action_type": "aiTap",
                        "action_description": "点击登录按钮",
                        "visual_target": "页面右上角的蓝色登录按钮",
                        "expected_result": "显示登录表单",
                        "validation_step": "检查登录表单是否显示"
                    }
                ],
                "validation_points": ["登录按钮可点击", "表单正确显示"],
                "yaml_content": """# 登录功能自动化脚本
# 验证用户登录功能的UI自动化测试脚本
# 生成时间: 2024-12-17T10:30:00
# 生成器: UI自动化分析平台

name: 登录功能自动化脚本
description: 验证用户登录功能的UI自动化测试脚本

steps:
  - name: 点击登录按钮
    action: aiTap
    locate: 页面右上角的蓝色登录按钮，它是一个圆角矩形按钮，白色文字'登录'，位于搜索框右侧
    expect: 显示登录表单弹窗或跳转到登录页面

  - name: 输入用户名
    action: aiInput
    locate: 用户名输入框，标签显示'用户名'或'邮箱'，位于登录表单的顶部，是一个白色背景的矩形输入框
    value: <EMAIL>
    expect: 输入框显示邮箱地址，光标位于输入内容后

  - name: 输入密码
    action: aiInput
    locate: 密码输入框，标签显示'密码'，位于用户名输入框下方，输入时显示为圆点或星号
    value: Test123456
    expect: 密码框显示遮蔽字符

  - name: 提交登录
    action: aiTap
    locate: 登录表单底部的'登录'或'提交'按钮，通常为蓝色或绿色背景，可能显示'登录'、'提交'或'Sign In'文字
    expect: 开始登录验证过程

  - name: 验证登录成功
    action: aiAssert
    locate: 界面显示登录成功的标识，如用户头像、欢迎信息，或者跳转到主页面显示用户相关内容
    expect: 登录成功，用户进入已登录状态"""
            }
        ]
    }

    return {
        "task_id": task_id,
        "message": "分析完成（演示模式）",
        "result": mock_result
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
