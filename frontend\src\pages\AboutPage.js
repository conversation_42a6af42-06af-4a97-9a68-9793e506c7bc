/**
 * 关于页面
 */
import React from 'react';

const AboutPage = () => {
  const features = [
    {
      icon: '🔍',
      title: 'UI元素识别',
      description: '基于AI-TARS技术，精确识别界面中的各种UI元素，包括按钮、输入框、链接等'
    },
    {
      icon: '🔄',
      title: '交互流程分析',
      description: '智能分析用户操作路径，识别关键交互节点，构建完整的用户行为流程'
    },
    {
      icon: '📝',
      title: '自动化脚本生成',
      description: '基于MidScene.js规范，自动生成可执行的UI自动化测试脚本'
    },
    {
      icon: '⚡',
      title: '实时进度展示',
      description: '基于SSE技术的实时进度推送，让您随时了解分析状态'
    },
    {
      icon: '🤖',
      title: '多智能体协作',
      description: '三个专业智能体协同工作，确保分析结果的准确性和完整性'
    },
    {
      icon: '📊',
      title: '结果可视化',
      description: '直观的结果展示界面，支持脚本预览、编辑和下载'
    }
  ];

  const techStack = [
    { name: 'FastAPI', description: '高性能Python Web框架', category: '后端' },
    { name: 'React', description: '现代化前端框架', category: '前端' },
    { name: 'Server-Sent Events', description: '实时数据推送', category: '通信' },
    { name: 'AI智能体', description: '基于大语言模型的智能分析', category: 'AI' },
    { name: 'MidScene.js', description: 'UI自动化测试框架', category: '测试' },
    { name: 'UI-TARS', description: 'UI元素识别技术', category: 'AI' }
  ];

  return (
    <div className="about-page">
      <div className="page-header">
        <h1>ℹ️ 关于平台</h1>
        <p>了解UI自动化分析平台的功能特性和技术架构</p>
      </div>

      <div className="page-content">
        {/* 平台介绍 */}
        <section className="intro-section">
          <div className="intro-content">
            <h2>🤖 UI自动化分析平台</h2>
            <p className="intro-text">
              这是一个基于AI智能体的UI界面分析和自动化测试脚本生成平台。
              通过上传UI界面截图，平台能够自动识别界面元素、分析交互流程，
              并生成符合MidScene.js规范的自动化测试脚本。
            </p>
            <div className="version-info">
              <span className="version-badge">v1.0.0</span>
              <span className="build-info">Build 2024.12.17</span>
            </div>
          </div>
        </section>

        {/* 核心功能 */}
        <section className="features-section">
          <h2>✨ 核心功能</h2>
          <div className="features-grid">
            {features.map((feature, index) => (
              <div key={index} className="feature-card">
                <div className="feature-icon">{feature.icon}</div>
                <h3>{feature.title}</h3>
                <p>{feature.description}</p>
              </div>
            ))}
          </div>
        </section>

        {/* 技术栈 */}
        <section className="tech-section">
          <h2>🛠️ 技术栈</h2>
          <div className="tech-grid">
            {techStack.map((tech, index) => (
              <div key={index} className="tech-item">
                <div className="tech-header">
                  <span className="tech-name">{tech.name}</span>
                  <span className="tech-category">{tech.category}</span>
                </div>
                <p className="tech-desc">{tech.description}</p>
              </div>
            ))}
          </div>
        </section>

        {/* 使用说明 */}
        <section className="usage-section">
          <h2>📖 使用说明</h2>
          <div className="usage-steps">
            <div className="step">
              <div className="step-number">1</div>
              <div className="step-content">
                <h3>上传截图</h3>
                <p>在分析页面上传UI界面截图，支持PNG、JPG等格式</p>
              </div>
            </div>
            <div className="step">
              <div className="step-number">2</div>
              <div className="step-content">
                <h3>描述功能</h3>
                <p>简要描述界面的主要功能，帮助AI更好地理解界面用途</p>
              </div>
            </div>
            <div className="step">
              <div className="step-number">3</div>
              <div className="step-content">
                <h3>实时分析</h3>
                <p>观看三个智能体协同工作，实时展示分析进度</p>
              </div>
            </div>
            <div className="step">
              <div className="step-number">4</div>
              <div className="step-content">
                <h3>获取结果</h3>
                <p>查看分析结果，下载生成的自动化测试脚本</p>
              </div>
            </div>
          </div>
        </section>

        {/* 联系信息 */}
        <section className="contact-section">
          <h2>📞 联系我们</h2>
          <div className="contact-info">
            <div className="contact-item">
              <strong>开发团队：</strong>
              <span>UI自动化分析平台开发组</span>
            </div>
            <div className="contact-item">
              <strong>技术支持：</strong>
              <span><EMAIL></span>
            </div>
            <div className="contact-item">
              <strong>项目地址：</strong>
              <span>https://github.com/ui-automation/platform</span>
            </div>
          </div>
        </section>
      </div>

      <style>{`
        .about-page {
          min-height: 100vh;
          background: #f8f9fa;
        }

        .page-header {
          background: white;
          border-bottom: 1px solid #e9ecef;
          padding: 24px 0;
          margin-bottom: 24px;
        }

        .page-header h1 {
          margin: 0 0 8px 0;
          color: #333;
          font-size: 28px;
          font-weight: 600;
          max-width: 1200px;
          margin-left: auto;
          margin-right: auto;
          padding: 0 24px;
        }

        .page-header p {
          margin: 0;
          color: #666;
          font-size: 16px;
          max-width: 1200px;
          margin-left: auto;
          margin-right: auto;
          padding: 0 24px;
        }

        .page-content {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 24px;
        }

        section {
          background: white;
          border-radius: 12px;
          padding: 32px;
          margin-bottom: 24px;
          box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }

        section h2 {
          margin: 0 0 24px 0;
          color: #333;
          font-size: 24px;
          font-weight: 600;
        }

        .intro-content h2 {
          margin-bottom: 16px;
        }

        .intro-text {
          font-size: 16px;
          line-height: 1.6;
          color: #666;
          margin-bottom: 20px;
        }

        .version-info {
          display: flex;
          gap: 12px;
          align-items: center;
        }

        .version-badge {
          background: #007bff;
          color: white;
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 500;
        }

        .build-info {
          color: #666;
          font-size: 14px;
        }

        .features-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 20px;
        }

        .feature-card {
          padding: 20px;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          text-align: center;
          transition: all 0.2s ease;
        }

        .feature-card:hover {
          border-color: #007bff;
          box-shadow: 0 4px 12px rgba(0,123,255,0.15);
        }

        .feature-icon {
          font-size: 32px;
          margin-bottom: 12px;
        }

        .feature-card h3 {
          margin: 0 0 8px 0;
          color: #333;
          font-size: 16px;
          font-weight: 600;
        }

        .feature-card p {
          margin: 0;
          color: #666;
          font-size: 14px;
          line-height: 1.5;
        }

        .tech-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 16px;
        }

        .tech-item {
          padding: 16px;
          border: 1px solid #e9ecef;
          border-radius: 8px;
        }

        .tech-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
        }

        .tech-name {
          font-weight: 600;
          color: #333;
        }

        .tech-category {
          background: #f8f9fa;
          color: #666;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
        }

        .tech-desc {
          margin: 0;
          color: #666;
          font-size: 14px;
        }

        .usage-steps {
          display: grid;
          gap: 20px;
        }

        .step {
          display: flex;
          gap: 16px;
          align-items: flex-start;
        }

        .step-number {
          width: 32px;
          height: 32px;
          background: #007bff;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          flex-shrink: 0;
        }

        .step-content h3 {
          margin: 0 0 4px 0;
          color: #333;
          font-size: 16px;
          font-weight: 600;
        }

        .step-content p {
          margin: 0;
          color: #666;
          font-size: 14px;
          line-height: 1.5;
        }

        .contact-info {
          display: grid;
          gap: 12px;
        }

        .contact-item {
          display: flex;
          gap: 12px;
          align-items: center;
        }

        .contact-item strong {
          min-width: 100px;
          color: #333;
        }

        .contact-item span {
          color: #666;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .page-header h1,
          .page-header p,
          .page-content {
            padding: 0 16px;
          }

          section {
            padding: 20px;
          }

          .features-grid {
            grid-template-columns: 1fr;
          }

          .tech-grid {
            grid-template-columns: 1fr;
          }

          .version-info {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
          }
        }
      `}</style>
    </div>
  );
};

export default AboutPage;
