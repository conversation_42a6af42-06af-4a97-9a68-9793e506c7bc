{"ast": null, "code": "/**\n * 简化的结果展示组件\n */import React,{useState}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SimpleResults=_ref=>{let{result}=_ref;const[activeTab,setActiveTab]=useState('elements');// 下载脚本功能\nconst downloadScript=(content,filename)=>{const blob=new Blob([content],{type:'text/yaml'});const url=URL.createObjectURL(blob);const a=document.createElement('a');a.href=url;a.download=\"\".concat(filename.replace(/[^a-zA-Z0-9\\u4e00-\\u9fa5]/g,'_'),\".yaml\");document.body.appendChild(a);a.click();document.body.removeChild(a);URL.revokeObjectURL(url);};if(!result||!result.result){return/*#__PURE__*/_jsx(\"div\",{style:{background:'white',padding:'24px',borderRadius:'12px',boxShadow:'0 2px 12px rgba(0,0,0,0.1)',textAlign:'center'},children:/*#__PURE__*/_jsx(\"p\",{children:\"\\u6CA1\\u6709\\u5206\\u6790\\u7ED3\\u679C\"})});}const{task_id,result:analysisResult}=result;const{elements=[],flows=[],automation_scripts=[]}=analysisResult;const renderElements=()=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h4\",{children:[\"\\uD83C\\uDFAF \\u8BC6\\u522B\\u5230\\u7684UI\\u5143\\u7D20 (\",elements.length,\"\\u4E2A)\"]}),/*#__PURE__*/_jsx(\"div\",{style:{display:'grid',gap:'16px',gridTemplateColumns:'repeat(auto-fill, minmax(300px, 1fr))'},children:elements.map(element=>/*#__PURE__*/_jsxs(\"div\",{style:{border:'1px solid #e9ecef',borderRadius:'8px',padding:'16px',background:'#f8f9fa'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'8px'},children:[/*#__PURE__*/_jsx(\"span\",{style:{fontWeight:'600',color:'#333'},children:element.name}),/*#__PURE__*/_jsx(\"span\",{style:{background:'#007bff',color:'white',padding:'2px 8px',borderRadius:'12px',fontSize:'12px'},children:element.element_type})]}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#666',marginBottom:'8px',fontSize:'14px'},children:element.description}),element.text_content&&/*#__PURE__*/_jsxs(\"div\",{style:{background:'#e3f2fd',padding:'8px',borderRadius:'4px',fontSize:'13px',marginBottom:'8px'},children:[\"\\u6587\\u5B57\\u5185\\u5BB9: \\\"\",element.text_content,\"\\\"\"]}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'13px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'4px'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u4F4D\\u7F6E:\"}),\" \",element.position.area]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'4px'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u89C6\\u89C9\\u7279\\u5F81:\"}),\" \",element.visual_features.color,\", \",element.visual_features.shape]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'4px'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u529F\\u80FD:\"}),\" \",element.functionality]}),/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'right',fontWeight:'500',color:'#28a745',marginTop:'8px'},children:[\"\\u7F6E\\u4FE1\\u5EA6: \",(element.confidence_score*100).toFixed(1),\"%\"]})]})]},element.id))})]});const renderFlows=()=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h4\",{children:[\"\\uD83D\\uDD17 \\u4EA4\\u4E92\\u6D41\\u7A0B (\",flows.length,\"\\u4E2A)\"]}),/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',flexDirection:'column',gap:'20px'},children:flows.map((flow,index)=>/*#__PURE__*/_jsxs(\"div\",{style:{border:'1px solid #e9ecef',borderRadius:'8px',padding:'20px',background:'#f8f9fa'},children:[/*#__PURE__*/_jsx(\"h5\",{style:{margin:'0 0 8px 0',color:'#333'},children:flow.flow_name}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#666',marginBottom:'16px'},children:flow.description}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'16px'},children:[/*#__PURE__*/_jsx(\"h6\",{style:{margin:'0 0 8px 0',color:'#333'},children:\"\\u64CD\\u4F5C\\u6B65\\u9AA4:\"}),/*#__PURE__*/_jsx(\"ol\",{style:{margin:'0',paddingLeft:'20px'},children:flow.steps.map((step,stepIndex)=>/*#__PURE__*/_jsxs(\"li\",{style:{marginBottom:'12px',padding:'8px',background:'white',borderRadius:'4px',borderLeft:'3px solid #007bff'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'500',color:'#333',marginBottom:'4px'},children:step.action}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'13px',color:'#666',marginBottom:'2px'},children:[\"\\u76EE\\u6807: \",step.target_element]}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'13px',color:'#666'},children:[\"\\u9884\\u671F\\u7ED3\\u679C: \",step.expected_result]})]},stepIndex))})]}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'13px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'8px'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u6210\\u529F\\u6807\\u51C6:\"}),\" \",flow.success_criteria]}),flow.error_scenarios.length>0&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u5F02\\u5E38\\u573A\\u666F:\"}),\" \",flow.error_scenarios.join(', ')]})]})]},index))})]});const renderScripts=()=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"h4\",{children:[\"\\uD83D\\uDCDD UI\\u81EA\\u52A8\\u5316\\u6D4B\\u8BD5\\u811A\\u672C (\",automation_scripts.length,\"\\u4E2A)\"]}),/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',flexDirection:'column',gap:'20px'},children:automation_scripts.map((script,index)=>/*#__PURE__*/_jsxs(\"div\",{style:{border:'1px solid #e9ecef',borderRadius:'8px',padding:'20px',background:'#f8f9fa'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'flex-start',marginBottom:'12px'},children:[/*#__PURE__*/_jsx(\"h5\",{style:{margin:'0',color:'#333'},children:script.script_name}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'8px',alignItems:'center'},children:[/*#__PURE__*/_jsx(\"span\",{style:{padding:'2px 8px',borderRadius:'12px',fontSize:'12px',fontWeight:'500',background:script.priority==='high'?'#dc3545':'#ffc107',color:script.priority==='high'?'white':'#212529'},children:script.priority}),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'12px',color:'#666'},children:script.estimated_duration})]})]}),/*#__PURE__*/_jsx(\"p\",{style:{color:'#666',marginBottom:'16px'},children:script.description}),script.preconditions.length>0&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'12px'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u524D\\u7F6E\\u6761\\u4EF6:\"}),/*#__PURE__*/_jsx(\"ul\",{style:{margin:'4px 0 0 0',paddingLeft:'20px'},children:script.preconditions.map((condition,i)=>/*#__PURE__*/_jsx(\"li\",{style:{fontSize:'13px',color:'#666',marginBottom:'2px'},children:condition},i))})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'12px'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u811A\\u672C\\u6B65\\u9AA4:\"}),/*#__PURE__*/_jsx(\"ol\",{style:{margin:'4px 0 0 0',paddingLeft:'20px'},children:script.test_steps.map((step,stepIndex)=>/*#__PURE__*/_jsxs(\"li\",{style:{marginBottom:'12px',padding:'8px',background:'white',borderRadius:'4px',borderLeft:'3px solid #007bff'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'4px'},children:[/*#__PURE__*/_jsx(\"span\",{style:{background:'#6c757d',color:'white',padding:'2px 6px',borderRadius:'4px',fontSize:'11px',marginRight:'8px'},children:step.action_type}),/*#__PURE__*/_jsx(\"span\",{style:{fontWeight:'500',color:'#333'},children:step.action_description})]}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'13px',color:'#666',marginBottom:'2px'},children:[\"\\u76EE\\u6807: \",step.visual_target]}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'13px',color:'#666',marginBottom:'2px'},children:[\"\\u9884\\u671F: \",step.expected_result]}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'13px',color:'#666'},children:[\"\\u9A8C\\u8BC1: \",step.validation_step]})]},stepIndex))})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'16px'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u9A8C\\u8BC1\\u8981\\u70B9:\"}),/*#__PURE__*/_jsx(\"ul\",{style:{margin:'4px 0 0 0',paddingLeft:'20px'},children:script.validation_points.map((point,i)=>/*#__PURE__*/_jsx(\"li\",{style:{fontSize:'13px',color:'#666',marginBottom:'2px'},children:point},i))})]}),script.yaml_content&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'8px'},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"MidScene.js YAML\\u811A\\u672C:\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>downloadScript(script.yaml_content,script.script_name),style:{background:'#28a745',color:'white',border:'none',padding:'4px 8px',borderRadius:'4px',fontSize:'12px',cursor:'pointer'},children:\"\\uD83D\\uDCE5 \\u4E0B\\u8F7D\\u811A\\u672C\"})]}),/*#__PURE__*/_jsx(\"pre\",{style:{background:'#f8f9fa',border:'1px solid #e9ecef',borderRadius:'4px',padding:'12px',fontSize:'12px',overflow:'auto',maxHeight:'300px',fontFamily:'Monaco, Consolas, \"Courier New\", monospace'},children:script.yaml_content})]})]},index))})]});return/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',padding:'24px',borderRadius:'12px',boxShadow:'0 2px 12px rgba(0,0,0,0.1)'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'flex-start',marginBottom:'24px',flexWrap:'wrap',gap:'16px'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0',color:'#333',fontSize:'24px',fontWeight:'600'},children:\"\\uD83D\\uDCCA \\u5206\\u6790\\u7ED3\\u679C\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',gap:'4px',fontSize:'14px',color:'#666',textAlign:'right'},children:[/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u4EFB\\u52A1ID: \",task_id]}),/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u72B6\\u6001: \",analysisResult.status]})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{border:'1px solid #e9ecef',borderRadius:'8px',overflow:'hidden'},children:[/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',background:'#f8f9fa',borderBottom:'1px solid #e9ecef'},children:[{key:'elements',label:\"UI\\u5143\\u7D20 (\".concat(elements.length,\")\")},{key:'flows',label:\"\\u4EA4\\u4E92\\u6D41\\u7A0B (\".concat(flows.length,\")\")},{key:'scripts',label:\"\\u81EA\\u52A8\\u5316\\u811A\\u672C (\".concat(automation_scripts.length,\")\")}].map(tab=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setActiveTab(tab.key),style:{flex:1,padding:'12px 16px',border:'none',background:activeTab===tab.key?'white':'transparent',cursor:'pointer',fontSize:'14px',fontWeight:'500',color:activeTab===tab.key?'#007bff':'#666',transition:'all 0.3s ease',borderBottom:activeTab===tab.key?'2px solid #007bff':'none'},children:tab.label},tab.key))}),/*#__PURE__*/_jsxs(\"div\",{style:{padding:'24px',minHeight:'400px'},children:[activeTab==='elements'&&renderElements(),activeTab==='flows'&&renderFlows(),activeTab==='scripts'&&renderScripts()]})]})]});};export default SimpleResults;", "map": {"version": 3, "names": ["React", "useState", "jsx", "_jsx", "jsxs", "_jsxs", "SimpleResults", "_ref", "result", "activeTab", "setActiveTab", "downloadScript", "content", "filename", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "concat", "replace", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "style", "background", "padding", "borderRadius", "boxShadow", "textAlign", "children", "task_id", "analysisResult", "elements", "flows", "automation_scripts", "renderElements", "length", "display", "gap", "gridTemplateColumns", "map", "element", "border", "justifyContent", "alignItems", "marginBottom", "fontWeight", "color", "name", "fontSize", "element_type", "description", "text_content", "position", "area", "visual_features", "shape", "functionality", "marginTop", "confidence_score", "toFixed", "id", "renderFlows", "flexDirection", "flow", "index", "margin", "flow_name", "paddingLeft", "steps", "step", "stepIndex", "borderLeft", "action", "target_element", "expected_result", "success_criteria", "error_scenarios", "join", "renderScripts", "script", "script_name", "priority", "estimated_duration", "preconditions", "condition", "i", "test_steps", "marginRight", "action_type", "action_description", "visual_target", "validation_step", "validation_points", "point", "yaml_content", "onClick", "cursor", "overflow", "maxHeight", "fontFamily", "flexWrap", "status", "borderBottom", "key", "label", "tab", "flex", "transition", "minHeight"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/components/SimpleResults.js"], "sourcesContent": ["/**\n * 简化的结果展示组件\n */\nimport React, { useState } from 'react';\n\nconst SimpleResults = ({ result }) => {\n  const [activeTab, setActiveTab] = useState('elements');\n\n  // 下载脚本功能\n  const downloadScript = (content, filename) => {\n    const blob = new Blob([content], { type: 'text/yaml' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `${filename.replace(/[^a-zA-Z0-9\\u4e00-\\u9fa5]/g, '_')}.yaml`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  if (!result || !result.result) {\n    return (\n      <div style={{\n        background: 'white',\n        padding: '24px',\n        borderRadius: '12px',\n        boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n        textAlign: 'center'\n      }}>\n        <p>没有分析结果</p>\n      </div>\n    );\n  }\n\n  const { task_id, result: analysisResult } = result;\n  const { elements = [], flows = [], automation_scripts = [] } = analysisResult;\n\n  const renderElements = () => (\n    <div>\n      <h4>🎯 识别到的UI元素 ({elements.length}个)</h4>\n      <div style={{ display: 'grid', gap: '16px', gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))' }}>\n        {elements.map((element) => (\n          <div key={element.id} style={{\n            border: '1px solid #e9ecef',\n            borderRadius: '8px',\n            padding: '16px',\n            background: '#f8f9fa'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>\n              <span style={{ fontWeight: '600', color: '#333' }}>{element.name}</span>\n              <span style={{\n                background: '#007bff',\n                color: 'white',\n                padding: '2px 8px',\n                borderRadius: '12px',\n                fontSize: '12px'\n              }}>\n                {element.element_type}\n              </span>\n            </div>\n            <div style={{ color: '#666', marginBottom: '8px', fontSize: '14px' }}>\n              {element.description}\n            </div>\n            {element.text_content && (\n              <div style={{\n                background: '#e3f2fd',\n                padding: '8px',\n                borderRadius: '4px',\n                fontSize: '13px',\n                marginBottom: '8px'\n              }}>\n                文字内容: \"{element.text_content}\"\n              </div>\n            )}\n            <div style={{ fontSize: '13px' }}>\n              <div style={{ marginBottom: '4px' }}>\n                <strong>位置:</strong> {element.position.area}\n              </div>\n              <div style={{ marginBottom: '4px' }}>\n                <strong>视觉特征:</strong> {element.visual_features.color}, {element.visual_features.shape}\n              </div>\n              <div style={{ marginBottom: '4px' }}>\n                <strong>功能:</strong> {element.functionality}\n              </div>\n              <div style={{ textAlign: 'right', fontWeight: '500', color: '#28a745', marginTop: '8px' }}>\n                置信度: {(element.confidence_score * 100).toFixed(1)}%\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n\n  const renderFlows = () => (\n    <div>\n      <h4>🔗 交互流程 ({flows.length}个)</h4>\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>\n        {flows.map((flow, index) => (\n          <div key={index} style={{\n            border: '1px solid #e9ecef',\n            borderRadius: '8px',\n            padding: '20px',\n            background: '#f8f9fa'\n          }}>\n            <h5 style={{ margin: '0 0 8px 0', color: '#333' }}>{flow.flow_name}</h5>\n            <p style={{ color: '#666', marginBottom: '16px' }}>{flow.description}</p>\n            \n            <div style={{ marginBottom: '16px' }}>\n              <h6 style={{ margin: '0 0 8px 0', color: '#333' }}>操作步骤:</h6>\n              <ol style={{ margin: '0', paddingLeft: '20px' }}>\n                {flow.steps.map((step, stepIndex) => (\n                  <li key={stepIndex} style={{\n                    marginBottom: '12px',\n                    padding: '8px',\n                    background: 'white',\n                    borderRadius: '4px',\n                    borderLeft: '3px solid #007bff'\n                  }}>\n                    <div style={{ fontWeight: '500', color: '#333', marginBottom: '4px' }}>\n                      {step.action}\n                    </div>\n                    <div style={{ fontSize: '13px', color: '#666', marginBottom: '2px' }}>\n                      目标: {step.target_element}\n                    </div>\n                    <div style={{ fontSize: '13px', color: '#666' }}>\n                      预期结果: {step.expected_result}\n                    </div>\n                  </li>\n                ))}\n              </ol>\n            </div>\n            \n            <div style={{ fontSize: '13px' }}>\n              <div style={{ marginBottom: '8px' }}>\n                <strong>成功标准:</strong> {flow.success_criteria}\n              </div>\n              {flow.error_scenarios.length > 0 && (\n                <div>\n                  <strong>异常场景:</strong> {flow.error_scenarios.join(', ')}\n                </div>\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n\n  const renderScripts = () => (\n    <div>\n      <h4>📝 UI自动化测试脚本 ({automation_scripts.length}个)</h4>\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>\n        {automation_scripts.map((script, index) => (\n          <div key={index} style={{\n            border: '1px solid #e9ecef',\n            borderRadius: '8px',\n            padding: '20px',\n            background: '#f8f9fa'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '12px' }}>\n              <h5 style={{ margin: '0', color: '#333' }}>{script.script_name}</h5>\n              <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>\n                <span style={{\n                  padding: '2px 8px',\n                  borderRadius: '12px',\n                  fontSize: '12px',\n                  fontWeight: '500',\n                  background: script.priority === 'high' ? '#dc3545' : '#ffc107',\n                  color: script.priority === 'high' ? 'white' : '#212529'\n                }}>\n                  {script.priority}\n                </span>\n                <span style={{ fontSize: '12px', color: '#666' }}>\n                  {script.estimated_duration}\n                </span>\n              </div>\n            </div>\n\n            <p style={{ color: '#666', marginBottom: '16px' }}>{script.description}</p>\n            \n            {script.preconditions.length > 0 && (\n              <div style={{ marginBottom: '12px' }}>\n                <strong>前置条件:</strong>\n                <ul style={{ margin: '4px 0 0 0', paddingLeft: '20px' }}>\n                  {script.preconditions.map((condition, i) => (\n                    <li key={i} style={{ fontSize: '13px', color: '#666', marginBottom: '2px' }}>\n                      {condition}\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            )}\n\n            <div style={{ marginBottom: '12px' }}>\n              <strong>脚本步骤:</strong>\n              <ol style={{ margin: '4px 0 0 0', paddingLeft: '20px' }}>\n                {script.test_steps.map((step, stepIndex) => (\n                  <li key={stepIndex} style={{\n                    marginBottom: '12px',\n                    padding: '8px',\n                    background: 'white',\n                    borderRadius: '4px',\n                    borderLeft: '3px solid #007bff'\n                  }}>\n                    <div style={{ marginBottom: '4px' }}>\n                      <span style={{\n                        background: '#6c757d',\n                        color: 'white',\n                        padding: '2px 6px',\n                        borderRadius: '4px',\n                        fontSize: '11px',\n                        marginRight: '8px'\n                      }}>\n                        {step.action_type}\n                      </span>\n                      <span style={{ fontWeight: '500', color: '#333' }}>\n                        {step.action_description}\n                      </span>\n                    </div>\n                    <div style={{ fontSize: '13px', color: '#666', marginBottom: '2px' }}>\n                      目标: {step.visual_target}\n                    </div>\n                    <div style={{ fontSize: '13px', color: '#666', marginBottom: '2px' }}>\n                      预期: {step.expected_result}\n                    </div>\n                    <div style={{ fontSize: '13px', color: '#666' }}>\n                      验证: {step.validation_step}\n                    </div>\n                  </li>\n                ))}\n              </ol>\n            </div>\n\n            <div style={{ marginBottom: '16px' }}>\n              <strong>验证要点:</strong>\n              <ul style={{ margin: '4px 0 0 0', paddingLeft: '20px' }}>\n                {script.validation_points.map((point, i) => (\n                  <li key={i} style={{ fontSize: '13px', color: '#666', marginBottom: '2px' }}>\n                    {point}\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {script.yaml_content && (\n              <div>\n                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>\n                  <strong>MidScene.js YAML脚本:</strong>\n                  <button\n                    onClick={() => downloadScript(script.yaml_content, script.script_name)}\n                    style={{\n                      background: '#28a745',\n                      color: 'white',\n                      border: 'none',\n                      padding: '4px 8px',\n                      borderRadius: '4px',\n                      fontSize: '12px',\n                      cursor: 'pointer'\n                    }}\n                  >\n                    📥 下载脚本\n                  </button>\n                </div>\n                <pre style={{\n                  background: '#f8f9fa',\n                  border: '1px solid #e9ecef',\n                  borderRadius: '4px',\n                  padding: '12px',\n                  fontSize: '12px',\n                  overflow: 'auto',\n                  maxHeight: '300px',\n                  fontFamily: 'Monaco, Consolas, \"Courier New\", monospace'\n                }}>\n                  {script.yaml_content}\n                </pre>\n              </div>\n            )}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n\n  return (\n    <div style={{ \n      background: 'white', \n      padding: '24px', \n      borderRadius: '12px', \n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)'\n    }}>\n      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '24px', flexWrap: 'wrap', gap: '16px' }}>\n        <h3 style={{ margin: '0', color: '#333', fontSize: '24px', fontWeight: '600' }}>\n          📊 分析结果\n        </h3>\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', fontSize: '14px', color: '#666', textAlign: 'right' }}>\n          <span>任务ID: {task_id}</span>\n          <span>状态: {analysisResult.status}</span>\n        </div>\n      </div>\n\n      <div style={{ border: '1px solid #e9ecef', borderRadius: '8px', overflow: 'hidden' }}>\n        <div style={{ display: 'flex', background: '#f8f9fa', borderBottom: '1px solid #e9ecef' }}>\n          {[\n            { key: 'elements', label: `UI元素 (${elements.length})` },\n            { key: 'flows', label: `交互流程 (${flows.length})` },\n            { key: 'scripts', label: `自动化脚本 (${automation_scripts.length})` }\n          ].map(tab => (\n            <button\n              key={tab.key}\n              onClick={() => setActiveTab(tab.key)}\n              style={{\n                flex: 1,\n                padding: '12px 16px',\n                border: 'none',\n                background: activeTab === tab.key ? 'white' : 'transparent',\n                cursor: 'pointer',\n                fontSize: '14px',\n                fontWeight: '500',\n                color: activeTab === tab.key ? '#007bff' : '#666',\n                transition: 'all 0.3s ease',\n                borderBottom: activeTab === tab.key ? '2px solid #007bff' : 'none'\n              }}\n            >\n              {tab.label}\n            </button>\n          ))}\n        </div>\n\n        <div style={{ padding: '24px', minHeight: '400px' }}>\n          {activeTab === 'elements' && renderElements()}\n          {activeTab === 'flows' && renderFlows()}\n          {activeTab === 'scripts' && renderScripts()}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleResults;\n"], "mappings": "AAAA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,aAAa,CAAGC,IAAA,EAAgB,IAAf,CAAEC,MAAO,CAAC,CAAAD,IAAA,CAC/B,KAAM,CAACE,SAAS,CAAEC,YAAY,CAAC,CAAGT,QAAQ,CAAC,UAAU,CAAC,CAEtD;AACA,KAAM,CAAAU,cAAc,CAAGA,CAACC,OAAO,CAAEC,QAAQ,GAAK,CAC5C,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACH,OAAO,CAAC,CAAE,CAAEI,IAAI,CAAE,WAAY,CAAC,CAAC,CACvD,KAAM,CAAAC,GAAG,CAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC,CACrC,KAAM,CAAAM,CAAC,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACrCF,CAAC,CAACG,IAAI,CAAGN,GAAG,CACZG,CAAC,CAACI,QAAQ,IAAAC,MAAA,CAAMZ,QAAQ,CAACa,OAAO,CAAC,4BAA4B,CAAE,GAAG,CAAC,SAAO,CAC1EL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,CAAC,CAAC,CAC5BA,CAAC,CAACS,KAAK,CAAC,CAAC,CACTR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,CAAC,CAAC,CAC5BF,GAAG,CAACa,eAAe,CAACd,GAAG,CAAC,CAC1B,CAAC,CAED,GAAI,CAACT,MAAM,EAAI,CAACA,MAAM,CAACA,MAAM,CAAE,CAC7B,mBACEL,IAAA,QAAK6B,KAAK,CAAE,CACVC,UAAU,CAAE,OAAO,CACnBC,OAAO,CAAE,MAAM,CACfC,YAAY,CAAE,MAAM,CACpBC,SAAS,CAAE,4BAA4B,CACvCC,SAAS,CAAE,QACb,CAAE,CAAAC,QAAA,cACAnC,IAAA,MAAAmC,QAAA,CAAG,sCAAM,CAAG,CAAC,CACV,CAAC,CAEV,CAEA,KAAM,CAAEC,OAAO,CAAE/B,MAAM,CAAEgC,cAAe,CAAC,CAAGhC,MAAM,CAClD,KAAM,CAAEiC,QAAQ,CAAG,EAAE,CAAEC,KAAK,CAAG,EAAE,CAAEC,kBAAkB,CAAG,EAAG,CAAC,CAAGH,cAAc,CAE7E,KAAM,CAAAI,cAAc,CAAGA,CAAA,gBACrBvC,KAAA,QAAAiC,QAAA,eACEjC,KAAA,OAAAiC,QAAA,EAAI,uDAAa,CAACG,QAAQ,CAACI,MAAM,CAAC,SAAE,EAAI,CAAC,cACzC1C,IAAA,QAAK6B,KAAK,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAEC,GAAG,CAAE,MAAM,CAAEC,mBAAmB,CAAE,uCAAwC,CAAE,CAAAV,QAAA,CACxGG,QAAQ,CAACQ,GAAG,CAAEC,OAAO,eACpB7C,KAAA,QAAsB2B,KAAK,CAAE,CAC3BmB,MAAM,CAAE,mBAAmB,CAC3BhB,YAAY,CAAE,KAAK,CACnBD,OAAO,CAAE,MAAM,CACfD,UAAU,CAAE,SACd,CAAE,CAAAK,QAAA,eACAjC,KAAA,QAAK2B,KAAK,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAEM,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAAhB,QAAA,eAC1GnC,IAAA,SAAM6B,KAAK,CAAE,CAAEuB,UAAU,CAAE,KAAK,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAlB,QAAA,CAAEY,OAAO,CAACO,IAAI,CAAO,CAAC,cACxEtD,IAAA,SAAM6B,KAAK,CAAE,CACXC,UAAU,CAAE,SAAS,CACrBuB,KAAK,CAAE,OAAO,CACdtB,OAAO,CAAE,SAAS,CAClBC,YAAY,CAAE,MAAM,CACpBuB,QAAQ,CAAE,MACZ,CAAE,CAAApB,QAAA,CACCY,OAAO,CAACS,YAAY,CACjB,CAAC,EACJ,CAAC,cACNxD,IAAA,QAAK6B,KAAK,CAAE,CAAEwB,KAAK,CAAE,MAAM,CAAEF,YAAY,CAAE,KAAK,CAAEI,QAAQ,CAAE,MAAO,CAAE,CAAApB,QAAA,CAClEY,OAAO,CAACU,WAAW,CACjB,CAAC,CACLV,OAAO,CAACW,YAAY,eACnBxD,KAAA,QAAK2B,KAAK,CAAE,CACVC,UAAU,CAAE,SAAS,CACrBC,OAAO,CAAE,KAAK,CACdC,YAAY,CAAE,KAAK,CACnBuB,QAAQ,CAAE,MAAM,CAChBJ,YAAY,CAAE,KAChB,CAAE,CAAAhB,QAAA,EAAC,8BACM,CAACY,OAAO,CAACW,YAAY,CAAC,IAC/B,EAAK,CACN,cACDxD,KAAA,QAAK2B,KAAK,CAAE,CAAE0B,QAAQ,CAAE,MAAO,CAAE,CAAApB,QAAA,eAC/BjC,KAAA,QAAK2B,KAAK,CAAE,CAAEsB,YAAY,CAAE,KAAM,CAAE,CAAAhB,QAAA,eAClCnC,IAAA,WAAAmC,QAAA,CAAQ,eAAG,CAAQ,CAAC,IAAC,CAACY,OAAO,CAACY,QAAQ,CAACC,IAAI,EACxC,CAAC,cACN1D,KAAA,QAAK2B,KAAK,CAAE,CAAEsB,YAAY,CAAE,KAAM,CAAE,CAAAhB,QAAA,eAClCnC,IAAA,WAAAmC,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAACY,OAAO,CAACc,eAAe,CAACR,KAAK,CAAC,IAAE,CAACN,OAAO,CAACc,eAAe,CAACC,KAAK,EACnF,CAAC,cACN5D,KAAA,QAAK2B,KAAK,CAAE,CAAEsB,YAAY,CAAE,KAAM,CAAE,CAAAhB,QAAA,eAClCnC,IAAA,WAAAmC,QAAA,CAAQ,eAAG,CAAQ,CAAC,IAAC,CAACY,OAAO,CAACgB,aAAa,EACxC,CAAC,cACN7D,KAAA,QAAK2B,KAAK,CAAE,CAAEK,SAAS,CAAE,OAAO,CAAEkB,UAAU,CAAE,KAAK,CAAEC,KAAK,CAAE,SAAS,CAAEW,SAAS,CAAE,KAAM,CAAE,CAAA7B,QAAA,EAAC,sBACpF,CAAC,CAACY,OAAO,CAACkB,gBAAgB,CAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,GACpD,EAAK,CAAC,EACH,CAAC,GA7CEnB,OAAO,CAACoB,EA8Cb,CACN,CAAC,CACC,CAAC,EACH,CACN,CAED,KAAM,CAAAC,WAAW,CAAGA,CAAA,gBAClBlE,KAAA,QAAAiC,QAAA,eACEjC,KAAA,OAAAiC,QAAA,EAAI,yCAAS,CAACI,KAAK,CAACG,MAAM,CAAC,SAAE,EAAI,CAAC,cAClC1C,IAAA,QAAK6B,KAAK,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAE0B,aAAa,CAAE,QAAQ,CAAEzB,GAAG,CAAE,MAAO,CAAE,CAAAT,QAAA,CACnEI,KAAK,CAACO,GAAG,CAAC,CAACwB,IAAI,CAAEC,KAAK,gBACrBrE,KAAA,QAAiB2B,KAAK,CAAE,CACtBmB,MAAM,CAAE,mBAAmB,CAC3BhB,YAAY,CAAE,KAAK,CACnBD,OAAO,CAAE,MAAM,CACfD,UAAU,CAAE,SACd,CAAE,CAAAK,QAAA,eACAnC,IAAA,OAAI6B,KAAK,CAAE,CAAE2C,MAAM,CAAE,WAAW,CAAEnB,KAAK,CAAE,MAAO,CAAE,CAAAlB,QAAA,CAAEmC,IAAI,CAACG,SAAS,CAAK,CAAC,cACxEzE,IAAA,MAAG6B,KAAK,CAAE,CAAEwB,KAAK,CAAE,MAAM,CAAEF,YAAY,CAAE,MAAO,CAAE,CAAAhB,QAAA,CAAEmC,IAAI,CAACb,WAAW,CAAI,CAAC,cAEzEvD,KAAA,QAAK2B,KAAK,CAAE,CAAEsB,YAAY,CAAE,MAAO,CAAE,CAAAhB,QAAA,eACnCnC,IAAA,OAAI6B,KAAK,CAAE,CAAE2C,MAAM,CAAE,WAAW,CAAEnB,KAAK,CAAE,MAAO,CAAE,CAAAlB,QAAA,CAAC,2BAAK,CAAI,CAAC,cAC7DnC,IAAA,OAAI6B,KAAK,CAAE,CAAE2C,MAAM,CAAE,GAAG,CAAEE,WAAW,CAAE,MAAO,CAAE,CAAAvC,QAAA,CAC7CmC,IAAI,CAACK,KAAK,CAAC7B,GAAG,CAAC,CAAC8B,IAAI,CAAEC,SAAS,gBAC9B3E,KAAA,OAAoB2B,KAAK,CAAE,CACzBsB,YAAY,CAAE,MAAM,CACpBpB,OAAO,CAAE,KAAK,CACdD,UAAU,CAAE,OAAO,CACnBE,YAAY,CAAE,KAAK,CACnB8C,UAAU,CAAE,mBACd,CAAE,CAAA3C,QAAA,eACAnC,IAAA,QAAK6B,KAAK,CAAE,CAAEuB,UAAU,CAAE,KAAK,CAAEC,KAAK,CAAE,MAAM,CAAEF,YAAY,CAAE,KAAM,CAAE,CAAAhB,QAAA,CACnEyC,IAAI,CAACG,MAAM,CACT,CAAC,cACN7E,KAAA,QAAK2B,KAAK,CAAE,CAAE0B,QAAQ,CAAE,MAAM,CAAEF,KAAK,CAAE,MAAM,CAAEF,YAAY,CAAE,KAAM,CAAE,CAAAhB,QAAA,EAAC,gBAChE,CAACyC,IAAI,CAACI,cAAc,EACrB,CAAC,cACN9E,KAAA,QAAK2B,KAAK,CAAE,CAAE0B,QAAQ,CAAE,MAAM,CAAEF,KAAK,CAAE,MAAO,CAAE,CAAAlB,QAAA,EAAC,4BACzC,CAACyC,IAAI,CAACK,eAAe,EACxB,CAAC,GAfCJ,SAgBL,CACL,CAAC,CACA,CAAC,EACF,CAAC,cAEN3E,KAAA,QAAK2B,KAAK,CAAE,CAAE0B,QAAQ,CAAE,MAAO,CAAE,CAAApB,QAAA,eAC/BjC,KAAA,QAAK2B,KAAK,CAAE,CAAEsB,YAAY,CAAE,KAAM,CAAE,CAAAhB,QAAA,eAClCnC,IAAA,WAAAmC,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAACmC,IAAI,CAACY,gBAAgB,EAC1C,CAAC,CACLZ,IAAI,CAACa,eAAe,CAACzC,MAAM,CAAG,CAAC,eAC9BxC,KAAA,QAAAiC,QAAA,eACEnC,IAAA,WAAAmC,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAACmC,IAAI,CAACa,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC,EACpD,CACN,EACE,CAAC,GA3CEb,KA4CL,CACN,CAAC,CACC,CAAC,EACH,CACN,CAED,KAAM,CAAAc,aAAa,CAAGA,CAAA,gBACpBnF,KAAA,QAAAiC,QAAA,eACEjC,KAAA,OAAAiC,QAAA,EAAI,6DAAc,CAACK,kBAAkB,CAACE,MAAM,CAAC,SAAE,EAAI,CAAC,cACpD1C,IAAA,QAAK6B,KAAK,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAE0B,aAAa,CAAE,QAAQ,CAAEzB,GAAG,CAAE,MAAO,CAAE,CAAAT,QAAA,CACnEK,kBAAkB,CAACM,GAAG,CAAC,CAACwC,MAAM,CAAEf,KAAK,gBACpCrE,KAAA,QAAiB2B,KAAK,CAAE,CACtBmB,MAAM,CAAE,mBAAmB,CAC3BhB,YAAY,CAAE,KAAK,CACnBD,OAAO,CAAE,MAAM,CACfD,UAAU,CAAE,SACd,CAAE,CAAAK,QAAA,eACAjC,KAAA,QAAK2B,KAAK,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAEM,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,YAAY,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAhB,QAAA,eAC/GnC,IAAA,OAAI6B,KAAK,CAAE,CAAE2C,MAAM,CAAE,GAAG,CAAEnB,KAAK,CAAE,MAAO,CAAE,CAAAlB,QAAA,CAAEmD,MAAM,CAACC,WAAW,CAAK,CAAC,cACpErF,KAAA,QAAK2B,KAAK,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAEC,GAAG,CAAE,KAAK,CAAEM,UAAU,CAAE,QAAS,CAAE,CAAAf,QAAA,eAChEnC,IAAA,SAAM6B,KAAK,CAAE,CACXE,OAAO,CAAE,SAAS,CAClBC,YAAY,CAAE,MAAM,CACpBuB,QAAQ,CAAE,MAAM,CAChBH,UAAU,CAAE,KAAK,CACjBtB,UAAU,CAAEwD,MAAM,CAACE,QAAQ,GAAK,MAAM,CAAG,SAAS,CAAG,SAAS,CAC9DnC,KAAK,CAAEiC,MAAM,CAACE,QAAQ,GAAK,MAAM,CAAG,OAAO,CAAG,SAChD,CAAE,CAAArD,QAAA,CACCmD,MAAM,CAACE,QAAQ,CACZ,CAAC,cACPxF,IAAA,SAAM6B,KAAK,CAAE,CAAE0B,QAAQ,CAAE,MAAM,CAAEF,KAAK,CAAE,MAAO,CAAE,CAAAlB,QAAA,CAC9CmD,MAAM,CAACG,kBAAkB,CACtB,CAAC,EACJ,CAAC,EACH,CAAC,cAENzF,IAAA,MAAG6B,KAAK,CAAE,CAAEwB,KAAK,CAAE,MAAM,CAAEF,YAAY,CAAE,MAAO,CAAE,CAAAhB,QAAA,CAAEmD,MAAM,CAAC7B,WAAW,CAAI,CAAC,CAE1E6B,MAAM,CAACI,aAAa,CAAChD,MAAM,CAAG,CAAC,eAC9BxC,KAAA,QAAK2B,KAAK,CAAE,CAAEsB,YAAY,CAAE,MAAO,CAAE,CAAAhB,QAAA,eACnCnC,IAAA,WAAAmC,QAAA,CAAQ,2BAAK,CAAQ,CAAC,cACtBnC,IAAA,OAAI6B,KAAK,CAAE,CAAE2C,MAAM,CAAE,WAAW,CAAEE,WAAW,CAAE,MAAO,CAAE,CAAAvC,QAAA,CACrDmD,MAAM,CAACI,aAAa,CAAC5C,GAAG,CAAC,CAAC6C,SAAS,CAAEC,CAAC,gBACrC5F,IAAA,OAAY6B,KAAK,CAAE,CAAE0B,QAAQ,CAAE,MAAM,CAAEF,KAAK,CAAE,MAAM,CAAEF,YAAY,CAAE,KAAM,CAAE,CAAAhB,QAAA,CACzEwD,SAAS,EADHC,CAEL,CACL,CAAC,CACA,CAAC,EACF,CACN,cAED1F,KAAA,QAAK2B,KAAK,CAAE,CAAEsB,YAAY,CAAE,MAAO,CAAE,CAAAhB,QAAA,eACnCnC,IAAA,WAAAmC,QAAA,CAAQ,2BAAK,CAAQ,CAAC,cACtBnC,IAAA,OAAI6B,KAAK,CAAE,CAAE2C,MAAM,CAAE,WAAW,CAAEE,WAAW,CAAE,MAAO,CAAE,CAAAvC,QAAA,CACrDmD,MAAM,CAACO,UAAU,CAAC/C,GAAG,CAAC,CAAC8B,IAAI,CAAEC,SAAS,gBACrC3E,KAAA,OAAoB2B,KAAK,CAAE,CACzBsB,YAAY,CAAE,MAAM,CACpBpB,OAAO,CAAE,KAAK,CACdD,UAAU,CAAE,OAAO,CACnBE,YAAY,CAAE,KAAK,CACnB8C,UAAU,CAAE,mBACd,CAAE,CAAA3C,QAAA,eACAjC,KAAA,QAAK2B,KAAK,CAAE,CAAEsB,YAAY,CAAE,KAAM,CAAE,CAAAhB,QAAA,eAClCnC,IAAA,SAAM6B,KAAK,CAAE,CACXC,UAAU,CAAE,SAAS,CACrBuB,KAAK,CAAE,OAAO,CACdtB,OAAO,CAAE,SAAS,CAClBC,YAAY,CAAE,KAAK,CACnBuB,QAAQ,CAAE,MAAM,CAChBuC,WAAW,CAAE,KACf,CAAE,CAAA3D,QAAA,CACCyC,IAAI,CAACmB,WAAW,CACb,CAAC,cACP/F,IAAA,SAAM6B,KAAK,CAAE,CAAEuB,UAAU,CAAE,KAAK,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAlB,QAAA,CAC/CyC,IAAI,CAACoB,kBAAkB,CACpB,CAAC,EACJ,CAAC,cACN9F,KAAA,QAAK2B,KAAK,CAAE,CAAE0B,QAAQ,CAAE,MAAM,CAAEF,KAAK,CAAE,MAAM,CAAEF,YAAY,CAAE,KAAM,CAAE,CAAAhB,QAAA,EAAC,gBAChE,CAACyC,IAAI,CAACqB,aAAa,EACpB,CAAC,cACN/F,KAAA,QAAK2B,KAAK,CAAE,CAAE0B,QAAQ,CAAE,MAAM,CAAEF,KAAK,CAAE,MAAM,CAAEF,YAAY,CAAE,KAAM,CAAE,CAAAhB,QAAA,EAAC,gBAChE,CAACyC,IAAI,CAACK,eAAe,EACtB,CAAC,cACN/E,KAAA,QAAK2B,KAAK,CAAE,CAAE0B,QAAQ,CAAE,MAAM,CAAEF,KAAK,CAAE,MAAO,CAAE,CAAAlB,QAAA,EAAC,gBAC3C,CAACyC,IAAI,CAACsB,eAAe,EACtB,CAAC,GA9BCrB,SA+BL,CACL,CAAC,CACA,CAAC,EACF,CAAC,cAEN3E,KAAA,QAAK2B,KAAK,CAAE,CAAEsB,YAAY,CAAE,MAAO,CAAE,CAAAhB,QAAA,eACnCnC,IAAA,WAAAmC,QAAA,CAAQ,2BAAK,CAAQ,CAAC,cACtBnC,IAAA,OAAI6B,KAAK,CAAE,CAAE2C,MAAM,CAAE,WAAW,CAAEE,WAAW,CAAE,MAAO,CAAE,CAAAvC,QAAA,CACrDmD,MAAM,CAACa,iBAAiB,CAACrD,GAAG,CAAC,CAACsD,KAAK,CAAER,CAAC,gBACrC5F,IAAA,OAAY6B,KAAK,CAAE,CAAE0B,QAAQ,CAAE,MAAM,CAAEF,KAAK,CAAE,MAAM,CAAEF,YAAY,CAAE,KAAM,CAAE,CAAAhB,QAAA,CACzEiE,KAAK,EADCR,CAEL,CACL,CAAC,CACA,CAAC,EACF,CAAC,CAELN,MAAM,CAACe,YAAY,eAClBnG,KAAA,QAAAiC,QAAA,eACEjC,KAAA,QAAK2B,KAAK,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAEM,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAAhB,QAAA,eAC1GnC,IAAA,WAAAmC,QAAA,CAAQ,+BAAmB,CAAQ,CAAC,cACpCnC,IAAA,WACEsG,OAAO,CAAEA,CAAA,GAAM9F,cAAc,CAAC8E,MAAM,CAACe,YAAY,CAAEf,MAAM,CAACC,WAAW,CAAE,CACvE1D,KAAK,CAAE,CACLC,UAAU,CAAE,SAAS,CACrBuB,KAAK,CAAE,OAAO,CACdL,MAAM,CAAE,MAAM,CACdjB,OAAO,CAAE,SAAS,CAClBC,YAAY,CAAE,KAAK,CACnBuB,QAAQ,CAAE,MAAM,CAChBgD,MAAM,CAAE,SACV,CAAE,CAAApE,QAAA,CACH,uCAED,CAAQ,CAAC,EACN,CAAC,cACNnC,IAAA,QAAK6B,KAAK,CAAE,CACVC,UAAU,CAAE,SAAS,CACrBkB,MAAM,CAAE,mBAAmB,CAC3BhB,YAAY,CAAE,KAAK,CACnBD,OAAO,CAAE,MAAM,CACfwB,QAAQ,CAAE,MAAM,CAChBiD,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,OAAO,CAClBC,UAAU,CAAE,4CACd,CAAE,CAAAvE,QAAA,CACCmD,MAAM,CAACe,YAAY,CACjB,CAAC,EACH,CACN,GA3HO9B,KA4HL,CACN,CAAC,CACC,CAAC,EACH,CACN,CAED,mBACErE,KAAA,QAAK2B,KAAK,CAAE,CACVC,UAAU,CAAE,OAAO,CACnBC,OAAO,CAAE,MAAM,CACfC,YAAY,CAAE,MAAM,CACpBC,SAAS,CAAE,4BACb,CAAE,CAAAE,QAAA,eACAjC,KAAA,QAAK2B,KAAK,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAEM,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,YAAY,CAAEC,YAAY,CAAE,MAAM,CAAEwD,QAAQ,CAAE,MAAM,CAAE/D,GAAG,CAAE,MAAO,CAAE,CAAAT,QAAA,eAC9InC,IAAA,OAAI6B,KAAK,CAAE,CAAE2C,MAAM,CAAE,GAAG,CAAEnB,KAAK,CAAE,MAAM,CAAEE,QAAQ,CAAE,MAAM,CAAEH,UAAU,CAAE,KAAM,CAAE,CAAAjB,QAAA,CAAC,uCAEhF,CAAI,CAAC,cACLjC,KAAA,QAAK2B,KAAK,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAE0B,aAAa,CAAE,QAAQ,CAAEzB,GAAG,CAAE,KAAK,CAAEW,QAAQ,CAAE,MAAM,CAAEF,KAAK,CAAE,MAAM,CAAEnB,SAAS,CAAE,OAAQ,CAAE,CAAAC,QAAA,eACxHjC,KAAA,SAAAiC,QAAA,EAAM,kBAAM,CAACC,OAAO,EAAO,CAAC,cAC5BlC,KAAA,SAAAiC,QAAA,EAAM,gBAAI,CAACE,cAAc,CAACuE,MAAM,EAAO,CAAC,EACrC,CAAC,EACH,CAAC,cAEN1G,KAAA,QAAK2B,KAAK,CAAE,CAAEmB,MAAM,CAAE,mBAAmB,CAAEhB,YAAY,CAAE,KAAK,CAAEwE,QAAQ,CAAE,QAAS,CAAE,CAAArE,QAAA,eACnFnC,IAAA,QAAK6B,KAAK,CAAE,CAAEc,OAAO,CAAE,MAAM,CAAEb,UAAU,CAAE,SAAS,CAAE+E,YAAY,CAAE,mBAAoB,CAAE,CAAA1E,QAAA,CACvF,CACC,CAAE2E,GAAG,CAAE,UAAU,CAAEC,KAAK,oBAAAzF,MAAA,CAAWgB,QAAQ,CAACI,MAAM,KAAI,CAAC,CACvD,CAAEoE,GAAG,CAAE,OAAO,CAAEC,KAAK,8BAAAzF,MAAA,CAAWiB,KAAK,CAACG,MAAM,KAAI,CAAC,CACjD,CAAEoE,GAAG,CAAE,SAAS,CAAEC,KAAK,oCAAAzF,MAAA,CAAYkB,kBAAkB,CAACE,MAAM,KAAI,CAAC,CAClE,CAACI,GAAG,CAACkE,GAAG,eACPhH,IAAA,WAEEsG,OAAO,CAAEA,CAAA,GAAM/F,YAAY,CAACyG,GAAG,CAACF,GAAG,CAAE,CACrCjF,KAAK,CAAE,CACLoF,IAAI,CAAE,CAAC,CACPlF,OAAO,CAAE,WAAW,CACpBiB,MAAM,CAAE,MAAM,CACdlB,UAAU,CAAExB,SAAS,GAAK0G,GAAG,CAACF,GAAG,CAAG,OAAO,CAAG,aAAa,CAC3DP,MAAM,CAAE,SAAS,CACjBhD,QAAQ,CAAE,MAAM,CAChBH,UAAU,CAAE,KAAK,CACjBC,KAAK,CAAE/C,SAAS,GAAK0G,GAAG,CAACF,GAAG,CAAG,SAAS,CAAG,MAAM,CACjDI,UAAU,CAAE,eAAe,CAC3BL,YAAY,CAAEvG,SAAS,GAAK0G,GAAG,CAACF,GAAG,CAAG,mBAAmB,CAAG,MAC9D,CAAE,CAAA3E,QAAA,CAED6E,GAAG,CAACD,KAAK,EAfLC,GAAG,CAACF,GAgBH,CACT,CAAC,CACC,CAAC,cAEN5G,KAAA,QAAK2B,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEoF,SAAS,CAAE,OAAQ,CAAE,CAAAhF,QAAA,EACjD7B,SAAS,GAAK,UAAU,EAAImC,cAAc,CAAC,CAAC,CAC5CnC,SAAS,GAAK,OAAO,EAAI8D,WAAW,CAAC,CAAC,CACtC9D,SAAS,GAAK,SAAS,EAAI+E,aAAa,CAAC,CAAC,EACxC,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAlF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}