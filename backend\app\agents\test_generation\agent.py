"""
UI自动化测试脚本生成智能体 - 基于MidScene.js规范生成测试脚本
"""
import json
import os
import requests
import logging
from typing import List, Dict, Any
from datetime import datetime
from app.core.midscene_generator import midscene_generator

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def generate_standard_script_name(script_name: str) -> str:
    """生成标准脚本名称格式：年月日+时分+脚本名称+spec.ts"""
    now = datetime.now()
    year = now.year
    month = str(now.month).zfill(2)
    day = str(now.day).zfill(2)
    hour = str(now.hour).zfill(2)
    minute = str(now.minute).zfill(2)

    # 清理脚本名称，去掉"自动化脚本"等后缀
    clean_name = (script_name
                  .replace('自动化脚本', '')
                  .replace('脚本', '')
                  .replace('测试', '')
                  .strip())

    return f"{year}{month}{day}-{hour}{minute}-{clean_name}.spec.ts"

class TestGenerationAgent:
    """UI自动化测试脚本生成智能体"""

    def __init__(self, api_key: str = None, base_url: str = None, model_name: str = None):
        self.prompt = self._load_prompt()
        self.api_key = api_key or os.getenv('OPENAI_API_KEY', 'sk-3b9cb1dbb58a421082ba5c9f0d6c07d6')
        self.base_url = base_url or os.getenv('OPENAI_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1')
        self.model_name = model_name or os.getenv('MIDSCENE_MODEL_NAME', 'qwen-vl-max-latest')

        # 验证配置
        if not self.api_key:
            logger.warning("未配置API密钥，将使用模拟数据")
            self.use_mock = True
        else:
            self.use_mock = False
    
    def _load_prompt(self) -> str:
        """加载提示词"""
        return """你是MidScene.js自动化测试专家，专门基于UI专家和交互分析师的分析结果，设计符合MidScene.js脚本风格的测试用例。

## MidScene.js 核心知识（基于官方文档）

### 支持的动作类型

#### 1. 复合操作
- **ai**: 自然语言描述的复合操作，如 "type 'computer' in search box, hit Enter"
- **aiAction**: ai的完整形式，功能相同

#### 2. 即时操作（精确控制时使用）
- **aiTap**: 点击操作，用于按钮、链接、菜单项
- **aiInput**: 文本输入，格式为 aiInput: "输入内容", locate: "元素描述"
- **aiHover**: 鼠标悬停，用于下拉菜单触发
- **aiScroll**: 滚动操作，支持方向和距离
- **aiKeyboardPress**: 键盘操作，如Enter、Tab等

#### 3. 数据提取操作
- **aiQuery**: 通用查询，支持复杂数据结构，使用多行格式
- **aiBoolean**: 布尔值查询
- **aiNumber**: 数值查询
- **aiString**: 字符串查询

#### 4. 验证和等待
- **aiAssert**: 断言验证
- **aiWaitFor**: 等待条件满足
- **sleep**: 固定等待（毫秒）

### MidScene.js 提示词最佳实践（基于官方指南）

#### 1. 提供详细描述和示例
- ✅ 优秀描述: "找到搜索框（搜索框的上方应该有区域切换按钮，如'国内'，'国际'），输入'耳机'，敲回车"
- ❌ 简单描述: "搜'耳机'"
- ✅ 详细断言: "界面上有个'外卖服务'的板块，并且标识着'正常'"
- ❌ 模糊断言: "外卖服务正在正常运行"

#### 2. 精确的视觉定位描述
- ✅ 详细位置: "页面右上角的'Add'按钮，它是一个带有'+'图标的按钮，位于'range'下拉菜单的右侧"
- ❌ 模糊位置: "Add按钮"
- 包含视觉特征: 颜色、形状、图标、相对位置
- 提供上下文参考: 周围元素作为定位锚点

#### 3. 单一职责原则（一个指令只做一件事）
- ✅ 分解操作:
  - "点击登录按钮"
  - "在表单中[邮箱]输入'<EMAIL>'"
  - "在表单中[密码]输入'test'"
  - "点击注册按钮"
- ❌ 复合操作: "点击登录按钮，然后点击注册按钮，在表单中输入邮箱和密码，然后点击注册按钮"

#### 4. API选择策略
- **确定交互类型时优先使用即时操作**: aiTap('登录按钮') > ai('点击登录按钮')
- **复杂流程使用ai**: 适合多步骤操作规划
- **数据提取使用aiQuery**: 避免使用aiAssert进行数据提取

#### 5. 基于视觉而非DOM属性
- ✅ 视觉描述: "标题是蓝色的"
- ❌ DOM属性: "标题有个`test-id-size`属性"
- ✅ 界面状态: "页面显示登录成功消息"
- ❌ 浏览器状态: "异步请求已经结束了"

#### 6. 提供选项而非精确数值
- ✅ 颜色选项: "文本的颜色，返回：蓝色/红色/黄色/绿色/白色/黑色/其他"
- ❌ 精确数值: "文本颜色的十六进制值"

#### 7. 交叉验证和断言策略
- 操作后检查结果: 每个关键操作后添加验证步骤
- 使用aiAssert验证状态: 确认操作是否成功
- 避免依赖不可见状态: 所有验证基于界面可见内容

## 设计原则

1. **基于真实分析**: 严格基于UI专家和交互分析师的输出设计测试
2. **MidScene.js风格**: 使用自然语言描述，符合MidScene.js的AI驱动特性
3. **视觉定位优先**: 充分利用UI专家提供的详细视觉特征
4. **流程完整性**: 确保测试场景覆盖完整的用户操作路径
5. **可执行性**: 每个步骤都能直接转换为MidScene.js YAML脚本
6. **提示词工程最佳实践**:
   - 详细描述胜过简单描述
   - 提供视觉上下文和参考点
   - 单一职责，每个步骤只做一件事
   - 基于界面可见内容而非技术实现
   - 为关键操作添加验证步骤
7. **稳定性优先**: 设计能够在多次运行中获得稳定响应的测试步骤
8. **错误处理**: 考虑异常情况和用户可能的错误操作
9. **多语言支持**: 支持中英文混合的界面描述
"""
    
    def analyze(self, elements: List[Dict[str, Any]], flows: List[Dict[str, Any]], description: str) -> List[Dict[str, Any]]:
        """
        生成UI自动化测试脚本

        Args:
            elements: UI元素列表
            flows: 交互流程列表
            description: 界面功能描述

        Returns:
            自动化脚本列表
        """
        try:
            if self.use_mock:
                logger.info("使用模拟数据生成测试脚本")
                scripts = self._generate_midscene_scripts(elements, flows, description)
            else:
                logger.info("使用AI模型生成测试脚本")
                scripts = self._ai_generate_scripts(elements, flows, description)
            return scripts

        except Exception as e:
            logger.error(f"UI自动化测试脚本生成失败: {e}")
            # 失败时回退到模拟数据
            mock_scripts = self._generate_midscene_scripts(elements, flows, description)
            return mock_scripts if mock_scripts else []

    def _ai_generate_scripts(self, elements: List[Dict[str, Any]], flows: List[Dict[str, Any]], description: str) -> List[Dict[str, Any]]:
        """使用AI模型生成真实的测试脚本"""
        try:
            # 构建请求
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            # 构建输入数据文本
            elements_text = json.dumps(elements, ensure_ascii=False, indent=2)
            flows_text = json.dumps(flows, ensure_ascii=False, indent=2)

            # 构建消息
            messages = [
                {
                    "role": "user",
                    "content": f"{self.prompt}\n\n界面功能描述：{description}\n\nUI元素信息：\n{elements_text}\n\n交互流程信息：\n{flows_text}\n\n请基于以上信息，生成符合MidScene.js规范的UI自动化测试脚本，并按照指定的JSON格式输出。"
                }
            ]

            payload = {
                "model": self.model_name,
                "messages": messages,
                "max_tokens": 4000,
                "temperature": 0.1
            }

            # 发送请求
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=60
            )

            if response.status_code != 200:
                raise Exception(f"API请求失败: {response.status_code} - {response.text}")

            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')

            # 解析JSON结果
            scripts = self._parse_ai_response(content)

            # 为每个脚本生成YAML内容
            for script in scripts:
                yaml_content = midscene_generator.generate_script(
                    script_name=script.get('script_name', '未命名脚本'),
                    description=script.get('description', '自动化测试脚本'),
                    elements=elements,
                    flows=flows,
                    priority=script.get('priority', 'medium')
                )
                script['yaml_content'] = yaml_content

            logger.info(f"AI模型生成了 {len(scripts)} 个测试脚本")
            return scripts

        except Exception as e:
            logger.error(f"AI模型调用失败: {e}")
            raise e

    def _parse_ai_response(self, content: str) -> List[Dict[str, Any]]:
        """解析AI模型的响应内容"""
        try:
            # 尝试直接解析JSON
            if content.strip().startswith('['):
                return json.loads(content)

            # 如果包含代码块，提取JSON部分
            if '```json' in content:
                start = content.find('```json') + 7
                end = content.find('```', start)
                json_str = content[start:end].strip()
                return json.loads(json_str)

            # 如果包含普通代码块，提取内容
            if '```' in content:
                start = content.find('```') + 3
                end = content.find('```', start)
                json_str = content[start:end].strip()
                return json.loads(json_str)

            # 尝试查找JSON数组
            start = content.find('[')
            end = content.rfind(']') + 1
            if start >= 0 and end > start:
                json_str = content[start:end]
                return json.loads(json_str)

            logger.warning("无法解析AI响应，使用模拟数据")
            return []

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return []

    def _generate_midscene_scripts(self, elements: List[Dict[str, Any]], flows: List[Dict[str, Any]], description: str) -> List[Dict[str, Any]]:
        """使用MidScene.js生成器生成脚本"""
        scripts = []

        # 为每个交互流程生成脚本
        for flow in flows:
            script_name = f"{flow.get('flow_name', '未命名流程')}自动化脚本"
            script_description = f"基于{description}的{flow.get('description', '自动化测试脚本')}"

            # 生成MidScene.js YAML脚本
            yaml_content = midscene_generator.generate_script(
                script_name=script_name,
                description=script_description,
                elements=elements,
                flows=[flow],
                priority="high" if "登录" in script_name or "主要" in script_name else "medium"
            )

            # 构建脚本对象
            script = self._create_script_from_flow(flow, elements, description)
            script['yaml_content'] = yaml_content
            scripts.append(script)

        # 如果没有流程，生成基础脚本
        if not flows and elements:
            script_name = f"{description}基础功能自动化脚本"
            script_description = f"基于{description}的基础交互功能测试"

            yaml_content = midscene_generator.generate_script(
                script_name=script_name,
                description=script_description,
                elements=elements,
                flows=[],
                priority="medium"
            )

            basic_script = self._create_basic_script(elements, description)
            basic_script['yaml_content'] = yaml_content
            scripts.append(basic_script)

        return scripts

    def _mock_analysis(self, elements: List[Dict[str, Any]], flows: List[Dict[str, Any]], description: str) -> List[Dict[str, Any]]:
        """模拟测试用例生成结果"""
        scenarios = []

        # 为每个交互流程生成测试场景
        for flow in flows:
            scenario = self._create_scenario_from_flow(flow, elements, description)
            if scenario:
                scenarios.append(scenario)

        # 如果没有流程，基于元素生成基础测试场景
        if not scenarios and elements:
            basic_scenario = self._create_basic_scenario(elements, description)
            if basic_scenario:
                scenarios.append(basic_scenario)

        return scenarios

    def _create_script_from_flow(self, flow: Dict[str, Any], elements: List[Dict[str, Any]], description: str) -> Dict[str, Any]:
        """基于交互流程创建自动化脚本"""
        flow_name = flow.get('flow_name', '未命名流程')
        flow_steps = flow.get('steps', [])

        if not flow_steps:
            return None

        test_steps = []
        for i, step in enumerate(flow_steps, 1):
            # 查找对应的UI元素
            target_element = self._find_element_by_name(step.get('target_element', ''), elements)

            # 生成测试步骤
            test_step = {
                "step_id": i,
                "action_type": self._determine_action_type(step.get('action', ''), target_element),
                "action_description": self._enhance_action_description(step, target_element),
                "visual_target": self._create_visual_target_description(target_element),
                "expected_result": step.get('expected_result', '操作执行成功'),
                "validation_step": self._create_validation_step(step, target_element)
            }
            test_steps.append(test_step)

        # 生成标准格式的脚本名称
        original_script_name = f"{flow_name}自动化脚本"
        standard_script_name = generate_standard_script_name(original_script_name)

        script = {
            "script_name": standard_script_name,
            "original_name": original_script_name,
            "description": f"验证{flow.get('description', flow_name)}的完整流程",
            "priority": "high" if "登录" in flow_name or "主要" in flow_name else "medium",
            "estimated_duration": f"{len(test_steps) * 10}秒",
            "preconditions": ["页面已加载完成", "用户处于初始状态"],
            "test_steps": test_steps,
            "validation_points": [
                f"{step.get('action', '操作')}成功执行" for step in flow_steps
            ]
        }

        return script

    def _create_basic_script(self, elements: List[Dict[str, Any]], description: str) -> Dict[str, Any]:
        """创建基础自动化脚本"""
        # 查找主要交互元素
        buttons = [e for e in elements if e.get('element_type') == 'button']
        inputs = [e for e in elements if e.get('element_type') == 'input']

        if not buttons and not inputs:
            return None

        test_steps = []
        step_id = 1

        # 为输入框生成输入步骤
        for input_elem in inputs:
            test_step = {
                "step_id": step_id,
                "action_type": "aiInput",
                "action_description": "测试数据",
                "visual_target": self._create_visual_target_description(input_elem),
                "expected_result": "输入内容显示正确",
                "validation_step": "检查输入框内容是否正确显示"
            }
            test_steps.append(test_step)
            step_id += 1

        # 为按钮生成点击步骤
        for button_elem in buttons:
            test_step = {
                "step_id": step_id,
                "action_type": "aiTap",
                "action_description": self._create_visual_target_description(button_elem),
                "visual_target": self._create_visual_target_description(button_elem),
                "expected_result": "按钮点击响应正常",
                "validation_step": "检查操作是否成功执行"
            }
            test_steps.append(test_step)
            step_id += 1

        # 生成标准格式的脚本名称
        original_script_name = "基础功能自动化脚本"
        standard_script_name = generate_standard_script_name(original_script_name)

        script = {
            "script_name": standard_script_name,
            "original_name": original_script_name,
            "description": f"验证{description}的基础交互功能",
            "priority": "medium",
            "estimated_duration": f"{len(test_steps) * 8}秒",
            "preconditions": ["页面已加载完成"],
            "test_steps": test_steps,
            "validation_points": ["所有交互元素正常响应"]
        }

        return script

    def _find_element_by_name(self, target_name: str, elements: List[Dict[str, Any]]) -> Dict[str, Any]:
        """根据名称查找UI元素"""
        for element in elements:
            if target_name in element.get('name', '') or target_name in element.get('description', ''):
                return element
        return {}

    def _determine_action_type(self, action: str, element: Dict[str, Any]) -> str:
        """确定动作类型"""
        action_lower = action.lower()
        element_type = element.get('element_type', '')

        if '点击' in action or 'click' in action_lower:
            return 'aiTap'
        elif '输入' in action or 'input' in action_lower or element_type == 'input':
            return 'aiInput'
        elif '悬停' in action or 'hover' in action_lower:
            return 'aiHover'
        elif '滚动' in action or 'scroll' in action_lower:
            return 'aiScroll'
        elif '等待' in action or 'wait' in action_lower:
            return 'aiWaitFor'
        elif '验证' in action or '检查' in action or 'assert' in action_lower:
            return 'aiAssert'
        else:
            return 'ai'  # 复合操作

    def _enhance_action_description(self, step: Dict[str, Any], element: Dict[str, Any]) -> str:
        """增强动作描述"""
        action = step.get('action', '')

        if not element:
            return action

        # 添加视觉特征描述
        visual_features = element.get('visual_features', {})
        position = element.get('position', {})

        enhanced_desc = action

        # 添加位置信息
        if position.get('area'):
            enhanced_desc += f"，位于{position['area']}"

        # 添加视觉特征
        if visual_features.get('color'):
            enhanced_desc += f"，{visual_features['color']}"

        if visual_features.get('shape'):
            enhanced_desc += f"，{visual_features['shape']}"

        return enhanced_desc

    def _create_visual_target_description(self, element: Dict[str, Any]) -> str:
        """创建视觉目标描述"""
        if not element:
            return "目标元素"

        name = element.get('name', '元素')
        description = element.get('description', '')
        visual_features = element.get('visual_features', {})
        position = element.get('position', {})
        text_content = element.get('text_content', '')

        # 构建详细的视觉描述
        visual_desc = name

        if text_content:
            visual_desc += f"（显示'{text_content}'文字）"

        if visual_features.get('color'):
            visual_desc += f"，{visual_features['color']}"

        if visual_features.get('shape'):
            visual_desc += f"，{visual_features['shape']}"

        if position.get('area'):
            visual_desc += f"，位于{position['area']}"

        if position.get('relative_to'):
            visual_desc += f"，{position['relative_to']}"

        # 如果有详细描述，优先使用
        if description and len(description) > len(visual_desc):
            return description

        return visual_desc

    def _create_validation_step(self, step: Dict[str, Any], element: Dict[str, Any]) -> str:
        """创建验证步骤描述"""
        action = step.get('action', '')
        expected_result = step.get('expected_result', '')
        element_name = element.get('name', '元素') if element else '元素'

        if '点击' in action:
            return f"检查{element_name}点击操作是否成功，确认{expected_result}"
        elif '输入' in action:
            return f"验证{element_name}输入内容是否正确显示，确认{expected_result}"
        elif '提交' in action:
            return f"检查{element_name}提交操作是否成功，确认{expected_result}"
        elif '登录' in action:
            return f"验证{element_name}登录状态，确认{expected_result}"
        else:
            return f"验证{element_name}操作结果，确认{expected_result}"
