{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\components\\\\SimpleUpload.js\",\n  _s = $RefreshSig$();\n/**\n * 简化的上传组件\n */\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SimpleUpload = ({\n  onUploadSuccess,\n  onUploadError\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n    setIsUploading(true);\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n      const response = await fetch('http://localhost:8001/api/v1/upload', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n      const result = await response.json();\n      onUploadSuccess(result);\n\n      // 重置表单\n      setSelectedFile(null);\n      setDescription('');\n      event.target.reset();\n    } catch (error) {\n      console.error('Upload error:', error);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  const handleFileManagementDemo = async () => {\n    setIsUploading(true);\n    try {\n      const formData = new FormData();\n      formData.append('description', '文件管理界面功能分析 - 基于提供的截图进行专项分析');\n      const response = await fetch('http://localhost:8001/api/v1/analyze/file-management', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n      const result = await response.json();\n      onUploadSuccess(result);\n    } catch (error) {\n      console.error('File management demo error:', error);\n      onUploadError(error.message || '文件管理界面分析失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'white',\n      padding: '24px',\n      borderRadius: '12px',\n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n      marginBottom: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: 'block',\n            marginBottom: '12px',\n            fontWeight: '600',\n            color: '#333',\n            fontSize: '16px'\n          },\n          children: \"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"file-upload-area\",\n          onClick: () => document.getElementById('file-input').click(),\n          style: {\n            height: '240px',\n            // 调高3倍（原来约80px）\n            border: '2px dashed #667eea',\n            borderRadius: '12px',\n            background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            transition: 'all 0.3s ease',\n            position: 'relative',\n            overflow: 'hidden'\n          },\n          onDragOver: e => {\n            e.preventDefault();\n            e.currentTarget.style.borderColor = '#4f46e5';\n            e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';\n          },\n          onDragLeave: e => {\n            e.currentTarget.style.borderColor = '#667eea';\n            e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';\n          },\n          onDrop: e => {\n            e.preventDefault();\n            const files = e.dataTransfer.files;\n            if (files.length > 0) {\n              setSelectedFile(files[0]);\n            }\n            e.currentTarget.style.borderColor = '#667eea';\n            e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"file-input\",\n            type: \"file\",\n            accept: \"image/*\",\n            onChange: handleFileSelect,\n            style: {\n              display: 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), selectedFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '16px',\n                color: '#667eea'\n              },\n              children: \"\\u2705\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: '0 0 8px 0',\n                color: '#333',\n                fontSize: '18px'\n              },\n              children: \"\\u6587\\u4EF6\\u5DF2\\u9009\\u62E9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0 0 8px 0',\n                color: '#666',\n                fontSize: '14px',\n                fontWeight: '500'\n              },\n              children: selectedFile.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0',\n                color: '#999',\n                fontSize: '12px'\n              },\n              children: [\"\\u5927\\u5C0F: \", (selectedFile.size / 1024 / 1024).toFixed(2), \" MB\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: e => {\n                e.stopPropagation();\n                setSelectedFile(null);\n                document.getElementById('file-input').value = '';\n              },\n              style: {\n                marginTop: '12px',\n                background: 'rgba(239, 68, 68, 0.1)',\n                color: '#ef4444',\n                border: '1px solid rgba(239, 68, 68, 0.3)',\n                padding: '6px 12px',\n                borderRadius: '6px',\n                fontSize: '12px',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              },\n              onMouseOver: e => {\n                e.target.style.background = 'rgba(239, 68, 68, 0.2)';\n              },\n              onMouseOut: e => {\n                e.target.style.background = 'rgba(239, 68, 68, 0.1)';\n              },\n              children: \"\\u91CD\\u65B0\\u9009\\u62E9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '16px',\n                color: '#667eea'\n              },\n              children: \"\\uD83D\\uDCE4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: '0 0 8px 0',\n                color: '#333',\n                fontSize: '18px'\n              },\n              children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u4E0A\\u4F20\\u56FE\\u7247\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0 0 4px 0',\n                color: '#666',\n                fontSize: '14px'\n              },\n              children: \"\\u652F\\u6301 PNG\\u3001JPG\\u3001JPEG\\u3001GIF \\u683C\\u5F0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0',\n                color: '#999',\n                fontSize: '12px'\n              },\n              children: \"\\u6587\\u4EF6\\u5927\\u5C0F\\u4E0D\\u8D85\\u8FC7 10MB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '16px',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                color: 'white',\n                padding: '8px 20px',\n                borderRadius: '20px',\n                fontSize: '14px',\n                fontWeight: '500',\n                display: 'inline-block'\n              },\n              children: \"\\u9009\\u62E9\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"description\",\n          style: {\n            display: 'block',\n            marginBottom: '8px',\n            fontWeight: '500'\n          },\n          children: \"\\u754C\\u9762\\u529F\\u80FD\\u63CF\\u8FF0:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          id: \"description\",\n          value: description,\n          onChange: e => setDescription(e.target.value),\n          placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u8FD9\\u4E2A\\u754C\\u9762\\u7684\\u4E3B\\u8981\\u529F\\u80FD\\uFF0C\\u4F8B\\u5982\\uFF1A\\u8FD9\\u662F\\u4E00\\u4E2A\\u767B\\u5F55\\u9875\\u9762\\uFF0C\\u5305\\u542B\\u7528\\u6237\\u540D\\u5BC6\\u7801\\u8F93\\u5165\\u6846\\u548C\\u767B\\u5F55\\u6309\\u94AE...\",\n          rows: 4,\n          style: {\n            width: '100%',\n            padding: '12px',\n            border: '1px solid #ddd',\n            borderRadius: '4px',\n            resize: 'vertical',\n            fontFamily: 'inherit'\n          },\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'right',\n            fontSize: '12px',\n            color: '#666',\n            marginTop: '4px'\n          },\n          children: [description.length, \"/500\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: isUploading || !selectedFile || !description.trim(),\n        style: {\n          background: isUploading ? '#6c757d' : '#007bff',\n          color: 'white',\n          border: 'none',\n          padding: '12px 24px',\n          borderRadius: '6px',\n          fontSize: '16px',\n          fontWeight: '500',\n          cursor: isUploading ? 'not-allowed' : 'pointer',\n          transition: 'all 0.3s ease',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        },\n        children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: '16px',\n              height: '16px',\n              border: '2px solid transparent',\n              borderTop: '2px solid white',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), \"\\u5206\\u6790\\u4E2D...\"]\n        }, void 0, true) : '🚀 开始分析'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        margin: '24px 0',\n        color: '#666',\n        fontSize: '14px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          height: '1px',\n          background: '#e9ecef'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          padding: '0 16px'\n        },\n        children: \"\\u6216\\u8005\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          height: '1px',\n          background: '#e9ecef'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          margin: '0 0 12px 0',\n          color: '#333',\n          fontSize: '16px'\n        },\n        children: \"\\uD83D\\uDCC1 \\u4E13\\u9879\\u5206\\u6790\\u6F14\\u793A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: '0 0 16px 0',\n          color: '#666',\n          fontSize: '14px'\n        },\n        children: \"\\u57FA\\u4E8E\\u60A8\\u63D0\\u4F9B\\u7684\\u6587\\u4EF6\\u7BA1\\u7406\\u754C\\u9762\\u622A\\u56FE\\uFF0C\\u751F\\u6210\\u4E13\\u95E8\\u7684UI\\u81EA\\u52A8\\u5316\\u6D4B\\u8BD5\\u811A\\u672C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: handleFileManagementDemo,\n        disabled: isUploading,\n        style: {\n          background: isUploading ? '#6c757d' : '#28a745',\n          color: 'white',\n          border: 'none',\n          padding: '12px 32px',\n          borderRadius: '6px',\n          fontSize: '16px',\n          fontWeight: '500',\n          cursor: isUploading ? 'not-allowed' : 'pointer',\n          transition: 'all 0.3s ease',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px',\n          margin: '0 auto'\n        },\n        children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: '16px',\n              height: '16px',\n              border: '2px solid transparent',\n              borderTop: '2px solid white',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 15\n          }, this), \"\\u5206\\u6790\\u4E2D...\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: \"\\uD83D\\uDCCA \\u5206\\u6790\\u6587\\u4EF6\\u7BA1\\u7406\\u754C\\u9762\"\n        }, void 0, false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleUpload, \"rg49S67Niskg9326cNUOfz9PB/M=\");\n_c = SimpleUpload;\nexport default SimpleUpload;\nvar _c;\n$RefreshReg$(_c, \"SimpleUpload\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SimpleUpload", "onUploadSuccess", "onUploadError", "_s", "selectedFile", "setSelectedFile", "description", "setDescription", "isUploading", "setIsUploading", "handleFileSelect", "event", "file", "target", "files", "handleSubmit", "preventDefault", "trim", "formData", "FormData", "append", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "status", "result", "reset", "error", "console", "message", "handleFileManagementDemo", "style", "background", "padding", "borderRadius", "boxShadow", "marginBottom", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "display", "fontWeight", "color", "fontSize", "className", "onClick", "document", "getElementById", "click", "height", "border", "flexDirection", "alignItems", "justifyContent", "cursor", "transition", "position", "overflow", "onDragOver", "e", "currentTarget", "borderColor", "onDragLeave", "onDrop", "dataTransfer", "length", "id", "type", "accept", "onChange", "textAlign", "margin", "name", "size", "toFixed", "stopPropagation", "value", "marginTop", "onMouseOver", "onMouseOut", "htmlFor", "placeholder", "rows", "width", "resize", "fontFamily", "required", "disabled", "gap", "borderTop", "animation", "flex", "jsx", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/components/SimpleUpload.js"], "sourcesContent": ["/**\n * 简化的上传组件\n */\nimport React, { useState } from 'react';\n\nconst SimpleUpload = ({ onUploadSuccess, onUploadError }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n\n  const handleFileSelect = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n\n  const handleSubmit = async (event) => {\n    event.preventDefault();\n\n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n\n    setIsUploading(true);\n\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n\n      const response = await fetch('http://localhost:8001/api/v1/upload', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      onUploadSuccess(result);\n\n      // 重置表单\n      setSelectedFile(null);\n      setDescription('');\n      event.target.reset();\n    } catch (error) {\n      console.error('Upload error:', error);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const handleFileManagementDemo = async () => {\n    setIsUploading(true);\n\n    try {\n      const formData = new FormData();\n      formData.append('description', '文件管理界面功能分析 - 基于提供的截图进行专项分析');\n\n      const response = await fetch('http://localhost:8001/api/v1/analyze/file-management', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      onUploadSuccess(result);\n\n    } catch (error) {\n      console.error('File management demo error:', error);\n      onUploadError(error.message || '文件管理界面分析失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  return (\n    <div style={{ \n      background: 'white', \n      padding: '24px', \n      borderRadius: '12px', \n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n      marginBottom: '24px'\n    }}>\n      <h3>📁 上传UI界面截图</h3>\n      <form onSubmit={handleSubmit}>\n        <div style={{ marginBottom: '24px' }}>\n          <label style={{ display: 'block', marginBottom: '12px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n            📁 上传UI界面截图\n          </label>\n          <div\n            className=\"file-upload-area\"\n            onClick={() => document.getElementById('file-input').click()}\n            style={{\n              height: '240px', // 调高3倍（原来约80px）\n              border: '2px dashed #667eea',\n              borderRadius: '12px',\n              background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.3s ease',\n              position: 'relative',\n              overflow: 'hidden'\n            }}\n            onDragOver={(e) => {\n              e.preventDefault();\n              e.currentTarget.style.borderColor = '#4f46e5';\n              e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';\n            }}\n            onDragLeave={(e) => {\n              e.currentTarget.style.borderColor = '#667eea';\n              e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';\n            }}\n            onDrop={(e) => {\n              e.preventDefault();\n              const files = e.dataTransfer.files;\n              if (files.length > 0) {\n                setSelectedFile(files[0]);\n              }\n              e.currentTarget.style.borderColor = '#667eea';\n              e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';\n            }}\n          >\n            <input\n              id=\"file-input\"\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={handleFileSelect}\n              style={{ display: 'none' }}\n            />\n\n            {selectedFile ? (\n              <div style={{ textAlign: 'center', padding: '20px' }}>\n                <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>✅</div>\n                <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>文件已选择</h3>\n                <p style={{ margin: '0 0 8px 0', color: '#666', fontSize: '14px', fontWeight: '500' }}>\n                  {selectedFile.name}\n                </p>\n                <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>\n                  大小: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB\n                </p>\n                <button\n                  type=\"button\"\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    setSelectedFile(null);\n                    document.getElementById('file-input').value = '';\n                  }}\n                  style={{\n                    marginTop: '12px',\n                    background: 'rgba(239, 68, 68, 0.1)',\n                    color: '#ef4444',\n                    border: '1px solid rgba(239, 68, 68, 0.3)',\n                    padding: '6px 12px',\n                    borderRadius: '6px',\n                    fontSize: '12px',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseOver={(e) => {\n                    e.target.style.background = 'rgba(239, 68, 68, 0.2)';\n                  }}\n                  onMouseOut={(e) => {\n                    e.target.style.background = 'rgba(239, 68, 68, 0.1)';\n                  }}\n                >\n                  重新选择\n                </button>\n              </div>\n            ) : (\n              <div style={{ textAlign: 'center', padding: '20px' }}>\n                <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>📤</div>\n                <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>点击或拖拽上传图片</h3>\n                <p style={{ margin: '0 0 4px 0', color: '#666', fontSize: '14px' }}>\n                  支持 PNG、JPG、JPEG、GIF 格式\n                </p>\n                <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>\n                  文件大小不超过 10MB\n                </p>\n                <div style={{\n                  marginTop: '16px',\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  color: 'white',\n                  padding: '8px 20px',\n                  borderRadius: '20px',\n                  fontSize: '14px',\n                  fontWeight: '500',\n                  display: 'inline-block'\n                }}>\n                  选择文件\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        <div style={{ marginBottom: '16px' }}>\n          <label htmlFor=\"description\" style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>\n            界面功能描述:\n          </label>\n          <textarea\n            id=\"description\"\n            value={description}\n            onChange={(e) => setDescription(e.target.value)}\n            placeholder=\"请描述这个界面的主要功能，例如：这是一个登录页面，包含用户名密码输入框和登录按钮...\"\n            rows={4}\n            style={{\n              width: '100%',\n              padding: '12px',\n              border: '1px solid #ddd',\n              borderRadius: '4px',\n              resize: 'vertical',\n              fontFamily: 'inherit'\n            }}\n            required\n          />\n          <div style={{ textAlign: 'right', fontSize: '12px', color: '#666', marginTop: '4px' }}>\n            {description.length}/500\n          </div>\n        </div>\n\n        <button\n          type=\"submit\"\n          disabled={isUploading || !selectedFile || !description.trim()}\n          style={{\n            background: isUploading ? '#6c757d' : '#007bff',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            fontSize: '16px',\n            fontWeight: '500',\n            cursor: isUploading ? 'not-allowed' : 'pointer',\n            transition: 'all 0.3s ease',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          }}\n        >\n          {isUploading ? (\n            <>\n              <span style={{\n                width: '16px',\n                height: '16px',\n                border: '2px solid transparent',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }}></span>\n              分析中...\n            </>\n          ) : (\n            '🚀 开始分析'\n          )}\n        </button>\n      </form>\n\n      {/* 分隔线 */}\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        margin: '24px 0',\n        color: '#666',\n        fontSize: '14px'\n      }}>\n        <div style={{ flex: 1, height: '1px', background: '#e9ecef' }}></div>\n        <span style={{ padding: '0 16px' }}>或者</span>\n        <div style={{ flex: 1, height: '1px', background: '#e9ecef' }}></div>\n      </div>\n\n      {/* 文件管理界面演示按钮 */}\n      <div style={{ textAlign: 'center' }}>\n        <h4 style={{ margin: '0 0 12px 0', color: '#333', fontSize: '16px' }}>\n          📁 专项分析演示\n        </h4>\n        <p style={{ margin: '0 0 16px 0', color: '#666', fontSize: '14px' }}>\n          基于您提供的文件管理界面截图，生成专门的UI自动化测试脚本\n        </p>\n        <button\n          type=\"button\"\n          onClick={handleFileManagementDemo}\n          disabled={isUploading}\n          style={{\n            background: isUploading ? '#6c757d' : '#28a745',\n            color: 'white',\n            border: 'none',\n            padding: '12px 32px',\n            borderRadius: '6px',\n            fontSize: '16px',\n            fontWeight: '500',\n            cursor: isUploading ? 'not-allowed' : 'pointer',\n            transition: 'all 0.3s ease',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px',\n            margin: '0 auto'\n          }}\n        >\n          {isUploading ? (\n            <>\n              <span style={{\n                width: '16px',\n                height: '16px',\n                border: '2px solid transparent',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }}></span>\n              分析中...\n            </>\n          ) : (\n            <>\n              📊 分析文件管理界面\n            </>\n          )}\n        </button>\n      </div>\n\n      <style jsx>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default SimpleUpload;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,YAAY,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMe,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACRP,eAAe,CAACO,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOJ,KAAK,IAAK;IACpCA,KAAK,CAACK,cAAc,CAAC,CAAC;IAEtB,IAAI,CAACZ,YAAY,EAAE;MACjBF,aAAa,CAAC,SAAS,CAAC;MACxB;IACF;IAEA,IAAI,CAACI,WAAW,CAACW,IAAI,CAAC,CAAC,EAAE;MACvBf,aAAa,CAAC,WAAW,CAAC;MAC1B;IACF;IAEAO,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,MAAMS,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEhB,YAAY,CAAC;MAC3Cc,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEd,WAAW,CAACW,IAAI,CAAC,CAAC,CAAC;MAElD,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,qCAAqC,EAAE;QAClEC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEN;MACR,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,uBAAuBR,QAAQ,CAACS,MAAM,EAAE,CAAC;MAC/E;MAEA,MAAMC,MAAM,GAAG,MAAMV,QAAQ,CAACM,IAAI,CAAC,CAAC;MACpC1B,eAAe,CAAC8B,MAAM,CAAC;;MAEvB;MACA1B,eAAe,CAAC,IAAI,CAAC;MACrBE,cAAc,CAAC,EAAE,CAAC;MAClBI,KAAK,CAACE,MAAM,CAACmB,KAAK,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC/B,aAAa,CAAC+B,KAAK,CAACE,OAAO,IAAI,UAAU,CAAC;IAC5C,CAAC,SAAS;MACR1B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM2B,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C3B,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,MAAMS,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE,4BAA4B,CAAC;MAE5D,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,sDAAsD,EAAE;QACnFC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEN;MACR,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,uBAAuBR,QAAQ,CAACS,MAAM,EAAE,CAAC;MAC/E;MAEA,MAAMC,MAAM,GAAG,MAAMV,QAAQ,CAACM,IAAI,CAAC,CAAC;MACpC1B,eAAe,CAAC8B,MAAM,CAAC;IAEzB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD/B,aAAa,CAAC+B,KAAK,CAACE,OAAO,IAAI,gBAAgB,CAAC;IAClD,CAAC,SAAS;MACR1B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,oBACEZ,OAAA;IAAKwC,KAAK,EAAE;MACVC,UAAU,EAAE,OAAO;MACnBC,OAAO,EAAE,MAAM;MACfC,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,4BAA4B;MACvCC,YAAY,EAAE;IAChB,CAAE;IAAAC,QAAA,gBACA9C,OAAA;MAAA8C,QAAA,EAAI;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpBlD,OAAA;MAAMmD,QAAQ,EAAEjC,YAAa;MAAA4B,QAAA,gBAC3B9C,OAAA;QAAKwC,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnC9C,OAAA;UAAOwC,KAAK,EAAE;YAAEY,OAAO,EAAE,OAAO;YAAEP,YAAY,EAAE,MAAM;YAAEQ,UAAU,EAAE,KAAK;YAAEC,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE9G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRlD,OAAA;UACEwD,SAAS,EAAC,kBAAkB;UAC5BC,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAACC,KAAK,CAAC,CAAE;UAC7DpB,KAAK,EAAE;YACLqB,MAAM,EAAE,OAAO;YAAE;YACjBC,MAAM,EAAE,oBAAoB;YAC5BnB,YAAY,EAAE,MAAM;YACpBF,UAAU,EAAElC,YAAY,GAAG,mDAAmD,GAAG,mDAAmD;YACpI6C,OAAO,EAAE,MAAM;YACfW,aAAa,EAAE,QAAQ;YACvBC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,MAAM,EAAE,SAAS;YACjBC,UAAU,EAAE,eAAe;YAC3BC,QAAQ,EAAE,UAAU;YACpBC,QAAQ,EAAE;UACZ,CAAE;UACFC,UAAU,EAAGC,CAAC,IAAK;YACjBA,CAAC,CAACpD,cAAc,CAAC,CAAC;YAClBoD,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACiC,WAAW,GAAG,SAAS;YAC7CF,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACC,UAAU,GAAG,mDAAmD;UACxF,CAAE;UACFiC,WAAW,EAAGH,CAAC,IAAK;YAClBA,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACiC,WAAW,GAAG,SAAS;YAC7CF,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACC,UAAU,GAAGlC,YAAY,GAAG,mDAAmD,GAAG,mDAAmD;UAC7J,CAAE;UACFoE,MAAM,EAAGJ,CAAC,IAAK;YACbA,CAAC,CAACpD,cAAc,CAAC,CAAC;YAClB,MAAMF,KAAK,GAAGsD,CAAC,CAACK,YAAY,CAAC3D,KAAK;YAClC,IAAIA,KAAK,CAAC4D,MAAM,GAAG,CAAC,EAAE;cACpBrE,eAAe,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B;YACAsD,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACiC,WAAW,GAAG,SAAS;YAC7CF,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACC,UAAU,GAAG,mDAAmD;UACxF,CAAE;UAAAK,QAAA,gBAEF9C,OAAA;YACE8E,EAAE,EAAC,YAAY;YACfC,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,SAAS;YAChBC,QAAQ,EAAEpE,gBAAiB;YAC3B2B,KAAK,EAAE;cAAEY,OAAO,EAAE;YAAO;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,EAED3C,YAAY,gBACXP,OAAA;YAAKwC,KAAK,EAAE;cAAE0C,SAAS,EAAE,QAAQ;cAAExC,OAAO,EAAE;YAAO,CAAE;YAAAI,QAAA,gBACnD9C,OAAA;cAAKwC,KAAK,EAAE;gBAAEe,QAAQ,EAAE,MAAM;gBAAEV,YAAY,EAAE,MAAM;gBAAES,KAAK,EAAE;cAAU,CAAE;cAAAR,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjFlD,OAAA;cAAIwC,KAAK,EAAE;gBAAE2C,MAAM,EAAE,WAAW;gBAAE7B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/ElD,OAAA;cAAGwC,KAAK,EAAE;gBAAE2C,MAAM,EAAE,WAAW;gBAAE7B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE,MAAM;gBAAEF,UAAU,EAAE;cAAM,CAAE;cAAAP,QAAA,EACnFvC,YAAY,CAAC6E;YAAI;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACJlD,OAAA;cAAGwC,KAAK,EAAE;gBAAE2C,MAAM,EAAE,GAAG;gBAAE7B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,GAAC,gBACtD,EAAC,CAACvC,YAAY,CAAC8E,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KACpD;YAAA;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlD,OAAA;cACE+E,IAAI,EAAC,QAAQ;cACbtB,OAAO,EAAGc,CAAC,IAAK;gBACdA,CAAC,CAACgB,eAAe,CAAC,CAAC;gBACnB/E,eAAe,CAAC,IAAI,CAAC;gBACrBkD,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAAC6B,KAAK,GAAG,EAAE;cAClD,CAAE;cACFhD,KAAK,EAAE;gBACLiD,SAAS,EAAE,MAAM;gBACjBhD,UAAU,EAAE,wBAAwB;gBACpCa,KAAK,EAAE,SAAS;gBAChBQ,MAAM,EAAE,kCAAkC;gBAC1CpB,OAAO,EAAE,UAAU;gBACnBC,YAAY,EAAE,KAAK;gBACnBY,QAAQ,EAAE,MAAM;gBAChBW,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE;cACd,CAAE;cACFuB,WAAW,EAAGnB,CAAC,IAAK;gBAClBA,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACC,UAAU,GAAG,wBAAwB;cACtD,CAAE;cACFkD,UAAU,EAAGpB,CAAC,IAAK;gBACjBA,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACC,UAAU,GAAG,wBAAwB;cACtD,CAAE;cAAAK,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAENlD,OAAA;YAAKwC,KAAK,EAAE;cAAE0C,SAAS,EAAE,QAAQ;cAAExC,OAAO,EAAE;YAAO,CAAE;YAAAI,QAAA,gBACnD9C,OAAA;cAAKwC,KAAK,EAAE;gBAAEe,QAAQ,EAAE,MAAM;gBAAEV,YAAY,EAAE,MAAM;gBAAES,KAAK,EAAE;cAAU,CAAE;cAAAR,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClFlD,OAAA;cAAIwC,KAAK,EAAE;gBAAE2C,MAAM,EAAE,WAAW;gBAAE7B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnFlD,OAAA;cAAGwC,KAAK,EAAE;gBAAE2C,MAAM,EAAE,WAAW;gBAAE7B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlD,OAAA;cAAGwC,KAAK,EAAE;gBAAE2C,MAAM,EAAE,GAAG;gBAAE7B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAC;YAE5D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlD,OAAA;cAAKwC,KAAK,EAAE;gBACViD,SAAS,EAAE,MAAM;gBACjBhD,UAAU,EAAE,mDAAmD;gBAC/Da,KAAK,EAAE,OAAO;gBACdZ,OAAO,EAAE,UAAU;gBACnBC,YAAY,EAAE,MAAM;gBACpBY,QAAQ,EAAE,MAAM;gBAChBF,UAAU,EAAE,KAAK;gBACjBD,OAAO,EAAE;cACX,CAAE;cAAAN,QAAA,EAAC;YAEH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlD,OAAA;QAAKwC,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnC9C,OAAA;UAAO4F,OAAO,EAAC,aAAa;UAACpD,KAAK,EAAE;YAAEY,OAAO,EAAE,OAAO;YAAEP,YAAY,EAAE,KAAK;YAAEQ,UAAU,EAAE;UAAM,CAAE;UAAAP,QAAA,EAAC;QAElG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRlD,OAAA;UACE8E,EAAE,EAAC,aAAa;UAChBU,KAAK,EAAE/E,WAAY;UACnBwE,QAAQ,EAAGV,CAAC,IAAK7D,cAAc,CAAC6D,CAAC,CAACvD,MAAM,CAACwE,KAAK,CAAE;UAChDK,WAAW,EAAC,qPAA6C;UACzDC,IAAI,EAAE,CAAE;UACRtD,KAAK,EAAE;YACLuD,KAAK,EAAE,MAAM;YACbrD,OAAO,EAAE,MAAM;YACfoB,MAAM,EAAE,gBAAgB;YACxBnB,YAAY,EAAE,KAAK;YACnBqD,MAAM,EAAE,UAAU;YAClBC,UAAU,EAAE;UACd,CAAE;UACFC,QAAQ;QAAA;UAAAnD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFlD,OAAA;UAAKwC,KAAK,EAAE;YAAE0C,SAAS,EAAE,OAAO;YAAE3B,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE,MAAM;YAAEmC,SAAS,EAAE;UAAM,CAAE;UAAA3C,QAAA,GACnFrC,WAAW,CAACoE,MAAM,EAAC,MACtB;QAAA;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlD,OAAA;QACE+E,IAAI,EAAC,QAAQ;QACboB,QAAQ,EAAExF,WAAW,IAAI,CAACJ,YAAY,IAAI,CAACE,WAAW,CAACW,IAAI,CAAC,CAAE;QAC9DoB,KAAK,EAAE;UACLC,UAAU,EAAE9B,WAAW,GAAG,SAAS,GAAG,SAAS;UAC/C2C,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdpB,OAAO,EAAE,WAAW;UACpBC,YAAY,EAAE,KAAK;UACnBY,QAAQ,EAAE,MAAM;UAChBF,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAEvD,WAAW,GAAG,aAAa,GAAG,SAAS;UAC/CwD,UAAU,EAAE,eAAe;UAC3Bf,OAAO,EAAE,MAAM;UACfY,UAAU,EAAE,QAAQ;UACpBoC,GAAG,EAAE;QACP,CAAE;QAAAtD,QAAA,EAEDnC,WAAW,gBACVX,OAAA,CAAAE,SAAA;UAAA4C,QAAA,gBACE9C,OAAA;YAAMwC,KAAK,EAAE;cACXuD,KAAK,EAAE,MAAM;cACblC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,uBAAuB;cAC/BuC,SAAS,EAAE,iBAAiB;cAC5B1D,YAAY,EAAE,KAAK;cACnB2D,SAAS,EAAE;YACb;UAAE;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,yBAEZ;QAAA,eAAE,CAAC,GAEH;MACD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGPlD,OAAA;MAAKwC,KAAK,EAAE;QACVY,OAAO,EAAE,MAAM;QACfY,UAAU,EAAE,QAAQ;QACpBmB,MAAM,EAAE,QAAQ;QAChB7B,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE;MACZ,CAAE;MAAAT,QAAA,gBACA9C,OAAA;QAAKwC,KAAK,EAAE;UAAE+D,IAAI,EAAE,CAAC;UAAE1C,MAAM,EAAE,KAAK;UAAEpB,UAAU,EAAE;QAAU;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrElD,OAAA;QAAMwC,KAAK,EAAE;UAAEE,OAAO,EAAE;QAAS,CAAE;QAAAI,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7ClD,OAAA;QAAKwC,KAAK,EAAE;UAAE+D,IAAI,EAAE,CAAC;UAAE1C,MAAM,EAAE,KAAK;UAAEpB,UAAU,EAAE;QAAU;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE,CAAC,eAGNlD,OAAA;MAAKwC,KAAK,EAAE;QAAE0C,SAAS,EAAE;MAAS,CAAE;MAAApC,QAAA,gBAClC9C,OAAA;QAAIwC,KAAK,EAAE;UAAE2C,MAAM,EAAE,YAAY;UAAE7B,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAEtE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLlD,OAAA;QAAGwC,KAAK,EAAE;UAAE2C,MAAM,EAAE,YAAY;UAAE7B,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAErE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJlD,OAAA;QACE+E,IAAI,EAAC,QAAQ;QACbtB,OAAO,EAAElB,wBAAyB;QAClC4D,QAAQ,EAAExF,WAAY;QACtB6B,KAAK,EAAE;UACLC,UAAU,EAAE9B,WAAW,GAAG,SAAS,GAAG,SAAS;UAC/C2C,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdpB,OAAO,EAAE,WAAW;UACpBC,YAAY,EAAE,KAAK;UACnBY,QAAQ,EAAE,MAAM;UAChBF,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAEvD,WAAW,GAAG,aAAa,GAAG,SAAS;UAC/CwD,UAAU,EAAE,eAAe;UAC3Bf,OAAO,EAAE,MAAM;UACfY,UAAU,EAAE,QAAQ;UACpBoC,GAAG,EAAE,KAAK;UACVjB,MAAM,EAAE;QACV,CAAE;QAAArC,QAAA,EAEDnC,WAAW,gBACVX,OAAA,CAAAE,SAAA;UAAA4C,QAAA,gBACE9C,OAAA;YAAMwC,KAAK,EAAE;cACXuD,KAAK,EAAE,MAAM;cACblC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,uBAAuB;cAC/BuC,SAAS,EAAE,iBAAiB;cAC5B1D,YAAY,EAAE,KAAK;cACnB2D,SAAS,EAAE;YACb;UAAE;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,yBAEZ;QAAA,eAAE,CAAC,gBAEHlD,OAAA,CAAAE,SAAA;UAAA4C,QAAA,EAAE;QAEF,gBAAE;MACH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENlD,OAAA;MAAOwG,GAAG;MAAA1D,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC5C,EAAA,CAlVIH,YAAY;AAAAsG,EAAA,GAAZtG,YAAY;AAoVlB,eAAeA,YAAY;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}