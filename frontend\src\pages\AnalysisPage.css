.analysis-page {
  min-height: 100vh;
  background: #f8f9fa;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 24px 0;
  margin-bottom: 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.header-content h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 28px;
  font-weight: 600;
}

.header-content p {
  margin: 0 0 16px 0;
  color: #666;
  font-size: 16px;
  line-height: 1.5;
}

.reset-button {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reset-button:hover {
  background: #5a6268;
}

.error-banner {
  background: #f8d7da;
  color: #721c24;
  padding: 12px 24px;
  margin: 0 24px 24px 24px;
  border-radius: 6px;
  border: 1px solid #f5c6cb;
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.error-icon {
  font-size: 18px;
}

.error-text {
  flex: 1;
  font-weight: 500;
}

.error-close {
  background: none;
  border: none;
  color: #721c24;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.error-close:hover {
  background: rgba(114, 28, 36, 0.1);
}

.page-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }

  .header-content h1 {
    font-size: 24px;
  }

  .header-content p {
    font-size: 14px;
  }

  .page-content {
    padding: 0 16px;
  }

  .error-banner {
    margin: 0 16px 24px 16px;
  }
}
