{"ast": null, "code": "/**\n * 简化的上传组件\n */import React,{useState}from'react';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const SimpleUpload=_ref=>{let{onUploadSuccess,onUploadError}=_ref;const[selectedFile,setSelectedFile]=useState(null);const[description,setDescription]=useState('');const[isUploading,setIsUploading]=useState(false);const handleFileSelect=event=>{const file=event.target.files[0];if(file){setSelectedFile(file);}};const handleSubmit=async event=>{event.preventDefault();if(!selectedFile){onUploadError('请选择图片文件');return;}if(!description.trim()){onUploadError('请输入界面功能描述');return;}setIsUploading(true);try{const formData=new FormData();formData.append('image_file',selectedFile);formData.append('description',description.trim());const response=await fetch('http://localhost:8001/api/v1/upload',{method:'POST',body:formData});if(!response.ok){const errorData=await response.json();throw new Error(errorData.detail||\"HTTP error! status: \".concat(response.status));}const result=await response.json();onUploadSuccess(result);// 重置表单\nsetSelectedFile(null);setDescription('');event.target.reset();}catch(error){console.error('Upload error:',error);onUploadError(error.message||'上传失败，请重试');}finally{setIsUploading(false);}};return/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',padding:'24px',borderRadius:'12px',boxShadow:'0 2px 12px rgba(0,0,0,0.1)',marginBottom:'24px'},children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'16px'},children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"file-input\",style:{display:'block',marginBottom:'8px',fontWeight:'500'},children:\"\\u9009\\u62E9\\u56FE\\u7247\\u6587\\u4EF6:\"}),/*#__PURE__*/_jsx(\"input\",{id:\"file-input\",type:\"file\",accept:\"image/*\",onChange:handleFileSelect,style:{width:'100%',padding:'8px',border:'1px solid #ddd',borderRadius:'4px'}}),selectedFile&&/*#__PURE__*/_jsxs(\"p\",{style:{marginTop:'8px',color:'#666',fontSize:'14px'},children:[\"\\u5DF2\\u9009\\u62E9: \",selectedFile.name,\" (\",(selectedFile.size/1024/1024).toFixed(2),\" MB)\"]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'16px'},children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"description\",style:{display:'block',marginBottom:'8px',fontWeight:'500'},children:\"\\u754C\\u9762\\u529F\\u80FD\\u63CF\\u8FF0:\"}),/*#__PURE__*/_jsx(\"textarea\",{id:\"description\",value:description,onChange:e=>setDescription(e.target.value),placeholder:\"\\u8BF7\\u63CF\\u8FF0\\u8FD9\\u4E2A\\u754C\\u9762\\u7684\\u4E3B\\u8981\\u529F\\u80FD\\uFF0C\\u4F8B\\u5982\\uFF1A\\u8FD9\\u662F\\u4E00\\u4E2A\\u767B\\u5F55\\u9875\\u9762\\uFF0C\\u5305\\u542B\\u7528\\u6237\\u540D\\u5BC6\\u7801\\u8F93\\u5165\\u6846\\u548C\\u767B\\u5F55\\u6309\\u94AE...\",rows:4,style:{width:'100%',padding:'12px',border:'1px solid #ddd',borderRadius:'4px',resize:'vertical',fontFamily:'inherit'},required:true}),/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'right',fontSize:'12px',color:'#666',marginTop:'4px'},children:[description.length,\"/500\"]})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:isUploading||!selectedFile||!description.trim(),style:{background:isUploading?'#6c757d':'#007bff',color:'white',border:'none',padding:'12px 24px',borderRadius:'6px',fontSize:'16px',fontWeight:'500',cursor:isUploading?'not-allowed':'pointer',transition:'all 0.3s ease',display:'flex',alignItems:'center',gap:'8px'},children:isUploading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{style:{width:'16px',height:'16px',border:'2px solid transparent',borderTop:'2px solid white',borderRadius:'50%',animation:'spin 1s linear infinite'}}),\"\\u5206\\u6790\\u4E2D...\"]}):'🚀 开始分析'})]}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:\"\\n        @keyframes spin {\\n          0% { transform: rotate(0deg); }\\n          100% { transform: rotate(360deg); }\\n        }\\n      \"})]});};export default SimpleUpload;", "map": {"version": 3, "names": ["React", "useState", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "SimpleUpload", "_ref", "onUploadSuccess", "onUploadError", "selectedFile", "setSelectedFile", "description", "setDescription", "isUploading", "setIsUploading", "handleFileSelect", "event", "file", "target", "files", "handleSubmit", "preventDefault", "trim", "formData", "FormData", "append", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "concat", "status", "result", "reset", "error", "console", "message", "style", "background", "padding", "borderRadius", "boxShadow", "marginBottom", "children", "onSubmit", "htmlFor", "display", "fontWeight", "id", "type", "accept", "onChange", "width", "border", "marginTop", "color", "fontSize", "name", "size", "toFixed", "value", "e", "placeholder", "rows", "resize", "fontFamily", "required", "textAlign", "length", "disabled", "cursor", "transition", "alignItems", "gap", "height", "borderTop", "animation"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/components/SimpleUpload.js"], "sourcesContent": ["/**\n * 简化的上传组件\n */\nimport React, { useState } from 'react';\n\nconst SimpleUpload = ({ onUploadSuccess, onUploadError }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n\n  const handleFileSelect = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n\n  const handleSubmit = async (event) => {\n    event.preventDefault();\n    \n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n\n    setIsUploading(true);\n\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n\n      const response = await fetch('http://localhost:8001/api/v1/upload', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      onUploadSuccess(result);\n\n      // 重置表单\n      setSelectedFile(null);\n      setDescription('');\n      event.target.reset();\n    } catch (error) {\n      console.error('Upload error:', error);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  return (\n    <div style={{ \n      background: 'white', \n      padding: '24px', \n      borderRadius: '12px', \n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n      marginBottom: '24px'\n    }}>\n      <h3>📁 上传UI界面截图</h3>\n      <form onSubmit={handleSubmit}>\n        <div style={{ marginBottom: '16px' }}>\n          <label htmlFor=\"file-input\" style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>\n            选择图片文件:\n          </label>\n          <input\n            id=\"file-input\"\n            type=\"file\"\n            accept=\"image/*\"\n            onChange={handleFileSelect}\n            style={{\n              width: '100%',\n              padding: '8px',\n              border: '1px solid #ddd',\n              borderRadius: '4px'\n            }}\n          />\n          {selectedFile && (\n            <p style={{ marginTop: '8px', color: '#666', fontSize: '14px' }}>\n              已选择: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)\n            </p>\n          )}\n        </div>\n\n        <div style={{ marginBottom: '16px' }}>\n          <label htmlFor=\"description\" style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>\n            界面功能描述:\n          </label>\n          <textarea\n            id=\"description\"\n            value={description}\n            onChange={(e) => setDescription(e.target.value)}\n            placeholder=\"请描述这个界面的主要功能，例如：这是一个登录页面，包含用户名密码输入框和登录按钮...\"\n            rows={4}\n            style={{\n              width: '100%',\n              padding: '12px',\n              border: '1px solid #ddd',\n              borderRadius: '4px',\n              resize: 'vertical',\n              fontFamily: 'inherit'\n            }}\n            required\n          />\n          <div style={{ textAlign: 'right', fontSize: '12px', color: '#666', marginTop: '4px' }}>\n            {description.length}/500\n          </div>\n        </div>\n\n        <button\n          type=\"submit\"\n          disabled={isUploading || !selectedFile || !description.trim()}\n          style={{\n            background: isUploading ? '#6c757d' : '#007bff',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            fontSize: '16px',\n            fontWeight: '500',\n            cursor: isUploading ? 'not-allowed' : 'pointer',\n            transition: 'all 0.3s ease',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          }}\n        >\n          {isUploading ? (\n            <>\n              <span style={{\n                width: '16px',\n                height: '16px',\n                border: '2px solid transparent',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }}></span>\n              分析中...\n            </>\n          ) : (\n            '🚀 开始分析'\n          )}\n        </button>\n      </form>\n\n      <style jsx>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default SimpleUpload;\n"], "mappings": "AAAA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAExC,KAAM,CAAAC,YAAY,CAAGC,IAAA,EAAwC,IAAvC,CAAEC,eAAe,CAAEC,aAAc,CAAC,CAAAF,IAAA,CACtD,KAAM,CAACG,YAAY,CAAEC,eAAe,CAAC,CAAGZ,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACa,WAAW,CAAEC,cAAc,CAAC,CAAGd,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACe,WAAW,CAAEC,cAAc,CAAC,CAAGhB,QAAQ,CAAC,KAAK,CAAC,CAErD,KAAM,CAAAiB,gBAAgB,CAAIC,KAAK,EAAK,CAClC,KAAM,CAAAC,IAAI,CAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAClC,GAAIF,IAAI,CAAE,CACRP,eAAe,CAACO,IAAI,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAG,YAAY,CAAG,KAAO,CAAAJ,KAAK,EAAK,CACpCA,KAAK,CAACK,cAAc,CAAC,CAAC,CAEtB,GAAI,CAACZ,YAAY,CAAE,CACjBD,aAAa,CAAC,SAAS,CAAC,CACxB,OACF,CAEA,GAAI,CAACG,WAAW,CAACW,IAAI,CAAC,CAAC,CAAE,CACvBd,aAAa,CAAC,WAAW,CAAC,CAC1B,OACF,CAEAM,cAAc,CAAC,IAAI,CAAC,CAEpB,GAAI,CACF,KAAM,CAAAS,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,YAAY,CAAEhB,YAAY,CAAC,CAC3Cc,QAAQ,CAACE,MAAM,CAAC,aAAa,CAAEd,WAAW,CAACW,IAAI,CAAC,CAAC,CAAC,CAElD,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,qCAAqC,CAAE,CAClEC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEN,QACR,CAAC,CAAC,CAEF,GAAI,CAACG,QAAQ,CAACI,EAAE,CAAE,CAChB,KAAM,CAAAC,SAAS,CAAG,KAAM,CAAAL,QAAQ,CAACM,IAAI,CAAC,CAAC,CACvC,KAAM,IAAI,CAAAC,KAAK,CAACF,SAAS,CAACG,MAAM,yBAAAC,MAAA,CAA2BT,QAAQ,CAACU,MAAM,CAAE,CAAC,CAC/E,CAEA,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAX,QAAQ,CAACM,IAAI,CAAC,CAAC,CACpCzB,eAAe,CAAC8B,MAAM,CAAC,CAEvB;AACA3B,eAAe,CAAC,IAAI,CAAC,CACrBE,cAAc,CAAC,EAAE,CAAC,CAClBI,KAAK,CAACE,MAAM,CAACoB,KAAK,CAAC,CAAC,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrC/B,aAAa,CAAC+B,KAAK,CAACE,OAAO,EAAI,UAAU,CAAC,CAC5C,CAAC,OAAS,CACR3B,cAAc,CAAC,KAAK,CAAC,CACvB,CACF,CAAC,CAED,mBACEZ,KAAA,QAAKwC,KAAK,CAAE,CACVC,UAAU,CAAE,OAAO,CACnBC,OAAO,CAAE,MAAM,CACfC,YAAY,CAAE,MAAM,CACpBC,SAAS,CAAE,4BAA4B,CACvCC,YAAY,CAAE,MAChB,CAAE,CAAAC,QAAA,eACAhD,IAAA,OAAAgD,QAAA,CAAI,qDAAW,CAAI,CAAC,cACpB9C,KAAA,SAAM+C,QAAQ,CAAE7B,YAAa,CAAA4B,QAAA,eAC3B9C,KAAA,QAAKwC,KAAK,CAAE,CAAEK,YAAY,CAAE,MAAO,CAAE,CAAAC,QAAA,eACnChD,IAAA,UAAOkD,OAAO,CAAC,YAAY,CAACR,KAAK,CAAE,CAAES,OAAO,CAAE,OAAO,CAAEJ,YAAY,CAAE,KAAK,CAAEK,UAAU,CAAE,KAAM,CAAE,CAAAJ,QAAA,CAAC,uCAEjG,CAAO,CAAC,cACRhD,IAAA,UACEqD,EAAE,CAAC,YAAY,CACfC,IAAI,CAAC,MAAM,CACXC,MAAM,CAAC,SAAS,CAChBC,QAAQ,CAAEzC,gBAAiB,CAC3B2B,KAAK,CAAE,CACLe,KAAK,CAAE,MAAM,CACbb,OAAO,CAAE,KAAK,CACdc,MAAM,CAAE,gBAAgB,CACxBb,YAAY,CAAE,KAChB,CAAE,CACH,CAAC,CACDpC,YAAY,eACXP,KAAA,MAAGwC,KAAK,CAAE,CAAEiB,SAAS,CAAE,KAAK,CAAEC,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAb,QAAA,EAAC,sBAC1D,CAACvC,YAAY,CAACqD,IAAI,CAAC,IAAE,CAAC,CAACrD,YAAY,CAACsD,IAAI,CAAG,IAAI,CAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,MAC1E,EAAG,CACJ,EACE,CAAC,cAEN9D,KAAA,QAAKwC,KAAK,CAAE,CAAEK,YAAY,CAAE,MAAO,CAAE,CAAAC,QAAA,eACnChD,IAAA,UAAOkD,OAAO,CAAC,aAAa,CAACR,KAAK,CAAE,CAAES,OAAO,CAAE,OAAO,CAAEJ,YAAY,CAAE,KAAK,CAAEK,UAAU,CAAE,KAAM,CAAE,CAAAJ,QAAA,CAAC,uCAElG,CAAO,CAAC,cACRhD,IAAA,aACEqD,EAAE,CAAC,aAAa,CAChBY,KAAK,CAAEtD,WAAY,CACnB6C,QAAQ,CAAGU,CAAC,EAAKtD,cAAc,CAACsD,CAAC,CAAChD,MAAM,CAAC+C,KAAK,CAAE,CAChDE,WAAW,CAAC,qPAA6C,CACzDC,IAAI,CAAE,CAAE,CACR1B,KAAK,CAAE,CACLe,KAAK,CAAE,MAAM,CACbb,OAAO,CAAE,MAAM,CACfc,MAAM,CAAE,gBAAgB,CACxBb,YAAY,CAAE,KAAK,CACnBwB,MAAM,CAAE,UAAU,CAClBC,UAAU,CAAE,SACd,CAAE,CACFC,QAAQ,MACT,CAAC,cACFrE,KAAA,QAAKwC,KAAK,CAAE,CAAE8B,SAAS,CAAE,OAAO,CAAEX,QAAQ,CAAE,MAAM,CAAED,KAAK,CAAE,MAAM,CAAED,SAAS,CAAE,KAAM,CAAE,CAAAX,QAAA,EACnFrC,WAAW,CAAC8D,MAAM,CAAC,MACtB,EAAK,CAAC,EACH,CAAC,cAENzE,IAAA,WACEsD,IAAI,CAAC,QAAQ,CACboB,QAAQ,CAAE7D,WAAW,EAAI,CAACJ,YAAY,EAAI,CAACE,WAAW,CAACW,IAAI,CAAC,CAAE,CAC9DoB,KAAK,CAAE,CACLC,UAAU,CAAE9B,WAAW,CAAG,SAAS,CAAG,SAAS,CAC/C+C,KAAK,CAAE,OAAO,CACdF,MAAM,CAAE,MAAM,CACdd,OAAO,CAAE,WAAW,CACpBC,YAAY,CAAE,KAAK,CACnBgB,QAAQ,CAAE,MAAM,CAChBT,UAAU,CAAE,KAAK,CACjBuB,MAAM,CAAE9D,WAAW,CAAG,aAAa,CAAG,SAAS,CAC/C+D,UAAU,CAAE,eAAe,CAC3BzB,OAAO,CAAE,MAAM,CACf0B,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,KACP,CAAE,CAAA9B,QAAA,CAEDnC,WAAW,cACVX,KAAA,CAAAE,SAAA,EAAA4C,QAAA,eACEhD,IAAA,SAAM0C,KAAK,CAAE,CACXe,KAAK,CAAE,MAAM,CACbsB,MAAM,CAAE,MAAM,CACdrB,MAAM,CAAE,uBAAuB,CAC/BsB,SAAS,CAAE,iBAAiB,CAC5BnC,YAAY,CAAE,KAAK,CACnBoC,SAAS,CAAE,yBACb,CAAE,CAAO,CAAC,wBAEZ,EAAE,CAAC,CAEH,SACD,CACK,CAAC,EACL,CAAC,cAEPjF,IAAA,UAAOD,GAAG,MAAAiD,QAAA,4IAKD,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAA3C,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}