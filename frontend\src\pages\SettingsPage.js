/**
 * 设置页面
 */
import React, { useState } from 'react';

const SettingsPage = () => {
  const [settings, setSettings] = useState({
    // API设置
    apiEndpoint: 'http://localhost:8001',
    timeout: 30,
    
    // 分析设置
    maxFileSize: 10,
    supportedFormats: ['png', 'jpg', 'jpeg', 'gif'],
    autoSave: true,
    
    // 智能体设置
    enableElementDetection: true,
    enableInteractionAnalysis: true,
    enableScriptGeneration: true,
    
    // 界面设置
    theme: 'light',
    language: 'zh-CN',
    showNotifications: true
  });

  const [saved, setSaved] = useState(false);

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleArraySettingChange = (key, index, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: prev[key].map((item, i) => i === index ? value : item)
    }));
  };

  const handleSaveSettings = () => {
    // 模拟保存设置
    localStorage.setItem('ui-automation-settings', JSON.stringify(settings));
    setSaved(true);
    setTimeout(() => setSaved(false), 3000);
  };

  const handleResetSettings = () => {
    if (window.confirm('确定要重置所有设置吗？')) {
      setSettings({
        apiEndpoint: 'http://localhost:8001',
        timeout: 30,
        maxFileSize: 10,
        supportedFormats: ['png', 'jpg', 'jpeg', 'gif'],
        autoSave: true,
        enableElementDetection: true,
        enableInteractionAnalysis: true,
        enableScriptGeneration: true,
        theme: 'light',
        language: 'zh-CN',
        showNotifications: true
      });
    }
  };

  return (
    <div className="settings-page">
      <div className="page-header">
        <h1>⚙️ 系统设置</h1>
        <p>配置系统参数和个人偏好</p>
      </div>

      <div className="page-content">
        <div className="settings-container">
          {/* API设置 */}
          <div className="settings-section">
            <h3>🔗 API设置</h3>
            <div className="setting-item">
              <label>API端点</label>
              <input
                type="text"
                value={settings.apiEndpoint}
                onChange={(e) => handleSettingChange('apiEndpoint', e.target.value)}
                placeholder="http://localhost:8001"
              />
            </div>
            <div className="setting-item">
              <label>请求超时 (秒)</label>
              <input
                type="number"
                value={settings.timeout}
                onChange={(e) => handleSettingChange('timeout', parseInt(e.target.value))}
                min="5"
                max="300"
              />
            </div>
          </div>

          {/* 分析设置 */}
          <div className="settings-section">
            <h3>🔍 分析设置</h3>
            <div className="setting-item">
              <label>最大文件大小 (MB)</label>
              <input
                type="number"
                value={settings.maxFileSize}
                onChange={(e) => handleSettingChange('maxFileSize', parseInt(e.target.value))}
                min="1"
                max="100"
              />
            </div>
            <div className="setting-item">
              <label>自动保存结果</label>
              <input
                type="checkbox"
                checked={settings.autoSave}
                onChange={(e) => handleSettingChange('autoSave', e.target.checked)}
              />
            </div>
          </div>

          {/* 智能体设置 */}
          <div className="settings-section">
            <h3>🤖 智能体设置</h3>
            <div className="setting-item">
              <label>启用元素识别智能体</label>
              <input
                type="checkbox"
                checked={settings.enableElementDetection}
                onChange={(e) => handleSettingChange('enableElementDetection', e.target.checked)}
              />
            </div>
            <div className="setting-item">
              <label>启用交互分析智能体</label>
              <input
                type="checkbox"
                checked={settings.enableInteractionAnalysis}
                onChange={(e) => handleSettingChange('enableInteractionAnalysis', e.target.checked)}
              />
            </div>
            <div className="setting-item">
              <label>启用脚本生成智能体</label>
              <input
                type="checkbox"
                checked={settings.enableScriptGeneration}
                onChange={(e) => handleSettingChange('enableScriptGeneration', e.target.checked)}
              />
            </div>
          </div>

          {/* 界面设置 */}
          <div className="settings-section">
            <h3>🎨 界面设置</h3>
            <div className="setting-item">
              <label>主题</label>
              <select
                value={settings.theme}
                onChange={(e) => handleSettingChange('theme', e.target.value)}
              >
                <option value="light">浅色主题</option>
                <option value="dark">深色主题</option>
                <option value="auto">跟随系统</option>
              </select>
            </div>
            <div className="setting-item">
              <label>语言</label>
              <select
                value={settings.language}
                onChange={(e) => handleSettingChange('language', e.target.value)}
              >
                <option value="zh-CN">简体中文</option>
                <option value="en-US">English</option>
              </select>
            </div>
            <div className="setting-item">
              <label>显示通知</label>
              <input
                type="checkbox"
                checked={settings.showNotifications}
                onChange={(e) => handleSettingChange('showNotifications', e.target.checked)}
              />
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="settings-actions">
            <button className="btn-primary" onClick={handleSaveSettings}>
              {saved ? '✅ 已保存' : '💾 保存设置'}
            </button>
            <button className="btn-secondary" onClick={handleResetSettings}>
              🔄 重置设置
            </button>
          </div>
        </div>
      </div>

      <style>{`
        .settings-page {
          min-height: 100vh;
          background: #f8f9fa;
        }

        .page-header {
          background: white;
          border-bottom: 1px solid #e9ecef;
          padding: 24px 0;
          margin-bottom: 24px;
        }

        .page-header h1 {
          margin: 0 0 8px 0;
          color: #333;
          font-size: 28px;
          font-weight: 600;
          max-width: 1200px;
          margin-left: auto;
          margin-right: auto;
          padding: 0 24px;
        }

        .page-header p {
          margin: 0;
          color: #666;
          font-size: 16px;
          max-width: 1200px;
          margin-left: auto;
          margin-right: auto;
          padding: 0 24px;
        }

        .page-content {
          max-width: 800px;
          margin: 0 auto;
          padding: 0 24px;
        }

        .settings-container {
          background: white;
          border-radius: 12px;
          padding: 24px;
          box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }

        .settings-section {
          margin-bottom: 32px;
          padding-bottom: 24px;
          border-bottom: 1px solid #e9ecef;
        }

        .settings-section:last-of-type {
          border-bottom: none;
          margin-bottom: 24px;
        }

        .settings-section h3 {
          margin: 0 0 20px 0;
          color: #333;
          font-size: 18px;
          font-weight: 600;
        }

        .setting-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          padding: 12px 0;
        }

        .setting-item:last-child {
          margin-bottom: 0;
        }

        .setting-item label {
          font-weight: 500;
          color: #333;
          flex: 1;
        }

        .setting-item input,
        .setting-item select {
          width: 200px;
          padding: 8px 12px;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 14px;
        }

        .setting-item input[type="checkbox"] {
          width: auto;
          transform: scale(1.2);
        }

        .setting-item input:focus,
        .setting-item select:focus {
          outline: none;
          border-color: #007bff;
          box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .settings-actions {
          display: flex;
          gap: 12px;
          justify-content: center;
          padding-top: 24px;
          border-top: 1px solid #e9ecef;
        }

        .btn-primary,
        .btn-secondary {
          border: none;
          padding: 12px 24px;
          border-radius: 6px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          min-width: 120px;
        }

        .btn-primary {
          background: #007bff;
          color: white;
        }

        .btn-primary:hover {
          background: #0056b3;
        }

        .btn-secondary {
          background: #6c757d;
          color: white;
        }

        .btn-secondary:hover {
          background: #5a6268;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .page-header h1,
          .page-header p,
          .page-content {
            padding: 0 16px;
          }

          .settings-container {
            padding: 16px;
          }

          .setting-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
          }

          .setting-item input,
          .setting-item select {
            width: 100%;
          }

          .settings-actions {
            flex-direction: column;
          }
        }
      `}</style>
    </div>
  );
};

export default SettingsPage;
