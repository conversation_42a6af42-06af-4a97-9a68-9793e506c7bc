{"version": 3, "file": "static/css/main.6d3263a6.css", "mappings": "AAAA,KACE,QAMF,CAEA,KACE,uEAEF,CCZA,SAME,kDAA6D,CAM7D,+BAAsC,CALtC,UAAY,CAGZ,YAAa,CACb,qBAAsB,CAPtB,YAAa,CAFb,MAAO,CADP,cAAe,CAEf,KAAM,CAKN,uBAAyB,CAHzB,WAAY,CAIZ,YAIF,CAEA,mBACE,UACF,CAEA,gBAEE,iCAA8C,CAG9C,6BAA8B,CAJ9B,YAKF,CAEA,sBAJE,kBAAmB,CADnB,YASF,CAJA,MAGE,QACF,CAEA,WACE,cAAe,CACf,aACF,CAEA,cAEE,cAAe,CACf,eAAgB,CAChB,eAAgB,CAHhB,QAIF,CAEA,aAEE,cAAe,CAEf,eAAgB,CAHhB,QAAS,CAET,UAEF,CAEA,cASE,kBAAmB,CARnB,oBAAiC,CACjC,WAAY,CAIZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CACf,YAAa,CAIb,cAAe,CAPf,WAAY,CAKZ,sBAAuB,CACvB,uBAAyB,CAPzB,UASF,CAEA,oBACE,gBACF,CAEA,aACE,QAAO,CAEP,eAAgB,CADhB,cAEF,CAEA,UACE,eAAgB,CAChB,QAAS,CACT,SACF,CAEA,UACE,iBACF,CAEA,UASE,kBAAmB,CAPnB,eAAgB,CAChB,WAAY,CASZ,eAAgB,CARhB,UAAY,CAGZ,cAAe,CACf,YAAa,CAEb,QAAS,CALT,iBAAkB,CAQlB,iBAAkB,CAPlB,eAAgB,CAKhB,uBAAyB,CAVzB,UAaF,CAEA,gBACE,oBACF,CAEA,iBACE,gBAAiC,CACjC,2BACF,CAEA,UACE,cAAe,CACf,aAAc,CACd,cACF,CAEA,aACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,UACE,cAAe,CACf,eAAgB,CAChB,eACF,CAEA,UACE,cAAe,CAEf,eAAgB,CADhB,UAEF,CAEA,gBAEE,8BAA2C,CAD3C,YAEF,CAEA,gBAEE,cAAe,CAEf,eAAgB,CAHhB,QAAS,CAET,UAEF,CAGA,yBAME,4BAEE,2BAA4B,CAD5B,UAEF,CAEA,qBACE,uBACF,CACF,CCjKA,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,KAIE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAyB,CACzB,UAAW,CANX,mIAEY,CAKZ,eACF,CAEA,KAEE,YAAa,CADb,gBAEF,CAEA,WAEE,aAAc,CADd,gBAAiB,CAEjB,cACF,CAGA,YACE,kDAA6D,CAC7D,UAAY,CACZ,cAAe,CAEf,iBAAkB,CADlB,iBAEF,CAEA,eACE,gBAAiB,CACjB,eAAgB,CAChB,kBAAmB,CACnB,+BACF,CAEA,cACE,gBAAiB,CAEjB,kBAAmB,CADnB,UAEF,CAEA,cACE,gBAAoC,CAEpC,0BAA0C,CAE1C,kBAAmB,CAHnB,UAAY,CAIZ,cAAe,CACf,cAAe,CAIf,SAAU,CAPV,gBAAiB,CAKjB,iBAAkB,CAClB,QAAS,CAFT,uBAIF,CAEA,oBACE,oBAAoC,CACpC,0BACF,CAGA,UACE,QAAO,CACP,iBAAkB,CAElB,gBAAiB,CADjB,+BAEF,CAEA,4BACE,gBACF,CAGA,gBAEE,gBAAiB,CADjB,UAEF,CAGA,cAOE,kBAAmB,CANnB,kBAAmB,CAQnB,wBAAyB,CALzB,iBAAkB,CAFlB,aAAc,CAId,YAAa,CAEb,QAAS,CAHT,kBAAmB,CAFnB,iBAOF,CAEA,YACE,cACF,CAEA,YACE,QACF,CAEA,aACE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAJlB,aAAc,CACd,cAAe,CACf,cAAe,CACf,WAAY,CAEZ,oCACF,CAEA,mBACE,oBACF,CAGA,cACE,eAAiB,CACjB,kBAAmB,CAEnB,+BAAyC,CADzC,YAAa,CAEb,iBACF,CAEA,iBACE,UAAW,CAEX,gBAAiB,CADjB,kBAEF,CAEA,+DAIE,yBAA0B,CAC1B,kBAAmB,CACnB,aAAc,CAHd,iBAIF,CAEA,wEAKE,aAAc,CAFd,gBAAiB,CACjB,kBAEF,CAEA,qEAGE,aAAc,CACd,kBACF,CAEA,aACE,kDAA6D,CAE7D,WAAY,CAEZ,iBAAkB,CAKlB,8BAA8C,CAR9C,UAAY,CAMZ,cAAe,CAFf,cAAe,CACf,eAAgB,CAHhB,iBAAkB,CAKlB,uBAEF,CAEA,mBAEE,+BAA+C,CAD/C,0BAEF,CAGA,eAKE,gBAAkB,CAFlB,eAAgB,CADhB,eAIF,CAEA,eAGE,kBAAmB,CAGnB,kBAAmB,CAEnB,6BAA8B,CAD9B,iBAAkB,CANlB,YAAa,CACb,6BAA8B,CAG9B,iBAAkB,CADlB,iBAKF,CAMA,wCAHE,eAQF,CALA,QAGE,kBAAmB,CAFnB,cAAe,CACf,eAGF,CAEA,mBACE,kBAAmB,CACnB,UACF,CAEA,gBACE,kBAAmB,CACnB,UACF,CAEA,kBACE,kBAAmB,CACnB,aACF,CAGA,cAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,aACF,CAEA,gBACE,kBAAmB,CAGnB,6BAA8B,CAD9B,iBAAkB,CADlB,YAGF,CAEA,mBACE,UAAW,CAEX,gBAAiB,CADjB,iBAEF,CAEA,kBACE,UAAW,CACX,cACF,CAGA,YACE,kBAAmB,CACnB,aAAc,CAGd,eAAgB,CAFhB,cAAe,CACf,iBAEF,CAEA,cAEE,cAAe,CADf,QAEF,CAGA,yBAKE,sCACE,aACF,CAEA,eACE,cACF,CAEA,cACE,cACF,CAEA,cAEE,eAAgB,CADhB,eAEF,CAEA,cACE,YACF,CAEA,cACE,yBACF,CACF", "sources": ["index.css", "components/Sidebar.css", "App.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n", ".sidebar {\n  position: fixed;\n  left: 0;\n  top: 0;\n  height: 100vh;\n  width: 280px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  transition: all 0.3s ease;\n  z-index: 1000;\n  display: flex;\n  flex-direction: column;\n  box-shadow: 2px 0 10px rgba(0,0,0,0.1);\n}\n\n.sidebar.collapsed {\n  width: 70px;\n}\n\n.sidebar-header {\n  padding: 20px;\n  border-bottom: 1px solid rgba(255,255,255,0.1);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.logo {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.logo-icon {\n  font-size: 32px;\n  line-height: 1;\n}\n\n.logo-text h2 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  line-height: 1.2;\n}\n\n.logo-text p {\n  margin: 0;\n  font-size: 14px;\n  opacity: 0.8;\n  line-height: 1.2;\n}\n\n.collapse-btn {\n  background: rgba(255,255,255,0.1);\n  border: none;\n  color: white;\n  width: 30px;\n  height: 30px;\n  border-radius: 6px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.2s ease;\n  font-size: 14px;\n}\n\n.collapse-btn:hover {\n  background: rgba(255,255,255,0.2);\n}\n\n.sidebar-nav {\n  flex: 1;\n  padding: 20px 0;\n  overflow-y: auto;\n}\n\n.nav-list {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n.nav-item {\n  margin-bottom: 8px;\n}\n\n.nav-link {\n  width: 100%;\n  background: none;\n  border: none;\n  color: white;\n  padding: 16px 20px;\n  text-align: left;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  transition: all 0.2s ease;\n  border-radius: 0;\n  position: relative;\n}\n\n.nav-link:hover {\n  background: rgba(255,255,255,0.1);\n}\n\n.nav-link.active {\n  background: rgba(255,255,255,0.2);\n  border-right: 3px solid white;\n}\n\n.nav-icon {\n  font-size: 20px;\n  line-height: 1;\n  min-width: 20px;\n}\n\n.nav-content {\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n}\n\n.nav-name {\n  font-size: 16px;\n  font-weight: 500;\n  line-height: 1.2;\n}\n\n.nav-desc {\n  font-size: 12px;\n  opacity: 0.8;\n  line-height: 1.2;\n}\n\n.sidebar-footer {\n  padding: 20px;\n  border-top: 1px solid rgba(255,255,255,0.1);\n}\n\n.version-info p {\n  margin: 0;\n  font-size: 12px;\n  opacity: 0.7;\n  line-height: 1.4;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .sidebar {\n    width: 100%;\n    transform: translateX(-100%);\n  }\n  \n  .sidebar.collapsed {\n    width: 100%;\n    transform: translateX(-100%);\n  }\n  \n  .sidebar.mobile-open {\n    transform: translateX(0);\n  }\n}\n", "/* UI自动化分析平台样式 */\n\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',\n    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f5f7fa;\n  color: #333;\n  line-height: 1.6;\n}\n\n.app {\n  min-height: 100vh;\n  display: flex;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 20px;\n}\n\n/* Header */\n.app-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 40px 0;\n  text-align: center;\n  position: relative;\n}\n\n.app-header h1 {\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin-bottom: 12px;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.app-header p {\n  font-size: 1.1rem;\n  opacity: 0.9;\n  margin-bottom: 20px;\n}\n\n.reset-button {\n  background: rgba(255, 255, 255, 0.2);\n  color: white;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  padding: 8px 16px;\n  border-radius: 20px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: all 0.3s ease;\n  position: absolute;\n  top: 20px;\n  left: 20px;\n}\n\n.reset-button:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: translateY(-1px);\n}\n\n/* Main */\n.app-main {\n  flex: 1;\n  margin-left: 280px;\n  transition: margin-left 0.3s ease;\n  min-height: 100vh;\n}\n\n.app-main.sidebar-collapsed {\n  margin-left: 70px;\n}\n\n/* 页面容器 */\n.page-container {\n  width: 100%;\n  min-height: 100vh;\n}\n\n/* Error Banner */\n.error-banner {\n  background: #f8d7da;\n  color: #721c24;\n  padding: 16px 20px;\n  border-radius: 8px;\n  margin-bottom: 24px;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  border: 1px solid #f5c6cb;\n}\n\n.error-icon {\n  font-size: 20px;\n}\n\n.error-text {\n  flex: 1;\n}\n\n.error-close {\n  background: none;\n  border: none;\n  color: #721c24;\n  cursor: pointer;\n  font-size: 18px;\n  padding: 4px;\n  border-radius: 4px;\n  transition: background-color 0.3s ease;\n}\n\n.error-close:hover {\n  background: rgba(114, 28, 36, 0.1);\n}\n\n/* Temporary Content */\n.temp-content {\n  background: white;\n  border-radius: 12px;\n  padding: 32px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n  text-align: center;\n}\n\n.temp-content h2 {\n  color: #333;\n  margin-bottom: 20px;\n  font-size: 1.8rem;\n}\n\n.upload-placeholder,\n.analysis-placeholder,\n.results-placeholder {\n  padding: 40px 20px;\n  border: 2px dashed #e9ecef;\n  border-radius: 12px;\n  margin: 20px 0;\n}\n\n.upload-placeholder h3,\n.analysis-placeholder h3,\n.results-placeholder h3 {\n  font-size: 1.5rem;\n  margin-bottom: 16px;\n  color: #495057;\n}\n\n.upload-placeholder p,\n.analysis-placeholder p,\n.results-placeholder p {\n  color: #6c757d;\n  margin-bottom: 20px;\n}\n\n.demo-button {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 12px 24px;\n  border-radius: 8px;\n  font-size: 16px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\n}\n\n.demo-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);\n}\n\n/* Demo Progress */\n.demo-progress {\n  margin: 24px 0;\n  text-align: left;\n  max-width: 400px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.progress-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  margin-bottom: 8px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border-left: 4px solid #e9ecef;\n}\n\n.progress-item span:first-child {\n  font-weight: 500;\n}\n\n.status {\n  font-size: 14px;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-weight: 500;\n}\n\n.status.processing {\n  background: #cce5ff;\n  color: #0066cc;\n}\n\n.status.pending {\n  background: #f0f0f0;\n  color: #666;\n}\n\n.status.completed {\n  background: #d4edda;\n  color: #155724;\n}\n\n/* Demo Results */\n.demo-results {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin: 24px 0;\n}\n\n.result-section {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 8px;\n  border-left: 4px solid #007bff;\n}\n\n.result-section h4 {\n  color: #333;\n  margin-bottom: 8px;\n  font-size: 1.1rem;\n}\n\n.result-section p {\n  color: #666;\n  font-size: 14px;\n}\n\n/* Footer */\n.app-footer {\n  background: #343a40;\n  color: #adb5bd;\n  padding: 20px 0;\n  text-align: center;\n  margin-top: auto;\n}\n\n.app-footer p {\n  margin: 0;\n  font-size: 14px;\n}\n\n/* Responsive */\n@media (max-width: 768px) {\n  .app-main {\n    margin-left: 0;\n  }\n\n  .app-main.sidebar-collapsed {\n    margin-left: 0;\n  }\n\n  .app-header h1 {\n    font-size: 2rem;\n  }\n\n  .app-header p {\n    font-size: 1rem;\n  }\n\n  .reset-button {\n    position: static;\n    margin-top: 16px;\n  }\n\n  .temp-content {\n    padding: 20px;\n  }\n\n  .demo-results {\n    grid-template-columns: 1fr;\n  }\n}\n"], "names": [], "sourceRoot": ""}