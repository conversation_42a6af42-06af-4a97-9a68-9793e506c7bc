{"ast": null, "code": "/**\n * 侧边导航栏组件\n */import React from'react';import'./Sidebar.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Sidebar=_ref=>{let{currentPage,onPageChange,collapsed,onToggleCollapse}=_ref;const menuItems=[{id:'analysis',name:'UI分析',icon:'🔍',description:'上传界面截图进行AI分析'},{id:'history',name:'历史记录',icon:'📋',description:'查看分析历史和结果'},{id:'settings',name:'设置',icon:'⚙️',description:'系统配置和参数设置'},{id:'about',name:'关于',icon:'ℹ️',description:'平台信息和使用说明'}];return/*#__PURE__*/_jsxs(\"div\",{className:\"sidebar \".concat(collapsed?'collapsed':''),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"sidebar-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"logo\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"logo-icon\",children:\"\\uD83E\\uDD16\"}),!collapsed&&/*#__PURE__*/_jsxs(\"div\",{className:\"logo-text\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"UI\\u81EA\\u52A8\\u5316\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u5206\\u6790\\u5E73\\u53F0\"})]})]}),/*#__PURE__*/_jsx(\"button\",{className:\"collapse-btn\",onClick:onToggleCollapse,title:collapsed?'展开侧边栏':'收起侧边栏',children:collapsed?'→':'←'})]}),/*#__PURE__*/_jsx(\"nav\",{className:\"sidebar-nav\",children:/*#__PURE__*/_jsx(\"ul\",{className:\"nav-list\",children:menuItems.map(item=>/*#__PURE__*/_jsx(\"li\",{className:\"nav-item\",children:/*#__PURE__*/_jsxs(\"button\",{className:\"nav-link \".concat(currentPage===item.id?'active':''),onClick:()=>onPageChange(item.id),title:collapsed?item.name:item.description,children:[/*#__PURE__*/_jsx(\"span\",{className:\"nav-icon\",children:item.icon}),!collapsed&&/*#__PURE__*/_jsxs(\"div\",{className:\"nav-content\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"nav-name\",children:item.name}),/*#__PURE__*/_jsx(\"span\",{className:\"nav-desc\",children:item.description})]})]})},item.id))})}),!collapsed&&/*#__PURE__*/_jsx(\"div\",{className:\"sidebar-footer\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"version-info\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\u7248\\u672C v1.0.0\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u57FA\\u4E8EAI\\u667A\\u80FD\\u4F53\"})]})})]});};export default Sidebar;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "Sidebar", "_ref", "currentPage", "onPageChange", "collapsed", "onToggleCollapse", "menuItems", "id", "name", "icon", "description", "className", "concat", "children", "onClick", "title", "map", "item"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/components/Sidebar.js"], "sourcesContent": ["/**\n * 侧边导航栏组件\n */\nimport React from 'react';\nimport './Sidebar.css';\n\nconst Sidebar = ({ currentPage, onPageChange, collapsed, onToggleCollapse }) => {\n  const menuItems = [\n    {\n      id: 'analysis',\n      name: 'UI分析',\n      icon: '🔍',\n      description: '上传界面截图进行AI分析'\n    },\n    {\n      id: 'history',\n      name: '历史记录',\n      icon: '📋',\n      description: '查看分析历史和结果'\n    },\n    {\n      id: 'settings',\n      name: '设置',\n      icon: '⚙️',\n      description: '系统配置和参数设置'\n    },\n    {\n      id: 'about',\n      name: '关于',\n      icon: 'ℹ️',\n      description: '平台信息和使用说明'\n    }\n  ];\n\n  return (\n    <div className={`sidebar ${collapsed ? 'collapsed' : ''}`}>\n      {/* 头部 */}\n      <div className=\"sidebar-header\">\n        <div className=\"logo\">\n          <span className=\"logo-icon\">🤖</span>\n          {!collapsed && (\n            <div className=\"logo-text\">\n              <h2>UI自动化</h2>\n              <p>分析平台</p>\n            </div>\n          )}\n        </div>\n        <button \n          className=\"collapse-btn\"\n          onClick={onToggleCollapse}\n          title={collapsed ? '展开侧边栏' : '收起侧边栏'}\n        >\n          {collapsed ? '→' : '←'}\n        </button>\n      </div>\n\n      {/* 导航菜单 */}\n      <nav className=\"sidebar-nav\">\n        <ul className=\"nav-list\">\n          {menuItems.map((item) => (\n            <li key={item.id} className=\"nav-item\">\n              <button\n                className={`nav-link ${currentPage === item.id ? 'active' : ''}`}\n                onClick={() => onPageChange(item.id)}\n                title={collapsed ? item.name : item.description}\n              >\n                <span className=\"nav-icon\">{item.icon}</span>\n                {!collapsed && (\n                  <div className=\"nav-content\">\n                    <span className=\"nav-name\">{item.name}</span>\n                    <span className=\"nav-desc\">{item.description}</span>\n                  </div>\n                )}\n              </button>\n            </li>\n          ))}\n        </ul>\n      </nav>\n\n      {/* 底部信息 */}\n      {!collapsed && (\n        <div className=\"sidebar-footer\">\n          <div className=\"version-info\">\n            <p>版本 v1.0.0</p>\n            <p>基于AI智能体</p>\n          </div>\n        </div>\n      )}\n\n\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": "AAAA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvB,KAAM,CAAAC,OAAO,CAAGC,IAAA,EAAgE,IAA/D,CAAEC,WAAW,CAAEC,YAAY,CAAEC,SAAS,CAAEC,gBAAiB,CAAC,CAAAJ,IAAA,CACzE,KAAM,CAAAK,SAAS,CAAG,CAChB,CACEC,EAAE,CAAE,UAAU,CACdC,IAAI,CAAE,MAAM,CACZC,IAAI,CAAE,IAAI,CACVC,WAAW,CAAE,cACf,CAAC,CACD,CACEH,EAAE,CAAE,SAAS,CACbC,IAAI,CAAE,MAAM,CACZC,IAAI,CAAE,IAAI,CACVC,WAAW,CAAE,WACf,CAAC,CACD,CACEH,EAAE,CAAE,UAAU,CACdC,IAAI,CAAE,IAAI,CACVC,IAAI,CAAE,IAAI,CACVC,WAAW,CAAE,WACf,CAAC,CACD,CACEH,EAAE,CAAE,OAAO,CACXC,IAAI,CAAE,IAAI,CACVC,IAAI,CAAE,IAAI,CACVC,WAAW,CAAE,WACf,CAAC,CACF,CAED,mBACEX,KAAA,QAAKY,SAAS,YAAAC,MAAA,CAAaR,SAAS,CAAG,WAAW,CAAG,EAAE,CAAG,CAAAS,QAAA,eAExDd,KAAA,QAAKY,SAAS,CAAC,gBAAgB,CAAAE,QAAA,eAC7Bd,KAAA,QAAKY,SAAS,CAAC,MAAM,CAAAE,QAAA,eACnBhB,IAAA,SAAMc,SAAS,CAAC,WAAW,CAAAE,QAAA,CAAC,cAAE,CAAM,CAAC,CACpC,CAACT,SAAS,eACTL,KAAA,QAAKY,SAAS,CAAC,WAAW,CAAAE,QAAA,eACxBhB,IAAA,OAAAgB,QAAA,CAAI,sBAAK,CAAI,CAAC,cACdhB,IAAA,MAAAgB,QAAA,CAAG,0BAAI,CAAG,CAAC,EACR,CACN,EACE,CAAC,cACNhB,IAAA,WACEc,SAAS,CAAC,cAAc,CACxBG,OAAO,CAAET,gBAAiB,CAC1BU,KAAK,CAAEX,SAAS,CAAG,OAAO,CAAG,OAAQ,CAAAS,QAAA,CAEpCT,SAAS,CAAG,GAAG,CAAG,GAAG,CAChB,CAAC,EACN,CAAC,cAGNP,IAAA,QAAKc,SAAS,CAAC,aAAa,CAAAE,QAAA,cAC1BhB,IAAA,OAAIc,SAAS,CAAC,UAAU,CAAAE,QAAA,CACrBP,SAAS,CAACU,GAAG,CAAEC,IAAI,eAClBpB,IAAA,OAAkBc,SAAS,CAAC,UAAU,CAAAE,QAAA,cACpCd,KAAA,WACEY,SAAS,aAAAC,MAAA,CAAcV,WAAW,GAAKe,IAAI,CAACV,EAAE,CAAG,QAAQ,CAAG,EAAE,CAAG,CACjEO,OAAO,CAAEA,CAAA,GAAMX,YAAY,CAACc,IAAI,CAACV,EAAE,CAAE,CACrCQ,KAAK,CAAEX,SAAS,CAAGa,IAAI,CAACT,IAAI,CAAGS,IAAI,CAACP,WAAY,CAAAG,QAAA,eAEhDhB,IAAA,SAAMc,SAAS,CAAC,UAAU,CAAAE,QAAA,CAAEI,IAAI,CAACR,IAAI,CAAO,CAAC,CAC5C,CAACL,SAAS,eACTL,KAAA,QAAKY,SAAS,CAAC,aAAa,CAAAE,QAAA,eAC1BhB,IAAA,SAAMc,SAAS,CAAC,UAAU,CAAAE,QAAA,CAAEI,IAAI,CAACT,IAAI,CAAO,CAAC,cAC7CX,IAAA,SAAMc,SAAS,CAAC,UAAU,CAAAE,QAAA,CAAEI,IAAI,CAACP,WAAW,CAAO,CAAC,EACjD,CACN,EACK,CAAC,EAbFO,IAAI,CAACV,EAcV,CACL,CAAC,CACA,CAAC,CACF,CAAC,CAGL,CAACH,SAAS,eACTP,IAAA,QAAKc,SAAS,CAAC,gBAAgB,CAAAE,QAAA,cAC7Bd,KAAA,QAAKY,SAAS,CAAC,cAAc,CAAAE,QAAA,eAC3BhB,IAAA,MAAAgB,QAAA,CAAG,qBAAS,CAAG,CAAC,cAChBhB,IAAA,MAAAgB,QAAA,CAAG,kCAAO,CAAG,CAAC,EACX,CAAC,CACH,CACN,EAGE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAb,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}