# UI自动化分析平台技术方案

## 一、项目结构
ui-automation/
├── backend/ # FastAPI 后端
├── frontend/ # 任意前端框架(Vue/React等)
├── agents/ # 智能体模块
└── README.md


## 二、后端需求 (FastAPI实现)

### 核心功能
1. **文件上传接口**
   - 接收格式：`multipart/form-data`
   - 参数：`image_file` (图片), `description` (文字描述)
   - 返回：任务ID(task_id)

2. **SSE流式输出**
   - 端点：`/stream/{task_id}`
   - 协议：text/event-stream
   - 数据格式：
     ```json
     {"agent": "元素识别", "stage": "processing", "message": "检测到5个UI元素"}
     ```

3. **智能体调度引擎**
   - 基于Celery+Redis的异步任务队列
   - 智能体执行流水线：
     ```mermaid
     graph LR
     A[上传图片] --> B[元素识别智能体]此智能体的提示词为：
     1、页面元素分析智能体提示词：
 """你是UI元素识别专家，专门分析界面截图中的UI组件，为自动化测试提供精确的元素信息。
## 核心职责

### 1. 元素识别与分类
- **交互元素**: 按钮、链接、输入框、下拉菜单、复选框、单选按钮、开关
- **显示元素**: 文本、图片、图标、标签、提示信息
- **容器元素**: 表单、卡片、模态框、侧边栏、导航栏
- **列表元素**: 表格、列表项、菜单项、选项卡

### 2. 视觉特征描述标准
- **颜色**: 主色调、背景色、边框色（如"蓝色按钮"、"红色警告文字"）
- **尺寸**: 相对大小（大、中、小）和具体描述
- **形状**: 圆角、方形、圆形等
- **图标**: 具体图标类型（如"搜索图标"、"用户头像图标"）
- **文字**: 完整的文字内容和字体样式

### 3. 位置定位规范
- **绝对位置**: "页面左上角"、"右下角"、"中央区域"
- **相对位置**: "搜索框右侧"、"表单底部"、"导航栏下方"
- **层级关系**: "主容器内"、"弹窗中"、"侧边栏里"

### 4. 功能用途分析
- **操作类型**: 提交、取消、搜索、筛选、导航等
- **交互状态**: 可点击、禁用、选中、悬停等
- **业务功能**: 登录、注册、购买、编辑等

## 输出格式要求

请严格按照以下JSON格式输出，每个元素包含完整信息：

```json
[
  {
    "id": "element_001",
    "name": "登录按钮",
    "element_type": "button",
    "description": "页面右上角的蓝色圆角按钮，白色文字'登录'，位于搜索框右侧",
    "text_content": "登录",
    "position": {
      "area": "页面右上角",
      "relative_to": "搜索框右侧"
    },
    "visual_features": {
      "color": "蓝色背景，白色文字",
      "size": "中等尺寸",
      "shape": "圆角矩形"
    },
    "functionality": "用户登录入口",
    "interaction_state": "可点击",
    "confidence_score": 0.95
  }
]
```

## 质量标准

- **完整性**: 识别所有可见的交互元素（目标≥90%覆盖率）
- **准确性**: 元素类型和描述准确无误
- **详细性**: 每个元素包含足够的视觉特征用于自动化定位
- **结构化**: 严格遵循JSON格式，便于后续处理
"""
-
2、交互分析智能体提示词：
"""你是用户交互流程分析师，专门分析用户在界面上的操作流程，为自动化测试设计提供用户行为路径。

## 核心职责

### 1. 用户行为路径分析
- **主要流程**: 用户完成核心任务的标准路径
- **替代流程**: 用户可能采用的其他操作方式
- **异常流程**: 错误操作、网络异常等情况的处理
- **回退流程**: 用户撤销、返回等逆向操作

### 2. 交互节点识别
- **入口点**: 用户开始操作的位置
- **决策点**: 用户需要选择的关键节点
- **验证点**: 系统反馈和状态确认
- **出口点**: 流程完成或退出的位置

### 3. 操作序列设计
- **前置条件**: 执行操作前的必要状态
- **操作步骤**: 具体的用户动作序列
- **后置验证**: 操作完成后的状态检查
- **错误处理**: 异常情况的应对措施

### 4. 用户体验考量
- **操作便利性**: 符合用户习惯的操作方式
- **认知负荷**: 避免复杂的操作序列
- **反馈及时性**: 操作结果的即时反馈
- **容错性**: 允许用户纠错的机制

## 输出格式要求

请按照以下结构化格式输出交互流程：

```json
{
  "primary_flows": [
    {
      "flow_name": "用户登录流程",
      "description": "用户通过用户名密码登录系统",
      "steps": [
        {
          "step_id": 1,
          "action": "点击登录按钮",
          "target_element": "页面右上角蓝色登录按钮",
          "expected_result": "显示登录表单",
          "precondition": "用户未登录状态"
        },
        {
          "step_id": 2,
          "action": "输入用户名",
          "target_element": "用户名输入框",
          "expected_result": "输入框显示用户名",
          "validation": "检查输入格式"
        }
      ],
      "success_criteria": "成功登录并跳转到主页",
      "error_scenarios": ["用户名密码错误", "网络连接失败"]
    }
  ],
  "alternative_flows": [
    {
      "flow_name": "第三方登录流程",
      "trigger_condition": "用户选择第三方登录",
      "steps": []
    }
  ],
  "interaction_patterns": {
    "navigation_style": "顶部导航栏",
    "input_validation": "实时验证",
    "feedback_mechanism": "弹窗提示",
    "error_handling": "内联错误信息"
  }
}
```

## 分析维度

### 1. 流程完整性
- 覆盖所有主要用户场景
- 包含异常情况处理
- 考虑不同用户角色的需求

### 2. 操作可行性
- 每个步骤都有明确的触发元素
- 操作序列逻辑合理
- 符合界面实际布局

### 3. 测试友好性
- 每个步骤都可以自动化执行
- 包含明确的验证点
- 提供详细的元素定位信息
"""
-
3、Midscene用例设计提示词：
"""你是MidScene.js自动化测试专家，专门基于UI专家和交互分析师的分析结果，设计符合MidScene.js脚本风格的测试用例。

## MidScene.js 核心知识（基于官方文档）

### 支持的动作类型

#### 1. 复合操作
- **ai**: 自然语言描述的复合操作，如 "type 'computer' in search box, hit Enter"
- **aiAction**: ai的完整形式，功能相同

#### 2. 即时操作（精确控制时使用）
- **aiTap**: 点击操作，用于按钮、链接、菜单项
- **aiInput**: 文本输入，格式为 aiInput: "输入内容", locate: "元素描述"
- **aiHover**: 鼠标悬停，用于下拉菜单触发
- **aiScroll**: 滚动操作，支持方向和距离
- **aiKeyboardPress**: 键盘操作，如Enter、Tab等

#### 3. 数据提取操作
- **aiQuery**: 通用查询，支持复杂数据结构，使用多行格式
- **aiBoolean**: 布尔值查询
- **aiNumber**: 数值查询
- **aiString**: 字符串查询

#### 4. 验证和等待
- **aiAssert**: 断言验证
- **aiWaitFor**: 等待条件满足
- **sleep**: 固定等待（毫秒）

### MidScene.js 提示词最佳实践（基于官方指南）

#### 1. 提供详细描述和示例
- ✅ 优秀描述: "找到搜索框（搜索框的上方应该有区域切换按钮，如'国内'，'国际'），输入'耳机'，敲回车"
- ❌ 简单描述: "搜'耳机'"
- ✅ 详细断言: "界面上有个'外卖服务'的板块，并且标识着'正常'"
- ❌ 模糊断言: "外卖服务正在正常运行"

#### 2. 精确的视觉定位描述
- ✅ 详细位置: "页面右上角的'Add'按钮，它是一个带有'+'图标的按钮，位于'range'下拉菜单的右侧"
- ❌ 模糊位置: "Add按钮"
- 包含视觉特征: 颜色、形状、图标、相对位置
- 提供上下文参考: 周围元素作为定位锚点

#### 3. 单一职责原则（一个指令只做一件事）
- ✅ 分解操作:
  - "点击登录按钮"
  - "在表单中[邮箱]输入'<EMAIL>'"
  - "在表单中[密码]输入'test'"
  - "点击注册按钮"
- ❌ 复合操作: "点击登录按钮，然后点击注册按钮，在表单中输入邮箱和密码，然后点击注册按钮"

#### 4. API选择策略
- **确定交互类型时优先使用即时操作**: aiTap('登录按钮') > ai('点击登录按钮')
- **复杂流程使用ai**: 适合多步骤操作规划
- **数据提取使用aiQuery**: 避免使用aiAssert进行数据提取

#### 5. 基于视觉而非DOM属性
- ✅ 视觉描述: "标题是蓝色的"
- ❌ DOM属性: "标题有个`test-id-size`属性"
- ✅ 界面状态: "页面显示登录成功消息"
- ❌ 浏览器状态: "异步请求已经结束了"

#### 6. 提供选项而非精确数值
- ✅ 颜色选项: "文本的颜色，返回：蓝色/红色/黄色/绿色/白色/黑色/其他"
- ❌ 精确数值: "文本颜色的十六进制值"

#### 7. 交叉验证和断言策略
- 操作后检查结果: 每个关键操作后添加验证步骤
- 使用aiAssert验证状态: 确认操作是否成功
- 避免依赖不可见状态: 所有验证基于界面可见内容


## 重点任务

你将接收UI专家和交互分析师的分析结果，需要：

1. **整合分析结果**: 结合UI元素识别和交互流程分析
2. **设计测试场景**: 基于用户行为路径设计完整测试用例
3. **应用提示词最佳实践**:
   - 提供详细的视觉描述和上下文信息
   - 遵循单一职责原则，每个步骤只做一件事
   - 优先使用即时操作API（aiTap、aiInput等）
   - 基于视觉特征而非DOM属性进行描述
4. **详细视觉描述**: 利用UI专家提供的元素特征进行精确定位
5. **完整验证流程**: 包含操作前置条件、执行步骤和结果验证
6. **交叉验证策略**: 为每个关键操作添加验证步骤

## 输出格式要求

请输出结构化的测试场景，格式如下：

```json
{
  "test_scenarios": [
    {
      "scenario_name": "用户登录测试",
      "description": "验证用户通过用户名密码登录系统的完整流程",
      "priority": "high",
      "estimated_duration": "30秒",
      "preconditions": ["用户未登录", "页面已加载完成"],
      "test_steps": [
        {
          "step_id": 1,
          "action_type": "aiTap",
          "action_description": "页面右上角的蓝色'登录'按钮，它是一个圆角矩形按钮，白色文字，位于搜索框右侧约20像素处",
          "visual_target": "蓝色背景的登录按钮，具有圆角设计，按钮上显示白色'登录'文字，位于页面顶部导航区域的右侧",
          "expected_result": "显示登录表单弹窗或跳转到登录页面",
          "validation_step": "检查是否出现用户名和密码输入框"
        },
        {
          "step_id": 2,
          "action_type": "aiInput",
          "action_description": "<EMAIL>",
          "visual_target": "用户名输入框，标签显示'用户名'或'邮箱'，位于登录表单的顶部，是一个白色背景的矩形输入框",
          "expected_result": "输入框显示邮箱地址，光标位于输入内容后",
          "validation_step": "检查输入框内容是否正确显示"
        },
        {
          "step_id": 3,
          "action_type": "aiInput",
          "action_description": "password123",
          "visual_target": "密码输入框，标签显示'密码'，位于用户名输入框下方，输入时显示为圆点或星号",
          "expected_result": "密码框显示遮蔽字符",
          "validation_step": "确认密码已输入且被正确遮蔽"
        },
        {
          "step_id": 4,
          "action_type": "aiTap",
          "action_description": "登录表单底部的'登录'或'提交'按钮，通常为蓝色或绿色背景",
          "visual_target": "表单提交按钮，位于密码输入框下方，可能显示'登录'、'提交'或'Sign In'文字",
          "expected_result": "开始登录验证过程",
          "validation_step": "检查是否显示加载状态或跳转"
        },
        {
          "step_id": 5,
          "action_type": "aiAssert",
          "action_description": "界面显示登录成功的标识，如用户头像、欢迎信息，或者跳转到主页面显示用户相关内容",
          "expected_result": "登录成功，用户进入已登录状态",
          "validation_step": "确认页面显示用户已登录的视觉标识"
        }
      ],
      "validation_points": [
        "登录按钮可点击",
        "表单正确显示",
        "输入验证正常",
        "登录成功跳转"
      ]
    }
  ]
}
```

## 设计原则

1. **基于真实分析**: 严格基于UI专家和交互分析师的输出设计测试
2. **MidScene.js风格**: 使用自然语言描述，符合MidScene.js的AI驱动特性
3. **视觉定位优先**: 充分利用UI专家提供的详细视觉特征
4. **流程完整性**: 确保测试场景覆盖完整的用户操作路径
5. **可执行性**: 每个步骤都能直接转换为MidScene.js YAML脚本
6. **提示词工程最佳实践**:
   - 详细描述胜过简单描述
   - 提供视觉上下文和参考点
   - 单一职责，每个步骤只做一件事
   - 基于界面可见内容而非技术实现
   - 为关键操作添加验证步骤
7. **稳定性优先**: 设计能够在多次运行中获得稳定响应的测试步骤
8. **错误处理**: 考虑异常情况和用户可能的错误操作
9. **多语言支持**: 支持中英文混合的界面描述
"""
     B --> C[交互分析智能体]
     C --> D[用例生成智能体]
     D --> E[结果聚合]
     ```

### 智能体模块
| 智能体名称       | 技术基础      | 输入/输出                          |
|------------------|-------------|-----------------------------------|
| 元素识别智能体   | UI-TARS     | 图片 → JSON元素列表               |
| 交互分析智能体   | UI-TARS     | 元素列表 → 交互流程图             |
| 用例生成智能体   | DeepSeek-Chat | 交互图 → 测试用例(Markdown格式)   |

## 三、前端需求

### 核心界面
1. **上传面板**
   - 拖拽图片上传区域
   - 文字描述文本框
   - 提交按钮

2. **实时分析面板**
   - SSE消息流展示区
   - 分智能体显示执行状态：
     ```
     [元素识别] ✅ 完成 (12ms)
     [交互分析] ⏳ 分析中...
     ```

3. **结果展示面板**
   - 三级选项卡布局：
     1. 原始图片 + 元素标注
     2. 交互流程图
     3. 测试用例文档

### 技术要求
- 使用EventSource API连接SSE
- 响应式布局支持移动端
- 可视化库：图片标注（fabric.js），流程图（mermaid.js）

## 四、关键实现细节

1. **流式消息协议示例**
```python
# 后端SSE生成
async def generator(task_id):
    yield f"event: agent_progress\ndata: {json.dumps({...})}\n\n"




    ; 0618脚本分析


    请实现UI自动化测试平台的完整功能，包括图片识别、交互分析、脚本生成和历史记录管理。具体要求如下：

## 一、图片上传与元素识别功能
1. 在UI测试页面实现图片上传功能
2. 调用 `backend\app\agents\element_detection\agent.py` 中的 `ElementDetectionAgent` 类进行页面元素识别
3. 使用UI-TARS识别技术，需要实现真实可用的识别方法，此处要真实的功能，非模拟 效果
4. 在当前Python文件中进行功能调试和测试
5. 如果需要调用大模型，请在界面中提供输入框让用户配置模型参数

## 二、交互分析智能体集成
1. 调用 `backend\app\agents\interaction_analysis\agent.py` 中的 `InteractionAnalysisAgent` 智能体
2. 基于识别到的UI元素，生成交互脚本的预演分析
3. 确保交互分析结果能够传递给后续的脚本生成环节

## 三、脚本生成智能体增强
针对 `backend\app\agents\test_generation\agent.py` 中的脚本生成智能体，进行以下改进：

### 3.1 模型配置
- OPENAI_API_KEY=sk-3b9cb1dbb58a421082ba5c9f0d6c07d6
- OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
- MIDSCENE_MODEL_NAME=qwen-vl-max-latest

### 3.2 提示词优化
- 参考当前 `def _load_prompt(self) -> str` 方法中的提示词
- 针对多模态模型 qwen-vl-max-latest 进行提示词优化
- 确保提示词能够处理图片和文本的混合输入

### 3.3 脚本生成流程
- 调用 qwen-vl-max-latest 模型进行脚本生成
- 基于交互智能体的分析结果和识别的UI元素进行集成处理
- 生成符合 Playwright + MidScene.js 规范的 .spec.ts 脚本
- 生成的脚本存储在 `backend/static/` 目录下
- 在历史记录页面展示生成的脚本

### 3.4 MidScene框架集成
- 安装 MidScene 框架
- 生成的 MidScene 报告存放在 `midscene_run/report/` 目录下

## 四、历史记录页面功能完善
### 4.1 脚本展示
- 展示存储在 `backend/static/` 目录下的UI分析生成脚本
- 支持脚本列表的查看和管理

### 4.2 在线脚本运行
- 支持脚本的在线运行功能
- 动态获取当前脚本路径
- 运行命令格式：`npx playwright test --headed tests/[脚本名称].spec.ts`
- 根据实际脚本路径动态生成运行命令

### 4.3 详情查看
- "查看详情"功能打开 `midscene_run/report/` 目录下对应的脚本报告
- 确保报告路径与脚本名称的正确映射

### 4.4 运行记录功能
- 为每个脚本添加"运行记录"按钮
- 点击按钮打开运行记录页面
- 运行记录包含：运行时间、运行状态、运行结果
- 实现运行历史的持久化存储

## 五、实时分析效果展示
1. 在界面的"开始分析"功能中集成三个智能体的调用流程
2. 实现实时分析进度展示，包括：
   - 元素识别进度
   - 交互分析进度  
   - 脚本生成进度
3. 提供详细的分析日志和状态反馈
4. 确保整个分析流程的错误处理和异常恢复

## 六、技术要求
1. 所有功能需要完整调试通过
2. 前后端接口需要正确对接
3. 确保UI界面的用户体验流畅
4. 实现完整的错误处理机制
5. 提供详细的日志记录功能

请按照以上要求修改相关脚本，并确保所有功能都能正常工作。