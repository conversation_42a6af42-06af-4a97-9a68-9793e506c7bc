{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\components\\\\RealTimeAnalysis.js\",\n  _s = $RefreshSig$();\n/**\n * 实时分析组件 - 基于SSE的实时进度展示\n */\nimport React, { useState, useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RealTimeAnalysis = ({\n  taskId,\n  onAnalysisComplete,\n  onAnalysisError\n}) => {\n  _s();\n  const [agents, setAgents] = useState([{\n    name: '元素识别智能体',\n    status: 'pending',\n    message: '等待开始...',\n    progress: 0,\n    duration: null,\n    details: [],\n    currentStep: ''\n  }, {\n    name: '交互分析智能体',\n    status: 'pending',\n    message: '等待开始...',\n    progress: 0,\n    duration: null,\n    details: [],\n    currentStep: ''\n  }, {\n    name: '脚本生成智能体',\n    status: 'pending',\n    message: '等待开始...',\n    progress: 0,\n    duration: null,\n    details: [],\n    currentStep: ''\n  }]);\n  const [messages, setMessages] = useState([]);\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionError, setConnectionError] = useState(null);\n  const [startTime, setStartTime] = useState(null);\n  const eventSourceRef = useRef(null);\n  useEffect(() => {\n    if (!taskId) return;\n\n    // 创建SSE连接\n    const eventSource = new EventSource(`http://localhost:8001/api/v1/stream/${taskId}`);\n    eventSourceRef.current = eventSource;\n    setStartTime(Date.now());\n    eventSource.onopen = () => {\n      console.log('SSE连接已建立');\n      setIsConnected(true);\n      setConnectionError(null);\n    };\n    eventSource.addEventListener('connected', event => {\n      const data = JSON.parse(event.data);\n      console.log('连接确认:', data);\n      addMessage({\n        type: 'system',\n        message: data.data.message,\n        timestamp: new Date().toISOString()\n      });\n    });\n    eventSource.addEventListener('agent_progress', event => {\n      const data = JSON.parse(event.data);\n      console.log('智能体进度:', data);\n      addMessage({\n        type: 'progress',\n        agent: data.data.agent,\n        message: data.data.message,\n        progress: data.data.progress,\n        timestamp: data.timestamp\n      });\n      updateAgentStatus(data.data);\n    });\n    eventSource.addEventListener('task_complete', event => {\n      const data = JSON.parse(event.data);\n      console.log('任务完成:', data);\n      addMessage({\n        type: 'complete',\n        message: data.data.message || '所有智能体执行完成！',\n        timestamp: data.timestamp\n      });\n\n      // 标记所有智能体为完成状态\n      setAgents(prev => prev.map(agent => ({\n        ...agent,\n        status: 'completed',\n        progress: 100,\n        duration: Date.now() - startTime\n      })));\n\n      // 获取完整的分析结果\n      setTimeout(async () => {\n        try {\n          const response = await fetch(`http://localhost:8001/api/v1/tasks/${taskId}`);\n          if (response.ok) {\n            const taskData = await response.json();\n            onAnalysisComplete(taskData);\n          } else {\n            onAnalysisComplete();\n          }\n        } catch (error) {\n          console.error('获取任务结果失败:', error);\n          onAnalysisComplete();\n        }\n      }, 1000);\n    });\n    eventSource.addEventListener('error', event => {\n      const data = JSON.parse(event.data);\n      console.error('SSE错误:', data);\n      onAnalysisError(data.data.message || '分析过程中发生错误');\n    });\n    eventSource.addEventListener('heartbeat', event => {\n      const data = JSON.parse(event.data);\n      console.log('心跳:', data);\n    });\n    eventSource.onerror = error => {\n      console.error('SSE连接错误:', error);\n      setIsConnected(false);\n      setConnectionError('连接中断，请刷新页面重试');\n    };\n    return () => {\n      if (eventSourceRef.current) {\n        eventSourceRef.current.close();\n      }\n    };\n  }, [taskId, onAnalysisComplete, onAnalysisError]);\n  const addMessage = message => {\n    setMessages(prev => [...prev, {\n      ...message,\n      id: Date.now() + Math.random()\n    }]);\n  };\n  const updateAgentStatus = progressData => {\n    setAgents(prev => prev.map(agent => {\n      if (agent.name === progressData.agent) {\n        return {\n          ...agent,\n          status: progressData.status,\n          message: progressData.message,\n          progress: progressData.progress || agent.progress,\n          duration: progressData.duration || agent.duration\n        };\n      }\n      return agent;\n    }));\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'pending':\n        return '⏳';\n      case 'processing':\n        return '🔄';\n      case 'completed':\n        return '✅';\n      case 'failed':\n        return '❌';\n      default:\n        return '⏳';\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'pending':\n        return '#6c757d';\n      case 'processing':\n        return '#007bff';\n      case 'completed':\n        return '#28a745';\n      case 'failed':\n        return '#dc3545';\n      default:\n        return '#6c757d';\n    }\n  };\n  const formatDuration = duration => {\n    if (!duration) return '';\n    return `${(duration / 1000).toFixed(1)}s`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'white',\n      borderRadius: '12px',\n      padding: '24px',\n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n      marginBottom: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0',\n          color: '#333',\n          fontSize: '20px',\n          fontWeight: '600'\n        },\n        children: \"\\uD83D\\uDD04 \\u5B9E\\u65F6\\u5206\\u6790\\u8FDB\\u5EA6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px',\n          fontSize: '14px',\n          fontWeight: '500',\n          color: isConnected ? '#28a745' : '#dc3545'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            width: '8px',\n            height: '8px',\n            borderRadius: '50%',\n            backgroundColor: 'currentColor',\n            animation: isConnected ? 'pulse 2s infinite' : 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), isConnected ? '实时连接中' : '连接中断']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this), connectionError && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: '#f8d7da',\n        color: '#721c24',\n        padding: '12px',\n        borderRadius: '6px',\n        marginBottom: '20px',\n        border: '1px solid #f5c6cb'\n      },\n      children: connectionError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gap: '20px',\n        marginBottom: '24px'\n      },\n      children: agents.map(agent => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          border: '2px solid #e9ecef',\n          borderRadius: '12px',\n          padding: '20px',\n          background: 'white',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.05)',\n          transition: 'all 0.3s ease'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '12px',\n            marginBottom: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '24px'\n            },\n            children: getStatusIcon(agent.status)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: '0 0 4px 0',\n                fontWeight: '600',\n                color: '#333',\n                fontSize: '18px'\n              },\n              children: agent.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0',\n                fontSize: '14px',\n                color: '#666'\n              },\n              children: agent.currentStep || agent.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'right'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '14px',\n                fontWeight: '500',\n                color: getStatusColor(agent.status),\n                display: 'block',\n                marginBottom: '4px'\n              },\n              children: [agent.status === 'pending' && '⏳ 等待中', agent.status === 'processing' && '🔄 处理中', agent.status === 'completed' && '✅ 已完成', agent.status === 'failed' && '❌ 失败']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this), agent.duration && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '12px',\n                color: '#999'\n              },\n              children: formatDuration(agent.duration)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100%',\n            height: '10px',\n            background: '#f1f3f4',\n            borderRadius: '5px',\n            overflow: 'hidden',\n            marginBottom: '16px',\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              height: '100%',\n              width: `${agent.progress}%`,\n              background: `linear-gradient(90deg, ${getStatusColor(agent.status)}, ${getStatusColor(agent.status)}dd)`,\n              transition: 'width 0.5s ease',\n              borderRadius: '5px',\n              position: 'relative'\n            },\n            children: agent.status === 'processing' && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: 0,\n                right: 0,\n                width: '20px',\n                height: '100%',\n                background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',\n                animation: 'shimmer 1.5s infinite'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              position: 'absolute',\n              right: '8px',\n              top: '50%',\n              transform: 'translateY(-50%)',\n              fontSize: '10px',\n              fontWeight: '600',\n              color: agent.progress > 50 ? 'white' : '#666'\n            },\n            children: [Math.round(agent.progress), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 13\n        }, this), agent.details && agent.details.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f8f9fa',\n            borderRadius: '8px',\n            padding: '12px',\n            marginTop: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              margin: '0 0 8px 0',\n              fontSize: '14px',\n              fontWeight: '600',\n              color: '#333',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '6px'\n            },\n            children: \"\\uD83D\\uDCCB \\u6267\\u884C\\u8BE6\\u60C5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              maxHeight: '120px',\n              overflowY: 'auto'\n            },\n            children: agent.details.map((detail, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'flex-start',\n                gap: '8px',\n                marginBottom: '6px',\n                fontSize: '12px',\n                lineHeight: '1.4'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: detail.type === 'success' ? '#28a745' : detail.type === 'error' ? '#dc3545' : detail.type === 'warning' ? '#ffc107' : '#007bff',\n                  minWidth: '16px'\n                },\n                children: detail.type === 'success' ? '✓' : detail.type === 'error' ? '✗' : detail.type === 'warning' ? '⚠' : '•'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#555',\n                  flex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  style: {\n                    color: '#333'\n                  },\n                  children: detail.timestamp\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 25\n                }, this), \" - \", detail.message]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 23\n              }, this)]\n            }, idx, true, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 15\n        }, this)]\n      }, agent.name, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          margin: '0 0 12px 0',\n          color: '#333',\n          fontSize: '16px',\n          fontWeight: '600'\n        },\n        children: \"\\uD83D\\uDCCB \\u6267\\u884C\\u65E5\\u5FD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          maxHeight: '200px',\n          overflowY: 'auto',\n          border: '1px solid #e9ecef',\n          borderRadius: '6px',\n          padding: '12px',\n          background: '#f8f9fa'\n        },\n        children: [messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '8px',\n            marginBottom: '8px',\n            fontSize: '13px',\n            lineHeight: '1.4'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#999',\n              fontFamily: 'monospace',\n              minWidth: '80px'\n            },\n            children: new Date(message.timestamp).toLocaleTimeString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: message.type === 'system' ? '#007bff' : message.type === 'complete' ? '#28a745' : '#333',\n              fontWeight: message.type === 'complete' ? '500' : 'normal',\n              minWidth: '80px'\n            },\n            children: message.agent ? `[${message.agent}]` : '[系统]'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#333',\n              flex: 1\n            },\n            children: message.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 15\n          }, this)]\n        }, message.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 13\n        }, this)), messages.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#999',\n            textAlign: 'center',\n            padding: '20px'\n          },\n          children: \"\\u7B49\\u5F85\\u6D88\\u606F...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes pulse {\n          0% { opacity: 1; }\n          50% { opacity: 0.5; }\n          100% { opacity: 1; }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this);\n};\n_s(RealTimeAnalysis, \"G6SLYQujTCPTz9nhl80xb/QO5JM=\");\n_c = RealTimeAnalysis;\nexport default RealTimeAnalysis;\nvar _c;\n$RefreshReg$(_c, \"RealTimeAnalysis\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "RealTimeAnalysis", "taskId", "onAnalysisComplete", "onAnalysisError", "_s", "agents", "setAgents", "name", "status", "message", "progress", "duration", "details", "currentStep", "messages", "setMessages", "isConnected", "setIsConnected", "connectionError", "setConnectionError", "startTime", "setStartTime", "eventSourceRef", "eventSource", "EventSource", "current", "Date", "now", "onopen", "console", "log", "addEventListener", "event", "data", "JSON", "parse", "addMessage", "type", "timestamp", "toISOString", "agent", "updateAgentStatus", "prev", "map", "setTimeout", "response", "fetch", "ok", "taskData", "json", "error", "onerror", "close", "id", "Math", "random", "progressData", "getStatusIcon", "getStatusColor", "formatDuration", "toFixed", "style", "background", "borderRadius", "padding", "boxShadow", "marginBottom", "children", "display", "justifyContent", "alignItems", "margin", "color", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gap", "width", "height", "backgroundColor", "animation", "border", "transition", "flex", "textAlign", "overflow", "position", "top", "right", "transform", "round", "length", "marginTop", "maxHeight", "overflowY", "detail", "idx", "lineHeight", "min<PERSON><PERSON><PERSON>", "fontFamily", "toLocaleTimeString", "jsx", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/components/RealTimeAnalysis.js"], "sourcesContent": ["/**\n * 实时分析组件 - 基于SSE的实时进度展示\n */\nimport React, { useState, useEffect, useRef } from 'react';\n\nconst RealTimeAnalysis = ({ taskId, onAnalysisComplete, onAnalysisError }) => {\n  const [agents, setAgents] = useState([\n    {\n      name: '元素识别智能体',\n      status: 'pending',\n      message: '等待开始...',\n      progress: 0,\n      duration: null,\n      details: [],\n      currentStep: ''\n    },\n    {\n      name: '交互分析智能体',\n      status: 'pending',\n      message: '等待开始...',\n      progress: 0,\n      duration: null,\n      details: [],\n      currentStep: ''\n    },\n    {\n      name: '脚本生成智能体',\n      status: 'pending',\n      message: '等待开始...',\n      progress: 0,\n      duration: null,\n      details: [],\n      currentStep: ''\n    },\n  ]);\n  const [messages, setMessages] = useState([]);\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionError, setConnectionError] = useState(null);\n  const [startTime, setStartTime] = useState(null);\n  const eventSourceRef = useRef(null);\n\n  useEffect(() => {\n    if (!taskId) return;\n\n    // 创建SSE连接\n    const eventSource = new EventSource(`http://localhost:8001/api/v1/stream/${taskId}`);\n    eventSourceRef.current = eventSource;\n    setStartTime(Date.now());\n\n    eventSource.onopen = () => {\n      console.log('SSE连接已建立');\n      setIsConnected(true);\n      setConnectionError(null);\n    };\n\n    eventSource.addEventListener('connected', (event) => {\n      const data = JSON.parse(event.data);\n      console.log('连接确认:', data);\n      addMessage({\n        type: 'system',\n        message: data.data.message,\n        timestamp: new Date().toISOString(),\n      });\n    });\n\n    eventSource.addEventListener('agent_progress', (event) => {\n      const data = JSON.parse(event.data);\n      console.log('智能体进度:', data);\n      \n      addMessage({\n        type: 'progress',\n        agent: data.data.agent,\n        message: data.data.message,\n        progress: data.data.progress,\n        timestamp: data.timestamp,\n      });\n      \n      updateAgentStatus(data.data);\n    });\n\n    eventSource.addEventListener('task_complete', (event) => {\n      const data = JSON.parse(event.data);\n      console.log('任务完成:', data);\n      \n      addMessage({\n        type: 'complete',\n        message: data.data.message || '所有智能体执行完成！',\n        timestamp: data.timestamp,\n      });\n\n      // 标记所有智能体为完成状态\n      setAgents(prev => prev.map(agent => ({\n        ...agent,\n        status: 'completed',\n        progress: 100,\n        duration: Date.now() - startTime,\n      })));\n\n      // 获取完整的分析结果\n      setTimeout(async () => {\n        try {\n          const response = await fetch(`http://localhost:8001/api/v1/tasks/${taskId}`);\n          if (response.ok) {\n            const taskData = await response.json();\n            onAnalysisComplete(taskData);\n          } else {\n            onAnalysisComplete();\n          }\n        } catch (error) {\n          console.error('获取任务结果失败:', error);\n          onAnalysisComplete();\n        }\n      }, 1000);\n    });\n\n    eventSource.addEventListener('error', (event) => {\n      const data = JSON.parse(event.data);\n      console.error('SSE错误:', data);\n      onAnalysisError(data.data.message || '分析过程中发生错误');\n    });\n\n    eventSource.addEventListener('heartbeat', (event) => {\n      const data = JSON.parse(event.data);\n      console.log('心跳:', data);\n    });\n\n    eventSource.onerror = (error) => {\n      console.error('SSE连接错误:', error);\n      setIsConnected(false);\n      setConnectionError('连接中断，请刷新页面重试');\n    };\n\n    return () => {\n      if (eventSourceRef.current) {\n        eventSourceRef.current.close();\n      }\n    };\n  }, [taskId, onAnalysisComplete, onAnalysisError]);\n\n  const addMessage = (message) => {\n    setMessages(prev => [...prev, { ...message, id: Date.now() + Math.random() }]);\n  };\n\n  const updateAgentStatus = (progressData) => {\n    setAgents(prev => prev.map(agent => {\n      if (agent.name === progressData.agent) {\n        return {\n          ...agent,\n          status: progressData.status,\n          message: progressData.message,\n          progress: progressData.progress || agent.progress,\n          duration: progressData.duration || agent.duration,\n        };\n      }\n      return agent;\n    }));\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'pending':\n        return '⏳';\n      case 'processing':\n        return '🔄';\n      case 'completed':\n        return '✅';\n      case 'failed':\n        return '❌';\n      default:\n        return '⏳';\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'pending':\n        return '#6c757d';\n      case 'processing':\n        return '#007bff';\n      case 'completed':\n        return '#28a745';\n      case 'failed':\n        return '#dc3545';\n      default:\n        return '#6c757d';\n    }\n  };\n\n  const formatDuration = (duration) => {\n    if (!duration) return '';\n    return `${(duration / 1000).toFixed(1)}s`;\n  };\n\n  return (\n    <div style={{ \n      background: 'white', \n      borderRadius: '12px', \n      padding: '24px', \n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n      marginBottom: '24px'\n    }}>\n      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>\n        <h3 style={{ margin: '0', color: '#333', fontSize: '20px', fontWeight: '600' }}>\n          🔄 实时分析进度\n        </h3>\n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px',\n          fontSize: '14px',\n          fontWeight: '500',\n          color: isConnected ? '#28a745' : '#dc3545'\n        }}>\n          <span style={{\n            width: '8px',\n            height: '8px',\n            borderRadius: '50%',\n            backgroundColor: 'currentColor',\n            animation: isConnected ? 'pulse 2s infinite' : 'none'\n          }}></span>\n          {isConnected ? '实时连接中' : '连接中断'}\n        </div>\n      </div>\n\n      {connectionError && (\n        <div style={{\n          background: '#f8d7da',\n          color: '#721c24',\n          padding: '12px',\n          borderRadius: '6px',\n          marginBottom: '20px',\n          border: '1px solid #f5c6cb'\n        }}>\n          {connectionError}\n        </div>\n      )}\n\n      {/* 智能体状态 */}\n      <div style={{ display: 'grid', gap: '20px', marginBottom: '24px' }}>\n        {agents.map((agent) => (\n          <div key={agent.name} style={{\n            border: '2px solid #e9ecef',\n            borderRadius: '12px',\n            padding: '20px',\n            background: 'white',\n            boxShadow: '0 2px 8px rgba(0,0,0,0.05)',\n            transition: 'all 0.3s ease'\n          }}>\n            {/* 智能体头部信息 */}\n            <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>\n              <span style={{ fontSize: '24px' }}>{getStatusIcon(agent.status)}</span>\n              <div style={{ flex: 1 }}>\n                <h3 style={{ margin: '0 0 4px 0', fontWeight: '600', color: '#333', fontSize: '18px' }}>\n                  {agent.name}\n                </h3>\n                <p style={{ margin: '0', fontSize: '14px', color: '#666' }}>\n                  {agent.currentStep || agent.message}\n                </p>\n              </div>\n              <div style={{ textAlign: 'right' }}>\n                <span style={{\n                  fontSize: '14px',\n                  fontWeight: '500',\n                  color: getStatusColor(agent.status),\n                  display: 'block',\n                  marginBottom: '4px'\n                }}>\n                  {agent.status === 'pending' && '⏳ 等待中'}\n                  {agent.status === 'processing' && '🔄 处理中'}\n                  {agent.status === 'completed' && '✅ 已完成'}\n                  {agent.status === 'failed' && '❌ 失败'}\n                </span>\n                {agent.duration && (\n                  <span style={{ fontSize: '12px', color: '#999' }}>\n                    {formatDuration(agent.duration)}\n                  </span>\n                )}\n              </div>\n            </div>\n\n            {/* 进度条 */}\n            <div style={{\n              width: '100%',\n              height: '10px',\n              background: '#f1f3f4',\n              borderRadius: '5px',\n              overflow: 'hidden',\n              marginBottom: '16px',\n              position: 'relative'\n            }}>\n              <div style={{\n                height: '100%',\n                width: `${agent.progress}%`,\n                background: `linear-gradient(90deg, ${getStatusColor(agent.status)}, ${getStatusColor(agent.status)}dd)`,\n                transition: 'width 0.5s ease',\n                borderRadius: '5px',\n                position: 'relative'\n              }}>\n                {agent.status === 'processing' && (\n                  <div style={{\n                    position: 'absolute',\n                    top: 0,\n                    right: 0,\n                    width: '20px',\n                    height: '100%',\n                    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',\n                    animation: 'shimmer 1.5s infinite'\n                  }}></div>\n                )}\n              </div>\n              <span style={{\n                position: 'absolute',\n                right: '8px',\n                top: '50%',\n                transform: 'translateY(-50%)',\n                fontSize: '10px',\n                fontWeight: '600',\n                color: agent.progress > 50 ? 'white' : '#666'\n              }}>\n                {Math.round(agent.progress)}%\n              </span>\n            </div>\n\n            {/* 详细执行步骤 */}\n            {agent.details && agent.details.length > 0 && (\n              <div style={{\n                background: '#f8f9fa',\n                borderRadius: '8px',\n                padding: '12px',\n                marginTop: '12px'\n              }}>\n                <h4 style={{\n                  margin: '0 0 8px 0',\n                  fontSize: '14px',\n                  fontWeight: '600',\n                  color: '#333',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '6px'\n                }}>\n                  📋 执行详情\n                </h4>\n                <div style={{ maxHeight: '120px', overflowY: 'auto' }}>\n                  {agent.details.map((detail, idx) => (\n                    <div key={idx} style={{\n                      display: 'flex',\n                      alignItems: 'flex-start',\n                      gap: '8px',\n                      marginBottom: '6px',\n                      fontSize: '12px',\n                      lineHeight: '1.4'\n                    }}>\n                      <span style={{\n                        color: detail.type === 'success' ? '#28a745' :\n                               detail.type === 'error' ? '#dc3545' :\n                               detail.type === 'warning' ? '#ffc107' : '#007bff',\n                        minWidth: '16px'\n                      }}>\n                        {detail.type === 'success' ? '✓' :\n                         detail.type === 'error' ? '✗' :\n                         detail.type === 'warning' ? '⚠' : '•'}\n                      </span>\n                      <span style={{ color: '#555', flex: 1 }}>\n                        <strong style={{ color: '#333' }}>{detail.timestamp}</strong> - {detail.message}\n                      </span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        ))}\n      </div>\n\n      {/* 消息流 */}\n      <div>\n        <h4 style={{ margin: '0 0 12px 0', color: '#333', fontSize: '16px', fontWeight: '600' }}>\n          📋 执行日志\n        </h4>\n        <div style={{\n          maxHeight: '200px',\n          overflowY: 'auto',\n          border: '1px solid #e9ecef',\n          borderRadius: '6px',\n          padding: '12px',\n          background: '#f8f9fa'\n        }}>\n          {messages.map((message) => (\n            <div key={message.id} style={{\n              display: 'flex',\n              gap: '8px',\n              marginBottom: '8px',\n              fontSize: '13px',\n              lineHeight: '1.4'\n            }}>\n              <span style={{ color: '#999', fontFamily: 'monospace', minWidth: '80px' }}>\n                {new Date(message.timestamp).toLocaleTimeString()}\n              </span>\n              <span style={{\n                color: message.type === 'system' ? '#007bff' : \n                       message.type === 'complete' ? '#28a745' : '#333',\n                fontWeight: message.type === 'complete' ? '500' : 'normal',\n                minWidth: '80px'\n              }}>\n                {message.agent ? `[${message.agent}]` : '[系统]'}\n              </span>\n              <span style={{ color: '#333', flex: 1 }}>{message.message}</span>\n            </div>\n          ))}\n          {messages.length === 0 && (\n            <div style={{ color: '#999', textAlign: 'center', padding: '20px' }}>\n              等待消息...\n            </div>\n          )}\n        </div>\n      </div>\n\n      <style jsx>{`\n        @keyframes pulse {\n          0% { opacity: 1; }\n          50% { opacity: 0.5; }\n          100% { opacity: 1; }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default RealTimeAnalysis;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,kBAAkB;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAC5E,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGX,QAAQ,CAAC,CACnC;IACEY,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE;EACf,CAAC,EACD;IACEN,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE;EACf,CAAC,EACD;IACEN,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE;EACf,CAAC,CACF,CAAC;EACF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM2B,cAAc,GAAGzB,MAAM,CAAC,IAAI,CAAC;EAEnCD,SAAS,CAAC,MAAM;IACd,IAAI,CAACK,MAAM,EAAE;;IAEb;IACA,MAAMsB,WAAW,GAAG,IAAIC,WAAW,CAAC,uCAAuCvB,MAAM,EAAE,CAAC;IACpFqB,cAAc,CAACG,OAAO,GAAGF,WAAW;IACpCF,YAAY,CAACK,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IAExBJ,WAAW,CAACK,MAAM,GAAG,MAAM;MACzBC,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;MACvBb,cAAc,CAAC,IAAI,CAAC;MACpBE,kBAAkB,CAAC,IAAI,CAAC;IAC1B,CAAC;IAEDI,WAAW,CAACQ,gBAAgB,CAAC,WAAW,EAAGC,KAAK,IAAK;MACnD,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;MACnCJ,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEG,IAAI,CAAC;MAC1BG,UAAU,CAAC;QACTC,IAAI,EAAE,QAAQ;QACd5B,OAAO,EAAEwB,IAAI,CAACA,IAAI,CAACxB,OAAO;QAC1B6B,SAAS,EAAE,IAAIZ,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFhB,WAAW,CAACQ,gBAAgB,CAAC,gBAAgB,EAAGC,KAAK,IAAK;MACxD,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;MACnCJ,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEG,IAAI,CAAC;MAE3BG,UAAU,CAAC;QACTC,IAAI,EAAE,UAAU;QAChBG,KAAK,EAAEP,IAAI,CAACA,IAAI,CAACO,KAAK;QACtB/B,OAAO,EAAEwB,IAAI,CAACA,IAAI,CAACxB,OAAO;QAC1BC,QAAQ,EAAEuB,IAAI,CAACA,IAAI,CAACvB,QAAQ;QAC5B4B,SAAS,EAAEL,IAAI,CAACK;MAClB,CAAC,CAAC;MAEFG,iBAAiB,CAACR,IAAI,CAACA,IAAI,CAAC;IAC9B,CAAC,CAAC;IAEFV,WAAW,CAACQ,gBAAgB,CAAC,eAAe,EAAGC,KAAK,IAAK;MACvD,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;MACnCJ,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEG,IAAI,CAAC;MAE1BG,UAAU,CAAC;QACTC,IAAI,EAAE,UAAU;QAChB5B,OAAO,EAAEwB,IAAI,CAACA,IAAI,CAACxB,OAAO,IAAI,YAAY;QAC1C6B,SAAS,EAAEL,IAAI,CAACK;MAClB,CAAC,CAAC;;MAEF;MACAhC,SAAS,CAACoC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACH,KAAK,KAAK;QACnC,GAAGA,KAAK;QACRhC,MAAM,EAAE,WAAW;QACnBE,QAAQ,EAAE,GAAG;QACbC,QAAQ,EAAEe,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGP;MACzB,CAAC,CAAC,CAAC,CAAC;;MAEJ;MACAwB,UAAU,CAAC,YAAY;QACrB,IAAI;UACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,sCAAsC7C,MAAM,EAAE,CAAC;UAC5E,IAAI4C,QAAQ,CAACE,EAAE,EAAE;YACf,MAAMC,QAAQ,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;YACtC/C,kBAAkB,CAAC8C,QAAQ,CAAC;UAC9B,CAAC,MAAM;YACL9C,kBAAkB,CAAC,CAAC;UACtB;QACF,CAAC,CAAC,OAAOgD,KAAK,EAAE;UACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;UACjChD,kBAAkB,CAAC,CAAC;QACtB;MACF,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;IAEFqB,WAAW,CAACQ,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;MAC/C,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;MACnCJ,OAAO,CAACqB,KAAK,CAAC,QAAQ,EAAEjB,IAAI,CAAC;MAC7B9B,eAAe,CAAC8B,IAAI,CAACA,IAAI,CAACxB,OAAO,IAAI,WAAW,CAAC;IACnD,CAAC,CAAC;IAEFc,WAAW,CAACQ,gBAAgB,CAAC,WAAW,EAAGC,KAAK,IAAK;MACnD,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;MACnCJ,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEG,IAAI,CAAC;IAC1B,CAAC,CAAC;IAEFV,WAAW,CAAC4B,OAAO,GAAID,KAAK,IAAK;MAC/BrB,OAAO,CAACqB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChCjC,cAAc,CAAC,KAAK,CAAC;MACrBE,kBAAkB,CAAC,cAAc,CAAC;IACpC,CAAC;IAED,OAAO,MAAM;MACX,IAAIG,cAAc,CAACG,OAAO,EAAE;QAC1BH,cAAc,CAACG,OAAO,CAAC2B,KAAK,CAAC,CAAC;MAChC;IACF,CAAC;EACH,CAAC,EAAE,CAACnD,MAAM,EAAEC,kBAAkB,EAAEC,eAAe,CAAC,CAAC;EAEjD,MAAMiC,UAAU,GAAI3B,OAAO,IAAK;IAC9BM,WAAW,CAAC2B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAAE,GAAGjC,OAAO;MAAE4C,EAAE,EAAE3B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG2B,IAAI,CAACC,MAAM,CAAC;IAAE,CAAC,CAAC,CAAC;EAChF,CAAC;EAED,MAAMd,iBAAiB,GAAIe,YAAY,IAAK;IAC1ClD,SAAS,CAACoC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACH,KAAK,IAAI;MAClC,IAAIA,KAAK,CAACjC,IAAI,KAAKiD,YAAY,CAAChB,KAAK,EAAE;QACrC,OAAO;UACL,GAAGA,KAAK;UACRhC,MAAM,EAAEgD,YAAY,CAAChD,MAAM;UAC3BC,OAAO,EAAE+C,YAAY,CAAC/C,OAAO;UAC7BC,QAAQ,EAAE8C,YAAY,CAAC9C,QAAQ,IAAI8B,KAAK,CAAC9B,QAAQ;UACjDC,QAAQ,EAAE6C,YAAY,CAAC7C,QAAQ,IAAI6B,KAAK,CAAC7B;QAC3C,CAAC;MACH;MACA,OAAO6B,KAAK;IACd,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMiB,aAAa,GAAIjD,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,SAAS;QACZ,OAAO,GAAG;MACZ,KAAK,YAAY;QACf,OAAO,IAAI;MACb,KAAK,WAAW;QACd,OAAO,GAAG;MACZ,KAAK,QAAQ;QACX,OAAO,GAAG;MACZ;QACE,OAAO,GAAG;IACd;EACF,CAAC;EAED,MAAMkD,cAAc,GAAIlD,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,YAAY;QACf,OAAO,SAAS;MAClB,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMmD,cAAc,GAAIhD,QAAQ,IAAK;IACnC,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IACxB,OAAO,GAAG,CAACA,QAAQ,GAAG,IAAI,EAAEiD,OAAO,CAAC,CAAC,CAAC,GAAG;EAC3C,CAAC;EAED,oBACE7D,OAAA;IAAK8D,KAAK,EAAE;MACVC,UAAU,EAAE,OAAO;MACnBC,YAAY,EAAE,MAAM;MACpBC,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,4BAA4B;MACvCC,YAAY,EAAE;IAChB,CAAE;IAAAC,QAAA,gBACApE,OAAA;MAAK8D,KAAK,EAAE;QAAEO,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEJ,YAAY,EAAE;MAAO,CAAE;MAAAC,QAAA,gBAC3GpE,OAAA;QAAI8D,KAAK,EAAE;UAAEU,MAAM,EAAE,GAAG;UAAEC,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAP,QAAA,EAAC;MAEhF;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL/E,OAAA;QAAK8D,KAAK,EAAE;UACVO,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBS,GAAG,EAAE,KAAK;UACVN,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,KAAK;UACjBF,KAAK,EAAExD,WAAW,GAAG,SAAS,GAAG;QACnC,CAAE;QAAAmD,QAAA,gBACApE,OAAA;UAAM8D,KAAK,EAAE;YACXmB,KAAK,EAAE,KAAK;YACZC,MAAM,EAAE,KAAK;YACblB,YAAY,EAAE,KAAK;YACnBmB,eAAe,EAAE,cAAc;YAC/BC,SAAS,EAAEnE,WAAW,GAAG,mBAAmB,GAAG;UACjD;QAAE;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACT9D,WAAW,GAAG,OAAO,GAAG,MAAM;MAAA;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL5D,eAAe,iBACdnB,OAAA;MAAK8D,KAAK,EAAE;QACVC,UAAU,EAAE,SAAS;QACrBU,KAAK,EAAE,SAAS;QAChBR,OAAO,EAAE,MAAM;QACfD,YAAY,EAAE,KAAK;QACnBG,YAAY,EAAE,MAAM;QACpBkB,MAAM,EAAE;MACV,CAAE;MAAAjB,QAAA,EACCjD;IAAe;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CACN,eAGD/E,OAAA;MAAK8D,KAAK,EAAE;QAAEO,OAAO,EAAE,MAAM;QAAEW,GAAG,EAAE,MAAM;QAAEb,YAAY,EAAE;MAAO,CAAE;MAAAC,QAAA,EAChE9D,MAAM,CAACsC,GAAG,CAAEH,KAAK,iBAChBzC,OAAA;QAAsB8D,KAAK,EAAE;UAC3BuB,MAAM,EAAE,mBAAmB;UAC3BrB,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE,MAAM;UACfF,UAAU,EAAE,OAAO;UACnBG,SAAS,EAAE,4BAA4B;UACvCoB,UAAU,EAAE;QACd,CAAE;QAAAlB,QAAA,gBAEApE,OAAA;UAAK8D,KAAK,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAES,GAAG,EAAE,MAAM;YAAEb,YAAY,EAAE;UAAO,CAAE;UAAAC,QAAA,gBACvFpE,OAAA;YAAM8D,KAAK,EAAE;cAAEY,QAAQ,EAAE;YAAO,CAAE;YAAAN,QAAA,EAAEV,aAAa,CAACjB,KAAK,CAAChC,MAAM;UAAC;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvE/E,OAAA;YAAK8D,KAAK,EAAE;cAAEyB,IAAI,EAAE;YAAE,CAAE;YAAAnB,QAAA,gBACtBpE,OAAA;cAAI8D,KAAK,EAAE;gBAAEU,MAAM,EAAE,WAAW;gBAAEG,UAAU,EAAE,KAAK;gBAAEF,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAN,QAAA,EACpF3B,KAAK,CAACjC;YAAI;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACL/E,OAAA;cAAG8D,KAAK,EAAE;gBAAEU,MAAM,EAAE,GAAG;gBAAEE,QAAQ,EAAE,MAAM;gBAAED,KAAK,EAAE;cAAO,CAAE;cAAAL,QAAA,EACxD3B,KAAK,CAAC3B,WAAW,IAAI2B,KAAK,CAAC/B;YAAO;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN/E,OAAA;YAAK8D,KAAK,EAAE;cAAE0B,SAAS,EAAE;YAAQ,CAAE;YAAApB,QAAA,gBACjCpE,OAAA;cAAM8D,KAAK,EAAE;gBACXY,QAAQ,EAAE,MAAM;gBAChBC,UAAU,EAAE,KAAK;gBACjBF,KAAK,EAAEd,cAAc,CAAClB,KAAK,CAAChC,MAAM,CAAC;gBACnC4D,OAAO,EAAE,OAAO;gBAChBF,YAAY,EAAE;cAChB,CAAE;cAAAC,QAAA,GACC3B,KAAK,CAAChC,MAAM,KAAK,SAAS,IAAI,OAAO,EACrCgC,KAAK,CAAChC,MAAM,KAAK,YAAY,IAAI,QAAQ,EACzCgC,KAAK,CAAChC,MAAM,KAAK,WAAW,IAAI,OAAO,EACvCgC,KAAK,CAAChC,MAAM,KAAK,QAAQ,IAAI,MAAM;YAAA;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,EACNtC,KAAK,CAAC7B,QAAQ,iBACbZ,OAAA;cAAM8D,KAAK,EAAE;gBAAEY,QAAQ,EAAE,MAAM;gBAAED,KAAK,EAAE;cAAO,CAAE;cAAAL,QAAA,EAC9CR,cAAc,CAACnB,KAAK,CAAC7B,QAAQ;YAAC;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN/E,OAAA;UAAK8D,KAAK,EAAE;YACVmB,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdnB,UAAU,EAAE,SAAS;YACrBC,YAAY,EAAE,KAAK;YACnByB,QAAQ,EAAE,QAAQ;YAClBtB,YAAY,EAAE,MAAM;YACpBuB,QAAQ,EAAE;UACZ,CAAE;UAAAtB,QAAA,gBACApE,OAAA;YAAK8D,KAAK,EAAE;cACVoB,MAAM,EAAE,MAAM;cACdD,KAAK,EAAE,GAAGxC,KAAK,CAAC9B,QAAQ,GAAG;cAC3BoD,UAAU,EAAE,0BAA0BJ,cAAc,CAAClB,KAAK,CAAChC,MAAM,CAAC,KAAKkD,cAAc,CAAClB,KAAK,CAAChC,MAAM,CAAC,KAAK;cACxG6E,UAAU,EAAE,iBAAiB;cAC7BtB,YAAY,EAAE,KAAK;cACnB0B,QAAQ,EAAE;YACZ,CAAE;YAAAtB,QAAA,EACC3B,KAAK,CAAChC,MAAM,KAAK,YAAY,iBAC5BT,OAAA;cAAK8D,KAAK,EAAE;gBACV4B,QAAQ,EAAE,UAAU;gBACpBC,GAAG,EAAE,CAAC;gBACNC,KAAK,EAAE,CAAC;gBACRX,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdnB,UAAU,EAAE,yEAAyE;gBACrFqB,SAAS,EAAE;cACb;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN/E,OAAA;YAAM8D,KAAK,EAAE;cACX4B,QAAQ,EAAE,UAAU;cACpBE,KAAK,EAAE,KAAK;cACZD,GAAG,EAAE,KAAK;cACVE,SAAS,EAAE,kBAAkB;cAC7BnB,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,KAAK;cACjBF,KAAK,EAAEhC,KAAK,CAAC9B,QAAQ,GAAG,EAAE,GAAG,OAAO,GAAG;YACzC,CAAE;YAAAyD,QAAA,GACCb,IAAI,CAACuC,KAAK,CAACrD,KAAK,CAAC9B,QAAQ,CAAC,EAAC,GAC9B;UAAA;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAGLtC,KAAK,CAAC5B,OAAO,IAAI4B,KAAK,CAAC5B,OAAO,CAACkF,MAAM,GAAG,CAAC,iBACxC/F,OAAA;UAAK8D,KAAK,EAAE;YACVC,UAAU,EAAE,SAAS;YACrBC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,MAAM;YACf+B,SAAS,EAAE;UACb,CAAE;UAAA5B,QAAA,gBACApE,OAAA;YAAI8D,KAAK,EAAE;cACTU,MAAM,EAAE,WAAW;cACnBE,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,KAAK;cACjBF,KAAK,EAAE,MAAM;cACbJ,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBS,GAAG,EAAE;YACP,CAAE;YAAAZ,QAAA,EAAC;UAEH;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL/E,OAAA;YAAK8D,KAAK,EAAE;cAAEmC,SAAS,EAAE,OAAO;cAAEC,SAAS,EAAE;YAAO,CAAE;YAAA9B,QAAA,EACnD3B,KAAK,CAAC5B,OAAO,CAAC+B,GAAG,CAAC,CAACuD,MAAM,EAAEC,GAAG,kBAC7BpG,OAAA;cAAe8D,KAAK,EAAE;gBACpBO,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,YAAY;gBACxBS,GAAG,EAAE,KAAK;gBACVb,YAAY,EAAE,KAAK;gBACnBO,QAAQ,EAAE,MAAM;gBAChB2B,UAAU,EAAE;cACd,CAAE;cAAAjC,QAAA,gBACApE,OAAA;gBAAM8D,KAAK,EAAE;kBACXW,KAAK,EAAE0B,MAAM,CAAC7D,IAAI,KAAK,SAAS,GAAG,SAAS,GACrC6D,MAAM,CAAC7D,IAAI,KAAK,OAAO,GAAG,SAAS,GACnC6D,MAAM,CAAC7D,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS;kBACxDgE,QAAQ,EAAE;gBACZ,CAAE;gBAAAlC,QAAA,EACC+B,MAAM,CAAC7D,IAAI,KAAK,SAAS,GAAG,GAAG,GAC/B6D,MAAM,CAAC7D,IAAI,KAAK,OAAO,GAAG,GAAG,GAC7B6D,MAAM,CAAC7D,IAAI,KAAK,SAAS,GAAG,GAAG,GAAG;cAAG;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACP/E,OAAA;gBAAM8D,KAAK,EAAE;kBAAEW,KAAK,EAAE,MAAM;kBAAEc,IAAI,EAAE;gBAAE,CAAE;gBAAAnB,QAAA,gBACtCpE,OAAA;kBAAQ8D,KAAK,EAAE;oBAAEW,KAAK,EAAE;kBAAO,CAAE;kBAAAL,QAAA,EAAE+B,MAAM,CAAC5D;gBAAS;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,OAAG,EAACoB,MAAM,CAACzF,OAAO;cAAA;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC;YAAA,GApBCqB,GAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqBR,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA,GAjIOtC,KAAK,CAACjC,IAAI;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkIf,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN/E,OAAA;MAAAoE,QAAA,gBACEpE,OAAA;QAAI8D,KAAK,EAAE;UAAEU,MAAM,EAAE,YAAY;UAAEC,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAP,QAAA,EAAC;MAEzF;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL/E,OAAA;QAAK8D,KAAK,EAAE;UACVmC,SAAS,EAAE,OAAO;UAClBC,SAAS,EAAE,MAAM;UACjBb,MAAM,EAAE,mBAAmB;UAC3BrB,YAAY,EAAE,KAAK;UACnBC,OAAO,EAAE,MAAM;UACfF,UAAU,EAAE;QACd,CAAE;QAAAK,QAAA,GACCrD,QAAQ,CAAC6B,GAAG,CAAElC,OAAO,iBACpBV,OAAA;UAAsB8D,KAAK,EAAE;YAC3BO,OAAO,EAAE,MAAM;YACfW,GAAG,EAAE,KAAK;YACVb,YAAY,EAAE,KAAK;YACnBO,QAAQ,EAAE,MAAM;YAChB2B,UAAU,EAAE;UACd,CAAE;UAAAjC,QAAA,gBACApE,OAAA;YAAM8D,KAAK,EAAE;cAAEW,KAAK,EAAE,MAAM;cAAE8B,UAAU,EAAE,WAAW;cAAED,QAAQ,EAAE;YAAO,CAAE;YAAAlC,QAAA,EACvE,IAAIzC,IAAI,CAACjB,OAAO,CAAC6B,SAAS,CAAC,CAACiE,kBAAkB,CAAC;UAAC;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACP/E,OAAA;YAAM8D,KAAK,EAAE;cACXW,KAAK,EAAE/D,OAAO,CAAC4B,IAAI,KAAK,QAAQ,GAAG,SAAS,GACrC5B,OAAO,CAAC4B,IAAI,KAAK,UAAU,GAAG,SAAS,GAAG,MAAM;cACvDqC,UAAU,EAAEjE,OAAO,CAAC4B,IAAI,KAAK,UAAU,GAAG,KAAK,GAAG,QAAQ;cAC1DgE,QAAQ,EAAE;YACZ,CAAE;YAAAlC,QAAA,EACC1D,OAAO,CAAC+B,KAAK,GAAG,IAAI/B,OAAO,CAAC+B,KAAK,GAAG,GAAG;UAAM;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACP/E,OAAA;YAAM8D,KAAK,EAAE;cAAEW,KAAK,EAAE,MAAM;cAAEc,IAAI,EAAE;YAAE,CAAE;YAAAnB,QAAA,EAAE1D,OAAO,CAACA;UAAO;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAlBzDrE,OAAO,CAAC4C,EAAE;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBf,CACN,CAAC,EACDhE,QAAQ,CAACgF,MAAM,KAAK,CAAC,iBACpB/F,OAAA;UAAK8D,KAAK,EAAE;YAAEW,KAAK,EAAE,MAAM;YAAEe,SAAS,EAAE,QAAQ;YAAEvB,OAAO,EAAE;UAAO,CAAE;UAAAG,QAAA,EAAC;QAErE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN/E,OAAA;MAAOyG,GAAG;MAAArC,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC1E,EAAA,CAraIJ,gBAAgB;AAAAyG,EAAA,GAAhBzG,gBAAgB;AAuatB,eAAeA,gBAAgB;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}