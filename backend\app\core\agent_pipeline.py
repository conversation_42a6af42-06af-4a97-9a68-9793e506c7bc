"""
多智能体协作分析流水线
协调元素识别、交互分析、脚本生成三个智能体的执行
"""
import asyncio
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from app.core.sse_manager import sse_manager, AgentProgress, AgentStatus
from app.agents.element_detection.agent import ElementDetectionAgent
from app.agents.interaction_analysis.agent import InteractionAnalysisAgent
from app.agents.test_generation.agent import TestGenerationAgent

class AgentPipeline:
    """智能体分析流水线"""
    
    def __init__(self):
        self.element_agent = ElementDetectionAgent()
        self.interaction_agent = InteractionAnalysisAgent()
        self.script_agent = TestGenerationAgent()
        
        # 智能体配置
        self.agents_config = [
            {
                "name": "元素识别智能体",
                "agent": self.element_agent,
                "method": "analyze",
                "description": "基于UI-TARS技术识别界面元素",
                "estimated_duration": 15  # 秒
            },
            {
                "name": "交互分析智能体", 
                "agent": self.interaction_agent,
                "method": "analyze",
                "description": "分析用户操作流程和交互路径",
                "estimated_duration": 10
            },
            {
                "name": "脚本生成智能体",
                "agent": self.script_agent,
                "method": "analyze", 
                "description": "基于MidScene.js规范生成自动化测试脚本",
                "estimated_duration": 20
            }
        ]
    
    async def execute_pipeline(self, 
                             task_id: str, 
                             image_path: str, 
                             description: str) -> Dict[str, Any]:
        """
        执行完整的智能体分析流水线
        
        Args:
            task_id: 任务ID
            image_path: 图片文件路径
            description: 界面功能描述
            
        Returns:
            分析结果
        """
        start_time = time.time()
        results = {
            "task_id": task_id,
            "status": "processing",
            "elements": [],
            "flows": [],
            "automation_scripts": [],
            "created_at": datetime.now().isoformat(),
            "error_message": None
        }
        
        try:
            # 发送开始消息
            await sse_manager.broadcast_progress(
                task_id,
                AgentProgress(
                    agent_name="系统",
                    status=AgentStatus.PROCESSING,
                    message="开始UI分析流水线...",
                    progress=0.0
                )
            )
            
            # 第一步：元素识别
            await self._update_progress(task_id, "元素识别智能体", AgentStatus.PROCESSING, "正在识别UI元素...", 10.0)
            
            elements = await self._execute_agent_step(
                self.element_agent,
                "analyze",
                [image_path, description],
                task_id,
                "元素识别智能体"
            )
            
            if not elements:
                raise Exception("元素识别失败，未识别到任何UI元素")
            
            results["elements"] = elements
            await self._update_progress(
                task_id, 
                "元素识别智能体", 
                AgentStatus.COMPLETED, 
                f"元素识别完成，识别到 {len(elements)} 个UI元素", 
                33.3,
                duration=time.time() - start_time
            )
            
            # 第二步：交互分析
            await self._update_progress(task_id, "交互分析智能体", AgentStatus.PROCESSING, "正在分析用户交互流程...", 40.0)
            
            flows = await self._execute_agent_step(
                self.interaction_agent,
                "analyze",
                [elements, description],
                task_id,
                "交互分析智能体"
            )
            
            if not flows:
                raise Exception("交互分析失败，未生成任何交互流程")
            
            results["flows"] = flows
            await self._update_progress(
                task_id,
                "交互分析智能体",
                AgentStatus.COMPLETED,
                f"交互分析完成，生成 {len(flows)} 个交互流程",
                66.6,
                duration=time.time() - start_time
            )
            
            # 第三步：脚本生成
            await self._update_progress(task_id, "脚本生成智能体", AgentStatus.PROCESSING, "正在生成MidScene.js自动化脚本...", 70.0)
            
            scripts = await self._execute_agent_step(
                self.script_agent,
                "analyze",
                [elements, flows, description],
                task_id,
                "脚本生成智能体"
            )
            
            if not scripts:
                raise Exception("脚本生成失败，未生成任何自动化脚本")
            
            results["automation_scripts"] = scripts
            await self._update_progress(
                task_id,
                "脚本生成智能体", 
                AgentStatus.COMPLETED,
                f"脚本生成完成，生成 {len(scripts)} 个自动化脚本",
                100.0,
                duration=time.time() - start_time
            )
            
            # 更新最终状态
            results["status"] = "completed"
            results["completed_at"] = datetime.now().isoformat()
            
            # 广播完成消息
            await sse_manager.broadcast_completion(task_id, results)
            
            return results
            
        except Exception as e:
            error_message = str(e)
            results["status"] = "failed"
            results["error_message"] = error_message
            
            # 广播错误消息
            await sse_manager.broadcast_error(task_id, error_message)
            
            return results
    
    async def _execute_agent_step(self, 
                                agent, 
                                method_name: str, 
                                args: List[Any],
                                task_id: str,
                                agent_name: str) -> Any:
        """
        执行智能体步骤
        
        Args:
            agent: 智能体实例
            method_name: 方法名称
            args: 方法参数
            task_id: 任务ID
            agent_name: 智能体名称
            
        Returns:
            执行结果
        """
        try:
            # 获取智能体方法
            method = getattr(agent, method_name)
            
            # 模拟异步执行（实际应该调用真实的AI模型）
            await asyncio.sleep(1)  # 模拟处理时间
            
            # 执行智能体方法
            result = method(*args)
            
            return result
            
        except Exception as e:
            await self._update_progress(
                task_id,
                agent_name,
                AgentStatus.FAILED,
                f"{agent_name}执行失败: {str(e)}",
                0.0
            )
            raise
    
    async def _update_progress(self, 
                             task_id: str,
                             agent_name: str, 
                             status: AgentStatus,
                             message: str,
                             progress: float,
                             duration: Optional[float] = None,
                             data: Optional[Dict] = None):
        """更新进度"""
        await sse_manager.broadcast_progress(
            task_id,
            AgentProgress(
                agent_name=agent_name,
                status=status,
                message=message,
                progress=progress,
                duration=duration,
                data=data
            )
        )
    
    def get_pipeline_info(self) -> Dict[str, Any]:
        """获取流水线信息"""
        return {
            "name": "UI自动化分析流水线",
            "description": "基于多智能体协作的UI界面分析和自动化脚本生成",
            "agents": [
                {
                    "name": config["name"],
                    "description": config["description"],
                    "estimated_duration": config["estimated_duration"]
                }
                for config in self.agents_config
            ],
            "total_estimated_duration": sum(config["estimated_duration"] for config in self.agents_config)
        }

class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.pipeline = AgentPipeline()
        self.active_tasks: Dict[str, Dict] = {}
    
    async def start_analysis(self, task_id: str, image_path: str, description: str) -> Dict[str, Any]:
        """启动分析任务"""
        # 记录任务
        self.active_tasks[task_id] = {
            "task_id": task_id,
            "image_path": image_path,
            "description": description,
            "status": "processing",
            "started_at": datetime.now().isoformat()
        }
        
        try:
            # 执行分析流水线
            result = await self.pipeline.execute_pipeline(task_id, image_path, description)
            
            # 更新任务状态
            self.active_tasks[task_id].update(result)
            
            return result
            
        except Exception as e:
            # 更新任务状态为失败
            self.active_tasks[task_id].update({
                "status": "failed",
                "error_message": str(e),
                "completed_at": datetime.now().isoformat()
            })
            raise
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        return self.active_tasks.get(task_id)
    
    def get_active_tasks(self) -> List[str]:
        """获取活跃任务列表"""
        return [
            task_id for task_id, task in self.active_tasks.items()
            if task.get("status") == "processing"
        ]
    
    def cleanup_task(self, task_id: str):
        """清理任务"""
        if task_id in self.active_tasks:
            del self.active_tasks[task_id]
        
        # 清理SSE连接
        sse_manager.cleanup_task(task_id)

# 全局任务管理器实例
task_manager = TaskManager()
