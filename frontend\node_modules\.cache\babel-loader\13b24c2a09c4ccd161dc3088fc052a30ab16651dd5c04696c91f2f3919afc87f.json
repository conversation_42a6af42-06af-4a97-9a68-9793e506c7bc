{"ast": null, "code": "import _objectSpread from\"E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";/**\n * 简化的上传组件 - 移除项目信息字段，添加实时分析界面\n */import React,{useState,useEffect,useRef}from'react';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const SimpleUpload=_ref=>{let{onUploadSuccess,onUploadError}=_ref;const[selectedFile,setSelectedFile]=useState(null);const[description,setDescription]=useState('');const[isUploading,setIsUploading]=useState(false);const[showAnalysis,setShowAnalysis]=useState(false);const[analysisProgress,setAnalysisProgress]=useState({overall:0,currentStep:'准备开始分析...',steps:[{name:'初始化',status:'pending',progress:0},{name:'元素分析和智能识别',status:'pending',progress:0},{name:'生成自动化测试脚本',status:'pending',progress:0}]});const[analysisLogs,setAnalysisLogs]=useState([]);const logContainerRef=useRef(null);// 自动滚动到最新日志\nuseEffect(()=>{if(logContainerRef.current){logContainerRef.current.scrollTop=logContainerRef.current.scrollHeight;}},[analysisLogs]);const handleFileSelect=event=>{const file=event.target.files[0];if(file){setSelectedFile(file);}};const handleSubmit=async event=>{event.preventDefault();if(!selectedFile){onUploadError('请选择图片文件');return;}if(!description.trim()){onUploadError('请输入界面功能描述');return;}setIsUploading(true);setShowAnalysis(true);try{const formData=new FormData();formData.append('image_file',selectedFile);formData.append('description',description.trim());// 开始模拟分析进度\nsimulateAnalysisProgress();const response=await fetch('http://localhost:8001/api/v1/upload',{method:'POST',body:formData});if(!response.ok){const errorData=await response.json();throw new Error(errorData.detail||\"HTTP error! status: \".concat(response.status));}const result=await response.json();onUploadSuccess(result);}catch(error){console.error('Upload error:',error);setShowAnalysis(false);onUploadError(error.message||'上传失败，请重试');}finally{setIsUploading(false);}};const addLog=function(message){let type=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'info';const timestamp=new Date().toLocaleTimeString();setAnalysisLogs(prev=>[...prev,{id:Date.now()+Math.random(),timestamp,message,type}]);};const simulateAnalysisProgress=()=>{let step=0;const steps=['初始化','元素分析和智能识别','生成自动化测试脚本'];// 清空之前的日志\nsetAnalysisLogs([]);// 详细的步骤日志\nconst stepLogs={0:[// 初始化\n'🚀 开始分析流程...','📋 检查上传文件格式和大小','🔧 初始化AI分析引擎','📊 加载UI元素识别模型','✅ 初始化完成'],1:[// 元素分析和智能识别\n'🔍 开始图像预处理...','🎯 检测UI界面元素','📝 识别文本内容和标签','🔘 分析按钮和交互元素','📋 识别输入框和表单元素','🎨 分析布局和样式信息','🧠 AI智能分类UI组件','✅ 元素分析完成'],2:[// 生成自动化测试脚本\n'📝 开始生成测试脚本...','🔧 构建MidScene.js测试框架','📋 生成元素定位策略','⚡ 创建交互操作脚本','🧪 添加断言和验证逻辑','📄 格式化YAML输出','✅ 测试脚本生成完成']};const updateProgress=()=>{if(step<steps.length){// 添加步骤开始日志\naddLog(\"\\u5F00\\u59CB\\u6267\\u884C: \".concat(steps[step]),'step');// 更新进度状态\nsetAnalysisProgress(prev=>_objectSpread(_objectSpread({},prev),{},{overall:Math.round((step+1)/steps.length*100),currentStep:\"\\u6B63\\u5728\\u6267\\u884C: \".concat(steps[step]),steps:prev.steps.map((s,index)=>_objectSpread(_objectSpread({},s),{},{status:index<step?'completed':index===step?'processing':'pending',progress:index<step?100:index===step?0:0}))}));// 模拟步骤内的详细日志\nconst currentStepLogs=stepLogs[step];let logIndex=0;const addStepLogs=()=>{if(logIndex<currentStepLogs.length){addLog(currentStepLogs[logIndex],'info');// 更新当前步骤进度\nconst progress=Math.round((logIndex+1)/currentStepLogs.length*100);setAnalysisProgress(prev=>_objectSpread(_objectSpread({},prev),{},{steps:prev.steps.map((s,index)=>index===step?_objectSpread(_objectSpread({},s),{},{progress}):s)}));logIndex++;setTimeout(addStepLogs,800+Math.random()*1200);}else{// 当前步骤完成\nsetAnalysisProgress(prev=>_objectSpread(_objectSpread({},prev),{},{steps:prev.steps.map((s,index)=>index===step?_objectSpread(_objectSpread({},s),{},{status:'completed',progress:100}):s)}));step++;setTimeout(updateProgress,1000);}};setTimeout(addStepLogs,500);}else{// 所有步骤完成\naddLog('🎉 所有分析步骤已完成！','success');addLog('📄 测试脚本已生成，可以下载使用','success');setAnalysisProgress(prev=>_objectSpread(_objectSpread({},prev),{},{overall:100,currentStep:'分析完成',steps:prev.steps.map(s=>_objectSpread(_objectSpread({},s),{},{status:'completed',progress:100}))}));}};// 开始分析\naddLog('🔥 启动UI自动化测试脚本分析...','start');setTimeout(updateProgress,1000);};return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'24px',height:'100vh',padding:'24px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{flex:showAnalysis?'0 0 400px':'1',background:'white',padding:'24px',borderRadius:'12px',boxShadow:'0 2px 12px rgba(0,0,0,0.1)',transition:'all 0.3s ease'},children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'24px'},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'12px',fontWeight:'600',color:'#333',fontSize:'16px'},children:\"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"file-upload-area\",onClick:()=>document.getElementById('file-input').click(),style:{height:'240px',border:'2px dashed #667eea',borderRadius:'12px',background:selectedFile?'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)':'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',display:'flex',flexDirection:'column',alignItems:'center',justifyContent:'center',cursor:'pointer',transition:'all 0.3s ease',position:'relative',overflow:'hidden'},onDragOver:e=>{e.preventDefault();e.currentTarget.style.borderColor='#4f46e5';e.currentTarget.style.background='linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';},onDragLeave:e=>{e.currentTarget.style.borderColor='#667eea';e.currentTarget.style.background=selectedFile?'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)':'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';},onDrop:e=>{e.preventDefault();const files=e.dataTransfer.files;if(files.length>0){setSelectedFile(files[0]);}e.currentTarget.style.borderColor='#667eea';e.currentTarget.style.background='linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';},children:[/*#__PURE__*/_jsx(\"input\",{id:\"file-input\",type:\"file\",accept:\"image/*\",onChange:handleFileSelect,style:{display:'none'}}),selectedFile?/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'20px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'48px',marginBottom:'16px',color:'#667eea'},children:\"\\u2705\"}),/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 8px 0',color:'#333',fontSize:'18px'},children:\"\\u6587\\u4EF6\\u5DF2\\u9009\\u62E9\"}),/*#__PURE__*/_jsx(\"p\",{style:{margin:'0 0 8px 0',color:'#666',fontSize:'14px',fontWeight:'500'},children:selectedFile.name}),/*#__PURE__*/_jsxs(\"p\",{style:{margin:'0',color:'#999',fontSize:'12px'},children:[\"\\u5927\\u5C0F: \",(selectedFile.size/1024/1024).toFixed(2),\" MB\"]}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:e=>{e.stopPropagation();setSelectedFile(null);document.getElementById('file-input').value='';},style:{marginTop:'12px',background:'rgba(239, 68, 68, 0.1)',color:'#ef4444',border:'1px solid rgba(239, 68, 68, 0.3)',padding:'6px 12px',borderRadius:'6px',fontSize:'12px',cursor:'pointer',transition:'all 0.2s ease'},children:\"\\u91CD\\u65B0\\u9009\\u62E9\"})]}):/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',padding:'20px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'48px',marginBottom:'16px',color:'#667eea'},children:\"\\uD83D\\uDCE4\"}),/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 8px 0',color:'#333',fontSize:'18px'},children:\"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u4E0A\\u4F20\\u56FE\\u7247\"}),/*#__PURE__*/_jsx(\"p\",{style:{margin:'0 0 4px 0',color:'#666',fontSize:'14px'},children:\"\\u652F\\u6301 PNG\\u3001JPG\\u3001JPEG\\u3001GIF \\u683C\\u5F0F\"}),/*#__PURE__*/_jsx(\"p\",{style:{margin:'0',color:'#999',fontSize:'12px'},children:\"\\u6587\\u4EF6\\u5927\\u5C0F\\u4E0D\\u8D85\\u8FC7 10MB\"}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'16px',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',color:'white',padding:'8px 20px',borderRadius:'20px',fontSize:'14px',fontWeight:'500',display:'inline-block'},children:\"\\u9009\\u62E9\\u6587\\u4EF6\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'20px'},children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"description\",style:{display:'block',marginBottom:'8px',fontWeight:'600',color:'#333',fontSize:'16px'},children:\"\\uD83C\\uDFAF \\u754C\\u9762\\u529F\\u80FD\\u63CF\\u8FF0\"}),/*#__PURE__*/_jsx(\"textarea\",{id:\"description\",value:description,onChange:e=>setDescription(e.target.value),placeholder:\"\\u8BF7\\u8BE6\\u7EC6\\u63CF\\u8FF0\\u8FD9\\u4E2A\\u754C\\u9762\\u7684\\u4E3B\\u8981\\u529F\\u80FD\\uFF0C\\u4F8B\\u5982\\uFF1A\\u8FD9\\u662F\\u4E00\\u4E2A\\u767B\\u5F55\\u9875\\u9762\\uFF0C\\u5305\\u542B\\u7528\\u6237\\u540D\\u5BC6\\u7801\\u8F93\\u5165\\u6846\\u548C\\u767B\\u5F55\\u6309\\u94AE...\",rows:4,style:{width:'100%',padding:'12px',border:'2px solid #e5e7eb',borderRadius:'8px',resize:'vertical',fontFamily:'inherit',fontSize:'14px',lineHeight:'1.5',transition:'border-color 0.2s ease',background:'#fafafa'},onFocus:e=>{e.target.style.borderColor='#667eea';e.target.style.background='#ffffff';},onBlur:e=>{e.target.style.borderColor='#e5e7eb';e.target.style.background='#fafafa';},required:true}),/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'right',fontSize:'12px',color:'#666',marginTop:'4px'},children:[description.length,\"/500\"]})]}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:isUploading||!selectedFile||!description.trim(),style:{background:isUploading?'#6c757d':'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',color:'white',border:'none',padding:'12px 24px',borderRadius:'8px',fontSize:'16px',fontWeight:'500',cursor:isUploading?'not-allowed':'pointer',transition:'all 0.3s ease',display:'flex',alignItems:'center',gap:'8px',width:'100%',justifyContent:'center'},children:isUploading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{style:{width:'16px',height:'16px',border:'2px solid transparent',borderTop:'2px solid white',borderRadius:'50%',animation:'spin 1s linear infinite'}}),\"\\u5F00\\u59CB\\u5206\\u6790\\u4E2D...\"]}):'🚀 开始分析'})]})]}),showAnalysis&&/*#__PURE__*/_jsxs(\"div\",{style:{flex:'1',background:'white',padding:'24px',borderRadius:'12px',boxShadow:'0 2px 12px rgba(0,0,0,0.1)',overflow:'hidden',display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'space-between',marginBottom:'24px',paddingBottom:'16px',borderBottom:'2px solid #f1f3f4'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:0,color:'#333',fontSize:'20px',fontWeight:'600'},children:\"\\uD83D\\uDD0D \\u5B9E\\u65F6\\u5206\\u6790\\u8FDB\\u5EA6\"}),/*#__PURE__*/_jsxs(\"div\",{style:{background:analysisProgress.overall===100?'#d4edda':'#f8f9fa',color:analysisProgress.overall===100?'#155724':'#666',padding:'8px 16px',borderRadius:'20px',fontSize:'14px',fontWeight:'500',border:analysisProgress.overall===100?'1px solid #c3e6cb':'none'},children:[analysisProgress.overall,\"%\"]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{background:'#f8f9ff',border:'1px solid #e5e7eb',borderRadius:'8px',padding:'16px',marginBottom:'20px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:'#666',marginBottom:'8px'},children:\"\\u5206\\u6790\\u72B6\\u6001\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'16px',fontWeight:'600',color:'#333'},children:analysisProgress.currentStep})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'20px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'16px',fontWeight:'600',color:'#333',marginBottom:'12px'},children:\"\\uD83D\\uDCCA \\u5206\\u6790\\u6B65\\u9AA4\\u6982\\u89C8\"}),/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',gap:'8px'},children:analysisProgress.steps.map((step,index)=>/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,padding:'12px 8px',background:step.status==='completed'?'#d4edda':step.status==='processing'?'#e3f2fd':'#f8f9fa',border:\"1px solid \".concat(step.status==='completed'?'#c3e6cb':step.status==='processing'?'#bbdefb':'#e9ecef'),borderRadius:'6px',textAlign:'center',fontSize:'12px',transition:'all 0.3s ease'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'16px',marginBottom:'4px',color:step.status==='completed'?'#28a745':step.status==='processing'?'#1976d2':'#6c757d'},children:step.status==='completed'?'✅':step.status==='processing'?'⚡':'⏳'}),/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'500',color:'#333',fontSize:'11px'},children:step.name}),/*#__PURE__*/_jsx(\"div\",{style:{width:'100%',height:'3px',background:'#e9ecef',borderRadius:'2px',overflow:'hidden',marginTop:'6px'},children:/*#__PURE__*/_jsx(\"div\",{style:{height:'100%',width:\"\".concat(step.progress,\"%\"),background:step.status==='completed'?'#28a745':step.status==='processing'?'#1976d2':'#e9ecef',transition:'width 0.5s ease',borderRadius:'2px'}})})]},index))})]}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'space-between',marginBottom:'12px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'16px',fontWeight:'600',color:'#333'},children:\"\\uD83D\\uDCDD \\u5B9E\\u65F6\\u5206\\u6790\\u65E5\\u5FD7\"}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'#666',background:'#f8f9fa',padding:'4px 8px',borderRadius:'12px'},children:[analysisLogs.length,\" \\u6761\\u65E5\\u5FD7\"]})]}),/*#__PURE__*/_jsx(\"div\",{ref:logContainerRef,style:{flex:1,background:'#1a1a1a',borderRadius:'8px',padding:'16px',overflow:'auto',fontFamily:'Monaco, Consolas, \"Courier New\", monospace',fontSize:'13px',lineHeight:'1.5',maxHeight:'400px'},children:analysisLogs.length===0?/*#__PURE__*/_jsx(\"div\",{style:{color:'#666',textAlign:'center',padding:'20px'},children:\"\\u7B49\\u5F85\\u5206\\u6790\\u5F00\\u59CB...\"}):analysisLogs.map(log=>/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'8px',padding:'6px 0',borderBottom:'1px solid #333'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'flex-start',gap:'8px'},children:[/*#__PURE__*/_jsx(\"span\",{style:{color:'#888',fontSize:'11px',minWidth:'60px',fontWeight:'500'},children:log.timestamp}),/*#__PURE__*/_jsx(\"span\",{style:{color:log.type==='start'?'#4fc3f7':log.type==='step'?'#ffb74d':log.type==='success'?'#81c784':log.type==='error'?'#e57373':'#e0e0e0',fontWeight:log.type==='step'||log.type==='start'||log.type==='success'?'600':'400'},children:log.message})]})},log.id))})]})]}),/*#__PURE__*/_jsx(\"style\",{children:\"\\n        @keyframes spin {\\n          0% { transform: rotate(0deg); }\\n          100% { transform: rotate(360deg); }\\n        }\\n      \"})]});};export default SimpleUpload;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "SimpleUpload", "_ref", "onUploadSuccess", "onUploadError", "selectedFile", "setSelectedFile", "description", "setDescription", "isUploading", "setIsUploading", "showAnalysis", "setShowAnalysis", "analysisProgress", "setAnalysisProgress", "overall", "currentStep", "steps", "name", "status", "progress", "analysisLogs", "setAnalysisLogs", "logContainerRef", "current", "scrollTop", "scrollHeight", "handleFileSelect", "event", "file", "target", "files", "handleSubmit", "preventDefault", "trim", "formData", "FormData", "append", "simulateAnalysisProgress", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "concat", "result", "error", "console", "message", "addLog", "type", "arguments", "length", "undefined", "timestamp", "Date", "toLocaleTimeString", "prev", "id", "now", "Math", "random", "step", "stepLogs", "updateProgress", "_objectSpread", "round", "map", "s", "index", "currentStepLogs", "logIndex", "addStepLogs", "setTimeout", "style", "display", "gap", "height", "padding", "children", "flex", "background", "borderRadius", "boxShadow", "transition", "onSubmit", "marginBottom", "fontWeight", "color", "fontSize", "className", "onClick", "document", "getElementById", "click", "border", "flexDirection", "alignItems", "justifyContent", "cursor", "position", "overflow", "onDragOver", "e", "currentTarget", "borderColor", "onDragLeave", "onDrop", "dataTransfer", "accept", "onChange", "textAlign", "margin", "size", "toFixed", "stopPropagation", "value", "marginTop", "htmlFor", "placeholder", "rows", "width", "resize", "fontFamily", "lineHeight", "onFocus", "onBlur", "required", "disabled", "borderTop", "animation", "paddingBottom", "borderBottom", "ref", "maxHeight", "log", "min<PERSON><PERSON><PERSON>"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/components/SimpleUpload.js"], "sourcesContent": ["/**\n * 简化的上传组件 - 移除项目信息字段，添加实时分析界面\n */\nimport React, { useState, useEffect, useRef } from 'react';\n\nconst SimpleUpload = ({ onUploadSuccess, onUploadError }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const [showAnalysis, setShowAnalysis] = useState(false);\n  const [analysisProgress, setAnalysisProgress] = useState({\n    overall: 0,\n    currentStep: '准备开始分析...',\n    steps: [\n      { name: '初始化', status: 'pending', progress: 0 },\n      { name: '元素分析和智能识别', status: 'pending', progress: 0 },\n      { name: '生成自动化测试脚本', status: 'pending', progress: 0 }\n    ]\n  });\n  const [analysisLogs, setAnalysisLogs] = useState([]);\n  const logContainerRef = useRef(null);\n\n  // 自动滚动到最新日志\n  useEffect(() => {\n    if (logContainerRef.current) {\n      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;\n    }\n  }, [analysisLogs]);\n\n  const handleFileSelect = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n\n  const handleSubmit = async (event) => {\n    event.preventDefault();\n\n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n\n    setIsUploading(true);\n    setShowAnalysis(true);\n\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n\n      // 开始模拟分析进度\n      simulateAnalysisProgress();\n\n      const response = await fetch('http://localhost:8001/api/v1/upload', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      onUploadSuccess(result);\n\n    } catch (error) {\n      console.error('Upload error:', error);\n      setShowAnalysis(false);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const addLog = (message, type = 'info') => {\n    const timestamp = new Date().toLocaleTimeString();\n    setAnalysisLogs(prev => [...prev, {\n      id: Date.now() + Math.random(),\n      timestamp,\n      message,\n      type\n    }]);\n  };\n\n  const simulateAnalysisProgress = () => {\n    let step = 0;\n    const steps = ['初始化', '元素分析和智能识别', '生成自动化测试脚本'];\n\n    // 清空之前的日志\n    setAnalysisLogs([]);\n\n    // 详细的步骤日志\n    const stepLogs = {\n      0: [ // 初始化\n        '🚀 开始分析流程...',\n        '📋 检查上传文件格式和大小',\n        '🔧 初始化AI分析引擎',\n        '📊 加载UI元素识别模型',\n        '✅ 初始化完成'\n      ],\n      1: [ // 元素分析和智能识别\n        '🔍 开始图像预处理...',\n        '🎯 检测UI界面元素',\n        '📝 识别文本内容和标签',\n        '🔘 分析按钮和交互元素',\n        '📋 识别输入框和表单元素',\n        '🎨 分析布局和样式信息',\n        '🧠 AI智能分类UI组件',\n        '✅ 元素分析完成'\n      ],\n      2: [ // 生成自动化测试脚本\n        '📝 开始生成测试脚本...',\n        '🔧 构建MidScene.js测试框架',\n        '📋 生成元素定位策略',\n        '⚡ 创建交互操作脚本',\n        '🧪 添加断言和验证逻辑',\n        '📄 格式化YAML输出',\n        '✅ 测试脚本生成完成'\n      ]\n    };\n\n    const updateProgress = () => {\n      if (step < steps.length) {\n        // 添加步骤开始日志\n        addLog(`开始执行: ${steps[step]}`, 'step');\n\n        // 更新进度状态\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: Math.round((step + 1) / steps.length * 100),\n          currentStep: `正在执行: ${steps[step]}`,\n          steps: prev.steps.map((s, index) => ({\n            ...s,\n            status: index < step ? 'completed' : index === step ? 'processing' : 'pending',\n            progress: index < step ? 100 : index === step ? 0 : 0\n          }))\n        }));\n\n        // 模拟步骤内的详细日志\n        const currentStepLogs = stepLogs[step];\n        let logIndex = 0;\n\n        const addStepLogs = () => {\n          if (logIndex < currentStepLogs.length) {\n            addLog(currentStepLogs[logIndex], 'info');\n\n            // 更新当前步骤进度\n            const progress = Math.round(((logIndex + 1) / currentStepLogs.length) * 100);\n            setAnalysisProgress(prev => ({\n              ...prev,\n              steps: prev.steps.map((s, index) =>\n                index === step ? { ...s, progress } : s\n              )\n            }));\n\n            logIndex++;\n            setTimeout(addStepLogs, 800 + Math.random() * 1200);\n          } else {\n            // 当前步骤完成\n            setAnalysisProgress(prev => ({\n              ...prev,\n              steps: prev.steps.map((s, index) =>\n                index === step ? { ...s, status: 'completed', progress: 100 } : s\n              )\n            }));\n\n            step++;\n            setTimeout(updateProgress, 1000);\n          }\n        };\n\n        setTimeout(addStepLogs, 500);\n\n      } else {\n        // 所有步骤完成\n        addLog('🎉 所有分析步骤已完成！', 'success');\n        addLog('📄 测试脚本已生成，可以下载使用', 'success');\n\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: 100,\n          currentStep: '分析完成',\n          steps: prev.steps.map(s => ({ ...s, status: 'completed', progress: 100 }))\n        }));\n      }\n    };\n\n    // 开始分析\n    addLog('🔥 启动UI自动化测试脚本分析...', 'start');\n    setTimeout(updateProgress, 1000);\n  };\n\n  return (\n    <div style={{ display: 'flex', gap: '24px', height: '100vh', padding: '24px' }}>\n      {/* 左侧上传区域 */}\n      <div style={{ \n        flex: showAnalysis ? '0 0 400px' : '1',\n        background: 'white', \n        padding: '24px', \n        borderRadius: '12px', \n        boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n        transition: 'all 0.3s ease'\n      }}>\n        <h3>📁 上传UI界面截图</h3>\n        <form onSubmit={handleSubmit}>\n          <div style={{ marginBottom: '24px' }}>\n            <label style={{ display: 'block', marginBottom: '12px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n              📁 上传UI界面截图\n            </label>\n            <div\n              className=\"file-upload-area\"\n              onClick={() => document.getElementById('file-input').click()}\n              style={{\n                height: '240px',\n                border: '2px dashed #667eea',\n                borderRadius: '12px',\n                background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                justifyContent: 'center',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease',\n                position: 'relative',\n                overflow: 'hidden'\n              }}\n              onDragOver={(e) => {\n                e.preventDefault();\n                e.currentTarget.style.borderColor = '#4f46e5';\n                e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';\n              }}\n              onDragLeave={(e) => {\n                e.currentTarget.style.borderColor = '#667eea';\n                e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';\n              }}\n              onDrop={(e) => {\n                e.preventDefault();\n                const files = e.dataTransfer.files;\n                if (files.length > 0) {\n                  setSelectedFile(files[0]);\n                }\n                e.currentTarget.style.borderColor = '#667eea';\n                e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';\n              }}\n            >\n              <input\n                id=\"file-input\"\n                type=\"file\"\n                accept=\"image/*\"\n                onChange={handleFileSelect}\n                style={{ display: 'none' }}\n              />\n\n              {selectedFile ? (\n                <div style={{ textAlign: 'center', padding: '20px' }}>\n                  <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>✅</div>\n                  <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>文件已选择</h3>\n                  <p style={{ margin: '0 0 8px 0', color: '#666', fontSize: '14px', fontWeight: '500' }}>\n                    {selectedFile.name}\n                  </p>\n                  <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>\n                    大小: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB\n                  </p>\n                  <button\n                    type=\"button\"\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      setSelectedFile(null);\n                      document.getElementById('file-input').value = '';\n                    }}\n                    style={{\n                      marginTop: '12px',\n                      background: 'rgba(239, 68, 68, 0.1)',\n                      color: '#ef4444',\n                      border: '1px solid rgba(239, 68, 68, 0.3)',\n                      padding: '6px 12px',\n                      borderRadius: '6px',\n                      fontSize: '12px',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    }}\n                  >\n                    重新选择\n                  </button>\n                </div>\n              ) : (\n                <div style={{ textAlign: 'center', padding: '20px' }}>\n                  <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>📤</div>\n                  <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>点击或拖拽上传图片</h3>\n                  <p style={{ margin: '0 0 4px 0', color: '#666', fontSize: '14px' }}>\n                    支持 PNG、JPG、JPEG、GIF 格式\n                  </p>\n                  <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>\n                    文件大小不超过 10MB\n                  </p>\n                  <div style={{\n                    marginTop: '16px',\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    color: 'white',\n                    padding: '8px 20px',\n                    borderRadius: '20px',\n                    fontSize: '14px',\n                    fontWeight: '500',\n                    display: 'inline-block'\n                  }}>\n                    选择文件\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* 界面功能描述 */}\n          <div style={{ marginBottom: '20px' }}>\n            <label htmlFor=\"description\" style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n              🎯 界面功能描述\n            </label>\n            <textarea\n              id=\"description\"\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              placeholder=\"请详细描述这个界面的主要功能，例如：这是一个登录页面，包含用户名密码输入框和登录按钮...\"\n              rows={4}\n              style={{\n                width: '100%',\n                padding: '12px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '8px',\n                resize: 'vertical',\n                fontFamily: 'inherit',\n                fontSize: '14px',\n                lineHeight: '1.5',\n                transition: 'border-color 0.2s ease',\n                background: '#fafafa'\n              }}\n              onFocus={(e) => {\n                e.target.style.borderColor = '#667eea';\n                e.target.style.background = '#ffffff';\n              }}\n              onBlur={(e) => {\n                e.target.style.borderColor = '#e5e7eb';\n                e.target.style.background = '#fafafa';\n              }}\n              required\n            />\n            <div style={{ textAlign: 'right', fontSize: '12px', color: '#666', marginTop: '4px' }}>\n              {description.length}/500\n            </div>\n          </div>\n\n          <button\n            type=\"submit\"\n            disabled={isUploading || !selectedFile || !description.trim()}\n            style={{\n              background: isUploading ? '#6c757d' : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '8px',\n              fontSize: '16px',\n              fontWeight: '500',\n              cursor: isUploading ? 'not-allowed' : 'pointer',\n              transition: 'all 0.3s ease',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px',\n              width: '100%',\n              justifyContent: 'center'\n            }}\n          >\n            {isUploading ? (\n              <>\n                <span style={{\n                  width: '16px',\n                  height: '16px',\n                  border: '2px solid transparent',\n                  borderTop: '2px solid white',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}></span>\n                开始分析中...\n              </>\n            ) : (\n              '🚀 开始分析'\n            )}\n          </button>\n        </form>\n      </div>\n\n      {/* 右侧实时分析界面 */}\n      {showAnalysis && (\n        <div style={{\n          flex: '1',\n          background: 'white',\n          padding: '24px',\n          borderRadius: '12px',\n          boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n          overflow: 'hidden',\n          display: 'flex',\n          flexDirection: 'column'\n        }}>\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            marginBottom: '24px',\n            paddingBottom: '16px',\n            borderBottom: '2px solid #f1f3f4'\n          }}>\n            <h3 style={{ margin: 0, color: '#333', fontSize: '20px', fontWeight: '600' }}>\n              🔍 实时分析进度\n            </h3>\n            <div style={{\n              background: analysisProgress.overall === 100 ? '#d4edda' : '#f8f9fa',\n              color: analysisProgress.overall === 100 ? '#155724' : '#666',\n              padding: '8px 16px',\n              borderRadius: '20px',\n              fontSize: '14px',\n              fontWeight: '500',\n              border: analysisProgress.overall === 100 ? '1px solid #c3e6cb' : 'none'\n            }}>\n              {analysisProgress.overall}%\n            </div>\n          </div>\n\n          {/* 当前步骤 */}\n          <div style={{\n            background: '#f8f9ff',\n            border: '1px solid #e5e7eb',\n            borderRadius: '8px',\n            padding: '16px',\n            marginBottom: '20px'\n          }}>\n            <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>\n              分析状态\n            </div>\n            <div style={{ fontSize: '16px', fontWeight: '600', color: '#333' }}>\n              {analysisProgress.currentStep}\n            </div>\n          </div>\n\n          {/* 分析步骤概览 */}\n          <div style={{ marginBottom: '20px' }}>\n            <div style={{ fontSize: '16px', fontWeight: '600', color: '#333', marginBottom: '12px' }}>\n              📊 分析步骤概览\n            </div>\n            <div style={{ display: 'flex', gap: '8px' }}>\n              {analysisProgress.steps.map((step, index) => (\n                <div key={index} style={{\n                  flex: 1,\n                  padding: '12px 8px',\n                  background: step.status === 'completed' ? '#d4edda' :\n                             step.status === 'processing' ? '#e3f2fd' : '#f8f9fa',\n                  border: `1px solid ${step.status === 'completed' ? '#c3e6cb' :\n                                      step.status === 'processing' ? '#bbdefb' : '#e9ecef'}`,\n                  borderRadius: '6px',\n                  textAlign: 'center',\n                  fontSize: '12px',\n                  transition: 'all 0.3s ease'\n                }}>\n                  <div style={{\n                    fontSize: '16px',\n                    marginBottom: '4px',\n                    color: step.status === 'completed' ? '#28a745' :\n                           step.status === 'processing' ? '#1976d2' : '#6c757d'\n                  }}>\n                    {step.status === 'completed' ? '✅' :\n                     step.status === 'processing' ? '⚡' : '⏳'}\n                  </div>\n                  <div style={{ fontWeight: '500', color: '#333', fontSize: '11px' }}>\n                    {step.name}\n                  </div>\n                  <div style={{\n                    width: '100%',\n                    height: '3px',\n                    background: '#e9ecef',\n                    borderRadius: '2px',\n                    overflow: 'hidden',\n                    marginTop: '6px'\n                  }}>\n                    <div style={{\n                      height: '100%',\n                      width: `${step.progress}%`,\n                      background: step.status === 'completed' ? '#28a745' :\n                                 step.status === 'processing' ? '#1976d2' : '#e9ecef',\n                      transition: 'width 0.5s ease',\n                      borderRadius: '2px'\n                    }}></div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* 实时日志区域 */}\n          <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              marginBottom: '12px'\n            }}>\n              <div style={{ fontSize: '16px', fontWeight: '600', color: '#333' }}>\n                📝 实时分析日志\n              </div>\n              <div style={{\n                fontSize: '12px',\n                color: '#666',\n                background: '#f8f9fa',\n                padding: '4px 8px',\n                borderRadius: '12px'\n              }}>\n                {analysisLogs.length} 条日志\n              </div>\n            </div>\n\n            <div\n              ref={logContainerRef}\n              style={{\n                flex: 1,\n                background: '#1a1a1a',\n                borderRadius: '8px',\n                padding: '16px',\n                overflow: 'auto',\n                fontFamily: 'Monaco, Consolas, \"Courier New\", monospace',\n                fontSize: '13px',\n                lineHeight: '1.5',\n                maxHeight: '400px'\n              }}>\n              {analysisLogs.length === 0 ? (\n                <div style={{ color: '#666', textAlign: 'center', padding: '20px' }}>\n                  等待分析开始...\n                </div>\n              ) : (\n                analysisLogs.map((log) => (\n                  <div key={log.id} style={{\n                    marginBottom: '8px',\n                    padding: '6px 0',\n                    borderBottom: '1px solid #333'\n                  }}>\n                    <div style={{\n                      display: 'flex',\n                      alignItems: 'flex-start',\n                      gap: '8px'\n                    }}>\n                      <span style={{\n                        color: '#888',\n                        fontSize: '11px',\n                        minWidth: '60px',\n                        fontWeight: '500'\n                      }}>\n                        {log.timestamp}\n                      </span>\n                      <span style={{\n                        color: log.type === 'start' ? '#4fc3f7' :\n                               log.type === 'step' ? '#ffb74d' :\n                               log.type === 'success' ? '#81c784' :\n                               log.type === 'error' ? '#e57373' : '#e0e0e0',\n                        fontWeight: log.type === 'step' || log.type === 'start' || log.type === 'success' ? '600' : '400'\n                      }}>\n                        {log.message}\n                      </span>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n\n      <style>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default SimpleUpload;\n"], "mappings": "wJAAA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE3D,KAAM,CAAAC,YAAY,CAAGC,IAAA,EAAwC,IAAvC,CAAEC,eAAe,CAAEC,aAAc,CAAC,CAAAF,IAAA,CACtD,KAAM,CAACG,YAAY,CAAEC,eAAe,CAAC,CAAGd,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACe,WAAW,CAAEC,cAAc,CAAC,CAAGhB,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACiB,WAAW,CAAEC,cAAc,CAAC,CAAGlB,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACmB,YAAY,CAAEC,eAAe,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACqB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGtB,QAAQ,CAAC,CACvDuB,OAAO,CAAE,CAAC,CACVC,WAAW,CAAE,WAAW,CACxBC,KAAK,CAAE,CACL,CAAEC,IAAI,CAAE,KAAK,CAAEC,MAAM,CAAE,SAAS,CAAEC,QAAQ,CAAE,CAAE,CAAC,CAC/C,CAAEF,IAAI,CAAE,WAAW,CAAEC,MAAM,CAAE,SAAS,CAAEC,QAAQ,CAAE,CAAE,CAAC,CACrD,CAAEF,IAAI,CAAE,WAAW,CAAEC,MAAM,CAAE,SAAS,CAAEC,QAAQ,CAAE,CAAE,CAAC,CAEzD,CAAC,CAAC,CACF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAA+B,eAAe,CAAG7B,MAAM,CAAC,IAAI,CAAC,CAEpC;AACAD,SAAS,CAAC,IAAM,CACd,GAAI8B,eAAe,CAACC,OAAO,CAAE,CAC3BD,eAAe,CAACC,OAAO,CAACC,SAAS,CAAGF,eAAe,CAACC,OAAO,CAACE,YAAY,CAC1E,CACF,CAAC,CAAE,CAACL,YAAY,CAAC,CAAC,CAElB,KAAM,CAAAM,gBAAgB,CAAIC,KAAK,EAAK,CAClC,KAAM,CAAAC,IAAI,CAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAClC,GAAIF,IAAI,CAAE,CACRvB,eAAe,CAACuB,IAAI,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAG,YAAY,CAAG,KAAO,CAAAJ,KAAK,EAAK,CACpCA,KAAK,CAACK,cAAc,CAAC,CAAC,CAEtB,GAAI,CAAC5B,YAAY,CAAE,CACjBD,aAAa,CAAC,SAAS,CAAC,CACxB,OACF,CAEA,GAAI,CAACG,WAAW,CAAC2B,IAAI,CAAC,CAAC,CAAE,CACvB9B,aAAa,CAAC,WAAW,CAAC,CAC1B,OACF,CAEAM,cAAc,CAAC,IAAI,CAAC,CACpBE,eAAe,CAAC,IAAI,CAAC,CAErB,GAAI,CACF,KAAM,CAAAuB,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,YAAY,CAAEhC,YAAY,CAAC,CAC3C8B,QAAQ,CAACE,MAAM,CAAC,aAAa,CAAE9B,WAAW,CAAC2B,IAAI,CAAC,CAAC,CAAC,CAElD;AACAI,wBAAwB,CAAC,CAAC,CAE1B,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,qCAAqC,CAAE,CAClEC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEP,QACR,CAAC,CAAC,CAEF,GAAI,CAACI,QAAQ,CAACI,EAAE,CAAE,CAChB,KAAM,CAAAC,SAAS,CAAG,KAAM,CAAAL,QAAQ,CAACM,IAAI,CAAC,CAAC,CACvC,KAAM,IAAI,CAAAC,KAAK,CAACF,SAAS,CAACG,MAAM,yBAAAC,MAAA,CAA2BT,QAAQ,CAACpB,MAAM,CAAE,CAAC,CAC/E,CAEA,KAAM,CAAA8B,MAAM,CAAG,KAAM,CAAAV,QAAQ,CAACM,IAAI,CAAC,CAAC,CACpC1C,eAAe,CAAC8C,MAAM,CAAC,CAEzB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrCtC,eAAe,CAAC,KAAK,CAAC,CACtBR,aAAa,CAAC8C,KAAK,CAACE,OAAO,EAAI,UAAU,CAAC,CAC5C,CAAC,OAAS,CACR1C,cAAc,CAAC,KAAK,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAA2C,MAAM,CAAG,QAAAA,CAACD,OAAO,CAAoB,IAAlB,CAAAE,IAAI,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,MAAM,CACpC,KAAM,CAAAG,SAAS,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,CACjDtC,eAAe,CAACuC,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,CAChCC,EAAE,CAAEH,IAAI,CAACI,GAAG,CAAC,CAAC,CAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAC9BP,SAAS,CACTN,OAAO,CACPE,IACF,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAhB,wBAAwB,CAAGA,CAAA,GAAM,CACrC,GAAI,CAAA4B,IAAI,CAAG,CAAC,CACZ,KAAM,CAAAjD,KAAK,CAAG,CAAC,KAAK,CAAE,WAAW,CAAE,WAAW,CAAC,CAE/C;AACAK,eAAe,CAAC,EAAE,CAAC,CAEnB;AACA,KAAM,CAAA6C,QAAQ,CAAG,CACf,CAAC,CAAE,CAAE;AACH,cAAc,CACd,gBAAgB,CAChB,cAAc,CACd,eAAe,CACf,SAAS,CACV,CACD,CAAC,CAAE,CAAE;AACH,eAAe,CACf,aAAa,CACb,cAAc,CACd,cAAc,CACd,eAAe,CACf,cAAc,CACd,eAAe,CACf,UAAU,CACX,CACD,CAAC,CAAE,CAAE;AACH,gBAAgB,CAChB,sBAAsB,CACtB,aAAa,CACb,YAAY,CACZ,cAAc,CACd,cAAc,CACd,YAAY,CAEhB,CAAC,CAED,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAIF,IAAI,CAAGjD,KAAK,CAACuC,MAAM,CAAE,CACvB;AACAH,MAAM,8BAAAL,MAAA,CAAU/B,KAAK,CAACiD,IAAI,CAAC,EAAI,MAAM,CAAC,CAEtC;AACApD,mBAAmB,CAAC+C,IAAI,EAAAQ,aAAA,CAAAA,aAAA,IACnBR,IAAI,MACP9C,OAAO,CAAEiD,IAAI,CAACM,KAAK,CAAC,CAACJ,IAAI,CAAG,CAAC,EAAIjD,KAAK,CAACuC,MAAM,CAAG,GAAG,CAAC,CACpDxC,WAAW,8BAAAgC,MAAA,CAAW/B,KAAK,CAACiD,IAAI,CAAC,CAAE,CACnCjD,KAAK,CAAE4C,IAAI,CAAC5C,KAAK,CAACsD,GAAG,CAAC,CAACC,CAAC,CAAEC,KAAK,GAAAJ,aAAA,CAAAA,aAAA,IAC1BG,CAAC,MACJrD,MAAM,CAAEsD,KAAK,CAAGP,IAAI,CAAG,WAAW,CAAGO,KAAK,GAAKP,IAAI,CAAG,YAAY,CAAG,SAAS,CAC9E9C,QAAQ,CAAEqD,KAAK,CAAGP,IAAI,CAAG,GAAG,CAAGO,KAAK,GAAKP,IAAI,CAAG,CAAC,CAAG,CAAC,EACrD,CAAC,EACH,CAAC,CAEH;AACA,KAAM,CAAAQ,eAAe,CAAGP,QAAQ,CAACD,IAAI,CAAC,CACtC,GAAI,CAAAS,QAAQ,CAAG,CAAC,CAEhB,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB,GAAID,QAAQ,CAAGD,eAAe,CAAClB,MAAM,CAAE,CACrCH,MAAM,CAACqB,eAAe,CAACC,QAAQ,CAAC,CAAE,MAAM,CAAC,CAEzC;AACA,KAAM,CAAAvD,QAAQ,CAAG4C,IAAI,CAACM,KAAK,CAAE,CAACK,QAAQ,CAAG,CAAC,EAAID,eAAe,CAAClB,MAAM,CAAI,GAAG,CAAC,CAC5E1C,mBAAmB,CAAC+C,IAAI,EAAAQ,aAAA,CAAAA,aAAA,IACnBR,IAAI,MACP5C,KAAK,CAAE4C,IAAI,CAAC5C,KAAK,CAACsD,GAAG,CAAC,CAACC,CAAC,CAAEC,KAAK,GAC7BA,KAAK,GAAKP,IAAI,CAAAG,aAAA,CAAAA,aAAA,IAAQG,CAAC,MAAEpD,QAAQ,GAAKoD,CACxC,CAAC,EACD,CAAC,CAEHG,QAAQ,EAAE,CACVE,UAAU,CAACD,WAAW,CAAE,GAAG,CAAGZ,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,IAAI,CAAC,CACrD,CAAC,IAAM,CACL;AACAnD,mBAAmB,CAAC+C,IAAI,EAAAQ,aAAA,CAAAA,aAAA,IACnBR,IAAI,MACP5C,KAAK,CAAE4C,IAAI,CAAC5C,KAAK,CAACsD,GAAG,CAAC,CAACC,CAAC,CAAEC,KAAK,GAC7BA,KAAK,GAAKP,IAAI,CAAAG,aAAA,CAAAA,aAAA,IAAQG,CAAC,MAAErD,MAAM,CAAE,WAAW,CAAEC,QAAQ,CAAE,GAAG,GAAKoD,CAClE,CAAC,EACD,CAAC,CAEHN,IAAI,EAAE,CACNW,UAAU,CAACT,cAAc,CAAE,IAAI,CAAC,CAClC,CACF,CAAC,CAEDS,UAAU,CAACD,WAAW,CAAE,GAAG,CAAC,CAE9B,CAAC,IAAM,CACL;AACAvB,MAAM,CAAC,eAAe,CAAE,SAAS,CAAC,CAClCA,MAAM,CAAC,mBAAmB,CAAE,SAAS,CAAC,CAEtCvC,mBAAmB,CAAC+C,IAAI,EAAAQ,aAAA,CAAAA,aAAA,IACnBR,IAAI,MACP9C,OAAO,CAAE,GAAG,CACZC,WAAW,CAAE,MAAM,CACnBC,KAAK,CAAE4C,IAAI,CAAC5C,KAAK,CAACsD,GAAG,CAACC,CAAC,EAAAH,aAAA,CAAAA,aAAA,IAAUG,CAAC,MAAErD,MAAM,CAAE,WAAW,CAAEC,QAAQ,CAAE,GAAG,EAAG,CAAC,EAC1E,CAAC,CACL,CACF,CAAC,CAED;AACAiC,MAAM,CAAC,qBAAqB,CAAE,OAAO,CAAC,CACtCwB,UAAU,CAACT,cAAc,CAAE,IAAI,CAAC,CAClC,CAAC,CAED,mBACEtE,KAAA,QAAKgF,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,GAAG,CAAE,MAAM,CAAEC,MAAM,CAAE,OAAO,CAAEC,OAAO,CAAE,MAAO,CAAE,CAAAC,QAAA,eAE7ErF,KAAA,QAAKgF,KAAK,CAAE,CACVM,IAAI,CAAEzE,YAAY,CAAG,WAAW,CAAG,GAAG,CACtC0E,UAAU,CAAE,OAAO,CACnBH,OAAO,CAAE,MAAM,CACfI,YAAY,CAAE,MAAM,CACpBC,SAAS,CAAE,4BAA4B,CACvCC,UAAU,CAAE,eACd,CAAE,CAAAL,QAAA,eACAvF,IAAA,OAAAuF,QAAA,CAAI,qDAAW,CAAI,CAAC,cACpBrF,KAAA,SAAM2F,QAAQ,CAAEzD,YAAa,CAAAmD,QAAA,eAC3BrF,KAAA,QAAKgF,KAAK,CAAE,CAAEY,YAAY,CAAE,MAAO,CAAE,CAAAP,QAAA,eACnCvF,IAAA,UAAOkF,KAAK,CAAE,CAAEC,OAAO,CAAE,OAAO,CAAEW,YAAY,CAAE,MAAM,CAAEC,UAAU,CAAE,KAAK,CAAEC,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAV,QAAA,CAAC,qDAE9G,CAAO,CAAC,cACRrF,KAAA,QACEgG,SAAS,CAAC,kBAAkB,CAC5BC,OAAO,CAAEA,CAAA,GAAMC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAACC,KAAK,CAAC,CAAE,CAC7DpB,KAAK,CAAE,CACLG,MAAM,CAAE,OAAO,CACfkB,MAAM,CAAE,oBAAoB,CAC5Bb,YAAY,CAAE,MAAM,CACpBD,UAAU,CAAEhF,YAAY,CAAG,mDAAmD,CAAG,mDAAmD,CACpI0E,OAAO,CAAE,MAAM,CACfqB,aAAa,CAAE,QAAQ,CACvBC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBC,MAAM,CAAE,SAAS,CACjBf,UAAU,CAAE,eAAe,CAC3BgB,QAAQ,CAAE,UAAU,CACpBC,QAAQ,CAAE,QACZ,CAAE,CACFC,UAAU,CAAGC,CAAC,EAAK,CACjBA,CAAC,CAAC1E,cAAc,CAAC,CAAC,CAClB0E,CAAC,CAACC,aAAa,CAAC9B,KAAK,CAAC+B,WAAW,CAAG,SAAS,CAC7CF,CAAC,CAACC,aAAa,CAAC9B,KAAK,CAACO,UAAU,CAAG,mDAAmD,CACxF,CAAE,CACFyB,WAAW,CAAGH,CAAC,EAAK,CAClBA,CAAC,CAACC,aAAa,CAAC9B,KAAK,CAAC+B,WAAW,CAAG,SAAS,CAC7CF,CAAC,CAACC,aAAa,CAAC9B,KAAK,CAACO,UAAU,CAAGhF,YAAY,CAAG,mDAAmD,CAAG,mDAAmD,CAC7J,CAAE,CACF0G,MAAM,CAAGJ,CAAC,EAAK,CACbA,CAAC,CAAC1E,cAAc,CAAC,CAAC,CAClB,KAAM,CAAAF,KAAK,CAAG4E,CAAC,CAACK,YAAY,CAACjF,KAAK,CAClC,GAAIA,KAAK,CAACyB,MAAM,CAAG,CAAC,CAAE,CACpBlD,eAAe,CAACyB,KAAK,CAAC,CAAC,CAAC,CAAC,CAC3B,CACA4E,CAAC,CAACC,aAAa,CAAC9B,KAAK,CAAC+B,WAAW,CAAG,SAAS,CAC7CF,CAAC,CAACC,aAAa,CAAC9B,KAAK,CAACO,UAAU,CAAG,mDAAmD,CACxF,CAAE,CAAAF,QAAA,eAEFvF,IAAA,UACEkE,EAAE,CAAC,YAAY,CACfR,IAAI,CAAC,MAAM,CACX2D,MAAM,CAAC,SAAS,CAChBC,QAAQ,CAAEvF,gBAAiB,CAC3BmD,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAO,CAAE,CAC5B,CAAC,CAED1E,YAAY,cACXP,KAAA,QAAKgF,KAAK,CAAE,CAAEqC,SAAS,CAAE,QAAQ,CAAEjC,OAAO,CAAE,MAAO,CAAE,CAAAC,QAAA,eACnDvF,IAAA,QAAKkF,KAAK,CAAE,CAAEe,QAAQ,CAAE,MAAM,CAAEH,YAAY,CAAE,MAAM,CAAEE,KAAK,CAAE,SAAU,CAAE,CAAAT,QAAA,CAAC,QAAC,CAAK,CAAC,cACjFvF,IAAA,OAAIkF,KAAK,CAAE,CAAEsC,MAAM,CAAE,WAAW,CAAExB,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAV,QAAA,CAAC,gCAAK,CAAI,CAAC,cAC/EvF,IAAA,MAAGkF,KAAK,CAAE,CAAEsC,MAAM,CAAE,WAAW,CAAExB,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAM,CAAEF,UAAU,CAAE,KAAM,CAAE,CAAAR,QAAA,CACnF9E,YAAY,CAACa,IAAI,CACjB,CAAC,cACJpB,KAAA,MAAGgF,KAAK,CAAE,CAAEsC,MAAM,CAAE,GAAG,CAAExB,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAV,QAAA,EAAC,gBACtD,CAAC,CAAC9E,YAAY,CAACgH,IAAI,CAAG,IAAI,CAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,KACpD,EAAG,CAAC,cACJ1H,IAAA,WACE0D,IAAI,CAAC,QAAQ,CACbyC,OAAO,CAAGY,CAAC,EAAK,CACdA,CAAC,CAACY,eAAe,CAAC,CAAC,CACnBjH,eAAe,CAAC,IAAI,CAAC,CACrB0F,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAACuB,KAAK,CAAG,EAAE,CAClD,CAAE,CACF1C,KAAK,CAAE,CACL2C,SAAS,CAAE,MAAM,CACjBpC,UAAU,CAAE,wBAAwB,CACpCO,KAAK,CAAE,SAAS,CAChBO,MAAM,CAAE,kCAAkC,CAC1CjB,OAAO,CAAE,UAAU,CACnBI,YAAY,CAAE,KAAK,CACnBO,QAAQ,CAAE,MAAM,CAChBU,MAAM,CAAE,SAAS,CACjBf,UAAU,CAAE,eACd,CAAE,CAAAL,QAAA,CACH,0BAED,CAAQ,CAAC,EACN,CAAC,cAENrF,KAAA,QAAKgF,KAAK,CAAE,CAAEqC,SAAS,CAAE,QAAQ,CAAEjC,OAAO,CAAE,MAAO,CAAE,CAAAC,QAAA,eACnDvF,IAAA,QAAKkF,KAAK,CAAE,CAAEe,QAAQ,CAAE,MAAM,CAAEH,YAAY,CAAE,MAAM,CAAEE,KAAK,CAAE,SAAU,CAAE,CAAAT,QAAA,CAAC,cAAE,CAAK,CAAC,cAClFvF,IAAA,OAAIkF,KAAK,CAAE,CAAEsC,MAAM,CAAE,WAAW,CAAExB,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAV,QAAA,CAAC,wDAAS,CAAI,CAAC,cACnFvF,IAAA,MAAGkF,KAAK,CAAE,CAAEsC,MAAM,CAAE,WAAW,CAAExB,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAV,QAAA,CAAC,2DAEpE,CAAG,CAAC,cACJvF,IAAA,MAAGkF,KAAK,CAAE,CAAEsC,MAAM,CAAE,GAAG,CAAExB,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAV,QAAA,CAAC,iDAE5D,CAAG,CAAC,cACJvF,IAAA,QAAKkF,KAAK,CAAE,CACV2C,SAAS,CAAE,MAAM,CACjBpC,UAAU,CAAE,mDAAmD,CAC/DO,KAAK,CAAE,OAAO,CACdV,OAAO,CAAE,UAAU,CACnBI,YAAY,CAAE,MAAM,CACpBO,QAAQ,CAAE,MAAM,CAChBF,UAAU,CAAE,KAAK,CACjBZ,OAAO,CAAE,cACX,CAAE,CAAAI,QAAA,CAAC,0BAEH,CAAK,CAAC,EACH,CACN,EACE,CAAC,EACH,CAAC,cAGNrF,KAAA,QAAKgF,KAAK,CAAE,CAAEY,YAAY,CAAE,MAAO,CAAE,CAAAP,QAAA,eACnCvF,IAAA,UAAO8H,OAAO,CAAC,aAAa,CAAC5C,KAAK,CAAE,CAAEC,OAAO,CAAE,OAAO,CAAEW,YAAY,CAAE,KAAK,CAAEC,UAAU,CAAE,KAAK,CAAEC,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAV,QAAA,CAAC,mDAEnI,CAAO,CAAC,cACRvF,IAAA,aACEkE,EAAE,CAAC,aAAa,CAChB0D,KAAK,CAAEjH,WAAY,CACnB2G,QAAQ,CAAGP,CAAC,EAAKnG,cAAc,CAACmG,CAAC,CAAC7E,MAAM,CAAC0F,KAAK,CAAE,CAChDG,WAAW,CAAC,iQAA+C,CAC3DC,IAAI,CAAE,CAAE,CACR9C,KAAK,CAAE,CACL+C,KAAK,CAAE,MAAM,CACb3C,OAAO,CAAE,MAAM,CACfiB,MAAM,CAAE,mBAAmB,CAC3Bb,YAAY,CAAE,KAAK,CACnBwC,MAAM,CAAE,UAAU,CAClBC,UAAU,CAAE,SAAS,CACrBlC,QAAQ,CAAE,MAAM,CAChBmC,UAAU,CAAE,KAAK,CACjBxC,UAAU,CAAE,wBAAwB,CACpCH,UAAU,CAAE,SACd,CAAE,CACF4C,OAAO,CAAGtB,CAAC,EAAK,CACdA,CAAC,CAAC7E,MAAM,CAACgD,KAAK,CAAC+B,WAAW,CAAG,SAAS,CACtCF,CAAC,CAAC7E,MAAM,CAACgD,KAAK,CAACO,UAAU,CAAG,SAAS,CACvC,CAAE,CACF6C,MAAM,CAAGvB,CAAC,EAAK,CACbA,CAAC,CAAC7E,MAAM,CAACgD,KAAK,CAAC+B,WAAW,CAAG,SAAS,CACtCF,CAAC,CAAC7E,MAAM,CAACgD,KAAK,CAACO,UAAU,CAAG,SAAS,CACvC,CAAE,CACF8C,QAAQ,MACT,CAAC,cACFrI,KAAA,QAAKgF,KAAK,CAAE,CAAEqC,SAAS,CAAE,OAAO,CAAEtB,QAAQ,CAAE,MAAM,CAAED,KAAK,CAAE,MAAM,CAAE6B,SAAS,CAAE,KAAM,CAAE,CAAAtC,QAAA,EACnF5E,WAAW,CAACiD,MAAM,CAAC,MACtB,EAAK,CAAC,EACH,CAAC,cAEN5D,IAAA,WACE0D,IAAI,CAAC,QAAQ,CACb8E,QAAQ,CAAE3H,WAAW,EAAI,CAACJ,YAAY,EAAI,CAACE,WAAW,CAAC2B,IAAI,CAAC,CAAE,CAC9D4C,KAAK,CAAE,CACLO,UAAU,CAAE5E,WAAW,CAAG,SAAS,CAAG,mDAAmD,CACzFmF,KAAK,CAAE,OAAO,CACdO,MAAM,CAAE,MAAM,CACdjB,OAAO,CAAE,WAAW,CACpBI,YAAY,CAAE,KAAK,CACnBO,QAAQ,CAAE,MAAM,CAChBF,UAAU,CAAE,KAAK,CACjBY,MAAM,CAAE9F,WAAW,CAAG,aAAa,CAAG,SAAS,CAC/C+E,UAAU,CAAE,eAAe,CAC3BT,OAAO,CAAE,MAAM,CACfsB,UAAU,CAAE,QAAQ,CACpBrB,GAAG,CAAE,KAAK,CACV6C,KAAK,CAAE,MAAM,CACbvB,cAAc,CAAE,QAClB,CAAE,CAAAnB,QAAA,CAED1E,WAAW,cACVX,KAAA,CAAAE,SAAA,EAAAmF,QAAA,eACEvF,IAAA,SAAMkF,KAAK,CAAE,CACX+C,KAAK,CAAE,MAAM,CACb5C,MAAM,CAAE,MAAM,CACdkB,MAAM,CAAE,uBAAuB,CAC/BkC,SAAS,CAAE,iBAAiB,CAC5B/C,YAAY,CAAE,KAAK,CACnBgD,SAAS,CAAE,yBACb,CAAE,CAAO,CAAC,oCAEZ,EAAE,CAAC,CAEH,SACD,CACK,CAAC,EACL,CAAC,EACJ,CAAC,CAGL3H,YAAY,eACXb,KAAA,QAAKgF,KAAK,CAAE,CACVM,IAAI,CAAE,GAAG,CACTC,UAAU,CAAE,OAAO,CACnBH,OAAO,CAAE,MAAM,CACfI,YAAY,CAAE,MAAM,CACpBC,SAAS,CAAE,4BAA4B,CACvCkB,QAAQ,CAAE,QAAQ,CAClB1B,OAAO,CAAE,MAAM,CACfqB,aAAa,CAAE,QACjB,CAAE,CAAAjB,QAAA,eACArF,KAAA,QAAKgF,KAAK,CAAE,CACVC,OAAO,CAAE,MAAM,CACfsB,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,eAAe,CAC/BZ,YAAY,CAAE,MAAM,CACpB6C,aAAa,CAAE,MAAM,CACrBC,YAAY,CAAE,mBAChB,CAAE,CAAArD,QAAA,eACAvF,IAAA,OAAIkF,KAAK,CAAE,CAAEsC,MAAM,CAAE,CAAC,CAAExB,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAM,CAAEF,UAAU,CAAE,KAAM,CAAE,CAAAR,QAAA,CAAC,mDAE9E,CAAI,CAAC,cACLrF,KAAA,QAAKgF,KAAK,CAAE,CACVO,UAAU,CAAExE,gBAAgB,CAACE,OAAO,GAAK,GAAG,CAAG,SAAS,CAAG,SAAS,CACpE6E,KAAK,CAAE/E,gBAAgB,CAACE,OAAO,GAAK,GAAG,CAAG,SAAS,CAAG,MAAM,CAC5DmE,OAAO,CAAE,UAAU,CACnBI,YAAY,CAAE,MAAM,CACpBO,QAAQ,CAAE,MAAM,CAChBF,UAAU,CAAE,KAAK,CACjBQ,MAAM,CAAEtF,gBAAgB,CAACE,OAAO,GAAK,GAAG,CAAG,mBAAmB,CAAG,MACnE,CAAE,CAAAoE,QAAA,EACCtE,gBAAgB,CAACE,OAAO,CAAC,GAC5B,EAAK,CAAC,EACH,CAAC,cAGNjB,KAAA,QAAKgF,KAAK,CAAE,CACVO,UAAU,CAAE,SAAS,CACrBc,MAAM,CAAE,mBAAmB,CAC3Bb,YAAY,CAAE,KAAK,CACnBJ,OAAO,CAAE,MAAM,CACfQ,YAAY,CAAE,MAChB,CAAE,CAAAP,QAAA,eACAvF,IAAA,QAAKkF,KAAK,CAAE,CAAEe,QAAQ,CAAE,MAAM,CAAED,KAAK,CAAE,MAAM,CAAEF,YAAY,CAAE,KAAM,CAAE,CAAAP,QAAA,CAAC,0BAEtE,CAAK,CAAC,cACNvF,IAAA,QAAKkF,KAAK,CAAE,CAAEe,QAAQ,CAAE,MAAM,CAAEF,UAAU,CAAE,KAAK,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAT,QAAA,CAChEtE,gBAAgB,CAACG,WAAW,CAC1B,CAAC,EACH,CAAC,cAGNlB,KAAA,QAAKgF,KAAK,CAAE,CAAEY,YAAY,CAAE,MAAO,CAAE,CAAAP,QAAA,eACnCvF,IAAA,QAAKkF,KAAK,CAAE,CAAEe,QAAQ,CAAE,MAAM,CAAEF,UAAU,CAAE,KAAK,CAAEC,KAAK,CAAE,MAAM,CAAEF,YAAY,CAAE,MAAO,CAAE,CAAAP,QAAA,CAAC,mDAE1F,CAAK,CAAC,cACNvF,IAAA,QAAKkF,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,GAAG,CAAE,KAAM,CAAE,CAAAG,QAAA,CACzCtE,gBAAgB,CAACI,KAAK,CAACsD,GAAG,CAAC,CAACL,IAAI,CAAEO,KAAK,gBACtC3E,KAAA,QAAiBgF,KAAK,CAAE,CACtBM,IAAI,CAAE,CAAC,CACPF,OAAO,CAAE,UAAU,CACnBG,UAAU,CAAEnB,IAAI,CAAC/C,MAAM,GAAK,WAAW,CAAG,SAAS,CACxC+C,IAAI,CAAC/C,MAAM,GAAK,YAAY,CAAG,SAAS,CAAG,SAAS,CAC/DgF,MAAM,cAAAnD,MAAA,CAAekB,IAAI,CAAC/C,MAAM,GAAK,WAAW,CAAG,SAAS,CACxC+C,IAAI,CAAC/C,MAAM,GAAK,YAAY,CAAG,SAAS,CAAG,SAAS,CAAE,CAC1EmE,YAAY,CAAE,KAAK,CACnB6B,SAAS,CAAE,QAAQ,CACnBtB,QAAQ,CAAE,MAAM,CAChBL,UAAU,CAAE,eACd,CAAE,CAAAL,QAAA,eACAvF,IAAA,QAAKkF,KAAK,CAAE,CACVe,QAAQ,CAAE,MAAM,CAChBH,YAAY,CAAE,KAAK,CACnBE,KAAK,CAAE1B,IAAI,CAAC/C,MAAM,GAAK,WAAW,CAAG,SAAS,CACvC+C,IAAI,CAAC/C,MAAM,GAAK,YAAY,CAAG,SAAS,CAAG,SACpD,CAAE,CAAAgE,QAAA,CACCjB,IAAI,CAAC/C,MAAM,GAAK,WAAW,CAAG,GAAG,CACjC+C,IAAI,CAAC/C,MAAM,GAAK,YAAY,CAAG,GAAG,CAAG,GAAG,CACtC,CAAC,cACNvB,IAAA,QAAKkF,KAAK,CAAE,CAAEa,UAAU,CAAE,KAAK,CAAEC,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAV,QAAA,CAChEjB,IAAI,CAAChD,IAAI,CACP,CAAC,cACNtB,IAAA,QAAKkF,KAAK,CAAE,CACV+C,KAAK,CAAE,MAAM,CACb5C,MAAM,CAAE,KAAK,CACbI,UAAU,CAAE,SAAS,CACrBC,YAAY,CAAE,KAAK,CACnBmB,QAAQ,CAAE,QAAQ,CAClBgB,SAAS,CAAE,KACb,CAAE,CAAAtC,QAAA,cACAvF,IAAA,QAAKkF,KAAK,CAAE,CACVG,MAAM,CAAE,MAAM,CACd4C,KAAK,IAAA7E,MAAA,CAAKkB,IAAI,CAAC9C,QAAQ,KAAG,CAC1BiE,UAAU,CAAEnB,IAAI,CAAC/C,MAAM,GAAK,WAAW,CAAG,SAAS,CACxC+C,IAAI,CAAC/C,MAAM,GAAK,YAAY,CAAG,SAAS,CAAG,SAAS,CAC/DqE,UAAU,CAAE,iBAAiB,CAC7BF,YAAY,CAAE,KAChB,CAAE,CAAM,CAAC,CACN,CAAC,GAxCEb,KAyCL,CACN,CAAC,CACC,CAAC,EACH,CAAC,cAGN3E,KAAA,QAAKgF,KAAK,CAAE,CAAEM,IAAI,CAAE,CAAC,CAAEL,OAAO,CAAE,MAAM,CAAEqB,aAAa,CAAE,QAAS,CAAE,CAAAjB,QAAA,eAChErF,KAAA,QAAKgF,KAAK,CAAE,CACVC,OAAO,CAAE,MAAM,CACfsB,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,eAAe,CAC/BZ,YAAY,CAAE,MAChB,CAAE,CAAAP,QAAA,eACAvF,IAAA,QAAKkF,KAAK,CAAE,CAAEe,QAAQ,CAAE,MAAM,CAAEF,UAAU,CAAE,KAAK,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAT,QAAA,CAAC,mDAEpE,CAAK,CAAC,cACNrF,KAAA,QAAKgF,KAAK,CAAE,CACVe,QAAQ,CAAE,MAAM,CAChBD,KAAK,CAAE,MAAM,CACbP,UAAU,CAAE,SAAS,CACrBH,OAAO,CAAE,SAAS,CAClBI,YAAY,CAAE,MAChB,CAAE,CAAAH,QAAA,EACC9D,YAAY,CAACmC,MAAM,CAAC,qBACvB,EAAK,CAAC,EACH,CAAC,cAEN5D,IAAA,QACE6I,GAAG,CAAElH,eAAgB,CACrBuD,KAAK,CAAE,CACLM,IAAI,CAAE,CAAC,CACPC,UAAU,CAAE,SAAS,CACrBC,YAAY,CAAE,KAAK,CACnBJ,OAAO,CAAE,MAAM,CACfuB,QAAQ,CAAE,MAAM,CAChBsB,UAAU,CAAE,4CAA4C,CACxDlC,QAAQ,CAAE,MAAM,CAChBmC,UAAU,CAAE,KAAK,CACjBU,SAAS,CAAE,OACb,CAAE,CAAAvD,QAAA,CACD9D,YAAY,CAACmC,MAAM,GAAK,CAAC,cACxB5D,IAAA,QAAKkF,KAAK,CAAE,CAAEc,KAAK,CAAE,MAAM,CAAEuB,SAAS,CAAE,QAAQ,CAAEjC,OAAO,CAAE,MAAO,CAAE,CAAAC,QAAA,CAAC,yCAErE,CAAK,CAAC,CAEN9D,YAAY,CAACkD,GAAG,CAAEoE,GAAG,eACnB/I,IAAA,QAAkBkF,KAAK,CAAE,CACvBY,YAAY,CAAE,KAAK,CACnBR,OAAO,CAAE,OAAO,CAChBsD,YAAY,CAAE,gBAChB,CAAE,CAAArD,QAAA,cACArF,KAAA,QAAKgF,KAAK,CAAE,CACVC,OAAO,CAAE,MAAM,CACfsB,UAAU,CAAE,YAAY,CACxBrB,GAAG,CAAE,KACP,CAAE,CAAAG,QAAA,eACAvF,IAAA,SAAMkF,KAAK,CAAE,CACXc,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,MAAM,CAChB+C,QAAQ,CAAE,MAAM,CAChBjD,UAAU,CAAE,KACd,CAAE,CAAAR,QAAA,CACCwD,GAAG,CAACjF,SAAS,CACV,CAAC,cACP9D,IAAA,SAAMkF,KAAK,CAAE,CACXc,KAAK,CAAE+C,GAAG,CAACrF,IAAI,GAAK,OAAO,CAAG,SAAS,CAChCqF,GAAG,CAACrF,IAAI,GAAK,MAAM,CAAG,SAAS,CAC/BqF,GAAG,CAACrF,IAAI,GAAK,SAAS,CAAG,SAAS,CAClCqF,GAAG,CAACrF,IAAI,GAAK,OAAO,CAAG,SAAS,CAAG,SAAS,CACnDqC,UAAU,CAAEgD,GAAG,CAACrF,IAAI,GAAK,MAAM,EAAIqF,GAAG,CAACrF,IAAI,GAAK,OAAO,EAAIqF,GAAG,CAACrF,IAAI,GAAK,SAAS,CAAG,KAAK,CAAG,KAC9F,CAAE,CAAA6B,QAAA,CACCwD,GAAG,CAACvF,OAAO,CACR,CAAC,EACJ,CAAC,EA3BEuF,GAAG,CAAC7E,EA4BT,CACN,CACF,CACE,CAAC,EACH,CAAC,EACH,CACN,cAEDlE,IAAA,UAAAuF,QAAA,4IAKS,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAAlF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}