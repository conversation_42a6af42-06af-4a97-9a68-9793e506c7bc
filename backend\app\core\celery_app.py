"""
Celery 配置
"""
from celery import Celery
import os

# Redis 配置
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")

# 创建 Celery 应用
celery_app = Celery(
    "ui_automation",
    broker=REDIS_URL,
    backend=REDIS_URL,
    include=[
        "app.core.analysis_pipeline",
        "app.agents.element_detection.agent",
        "app.agents.interaction_analysis.agent", 
        "app.agents.test_generation.agent"
    ]
)

# Celery 配置
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="Asia/Shanghai",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30分钟超时
    task_soft_time_limit=25 * 60,  # 25分钟软超时
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# 任务路由配置
celery_app.conf.task_routes = {
    "app.core.analysis_pipeline.*": {"queue": "analysis"},
    "app.agents.*": {"queue": "agents"},
}
