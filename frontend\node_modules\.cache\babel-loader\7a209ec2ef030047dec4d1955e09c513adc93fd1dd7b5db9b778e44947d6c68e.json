{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\components\\\\SimpleUpload.js\",\n  _s = $RefreshSig$();\n/**\n * 简化的上传组件\n */\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SimpleUpload = ({\n  onUploadSuccess,\n  onUploadError\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n    setIsUploading(true);\n    try {\n      // 生成任务ID并启动实时分析\n      const taskId = 'task_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n\n      // 模拟上传成功，返回任务ID\n      onUploadSuccess({\n        task_id: taskId,\n        message: \"文件上传成功，开始实时分析\",\n        description: description.trim(),\n        filename: selectedFile.name\n      });\n\n      // 重置表单\n      setSelectedFile(null);\n      setDescription('');\n      event.target.reset();\n    } catch (error) {\n      console.error('Upload error:', error);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'white',\n      padding: '24px',\n      borderRadius: '12px',\n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n      marginBottom: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"file-input\",\n          style: {\n            display: 'block',\n            marginBottom: '8px',\n            fontWeight: '500'\n          },\n          children: \"\\u9009\\u62E9\\u56FE\\u7247\\u6587\\u4EF6:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          id: \"file-input\",\n          type: \"file\",\n          accept: \"image/*\",\n          onChange: handleFileSelect,\n          style: {\n            width: '100%',\n            padding: '8px',\n            border: '1px solid #ddd',\n            borderRadius: '4px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), selectedFile && /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            marginTop: '8px',\n            color: '#666',\n            fontSize: '14px'\n          },\n          children: [\"\\u5DF2\\u9009\\u62E9: \", selectedFile.name, \" (\", (selectedFile.size / 1024 / 1024).toFixed(2), \" MB)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"description\",\n          style: {\n            display: 'block',\n            marginBottom: '8px',\n            fontWeight: '500'\n          },\n          children: \"\\u754C\\u9762\\u529F\\u80FD\\u63CF\\u8FF0:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          id: \"description\",\n          value: description,\n          onChange: e => setDescription(e.target.value),\n          placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u8FD9\\u4E2A\\u754C\\u9762\\u7684\\u4E3B\\u8981\\u529F\\u80FD\\uFF0C\\u4F8B\\u5982\\uFF1A\\u8FD9\\u662F\\u4E00\\u4E2A\\u767B\\u5F55\\u9875\\u9762\\uFF0C\\u5305\\u542B\\u7528\\u6237\\u540D\\u5BC6\\u7801\\u8F93\\u5165\\u6846\\u548C\\u767B\\u5F55\\u6309\\u94AE...\",\n          rows: 4,\n          style: {\n            width: '100%',\n            padding: '12px',\n            border: '1px solid #ddd',\n            borderRadius: '4px',\n            resize: 'vertical',\n            fontFamily: 'inherit'\n          },\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'right',\n            fontSize: '12px',\n            color: '#666',\n            marginTop: '4px'\n          },\n          children: [description.length, \"/500\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: isUploading || !selectedFile || !description.trim(),\n        style: {\n          background: isUploading ? '#6c757d' : '#007bff',\n          color: 'white',\n          border: 'none',\n          padding: '12px 24px',\n          borderRadius: '6px',\n          fontSize: '16px',\n          fontWeight: '500',\n          cursor: isUploading ? 'not-allowed' : 'pointer',\n          transition: 'all 0.3s ease',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        },\n        children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: '16px',\n              height: '16px',\n              border: '2px solid transparent',\n              borderTop: '2px solid white',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 15\n          }, this), \"\\u5206\\u6790\\u4E2D...\"]\n        }, void 0, true) : '🚀 开始分析'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleUpload, \"rg49S67Niskg9326cNUOfz9PB/M=\");\n_c = SimpleUpload;\nexport default SimpleUpload;\nvar _c;\n$RefreshReg$(_c, \"SimpleUpload\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SimpleUpload", "onUploadSuccess", "onUploadError", "_s", "selectedFile", "setSelectedFile", "description", "setDescription", "isUploading", "setIsUploading", "handleFileSelect", "event", "file", "target", "files", "handleSubmit", "preventDefault", "trim", "taskId", "Date", "now", "Math", "random", "toString", "substr", "task_id", "message", "filename", "name", "reset", "error", "console", "style", "background", "padding", "borderRadius", "boxShadow", "marginBottom", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "display", "fontWeight", "id", "type", "accept", "onChange", "width", "border", "marginTop", "color", "fontSize", "size", "toFixed", "value", "e", "placeholder", "rows", "resize", "fontFamily", "required", "textAlign", "length", "disabled", "cursor", "transition", "alignItems", "gap", "height", "borderTop", "animation", "jsx", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/components/SimpleUpload.js"], "sourcesContent": ["/**\n * 简化的上传组件\n */\nimport React, { useState } from 'react';\n\nconst SimpleUpload = ({ onUploadSuccess, onUploadError }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n\n  const handleFileSelect = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n\n  const handleSubmit = async (event) => {\n    event.preventDefault();\n    \n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n\n    setIsUploading(true);\n\n    try {\n      // 生成任务ID并启动实时分析\n      const taskId = 'task_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n\n      // 模拟上传成功，返回任务ID\n      onUploadSuccess({\n        task_id: taskId,\n        message: \"文件上传成功，开始实时分析\",\n        description: description.trim(),\n        filename: selectedFile.name\n      });\n\n      // 重置表单\n      setSelectedFile(null);\n      setDescription('');\n      event.target.reset();\n    } catch (error) {\n      console.error('Upload error:', error);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  return (\n    <div style={{ \n      background: 'white', \n      padding: '24px', \n      borderRadius: '12px', \n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n      marginBottom: '24px'\n    }}>\n      <h3>📁 上传UI界面截图</h3>\n      <form onSubmit={handleSubmit}>\n        <div style={{ marginBottom: '16px' }}>\n          <label htmlFor=\"file-input\" style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>\n            选择图片文件:\n          </label>\n          <input\n            id=\"file-input\"\n            type=\"file\"\n            accept=\"image/*\"\n            onChange={handleFileSelect}\n            style={{\n              width: '100%',\n              padding: '8px',\n              border: '1px solid #ddd',\n              borderRadius: '4px'\n            }}\n          />\n          {selectedFile && (\n            <p style={{ marginTop: '8px', color: '#666', fontSize: '14px' }}>\n              已选择: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)\n            </p>\n          )}\n        </div>\n\n        <div style={{ marginBottom: '16px' }}>\n          <label htmlFor=\"description\" style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>\n            界面功能描述:\n          </label>\n          <textarea\n            id=\"description\"\n            value={description}\n            onChange={(e) => setDescription(e.target.value)}\n            placeholder=\"请描述这个界面的主要功能，例如：这是一个登录页面，包含用户名密码输入框和登录按钮...\"\n            rows={4}\n            style={{\n              width: '100%',\n              padding: '12px',\n              border: '1px solid #ddd',\n              borderRadius: '4px',\n              resize: 'vertical',\n              fontFamily: 'inherit'\n            }}\n            required\n          />\n          <div style={{ textAlign: 'right', fontSize: '12px', color: '#666', marginTop: '4px' }}>\n            {description.length}/500\n          </div>\n        </div>\n\n        <button\n          type=\"submit\"\n          disabled={isUploading || !selectedFile || !description.trim()}\n          style={{\n            background: isUploading ? '#6c757d' : '#007bff',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            fontSize: '16px',\n            fontWeight: '500',\n            cursor: isUploading ? 'not-allowed' : 'pointer',\n            transition: 'all 0.3s ease',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          }}\n        >\n          {isUploading ? (\n            <>\n              <span style={{\n                width: '16px',\n                height: '16px',\n                border: '2px solid transparent',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }}></span>\n              分析中...\n            </>\n          ) : (\n            '🚀 开始分析'\n          )}\n        </button>\n      </form>\n\n      <style jsx>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default SimpleUpload;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,YAAY,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMe,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACRP,eAAe,CAACO,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOJ,KAAK,IAAK;IACpCA,KAAK,CAACK,cAAc,CAAC,CAAC;IAEtB,IAAI,CAACZ,YAAY,EAAE;MACjBF,aAAa,CAAC,SAAS,CAAC;MACxB;IACF;IAEA,IAAI,CAACI,WAAW,CAACW,IAAI,CAAC,CAAC,EAAE;MACvBf,aAAa,CAAC,WAAW,CAAC;MAC1B;IACF;IAEAO,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF;MACA,MAAMS,MAAM,GAAG,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;;MAEnF;MACAvB,eAAe,CAAC;QACdwB,OAAO,EAAEP,MAAM;QACfQ,OAAO,EAAE,eAAe;QACxBpB,WAAW,EAAEA,WAAW,CAACW,IAAI,CAAC,CAAC;QAC/BU,QAAQ,EAAEvB,YAAY,CAACwB;MACzB,CAAC,CAAC;;MAEF;MACAvB,eAAe,CAAC,IAAI,CAAC;MACrBE,cAAc,CAAC,EAAE,CAAC;MAClBI,KAAK,CAACE,MAAM,CAACgB,KAAK,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC5B,aAAa,CAAC4B,KAAK,CAACJ,OAAO,IAAI,UAAU,CAAC;IAC5C,CAAC,SAAS;MACRjB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,oBACEZ,OAAA;IAAKmC,KAAK,EAAE;MACVC,UAAU,EAAE,OAAO;MACnBC,OAAO,EAAE,MAAM;MACfC,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,4BAA4B;MACvCC,YAAY,EAAE;IAChB,CAAE;IAAAC,QAAA,gBACAzC,OAAA;MAAAyC,QAAA,EAAI;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpB7C,OAAA;MAAM8C,QAAQ,EAAE5B,YAAa;MAAAuB,QAAA,gBAC3BzC,OAAA;QAAKmC,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnCzC,OAAA;UAAO+C,OAAO,EAAC,YAAY;UAACZ,KAAK,EAAE;YAAEa,OAAO,EAAE,OAAO;YAAER,YAAY,EAAE,KAAK;YAAES,UAAU,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAEjG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA;UACEkD,EAAE,EAAC,YAAY;UACfC,IAAI,EAAC,MAAM;UACXC,MAAM,EAAC,SAAS;UAChBC,QAAQ,EAAExC,gBAAiB;UAC3BsB,KAAK,EAAE;YACLmB,KAAK,EAAE,MAAM;YACbjB,OAAO,EAAE,KAAK;YACdkB,MAAM,EAAE,gBAAgB;YACxBjB,YAAY,EAAE;UAChB;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACDtC,YAAY,iBACXP,OAAA;UAAGmC,KAAK,EAAE;YAAEqB,SAAS,EAAE,KAAK;YAAEC,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAjB,QAAA,GAAC,sBAC1D,EAAClC,YAAY,CAACwB,IAAI,EAAC,IAAE,EAAC,CAACxB,YAAY,CAACoD,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,MAC1E;QAAA;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN7C,OAAA;QAAKmC,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnCzC,OAAA;UAAO+C,OAAO,EAAC,aAAa;UAACZ,KAAK,EAAE;YAAEa,OAAO,EAAE,OAAO;YAAER,YAAY,EAAE,KAAK;YAAES,UAAU,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAElG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR7C,OAAA;UACEkD,EAAE,EAAC,aAAa;UAChBW,KAAK,EAAEpD,WAAY;UACnB4C,QAAQ,EAAGS,CAAC,IAAKpD,cAAc,CAACoD,CAAC,CAAC9C,MAAM,CAAC6C,KAAK,CAAE;UAChDE,WAAW,EAAC,qPAA6C;UACzDC,IAAI,EAAE,CAAE;UACR7B,KAAK,EAAE;YACLmB,KAAK,EAAE,MAAM;YACbjB,OAAO,EAAE,MAAM;YACfkB,MAAM,EAAE,gBAAgB;YACxBjB,YAAY,EAAE,KAAK;YACnB2B,MAAM,EAAE,UAAU;YAClBC,UAAU,EAAE;UACd,CAAE;UACFC,QAAQ;QAAA;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACF7C,OAAA;UAAKmC,KAAK,EAAE;YAAEiC,SAAS,EAAE,OAAO;YAAEV,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE,MAAM;YAAED,SAAS,EAAE;UAAM,CAAE;UAAAf,QAAA,GACnFhC,WAAW,CAAC4D,MAAM,EAAC,MACtB;QAAA;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7C,OAAA;QACEmD,IAAI,EAAC,QAAQ;QACbmB,QAAQ,EAAE3D,WAAW,IAAI,CAACJ,YAAY,IAAI,CAACE,WAAW,CAACW,IAAI,CAAC,CAAE;QAC9De,KAAK,EAAE;UACLC,UAAU,EAAEzB,WAAW,GAAG,SAAS,GAAG,SAAS;UAC/C8C,KAAK,EAAE,OAAO;UACdF,MAAM,EAAE,MAAM;UACdlB,OAAO,EAAE,WAAW;UACpBC,YAAY,EAAE,KAAK;UACnBoB,QAAQ,EAAE,MAAM;UAChBT,UAAU,EAAE,KAAK;UACjBsB,MAAM,EAAE5D,WAAW,GAAG,aAAa,GAAG,SAAS;UAC/C6D,UAAU,EAAE,eAAe;UAC3BxB,OAAO,EAAE,MAAM;UACfyB,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE;QACP,CAAE;QAAAjC,QAAA,EAED9B,WAAW,gBACVX,OAAA,CAAAE,SAAA;UAAAuC,QAAA,gBACEzC,OAAA;YAAMmC,KAAK,EAAE;cACXmB,KAAK,EAAE,MAAM;cACbqB,MAAM,EAAE,MAAM;cACdpB,MAAM,EAAE,uBAAuB;cAC/BqB,SAAS,EAAE,iBAAiB;cAC5BtC,YAAY,EAAE,KAAK;cACnBuC,SAAS,EAAE;YACb;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,yBAEZ;QAAA,eAAE,CAAC,GAEH;MACD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEP7C,OAAA;MAAO8E,GAAG;MAAArC,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACvC,EAAA,CAzJIH,YAAY;AAAA4E,EAAA,GAAZ5E,YAAY;AA2JlB,eAAeA,YAAY;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}