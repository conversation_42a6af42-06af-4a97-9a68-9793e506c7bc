/**
 * 简化的上传组件
 */
import React, { useState } from 'react';

const SimpleUpload = ({ onUploadSuccess, onUploadError }) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [description, setDescription] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [additionalInfo, setAdditionalInfo] = useState({
    projectName: '',
    testType: 'functional',
    priority: 'medium',
    expectedElements: '',
    specialRequirements: ''
  });

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!selectedFile) {
      onUploadError('请选择图片文件');
      return;
    }

    if (!description.trim()) {
      onUploadError('请输入界面功能描述');
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('image_file', selectedFile);
      formData.append('description', description.trim());
      formData.append('project_name', additionalInfo.projectName);
      formData.append('test_type', additionalInfo.testType);
      formData.append('priority', additionalInfo.priority);
      formData.append('expected_elements', additionalInfo.expectedElements);
      formData.append('special_requirements', additionalInfo.specialRequirements);

      const response = await fetch('http://localhost:8001/api/v1/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      onUploadSuccess(result);

      // 重置表单
      setSelectedFile(null);
      setDescription('');
      event.target.reset();
    } catch (error) {
      console.error('Upload error:', error);
      onUploadError(error.message || '上传失败，请重试');
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileManagementDemo = async () => {
    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('description', '文件管理界面功能分析 - 基于提供的截图进行专项分析');

      const response = await fetch('http://localhost:8001/api/v1/analyze/file-management', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      onUploadSuccess(result);

    } catch (error) {
      console.error('File management demo error:', error);
      onUploadError(error.message || '文件管理界面分析失败，请重试');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div style={{ 
      background: 'white', 
      padding: '24px', 
      borderRadius: '12px', 
      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',
      marginBottom: '24px'
    }}>
      <h3>📁 上传UI界面截图</h3>
      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: '24px' }}>
          <label style={{ display: 'block', marginBottom: '12px', fontWeight: '600', color: '#333', fontSize: '16px' }}>
            📁 上传UI界面截图
          </label>
          <div
            className="file-upload-area"
            onClick={() => document.getElementById('file-input').click()}
            style={{
              height: '240px', // 调高3倍（原来约80px）
              border: '2px dashed #667eea',
              borderRadius: '12px',
              background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              position: 'relative',
              overflow: 'hidden'
            }}
            onDragOver={(e) => {
              e.preventDefault();
              e.currentTarget.style.borderColor = '#4f46e5';
              e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';
            }}
            onDragLeave={(e) => {
              e.currentTarget.style.borderColor = '#667eea';
              e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';
            }}
            onDrop={(e) => {
              e.preventDefault();
              const files = e.dataTransfer.files;
              if (files.length > 0) {
                setSelectedFile(files[0]);
              }
              e.currentTarget.style.borderColor = '#667eea';
              e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';
            }}
          >
            <input
              id="file-input"
              type="file"
              accept="image/*"
              onChange={handleFileSelect}
              style={{ display: 'none' }}
            />

            {selectedFile ? (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>✅</div>
                <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>文件已选择</h3>
                <p style={{ margin: '0 0 8px 0', color: '#666', fontSize: '14px', fontWeight: '500' }}>
                  {selectedFile.name}
                </p>
                <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>
                  大小: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedFile(null);
                    document.getElementById('file-input').value = '';
                  }}
                  style={{
                    marginTop: '12px',
                    background: 'rgba(239, 68, 68, 0.1)',
                    color: '#ef4444',
                    border: '1px solid rgba(239, 68, 68, 0.3)',
                    padding: '6px 12px',
                    borderRadius: '6px',
                    fontSize: '12px',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseOver={(e) => {
                    e.target.style.background = 'rgba(239, 68, 68, 0.2)';
                  }}
                  onMouseOut={(e) => {
                    e.target.style.background = 'rgba(239, 68, 68, 0.1)';
                  }}
                >
                  重新选择
                </button>
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>📤</div>
                <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>点击或拖拽上传图片</h3>
                <p style={{ margin: '0 0 4px 0', color: '#666', fontSize: '14px' }}>
                  支持 PNG、JPG、JPEG、GIF 格式
                </p>
                <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>
                  文件大小不超过 10MB
                </p>
                <div style={{
                  marginTop: '16px',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  padding: '8px 20px',
                  borderRadius: '20px',
                  fontSize: '14px',
                  fontWeight: '500',
                  display: 'inline-block'
                }}>
                  选择文件
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 界面功能描述 */}
        <div style={{ marginBottom: '20px' }}>
          <label htmlFor="description" style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333', fontSize: '16px' }}>
            🎯 界面功能描述
          </label>
          <textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="请详细描述这个界面的主要功能，例如：这是一个登录页面，包含用户名密码输入框和登录按钮..."
            rows={4}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e5e7eb',
              borderRadius: '8px',
              resize: 'vertical',
              fontFamily: 'inherit',
              fontSize: '14px',
              lineHeight: '1.5',
              transition: 'border-color 0.2s ease',
              background: '#fafafa'
            }}
            onFocus={(e) => {
              e.target.style.borderColor = '#667eea';
              e.target.style.background = '#ffffff';
            }}
            onBlur={(e) => {
              e.target.style.borderColor = '#e5e7eb';
              e.target.style.background = '#fafafa';
            }}
            required
          />
          <div style={{ textAlign: 'right', fontSize: '12px', color: '#666', marginTop: '4px' }}>
            {description.length}/500
          </div>
        </div>

        {/* 项目信息 */}
        <div style={{ marginBottom: '20px' }}>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333', fontSize: '16px' }}>
            📋 项目信息
          </label>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>
            <div>
              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', color: '#555' }}>
                项目名称
              </label>
              <input
                type="text"
                value={additionalInfo.projectName}
                onChange={(e) => setAdditionalInfo(prev => ({ ...prev, projectName: e.target.value }))}
                placeholder="输入项目名称"
                style={{
                  width: '100%',
                  padding: '10px 12px',
                  border: '2px solid #e5e7eb',
                  borderRadius: '6px',
                  fontSize: '14px',
                  transition: 'border-color 0.2s ease',
                  background: '#fafafa'
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = '#667eea';
                  e.target.style.background = '#ffffff';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#e5e7eb';
                  e.target.style.background = '#fafafa';
                }}
              />
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', color: '#555' }}>
                测试类型
              </label>
              <select
                value={additionalInfo.testType}
                onChange={(e) => setAdditionalInfo(prev => ({ ...prev, testType: e.target.value }))}
                style={{
                  width: '100%',
                  padding: '10px 12px',
                  border: '2px solid #e5e7eb',
                  borderRadius: '6px',
                  fontSize: '14px',
                  background: '#fafafa',
                  cursor: 'pointer'
                }}
              >
                <option value="functional">功能测试</option>
                <option value="ui">UI测试</option>
                <option value="integration">集成测试</option>
                <option value="regression">回归测试</option>
              </select>
            </div>
          </div>
        </div>

        {/* 测试优先级和预期元素 */}
        <div style={{ marginBottom: '20px' }}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>
            <div>
              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', color: '#555' }}>
                优先级
              </label>
              <select
                value={additionalInfo.priority}
                onChange={(e) => setAdditionalInfo(prev => ({ ...prev, priority: e.target.value }))}
                style={{
                  width: '100%',
                  padding: '10px 12px',
                  border: '2px solid #e5e7eb',
                  borderRadius: '6px',
                  fontSize: '14px',
                  background: '#fafafa',
                  cursor: 'pointer'
                }}
              >
                <option value="low">低</option>
                <option value="medium">中</option>
                <option value="high">高</option>
                <option value="critical">紧急</option>
              </select>
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', color: '#555' }}>
                预期元素数量
              </label>
              <input
                type="text"
                value={additionalInfo.expectedElements}
                onChange={(e) => setAdditionalInfo(prev => ({ ...prev, expectedElements: e.target.value }))}
                placeholder="例如：5-10个按钮，2个输入框"
                style={{
                  width: '100%',
                  padding: '10px 12px',
                  border: '2px solid #e5e7eb',
                  borderRadius: '6px',
                  fontSize: '14px',
                  transition: 'border-color 0.2s ease',
                  background: '#fafafa'
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = '#667eea';
                  e.target.style.background = '#ffffff';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#e5e7eb';
                  e.target.style.background = '#fafafa';
                }}
              />
            </div>
          </div>
        </div>

        {/* 特殊要求 */}
        <div style={{ marginBottom: '24px' }}>
          <label style={{ display: 'block', marginBottom: '8px', fontSize: '14px', color: '#555' }}>
            特殊要求或注意事项
          </label>
          <textarea
            value={additionalInfo.specialRequirements}
            onChange={(e) => setAdditionalInfo(prev => ({ ...prev, specialRequirements: e.target.value }))}
            placeholder="例如：需要特别关注响应式设计、无障碍访问、特定浏览器兼容性等..."
            rows={3}
            style={{
              width: '100%',
              padding: '12px',
              border: '2px solid #e5e7eb',
              borderRadius: '8px',
              resize: 'vertical',
              fontFamily: 'inherit',
              fontSize: '14px',
              lineHeight: '1.5',
              transition: 'border-color 0.2s ease',
              background: '#fafafa'
            }}
            onFocus={(e) => {
              e.target.style.borderColor = '#667eea';
              e.target.style.background = '#ffffff';
            }}
            onBlur={(e) => {
              e.target.style.borderColor = '#e5e7eb';
              e.target.style.background = '#fafafa';
            }}
          />
        </div>

        <button
          type="submit"
          disabled={isUploading || !selectedFile || !description.trim()}
          style={{
            background: isUploading ? '#6c757d' : '#007bff',
            color: 'white',
            border: 'none',
            padding: '12px 24px',
            borderRadius: '6px',
            fontSize: '16px',
            fontWeight: '500',
            cursor: isUploading ? 'not-allowed' : 'pointer',
            transition: 'all 0.3s ease',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          {isUploading ? (
            <>
              <span style={{
                width: '16px',
                height: '16px',
                border: '2px solid transparent',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }}></span>
              分析中...
            </>
          ) : (
            '🚀 开始分析'
          )}
        </button>
      </form>

      {/* 分隔线 */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        margin: '24px 0',
        color: '#666',
        fontSize: '14px'
      }}>
        <div style={{ flex: 1, height: '1px', background: '#e9ecef' }}></div>
        <span style={{ padding: '0 16px' }}>或者</span>
        <div style={{ flex: 1, height: '1px', background: '#e9ecef' }}></div>
      </div>

      {/* 文件管理界面演示按钮 */}
      <div style={{ textAlign: 'center' }}>
        <h4 style={{ margin: '0 0 12px 0', color: '#333', fontSize: '16px' }}>
          📁 专项分析演示
        </h4>
        <p style={{ margin: '0 0 16px 0', color: '#666', fontSize: '14px' }}>
          基于您提供的文件管理界面截图，生成专门的UI自动化测试脚本
        </p>
        <button
          type="button"
          onClick={handleFileManagementDemo}
          disabled={isUploading}
          style={{
            background: isUploading ? '#6c757d' : '#28a745',
            color: 'white',
            border: 'none',
            padding: '12px 32px',
            borderRadius: '6px',
            fontSize: '16px',
            fontWeight: '500',
            cursor: isUploading ? 'not-allowed' : 'pointer',
            transition: 'all 0.3s ease',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            margin: '0 auto'
          }}
        >
          {isUploading ? (
            <>
              <span style={{
                width: '16px',
                height: '16px',
                border: '2px solid transparent',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }}></span>
              分析中...
            </>
          ) : (
            <>
              📊 分析文件管理界面
            </>
          )}
        </button>
      </div>

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default SimpleUpload;
