/**
 * 简化的上传组件
 */
import React, { useState } from 'react';

const SimpleUpload = ({ onUploadSuccess, onUploadError }) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [description, setDescription] = useState('');
  const [isUploading, setIsUploading] = useState(false);

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!selectedFile) {
      onUploadError('请选择图片文件');
      return;
    }

    if (!description.trim()) {
      onUploadError('请输入界面功能描述');
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('image_file', selectedFile);
      formData.append('description', description.trim());

      const response = await fetch('http://localhost:8001/api/v1/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      onUploadSuccess(result);

      // 重置表单
      setSelectedFile(null);
      setDescription('');
      event.target.reset();
    } catch (error) {
      console.error('Upload error:', error);
      onUploadError(error.message || '上传失败，请重试');
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileManagementDemo = async () => {
    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('description', '文件管理界面功能分析 - 基于提供的截图进行专项分析');

      const response = await fetch('http://localhost:8001/api/v1/analyze/file-management', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      onUploadSuccess(result);

    } catch (error) {
      console.error('File management demo error:', error);
      onUploadError(error.message || '文件管理界面分析失败，请重试');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div style={{ 
      background: 'white', 
      padding: '24px', 
      borderRadius: '12px', 
      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',
      marginBottom: '24px'
    }}>
      <h3>📁 上传UI界面截图</h3>
      <form onSubmit={handleSubmit}>
        <div style={{ marginBottom: '16px' }}>
          <label htmlFor="file-input" style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
            选择图片文件:
          </label>
          <input
            id="file-input"
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            style={{
              width: '100%',
              padding: '8px',
              border: '1px solid #ddd',
              borderRadius: '4px'
            }}
          />
          {selectedFile && (
            <p style={{ marginTop: '8px', color: '#666', fontSize: '14px' }}>
              已选择: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
            </p>
          )}
        </div>

        <div style={{ marginBottom: '16px' }}>
          <label htmlFor="description" style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
            界面功能描述:
          </label>
          <textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="请描述这个界面的主要功能，例如：这是一个登录页面，包含用户名密码输入框和登录按钮..."
            rows={4}
            style={{
              width: '100%',
              padding: '12px',
              border: '1px solid #ddd',
              borderRadius: '4px',
              resize: 'vertical',
              fontFamily: 'inherit'
            }}
            required
          />
          <div style={{ textAlign: 'right', fontSize: '12px', color: '#666', marginTop: '4px' }}>
            {description.length}/500
          </div>
        </div>

        <button
          type="submit"
          disabled={isUploading || !selectedFile || !description.trim()}
          style={{
            background: isUploading ? '#6c757d' : '#007bff',
            color: 'white',
            border: 'none',
            padding: '12px 24px',
            borderRadius: '6px',
            fontSize: '16px',
            fontWeight: '500',
            cursor: isUploading ? 'not-allowed' : 'pointer',
            transition: 'all 0.3s ease',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          {isUploading ? (
            <>
              <span style={{
                width: '16px',
                height: '16px',
                border: '2px solid transparent',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }}></span>
              分析中...
            </>
          ) : (
            '🚀 开始分析'
          )}
        </button>
      </form>

      {/* 分隔线 */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        margin: '24px 0',
        color: '#666',
        fontSize: '14px'
      }}>
        <div style={{ flex: 1, height: '1px', background: '#e9ecef' }}></div>
        <span style={{ padding: '0 16px' }}>或者</span>
        <div style={{ flex: 1, height: '1px', background: '#e9ecef' }}></div>
      </div>

      {/* 文件管理界面演示按钮 */}
      <div style={{ textAlign: 'center' }}>
        <h4 style={{ margin: '0 0 12px 0', color: '#333', fontSize: '16px' }}>
          📁 专项分析演示
        </h4>
        <p style={{ margin: '0 0 16px 0', color: '#666', fontSize: '14px' }}>
          基于您提供的文件管理界面截图，生成专门的UI自动化测试脚本
        </p>
        <button
          type="button"
          onClick={handleFileManagementDemo}
          disabled={isUploading}
          style={{
            background: isUploading ? '#6c757d' : '#28a745',
            color: 'white',
            border: 'none',
            padding: '12px 32px',
            borderRadius: '6px',
            fontSize: '16px',
            fontWeight: '500',
            cursor: isUploading ? 'not-allowed' : 'pointer',
            transition: 'all 0.3s ease',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            margin: '0 auto'
          }}
        >
          {isUploading ? (
            <>
              <span style={{
                width: '16px',
                height: '16px',
                border: '2px solid transparent',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }}></span>
              分析中...
            </>
          ) : (
            <>
              📊 分析文件管理界面
            </>
          )}
        </button>
      </div>

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default SimpleUpload;
