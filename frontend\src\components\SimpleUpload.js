
import { useState, useEffect, useRef } from 'react';

const SimpleUpload = ({ onUploadSuccess, onUploadError }) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [description, setDescription] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [showAnalysis, setShowAnalysis] = useState(true); // 默认显示进度框以便查看效果
  const [analysisProgress, setAnalysisProgress] = useState({
    overall: 0,
    currentStep: '准备开始分析...',
    steps: [
      { name: '初始化', status: 'pending', progress: 0 },
      { name: '元素分析和智能识别', status: 'pending', progress: 0 },
      { name: '生成自动化测试脚本', status: 'pending', progress: 0 }
    ]
  });
  const [analysisLogs, setAnalysisLogs] = useState([]);
  const logContainerRef = useRef(null);

  // 自动滚动到最新日志
  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [analysisLogs]);

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      console.log('📁 文件选择:', {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: new Date(file.lastModified).toLocaleString()
      });
      setSelectedFile(file);
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    console.log('🚀 开始提交表单...');
    console.log('  - 文件:', selectedFile?.name);
    console.log('  - 描述:', description.trim());

    if (!selectedFile) {
      console.log('  ❌ 验证失败: 未选择文件');
      onUploadError('请选择图片文件');
      return;
    }

    if (!description.trim()) {
      console.log('  ❌ 验证失败: 描述为空');
      onUploadError('请输入界面功能描述');
      return;
    }

    console.log('  ✅ 表单验证通过');
    setIsUploading(true);
    setShowAnalysis(true);
    console.log('  📊 显示分析界面');

    try {
      const formData = new FormData();
      formData.append('image_file', selectedFile);
      formData.append('description', description.trim());

      console.log('  📦 构建FormData完成');
      console.log('  🎯 开始模拟分析进度...');

      // 开始模拟分析进度
      simulateAnalysisProgress();

      console.log('  📡 发送上传请求到后端...');
      const response = await fetch('http://localhost:8003/api/v1/upload', {
        method: 'POST',
        body: formData,
        mode: 'cors',
      });

      console.log('  📨 收到后端响应:', response.status, response.statusText);

      if (!response.ok) {
        const errorData = await response.json();
        console.log('  ❌ 后端返回错误:', errorData);
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('  ✅ 上传成功:', result);
      onUploadSuccess(result);

    } catch (error) {
      console.error('  ❌ 上传失败:', error);
      setShowAnalysis(false);
      onUploadError(error.message || '上传失败，请重试');
    } finally {
      setIsUploading(false);
      console.log('  🏁 上传流程结束');
    }
  };

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
    setAnalysisLogs(prev => [...prev, {
      id: Date.now() + Math.random(),
      timestamp,
      message,
      type
    }]);
  };

  const simulateAnalysisProgress = () => {
    console.log('🎬 开始模拟分析进度...');
    let step = 0;
    const steps = ['初始化', '元素分析和智能识别', '生成自动化测试脚本'];

    // 清空之前的日志
    setAnalysisLogs([]);
    console.log('  🧹 清空之前的日志');

    // 详细的步骤日志
    const stepLogs = {
      0: [ // 初始化
        '🚀 开始分析流程...',
        '📋 检查上传文件格式和大小',
        '🔧 初始化AI分析引擎',
        '📊 加载UI元素识别模型',
        '✅ 初始化完成'
      ],
      1: [ // 元素分析和智能识别
        '🔍 开始图像预处理...',
        '🎯 检测UI界面元素',
        '📝 识别文本内容和标签',
        '🔘 分析按钮和交互元素',
        '📋 识别输入框和表单元素',
        '🎨 分析布局和样式信息',
        '🧠 AI智能分类UI组件',
        '✅ 元素分析完成'
      ],
      2: [ // 生成自动化测试脚本
        '📝 开始生成测试脚本...',
        '🔧 构建MidScene.js测试框架',
        '📋 生成元素定位策略',
        '⚡ 创建交互操作脚本',
        '🧪 添加断言和验证逻辑',
        '📄 格式化YAML输出',
        '✅ 测试脚本生成完成'
      ]
    };

    const updateProgress = () => {
      if (step < steps.length) {
        console.log(`  📋 开始步骤 ${step + 1}: ${steps[step]}`);
        // 添加步骤开始日志
        addLog(`开始执行: ${steps[step]}`, 'step');

        // 更新进度状态
        setAnalysisProgress(prev => ({
          ...prev,
          overall: Math.round((step + 1) / steps.length * 100),
          currentStep: `正在执行: ${steps[step]}`,
          steps: prev.steps.map((s, index) => ({
            ...s,
            status: index < step ? 'completed' : index === step ? 'processing' : 'pending',
            progress: index < step ? 100 : index === step ? 0 : 0
          }))
        }));
        console.log(`    📊 整体进度: ${Math.round((step + 1) / steps.length * 100)}%`);

        // 模拟步骤内的详细日志
        const currentStepLogs = stepLogs[step];
        let logIndex = 0;

        const addStepLogs = () => {
          if (logIndex < currentStepLogs.length) {
            addLog(currentStepLogs[logIndex], 'info');

            // 更新当前步骤进度
            const progress = Math.round(((logIndex + 1) / currentStepLogs.length) * 100);
            console.log(`      🔄 步骤进度: ${progress}% (${logIndex + 1}/${currentStepLogs.length})`);
            setAnalysisProgress(prev => ({
              ...prev,
              steps: prev.steps.map((s, index) =>
                index === step ? { ...s, progress } : s
              )
            }));

            logIndex++;
            setTimeout(addStepLogs, 800 + Math.random() * 1200);
          } else {
            // 当前步骤完成
            console.log(`    ✅ 步骤 ${step + 1} 完成: ${steps[step]}`);
            setAnalysisProgress(prev => ({
              ...prev,
              steps: prev.steps.map((s, index) =>
                index === step ? { ...s, status: 'completed', progress: 100 } : s
              )
            }));

            step++;
            setTimeout(updateProgress, 1000);
          }
        };

        setTimeout(addStepLogs, 500);

      } else {
        // 所有步骤完成
        console.log('  🎉 所有分析步骤已完成！');
        addLog('🎉 所有分析步骤已完成！', 'success');
        addLog('📄 测试脚本已生成，可以下载使用', 'success');

        setAnalysisProgress(prev => ({
          ...prev,
          overall: 100,
          currentStep: '分析完成',
          steps: prev.steps.map(s => ({ ...s, status: 'completed', progress: 100 }))
        }));
        console.log('  📊 最终进度: 100%');
      }
    };

    // 开始分析
    console.log('  🔥 启动UI自动化测试脚本分析...');
    addLog('🔥 启动UI自动化测试脚本分析...', 'start');
    setTimeout(updateProgress, 1000);
  };

  return (
    <div style={{ 
      display: 'flex', 
      gap: '24px', 
      height: '100vh', 
      padding: '24px',
      width: '100%',
      boxSizing: 'border-box',
      overflow: 'hidden'
    }}>
      {/* Left upload area - no duplicate header */}
      <div style={{ 
        flex: showAnalysis ? '0 0 400px' : '1',
        background: 'white',
        padding: '24px',
        borderRadius: '12px',
        boxShadow: '0 2px 12px rgba(0,0,0,0.1)',
        transition: 'all 0.3s ease'
      }}>
        <form onSubmit={handleSubmit}>
          <div style={{ marginBottom: '24px' }}>
            <label style={{ display: 'block', marginBottom: '12px', fontWeight: '600', color: '#333', fontSize: '16px' }}>
              📁 上传UI界面截图
            </label>
            <div
              className="file-upload-area"
              onClick={() => document.getElementById('file-input').click()}
              style={{
                height: '240px',
                border: '2px dashed #667eea',
                borderRadius: '12px',
                background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                position: 'relative',
                overflow: 'hidden'
              }}
              onDragOver={(e) => {
                e.preventDefault();
                e.currentTarget.style.borderColor = '#4f46e5';
                e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';
              }}
              onDragLeave={(e) => {
                e.currentTarget.style.borderColor = '#667eea';
                e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';
              }}
              onDrop={(e) => {
                e.preventDefault();
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                  setSelectedFile(files[0]);
                }
                e.currentTarget.style.borderColor = '#667eea';
                e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';
              }}
            >
              <input
                id="file-input"
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                style={{ display: 'none' }}
              />

              {selectedFile ? (
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>✅</div>
                  <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>文件已选择</h3>
                  <p style={{ margin: '0 0 8px 0', color: '#666', fontSize: '14px', fontWeight: '500' }}>
                    {selectedFile.name}
                  </p>
                  <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>
                    大小: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedFile(null);
                      document.getElementById('file-input').value = '';
                    }}
                    style={{
                      marginTop: '12px',
                      background: 'rgba(239, 68, 68, 0.1)',
                      color: '#ef4444',
                      border: '1px solid rgba(239, 68, 68, 0.3)',
                      padding: '6px 12px',
                      borderRadius: '6px',
                      fontSize: '12px',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease'
                    }}
                  >
                    重新选择
                  </button>
                </div>
              ) : (
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>📤</div>
                  <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>点击或拖拽上传图片</h3>
                  <p style={{ margin: '0 0 4px 0', color: '#666', fontSize: '14px' }}>
                    支持 PNG、JPG、JPEG、GIF 格式
                  </p>
                  <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>
                    文件大小不超过 10MB
                  </p>
                  <div style={{
                    marginTop: '16px',
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    color: 'white',
                    padding: '8px 20px',
                    borderRadius: '20px',
                    fontSize: '14px',
                    fontWeight: '500',
                    display: 'inline-block'
                  }}>
                    选择文件
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 界面功能描述 */}
          <div style={{ marginBottom: '20px' }}>
            <label htmlFor="description" style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333', fontSize: '16px' }}>
              🎯 界面功能描述
            </label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="请详细描述这个界面的主要功能，例如：这是一个登录页面，包含用户名密码输入框和登录按钮..."
              rows={4}
              style={{
                width: '100%',
                padding: '12px',
                border: '2px solid #e5e7eb',
                borderRadius: '8px',
                resize: 'vertical',
                fontFamily: 'inherit',
                fontSize: '14px',
                lineHeight: '1.5',
                transition: 'border-color 0.2s ease',
                background: '#fafafa'
              }}
              onFocus={(e) => {
                e.target.style.borderColor = '#667eea';
                e.target.style.background = '#ffffff';
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#e5e7eb';
                e.target.style.background = '#fafafa';
              }}
              required
            />
            <div style={{ textAlign: 'right', fontSize: '12px', color: '#666', marginTop: '4px' }}>
              {description.length}/500
            </div>
          </div>

          <button
            type="submit"
            disabled={isUploading || !selectedFile || !description.trim()}
            style={{
              background: isUploading ? '#6c757d' : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '8px',
              fontSize: '16px',
              fontWeight: '500',
              cursor: isUploading ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              width: '100%',
              justifyContent: 'center',
              marginBottom: '12px'
            }}
          >
            {isUploading ? (
              <>
                <span style={{
                  width: '16px',
                  height: '16px',
                  border: '2px solid transparent',
                  borderTop: '2px solid white',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }}></span>
                开始分析中...
              </>
            ) : (
              '🚀 开始分析'
            )}
          </button>

          {/* 测试按钮 - 用于演示进度框效果 */}
          <button
            type="button"
            onClick={() => {
              setShowAnalysis(true);
              simulateAnalysisProgress();
            }}
            style={{
              background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
              color: 'white',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              width: '100%',
              justifyContent: 'center'
            }}
          >
            🧪 测试进度框效果
          </button>
        </form>
      </div>

      {/* 右侧实时分析界面 - 按照截图样式设计 */}
      {showAnalysis && (
        <div style={{
          flex: '1',
          background: '#f8f9fa',
          padding: '20px',
          borderRadius: '12px',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          minWidth: '400px',
          maxWidth: '500px',
          border: '1px solid #e9ecef'
        }}>
          {/* 标题区域 */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '20px',
            paddingBottom: '12px',
            borderBottom: '1px solid #dee2e6'
          }}>
            <h3 style={{
              margin: 0,
              color: '#495057',
              fontSize: '16px',
              fontWeight: '600',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              实时分析进度
            </h3>
            <div style={{
              background: '#e9ecef',
              color: '#6c757d',
              padding: '4px 12px',
              borderRadius: '12px',
              fontSize: '12px',
              fontWeight: '500'
            }}>
              {analysisProgress.overall}%
            </div>
          </div>

          {/* 当前分析状态 */}
          <div style={{
            background: 'white',
            border: '1px solid #dee2e6',
            borderRadius: '8px',
            padding: '16px',
            marginBottom: '16px'
          }}>
            <div style={{ fontSize: '12px', color: '#6c757d', marginBottom: '8px' }}>
              分析状态
            </div>
            <div style={{ fontSize: '14px', fontWeight: '500', color: '#495057' }}>
              {analysisProgress.currentStep}
            </div>
          </div>

          {/* 分析步骤列表 */}
          <div style={{ marginBottom: '16px' }}>
            {analysisProgress.steps.map((step, index) => (
              <div key={index} style={{
                background: 'white',
                border: '1px solid #dee2e6',
                borderRadius: '8px',
                padding: '12px 16px',
                marginBottom: '8px',
                display: 'flex',
                alignItems: 'center',
                gap: '12px'
              }}>
                {/* 步骤图标 */}
                <div style={{
                  width: '24px',
                  height: '24px',
                  borderRadius: '50%',
                  background: step.status === 'completed' ? '#28a745' :
                             step.status === 'processing' ? '#007bff' : '#e9ecef',
                  color: step.status === 'completed' || step.status === 'processing' ? 'white' : '#6c757d',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '12px',
                  fontWeight: '600'
                }}>
                  {step.status === 'completed' ? '✓' :
                   step.status === 'processing' ? '⟳' : index + 1}
                </div>

                {/* 步骤内容和进度条 */}
                <div style={{ flex: 1 }}>
                  <div style={{
                    fontSize: '14px',
                    fontWeight: '500',
                    marginBottom: '4px',
                    color: step.status === 'processing' ? '#007bff' : '#495057'
                  }}>
                    {step.name}
                  </div>
                  {/* 进度条 */}
                  <div style={{
                    width: '100%',
                    height: '4px',
                    background: '#e9ecef',
                    borderRadius: '2px',
                    overflow: 'hidden'
                  }}>
                    <div style={{
                      width: `${step.progress}%`,
                      height: '100%',
                      background: step.status === 'completed' ? '#28a745' :
                                 step.status === 'processing' ? '#007bff' : '#e9ecef',
                      transition: 'width 0.3s ease'
                    }}></div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 实时日志区域 */}
          <div style={{
            background: 'white',
            border: '1px solid #dee2e6',
            borderRadius: '8px',
            padding: '16px',
            marginBottom: '16px',
            flex: '1',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <div style={{
              fontSize: '12px',
              color: '#6c757d',
              marginBottom: '12px',
              fontWeight: '600',
              display: 'flex',
              alignItems: 'center',
              gap: '6px'
            }}>
              📋 实时分析日志
            </div>
            <div
              ref={logContainerRef}
              style={{
                flex: '1',
                maxHeight: '200px',
                overflowY: 'auto',
                fontSize: '12px',
                lineHeight: '1.4',
                fontFamily: 'Monaco, Consolas, "Courier New", monospace'
              }}
            >
              {analysisLogs.length === 0 ? (
                <div style={{ color: '#6c757d', fontStyle: 'italic' }}>
                  等待分析开始...
                </div>
              ) : (
                analysisLogs.map((log) => (
                  <div key={log.id} style={{
                    marginBottom: '4px',
                    padding: '4px 8px',
                    borderRadius: '4px',
                    background: log.type === 'start' ? '#e3f2fd' :
                               log.type === 'step' ? '#f3e5f5' :
                               log.type === 'success' ? '#e8f5e8' :
                               log.type === 'error' ? '#ffebee' : '#f8f9fa',
                    color: log.type === 'start' ? '#1976d2' :
                           log.type === 'step' ? '#7b1fa2' :
                           log.type === 'success' ? '#388e3c' :
                           log.type === 'error' ? '#d32f2f' : '#495057'
                  }}>
                    <span style={{ color: '#6c757d', marginRight: '8px' }}>
                      [{log.timestamp}]
                    </span>
                    {log.message}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 底部操作按钮 */}
          <div style={{
            marginTop: 'auto',
            paddingTop: '16px',
            borderTop: '1px solid #dee2e6'
          }}>
            <button
              onClick={() => setShowAnalysis(false)}
              style={{
                width: '100%',
                background: '#6c757d',
                color: 'white',
                border: 'none',
                padding: '10px 16px',
                borderRadius: '6px',
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'background-color 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px'
              }}
              onMouseOver={(e) => e.target.style.background = '#5a6268'}
              onMouseOut={(e) => e.target.style.background = '#6c757d'}
            >
              <span>→</span>
              继续
            </button>
          </div>
        </div>
      )}

      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default SimpleUpload;



