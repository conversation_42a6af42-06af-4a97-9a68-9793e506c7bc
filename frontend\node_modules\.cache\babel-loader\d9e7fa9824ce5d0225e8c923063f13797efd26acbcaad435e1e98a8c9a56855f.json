{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\components\\\\SimpleUpload.js\",\n  _s = $RefreshSig$();\n/**\n * 简化的上传组件\n */\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SimpleUpload = ({\n  onUploadSuccess,\n  onUploadError\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const [showAnalysis, setShowAnalysis] = useState(false);\n  const [analysisProgress, setAnalysisProgress] = useState({\n    overall: 0,\n    currentStep: '准备开始分析...',\n    steps: [{\n      name: '初始化',\n      status: 'pending',\n      progress: 0\n    }, {\n      name: '元素分析和智能识别',\n      status: 'pending',\n      progress: 0\n    }, {\n      name: '生成自动化测试脚本',\n      status: 'pending',\n      progress: 0\n    }]\n  });\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n    setIsUploading(true);\n    setShowAnalysis(true);\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n\n      // 开始模拟分析进度\n      simulateAnalysisProgress();\n      const response = await fetch('http://localhost:8001/api/v1/upload', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n      const result = await response.json();\n      onUploadSuccess(result);\n    } catch (error) {\n      console.error('Upload error:', error);\n      setShowAnalysis(false);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  const simulateAnalysisProgress = () => {\n    let step = 0;\n    const steps = ['初始化', '元素分析和智能识别', '生成自动化测试脚本'];\n    const updateProgress = () => {\n      if (step < steps.length) {\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: Math.round((step + 1) / steps.length * 100),\n          currentStep: `正在执行: ${steps[step]}`,\n          steps: prev.steps.map((s, index) => ({\n            ...s,\n            status: index < step ? 'completed' : index === step ? 'processing' : 'pending',\n            progress: index < step ? 100 : index === step ? Math.random() * 80 + 20 : 0\n          }))\n        }));\n        step++;\n        setTimeout(updateProgress, 2000 + Math.random() * 3000);\n      } else {\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: 100,\n          currentStep: '分析完成',\n          steps: prev.steps.map(s => ({\n            ...s,\n            status: 'completed',\n            progress: 100\n          }))\n        }));\n      }\n    };\n    setTimeout(updateProgress, 1000);\n  };\n  const handleFileManagementDemo = async () => {\n    setIsUploading(true);\n    try {\n      const formData = new FormData();\n      formData.append('description', '文件管理界面功能分析 - 基于提供的截图进行专项分析');\n      const response = await fetch('http://localhost:8001/api/v1/analyze/file-management', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n      const result = await response.json();\n      onUploadSuccess(result);\n    } catch (error) {\n      console.error('File management demo error:', error);\n      onUploadError(error.message || '文件管理界面分析失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'white',\n      padding: '24px',\n      borderRadius: '12px',\n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n      marginBottom: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: 'block',\n            marginBottom: '12px',\n            fontWeight: '600',\n            color: '#333',\n            fontSize: '16px'\n          },\n          children: \"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"file-upload-area\",\n          onClick: () => document.getElementById('file-input').click(),\n          style: {\n            height: '240px',\n            // 调高3倍（原来约80px）\n            border: '2px dashed #667eea',\n            borderRadius: '12px',\n            background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            transition: 'all 0.3s ease',\n            position: 'relative',\n            overflow: 'hidden'\n          },\n          onDragOver: e => {\n            e.preventDefault();\n            e.currentTarget.style.borderColor = '#4f46e5';\n            e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';\n          },\n          onDragLeave: e => {\n            e.currentTarget.style.borderColor = '#667eea';\n            e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';\n          },\n          onDrop: e => {\n            e.preventDefault();\n            const files = e.dataTransfer.files;\n            if (files.length > 0) {\n              setSelectedFile(files[0]);\n            }\n            e.currentTarget.style.borderColor = '#667eea';\n            e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"file-input\",\n            type: \"file\",\n            accept: \"image/*\",\n            onChange: handleFileSelect,\n            style: {\n              display: 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), selectedFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '16px',\n                color: '#667eea'\n              },\n              children: \"\\u2705\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: '0 0 8px 0',\n                color: '#333',\n                fontSize: '18px'\n              },\n              children: \"\\u6587\\u4EF6\\u5DF2\\u9009\\u62E9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0 0 8px 0',\n                color: '#666',\n                fontSize: '14px',\n                fontWeight: '500'\n              },\n              children: selectedFile.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0',\n                color: '#999',\n                fontSize: '12px'\n              },\n              children: [\"\\u5927\\u5C0F: \", (selectedFile.size / 1024 / 1024).toFixed(2), \" MB\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: e => {\n                e.stopPropagation();\n                setSelectedFile(null);\n                document.getElementById('file-input').value = '';\n              },\n              style: {\n                marginTop: '12px',\n                background: 'rgba(239, 68, 68, 0.1)',\n                color: '#ef4444',\n                border: '1px solid rgba(239, 68, 68, 0.3)',\n                padding: '6px 12px',\n                borderRadius: '6px',\n                fontSize: '12px',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              },\n              onMouseOver: e => {\n                e.target.style.background = 'rgba(239, 68, 68, 0.2)';\n              },\n              onMouseOut: e => {\n                e.target.style.background = 'rgba(239, 68, 68, 0.1)';\n              },\n              children: \"\\u91CD\\u65B0\\u9009\\u62E9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '16px',\n                color: '#667eea'\n              },\n              children: \"\\uD83D\\uDCE4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: '0 0 8px 0',\n                color: '#333',\n                fontSize: '18px'\n              },\n              children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u4E0A\\u4F20\\u56FE\\u7247\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0 0 4px 0',\n                color: '#666',\n                fontSize: '14px'\n              },\n              children: \"\\u652F\\u6301 PNG\\u3001JPG\\u3001JPEG\\u3001GIF \\u683C\\u5F0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0',\n                color: '#999',\n                fontSize: '12px'\n              },\n              children: \"\\u6587\\u4EF6\\u5927\\u5C0F\\u4E0D\\u8D85\\u8FC7 10MB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '16px',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                color: 'white',\n                padding: '8px 20px',\n                borderRadius: '20px',\n                fontSize: '14px',\n                fontWeight: '500',\n                display: 'inline-block'\n              },\n              children: \"\\u9009\\u62E9\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"description\",\n          style: {\n            display: 'block',\n            marginBottom: '8px',\n            fontWeight: '600',\n            color: '#333',\n            fontSize: '16px'\n          },\n          children: \"\\uD83C\\uDFAF \\u754C\\u9762\\u529F\\u80FD\\u63CF\\u8FF0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          id: \"description\",\n          value: description,\n          onChange: e => setDescription(e.target.value),\n          placeholder: \"\\u8BF7\\u8BE6\\u7EC6\\u63CF\\u8FF0\\u8FD9\\u4E2A\\u754C\\u9762\\u7684\\u4E3B\\u8981\\u529F\\u80FD\\uFF0C\\u4F8B\\u5982\\uFF1A\\u8FD9\\u662F\\u4E00\\u4E2A\\u767B\\u5F55\\u9875\\u9762\\uFF0C\\u5305\\u542B\\u7528\\u6237\\u540D\\u5BC6\\u7801\\u8F93\\u5165\\u6846\\u548C\\u767B\\u5F55\\u6309\\u94AE...\",\n          rows: 4,\n          style: {\n            width: '100%',\n            padding: '12px',\n            border: '2px solid #e5e7eb',\n            borderRadius: '8px',\n            resize: 'vertical',\n            fontFamily: 'inherit',\n            fontSize: '14px',\n            lineHeight: '1.5',\n            transition: 'border-color 0.2s ease',\n            background: '#fafafa'\n          },\n          onFocus: e => {\n            e.target.style.borderColor = '#667eea';\n            e.target.style.background = '#ffffff';\n          },\n          onBlur: e => {\n            e.target.style.borderColor = '#e5e7eb';\n            e.target.style.background = '#fafafa';\n          },\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'right',\n            fontSize: '12px',\n            color: '#666',\n            marginTop: '4px'\n          },\n          children: [description.length, \"/500\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: 'block',\n            marginBottom: '8px',\n            fontWeight: '600',\n            color: '#333',\n            fontSize: '16px'\n          },\n          children: \"\\uD83D\\uDCCB \\u9879\\u76EE\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: '16px',\n            marginBottom: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '4px',\n                fontSize: '14px',\n                color: '#555'\n              },\n              children: \"\\u9879\\u76EE\\u540D\\u79F0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: additionalInfo.projectName,\n              onChange: e => setAdditionalInfo(prev => ({\n                ...prev,\n                projectName: e.target.value\n              })),\n              placeholder: \"\\u8F93\\u5165\\u9879\\u76EE\\u540D\\u79F0\",\n              style: {\n                width: '100%',\n                padding: '10px 12px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '6px',\n                fontSize: '14px',\n                transition: 'border-color 0.2s ease',\n                background: '#fafafa'\n              },\n              onFocus: e => {\n                e.target.style.borderColor = '#667eea';\n                e.target.style.background = '#ffffff';\n              },\n              onBlur: e => {\n                e.target.style.borderColor = '#e5e7eb';\n                e.target.style.background = '#fafafa';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '4px',\n                fontSize: '14px',\n                color: '#555'\n              },\n              children: \"\\u6D4B\\u8BD5\\u7C7B\\u578B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: additionalInfo.testType,\n              onChange: e => setAdditionalInfo(prev => ({\n                ...prev,\n                testType: e.target.value\n              })),\n              style: {\n                width: '100%',\n                padding: '10px 12px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '6px',\n                fontSize: '14px',\n                background: '#fafafa',\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"functional\",\n                children: \"\\u529F\\u80FD\\u6D4B\\u8BD5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"ui\",\n                children: \"UI\\u6D4B\\u8BD5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"integration\",\n                children: \"\\u96C6\\u6210\\u6D4B\\u8BD5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"regression\",\n                children: \"\\u56DE\\u5F52\\u6D4B\\u8BD5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: '16px',\n            marginBottom: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '4px',\n                fontSize: '14px',\n                color: '#555'\n              },\n              children: \"\\u4F18\\u5148\\u7EA7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: additionalInfo.priority,\n              onChange: e => setAdditionalInfo(prev => ({\n                ...prev,\n                priority: e.target.value\n              })),\n              style: {\n                width: '100%',\n                padding: '10px 12px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '6px',\n                fontSize: '14px',\n                background: '#fafafa',\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"low\",\n                children: \"\\u4F4E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"medium\",\n                children: \"\\u4E2D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"high\",\n                children: \"\\u9AD8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"critical\",\n                children: \"\\u7D27\\u6025\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '4px',\n                fontSize: '14px',\n                color: '#555'\n              },\n              children: \"\\u9884\\u671F\\u5143\\u7D20\\u6570\\u91CF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: additionalInfo.expectedElements,\n              onChange: e => setAdditionalInfo(prev => ({\n                ...prev,\n                expectedElements: e.target.value\n              })),\n              placeholder: \"\\u4F8B\\u5982\\uFF1A5-10\\u4E2A\\u6309\\u94AE\\uFF0C2\\u4E2A\\u8F93\\u5165\\u6846\",\n              style: {\n                width: '100%',\n                padding: '10px 12px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '6px',\n                fontSize: '14px',\n                transition: 'border-color 0.2s ease',\n                background: '#fafafa'\n              },\n              onFocus: e => {\n                e.target.style.borderColor = '#667eea';\n                e.target.style.background = '#ffffff';\n              },\n              onBlur: e => {\n                e.target.style.borderColor = '#e5e7eb';\n                e.target.style.background = '#fafafa';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: 'block',\n            marginBottom: '8px',\n            fontSize: '14px',\n            color: '#555'\n          },\n          children: \"\\u7279\\u6B8A\\u8981\\u6C42\\u6216\\u6CE8\\u610F\\u4E8B\\u9879\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: additionalInfo.specialRequirements,\n          onChange: e => setAdditionalInfo(prev => ({\n            ...prev,\n            specialRequirements: e.target.value\n          })),\n          placeholder: \"\\u4F8B\\u5982\\uFF1A\\u9700\\u8981\\u7279\\u522B\\u5173\\u6CE8\\u54CD\\u5E94\\u5F0F\\u8BBE\\u8BA1\\u3001\\u65E0\\u969C\\u788D\\u8BBF\\u95EE\\u3001\\u7279\\u5B9A\\u6D4F\\u89C8\\u5668\\u517C\\u5BB9\\u6027\\u7B49...\",\n          rows: 3,\n          style: {\n            width: '100%',\n            padding: '12px',\n            border: '2px solid #e5e7eb',\n            borderRadius: '8px',\n            resize: 'vertical',\n            fontFamily: 'inherit',\n            fontSize: '14px',\n            lineHeight: '1.5',\n            transition: 'border-color 0.2s ease',\n            background: '#fafafa'\n          },\n          onFocus: e => {\n            e.target.style.borderColor = '#667eea';\n            e.target.style.background = '#ffffff';\n          },\n          onBlur: e => {\n            e.target.style.borderColor = '#e5e7eb';\n            e.target.style.background = '#fafafa';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: isUploading || !selectedFile || !description.trim(),\n        style: {\n          background: isUploading ? '#6c757d' : '#007bff',\n          color: 'white',\n          border: 'none',\n          padding: '12px 24px',\n          borderRadius: '6px',\n          fontSize: '16px',\n          fontWeight: '500',\n          cursor: isUploading ? 'not-allowed' : 'pointer',\n          transition: 'all 0.3s ease',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        },\n        children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: '16px',\n              height: '16px',\n              border: '2px solid transparent',\n              borderTop: '2px solid white',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 15\n          }, this), \"\\u5206\\u6790\\u4E2D...\"]\n        }, void 0, true) : '🚀 开始分析'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        margin: '24px 0',\n        color: '#666',\n        fontSize: '14px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          height: '1px',\n          background: '#e9ecef'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          padding: '0 16px'\n        },\n        children: \"\\u6216\\u8005\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          height: '1px',\n          background: '#e9ecef'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 482,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          margin: '0 0 12px 0',\n          color: '#333',\n          fontSize: '16px'\n        },\n        children: \"\\uD83D\\uDCC1 \\u4E13\\u9879\\u5206\\u6790\\u6F14\\u793A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: '0 0 16px 0',\n          color: '#666',\n          fontSize: '14px'\n        },\n        children: \"\\u57FA\\u4E8E\\u60A8\\u63D0\\u4F9B\\u7684\\u6587\\u4EF6\\u7BA1\\u7406\\u754C\\u9762\\u622A\\u56FE\\uFF0C\\u751F\\u6210\\u4E13\\u95E8\\u7684UI\\u81EA\\u52A8\\u5316\\u6D4B\\u8BD5\\u811A\\u672C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: handleFileManagementDemo,\n        disabled: isUploading,\n        style: {\n          background: isUploading ? '#6c757d' : '#28a745',\n          color: 'white',\n          border: 'none',\n          padding: '12px 32px',\n          borderRadius: '6px',\n          fontSize: '16px',\n          fontWeight: '500',\n          cursor: isUploading ? 'not-allowed' : 'pointer',\n          transition: 'all 0.3s ease',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px',\n          margin: '0 auto'\n        },\n        children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: '16px',\n              height: '16px',\n              border: '2px solid transparent',\n              borderTop: '2px solid white',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 15\n          }, this), \"\\u5206\\u6790\\u4E2D...\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: \"\\uD83D\\uDCCA \\u5206\\u6790\\u6587\\u4EF6\\u7BA1\\u7406\\u754C\\u9762\"\n        }, void 0, false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 495,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 542,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleUpload, \"uXdqIXhBuTsq5cLMg12a1LPI6l0=\");\n_c = SimpleUpload;\nexport default SimpleUpload;\nvar _c;\n$RefreshReg$(_c, \"SimpleUpload\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SimpleUpload", "onUploadSuccess", "onUploadError", "_s", "selectedFile", "setSelectedFile", "description", "setDescription", "isUploading", "setIsUploading", "showAnalysis", "setShowAnalysis", "analysisProgress", "setAnalysisProgress", "overall", "currentStep", "steps", "name", "status", "progress", "handleFileSelect", "event", "file", "target", "files", "handleSubmit", "preventDefault", "trim", "formData", "FormData", "append", "simulateAnalysisProgress", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "result", "error", "console", "message", "step", "updateProgress", "length", "prev", "Math", "round", "map", "s", "index", "random", "setTimeout", "handleFileManagementDemo", "style", "background", "padding", "borderRadius", "boxShadow", "marginBottom", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "display", "fontWeight", "color", "fontSize", "className", "onClick", "document", "getElementById", "click", "height", "border", "flexDirection", "alignItems", "justifyContent", "cursor", "transition", "position", "overflow", "onDragOver", "e", "currentTarget", "borderColor", "onDragLeave", "onDrop", "dataTransfer", "id", "type", "accept", "onChange", "textAlign", "margin", "size", "toFixed", "stopPropagation", "value", "marginTop", "onMouseOver", "onMouseOut", "htmlFor", "placeholder", "rows", "width", "resize", "fontFamily", "lineHeight", "onFocus", "onBlur", "required", "gridTemplateColumns", "gap", "additionalInfo", "projectName", "setAdditionalInfo", "testType", "priority", "expectedElements", "specialRequirements", "disabled", "borderTop", "animation", "flex", "jsx", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/components/SimpleUpload.js"], "sourcesContent": ["/**\n * 简化的上传组件\n */\nimport React, { useState } from 'react';\n\nconst SimpleUpload = ({ onUploadSuccess, onUploadError }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const [showAnalysis, setShowAnalysis] = useState(false);\n  const [analysisProgress, setAnalysisProgress] = useState({\n    overall: 0,\n    currentStep: '准备开始分析...',\n    steps: [\n      { name: '初始化', status: 'pending', progress: 0 },\n      { name: '元素分析和智能识别', status: 'pending', progress: 0 },\n      { name: '生成自动化测试脚本', status: 'pending', progress: 0 }\n    ]\n  });\n\n  const handleFileSelect = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n\n  const handleSubmit = async (event) => {\n    event.preventDefault();\n\n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n\n    setIsUploading(true);\n    setShowAnalysis(true);\n\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n\n      // 开始模拟分析进度\n      simulateAnalysisProgress();\n\n      const response = await fetch('http://localhost:8001/api/v1/upload', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      onUploadSuccess(result);\n\n    } catch (error) {\n      console.error('Upload error:', error);\n      setShowAnalysis(false);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const simulateAnalysisProgress = () => {\n    let step = 0;\n    const steps = ['初始化', '元素分析和智能识别', '生成自动化测试脚本'];\n\n    const updateProgress = () => {\n      if (step < steps.length) {\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: Math.round((step + 1) / steps.length * 100),\n          currentStep: `正在执行: ${steps[step]}`,\n          steps: prev.steps.map((s, index) => ({\n            ...s,\n            status: index < step ? 'completed' : index === step ? 'processing' : 'pending',\n            progress: index < step ? 100 : index === step ? Math.random() * 80 + 20 : 0\n          }))\n        }));\n\n        step++;\n        setTimeout(updateProgress, 2000 + Math.random() * 3000);\n      } else {\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: 100,\n          currentStep: '分析完成',\n          steps: prev.steps.map(s => ({ ...s, status: 'completed', progress: 100 }))\n        }));\n      }\n    };\n\n    setTimeout(updateProgress, 1000);\n  };\n\n  const handleFileManagementDemo = async () => {\n    setIsUploading(true);\n\n    try {\n      const formData = new FormData();\n      formData.append('description', '文件管理界面功能分析 - 基于提供的截图进行专项分析');\n\n      const response = await fetch('http://localhost:8001/api/v1/analyze/file-management', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      onUploadSuccess(result);\n\n    } catch (error) {\n      console.error('File management demo error:', error);\n      onUploadError(error.message || '文件管理界面分析失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  return (\n    <div style={{ \n      background: 'white', \n      padding: '24px', \n      borderRadius: '12px', \n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n      marginBottom: '24px'\n    }}>\n      <h3>📁 上传UI界面截图</h3>\n      <form onSubmit={handleSubmit}>\n        <div style={{ marginBottom: '24px' }}>\n          <label style={{ display: 'block', marginBottom: '12px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n            📁 上传UI界面截图\n          </label>\n          <div\n            className=\"file-upload-area\"\n            onClick={() => document.getElementById('file-input').click()}\n            style={{\n              height: '240px', // 调高3倍（原来约80px）\n              border: '2px dashed #667eea',\n              borderRadius: '12px',\n              background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.3s ease',\n              position: 'relative',\n              overflow: 'hidden'\n            }}\n            onDragOver={(e) => {\n              e.preventDefault();\n              e.currentTarget.style.borderColor = '#4f46e5';\n              e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';\n            }}\n            onDragLeave={(e) => {\n              e.currentTarget.style.borderColor = '#667eea';\n              e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';\n            }}\n            onDrop={(e) => {\n              e.preventDefault();\n              const files = e.dataTransfer.files;\n              if (files.length > 0) {\n                setSelectedFile(files[0]);\n              }\n              e.currentTarget.style.borderColor = '#667eea';\n              e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';\n            }}\n          >\n            <input\n              id=\"file-input\"\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={handleFileSelect}\n              style={{ display: 'none' }}\n            />\n\n            {selectedFile ? (\n              <div style={{ textAlign: 'center', padding: '20px' }}>\n                <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>✅</div>\n                <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>文件已选择</h3>\n                <p style={{ margin: '0 0 8px 0', color: '#666', fontSize: '14px', fontWeight: '500' }}>\n                  {selectedFile.name}\n                </p>\n                <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>\n                  大小: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB\n                </p>\n                <button\n                  type=\"button\"\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    setSelectedFile(null);\n                    document.getElementById('file-input').value = '';\n                  }}\n                  style={{\n                    marginTop: '12px',\n                    background: 'rgba(239, 68, 68, 0.1)',\n                    color: '#ef4444',\n                    border: '1px solid rgba(239, 68, 68, 0.3)',\n                    padding: '6px 12px',\n                    borderRadius: '6px',\n                    fontSize: '12px',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseOver={(e) => {\n                    e.target.style.background = 'rgba(239, 68, 68, 0.2)';\n                  }}\n                  onMouseOut={(e) => {\n                    e.target.style.background = 'rgba(239, 68, 68, 0.1)';\n                  }}\n                >\n                  重新选择\n                </button>\n              </div>\n            ) : (\n              <div style={{ textAlign: 'center', padding: '20px' }}>\n                <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>📤</div>\n                <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>点击或拖拽上传图片</h3>\n                <p style={{ margin: '0 0 4px 0', color: '#666', fontSize: '14px' }}>\n                  支持 PNG、JPG、JPEG、GIF 格式\n                </p>\n                <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>\n                  文件大小不超过 10MB\n                </p>\n                <div style={{\n                  marginTop: '16px',\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  color: 'white',\n                  padding: '8px 20px',\n                  borderRadius: '20px',\n                  fontSize: '14px',\n                  fontWeight: '500',\n                  display: 'inline-block'\n                }}>\n                  选择文件\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* 界面功能描述 */}\n        <div style={{ marginBottom: '20px' }}>\n          <label htmlFor=\"description\" style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n            🎯 界面功能描述\n          </label>\n          <textarea\n            id=\"description\"\n            value={description}\n            onChange={(e) => setDescription(e.target.value)}\n            placeholder=\"请详细描述这个界面的主要功能，例如：这是一个登录页面，包含用户名密码输入框和登录按钮...\"\n            rows={4}\n            style={{\n              width: '100%',\n              padding: '12px',\n              border: '2px solid #e5e7eb',\n              borderRadius: '8px',\n              resize: 'vertical',\n              fontFamily: 'inherit',\n              fontSize: '14px',\n              lineHeight: '1.5',\n              transition: 'border-color 0.2s ease',\n              background: '#fafafa'\n            }}\n            onFocus={(e) => {\n              e.target.style.borderColor = '#667eea';\n              e.target.style.background = '#ffffff';\n            }}\n            onBlur={(e) => {\n              e.target.style.borderColor = '#e5e7eb';\n              e.target.style.background = '#fafafa';\n            }}\n            required\n          />\n          <div style={{ textAlign: 'right', fontSize: '12px', color: '#666', marginTop: '4px' }}>\n            {description.length}/500\n          </div>\n        </div>\n\n        {/* 项目信息 */}\n        <div style={{ marginBottom: '20px' }}>\n          <label style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n            📋 项目信息\n          </label>\n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>\n            <div>\n              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', color: '#555' }}>\n                项目名称\n              </label>\n              <input\n                type=\"text\"\n                value={additionalInfo.projectName}\n                onChange={(e) => setAdditionalInfo(prev => ({ ...prev, projectName: e.target.value }))}\n                placeholder=\"输入项目名称\"\n                style={{\n                  width: '100%',\n                  padding: '10px 12px',\n                  border: '2px solid #e5e7eb',\n                  borderRadius: '6px',\n                  fontSize: '14px',\n                  transition: 'border-color 0.2s ease',\n                  background: '#fafafa'\n                }}\n                onFocus={(e) => {\n                  e.target.style.borderColor = '#667eea';\n                  e.target.style.background = '#ffffff';\n                }}\n                onBlur={(e) => {\n                  e.target.style.borderColor = '#e5e7eb';\n                  e.target.style.background = '#fafafa';\n                }}\n              />\n            </div>\n            <div>\n              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', color: '#555' }}>\n                测试类型\n              </label>\n              <select\n                value={additionalInfo.testType}\n                onChange={(e) => setAdditionalInfo(prev => ({ ...prev, testType: e.target.value }))}\n                style={{\n                  width: '100%',\n                  padding: '10px 12px',\n                  border: '2px solid #e5e7eb',\n                  borderRadius: '6px',\n                  fontSize: '14px',\n                  background: '#fafafa',\n                  cursor: 'pointer'\n                }}\n              >\n                <option value=\"functional\">功能测试</option>\n                <option value=\"ui\">UI测试</option>\n                <option value=\"integration\">集成测试</option>\n                <option value=\"regression\">回归测试</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* 测试优先级和预期元素 */}\n        <div style={{ marginBottom: '20px' }}>\n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>\n            <div>\n              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', color: '#555' }}>\n                优先级\n              </label>\n              <select\n                value={additionalInfo.priority}\n                onChange={(e) => setAdditionalInfo(prev => ({ ...prev, priority: e.target.value }))}\n                style={{\n                  width: '100%',\n                  padding: '10px 12px',\n                  border: '2px solid #e5e7eb',\n                  borderRadius: '6px',\n                  fontSize: '14px',\n                  background: '#fafafa',\n                  cursor: 'pointer'\n                }}\n              >\n                <option value=\"low\">低</option>\n                <option value=\"medium\">中</option>\n                <option value=\"high\">高</option>\n                <option value=\"critical\">紧急</option>\n              </select>\n            </div>\n            <div>\n              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', color: '#555' }}>\n                预期元素数量\n              </label>\n              <input\n                type=\"text\"\n                value={additionalInfo.expectedElements}\n                onChange={(e) => setAdditionalInfo(prev => ({ ...prev, expectedElements: e.target.value }))}\n                placeholder=\"例如：5-10个按钮，2个输入框\"\n                style={{\n                  width: '100%',\n                  padding: '10px 12px',\n                  border: '2px solid #e5e7eb',\n                  borderRadius: '6px',\n                  fontSize: '14px',\n                  transition: 'border-color 0.2s ease',\n                  background: '#fafafa'\n                }}\n                onFocus={(e) => {\n                  e.target.style.borderColor = '#667eea';\n                  e.target.style.background = '#ffffff';\n                }}\n                onBlur={(e) => {\n                  e.target.style.borderColor = '#e5e7eb';\n                  e.target.style.background = '#fafafa';\n                }}\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* 特殊要求 */}\n        <div style={{ marginBottom: '24px' }}>\n          <label style={{ display: 'block', marginBottom: '8px', fontSize: '14px', color: '#555' }}>\n            特殊要求或注意事项\n          </label>\n          <textarea\n            value={additionalInfo.specialRequirements}\n            onChange={(e) => setAdditionalInfo(prev => ({ ...prev, specialRequirements: e.target.value }))}\n            placeholder=\"例如：需要特别关注响应式设计、无障碍访问、特定浏览器兼容性等...\"\n            rows={3}\n            style={{\n              width: '100%',\n              padding: '12px',\n              border: '2px solid #e5e7eb',\n              borderRadius: '8px',\n              resize: 'vertical',\n              fontFamily: 'inherit',\n              fontSize: '14px',\n              lineHeight: '1.5',\n              transition: 'border-color 0.2s ease',\n              background: '#fafafa'\n            }}\n            onFocus={(e) => {\n              e.target.style.borderColor = '#667eea';\n              e.target.style.background = '#ffffff';\n            }}\n            onBlur={(e) => {\n              e.target.style.borderColor = '#e5e7eb';\n              e.target.style.background = '#fafafa';\n            }}\n          />\n        </div>\n\n        <button\n          type=\"submit\"\n          disabled={isUploading || !selectedFile || !description.trim()}\n          style={{\n            background: isUploading ? '#6c757d' : '#007bff',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            fontSize: '16px',\n            fontWeight: '500',\n            cursor: isUploading ? 'not-allowed' : 'pointer',\n            transition: 'all 0.3s ease',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          }}\n        >\n          {isUploading ? (\n            <>\n              <span style={{\n                width: '16px',\n                height: '16px',\n                border: '2px solid transparent',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }}></span>\n              分析中...\n            </>\n          ) : (\n            '🚀 开始分析'\n          )}\n        </button>\n      </form>\n\n      {/* 分隔线 */}\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        margin: '24px 0',\n        color: '#666',\n        fontSize: '14px'\n      }}>\n        <div style={{ flex: 1, height: '1px', background: '#e9ecef' }}></div>\n        <span style={{ padding: '0 16px' }}>或者</span>\n        <div style={{ flex: 1, height: '1px', background: '#e9ecef' }}></div>\n      </div>\n\n      {/* 文件管理界面演示按钮 */}\n      <div style={{ textAlign: 'center' }}>\n        <h4 style={{ margin: '0 0 12px 0', color: '#333', fontSize: '16px' }}>\n          📁 专项分析演示\n        </h4>\n        <p style={{ margin: '0 0 16px 0', color: '#666', fontSize: '14px' }}>\n          基于您提供的文件管理界面截图，生成专门的UI自动化测试脚本\n        </p>\n        <button\n          type=\"button\"\n          onClick={handleFileManagementDemo}\n          disabled={isUploading}\n          style={{\n            background: isUploading ? '#6c757d' : '#28a745',\n            color: 'white',\n            border: 'none',\n            padding: '12px 32px',\n            borderRadius: '6px',\n            fontSize: '16px',\n            fontWeight: '500',\n            cursor: isUploading ? 'not-allowed' : 'pointer',\n            transition: 'all 0.3s ease',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px',\n            margin: '0 auto'\n          }}\n        >\n          {isUploading ? (\n            <>\n              <span style={{\n                width: '16px',\n                height: '16px',\n                border: '2px solid transparent',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }}></span>\n              分析中...\n            </>\n          ) : (\n            <>\n              📊 分析文件管理界面\n            </>\n          )}\n        </button>\n      </div>\n\n      <style jsx>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default SimpleUpload;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,YAAY,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlB,QAAQ,CAAC;IACvDmB,OAAO,EAAE,CAAC;IACVC,WAAW,EAAE,WAAW;IACxBC,KAAK,EAAE,CACL;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAE,CAAC,EAC/C;MAAEF,IAAI,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAE,CAAC,EACrD;MAAEF,IAAI,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAE,CAAC;EAEzD,CAAC,CAAC;EAEF,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACRjB,eAAe,CAACiB,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOJ,KAAK,IAAK;IACpCA,KAAK,CAACK,cAAc,CAAC,CAAC;IAEtB,IAAI,CAACtB,YAAY,EAAE;MACjBF,aAAa,CAAC,SAAS,CAAC;MACxB;IACF;IAEA,IAAI,CAACI,WAAW,CAACqB,IAAI,CAAC,CAAC,EAAE;MACvBzB,aAAa,CAAC,WAAW,CAAC;MAC1B;IACF;IAEAO,cAAc,CAAC,IAAI,CAAC;IACpBE,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF,MAAMiB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE1B,YAAY,CAAC;MAC3CwB,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAExB,WAAW,CAACqB,IAAI,CAAC,CAAC,CAAC;;MAElD;MACAI,wBAAwB,CAAC,CAAC;MAE1B,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,qCAAqC,EAAE;QAClEC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEP;MACR,CAAC,CAAC;MAEF,IAAI,CAACI,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,uBAAuBR,QAAQ,CAACd,MAAM,EAAE,CAAC;MAC/E;MAEA,MAAMuB,MAAM,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MACpCrC,eAAe,CAACwC,MAAM,CAAC;IAEzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC/B,eAAe,CAAC,KAAK,CAAC;MACtBT,aAAa,CAACwC,KAAK,CAACE,OAAO,IAAI,UAAU,CAAC;IAC5C,CAAC,SAAS;MACRnC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMsB,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAIc,IAAI,GAAG,CAAC;IACZ,MAAM7B,KAAK,GAAG,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,CAAC;IAE/C,MAAM8B,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAID,IAAI,GAAG7B,KAAK,CAAC+B,MAAM,EAAE;QACvBlC,mBAAmB,CAACmC,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACPlC,OAAO,EAAEmC,IAAI,CAACC,KAAK,CAAC,CAACL,IAAI,GAAG,CAAC,IAAI7B,KAAK,CAAC+B,MAAM,GAAG,GAAG,CAAC;UACpDhC,WAAW,EAAE,SAASC,KAAK,CAAC6B,IAAI,CAAC,EAAE;UACnC7B,KAAK,EAAEgC,IAAI,CAAChC,KAAK,CAACmC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,MAAM;YACnC,GAAGD,CAAC;YACJlC,MAAM,EAAEmC,KAAK,GAAGR,IAAI,GAAG,WAAW,GAAGQ,KAAK,KAAKR,IAAI,GAAG,YAAY,GAAG,SAAS;YAC9E1B,QAAQ,EAAEkC,KAAK,GAAGR,IAAI,GAAG,GAAG,GAAGQ,KAAK,KAAKR,IAAI,GAAGI,IAAI,CAACK,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;UAC5E,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEHT,IAAI,EAAE;QACNU,UAAU,CAACT,cAAc,EAAE,IAAI,GAAGG,IAAI,CAACK,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;MACzD,CAAC,MAAM;QACLzC,mBAAmB,CAACmC,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACPlC,OAAO,EAAE,GAAG;UACZC,WAAW,EAAE,MAAM;UACnBC,KAAK,EAAEgC,IAAI,CAAChC,KAAK,CAACmC,GAAG,CAACC,CAAC,KAAK;YAAE,GAAGA,CAAC;YAAElC,MAAM,EAAE,WAAW;YAAEC,QAAQ,EAAE;UAAI,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;MACL;IACF,CAAC;IAEDoC,UAAU,CAACT,cAAc,EAAE,IAAI,CAAC;EAClC,CAAC;EAED,MAAMU,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C/C,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,MAAMmB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE,4BAA4B,CAAC;MAE5D,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,sDAAsD,EAAE;QACnFC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEP;MACR,CAAC,CAAC;MAEF,IAAI,CAACI,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,uBAAuBR,QAAQ,CAACd,MAAM,EAAE,CAAC;MAC/E;MAEA,MAAMuB,MAAM,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MACpCrC,eAAe,CAACwC,MAAM,CAAC;IAEzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDxC,aAAa,CAACwC,KAAK,CAACE,OAAO,IAAI,gBAAgB,CAAC;IAClD,CAAC,SAAS;MACRnC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,oBACEZ,OAAA;IAAK4D,KAAK,EAAE;MACVC,UAAU,EAAE,OAAO;MACnBC,OAAO,EAAE,MAAM;MACfC,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,4BAA4B;MACvCC,YAAY,EAAE;IAChB,CAAE;IAAAC,QAAA,gBACAlE,OAAA;MAAAkE,QAAA,EAAI;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpBtE,OAAA;MAAMuE,QAAQ,EAAE3C,YAAa;MAAAsC,QAAA,gBAC3BlE,OAAA;QAAK4D,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnClE,OAAA;UAAO4D,KAAK,EAAE;YAAEY,OAAO,EAAE,OAAO;YAAEP,YAAY,EAAE,MAAM;YAAEQ,UAAU,EAAE,KAAK;YAAEC,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE9G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRtE,OAAA;UACE4E,SAAS,EAAC,kBAAkB;UAC5BC,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAACC,KAAK,CAAC,CAAE;UAC7DpB,KAAK,EAAE;YACLqB,MAAM,EAAE,OAAO;YAAE;YACjBC,MAAM,EAAE,oBAAoB;YAC5BnB,YAAY,EAAE,MAAM;YACpBF,UAAU,EAAEtD,YAAY,GAAG,mDAAmD,GAAG,mDAAmD;YACpIiE,OAAO,EAAE,MAAM;YACfW,aAAa,EAAE,QAAQ;YACvBC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,MAAM,EAAE,SAAS;YACjBC,UAAU,EAAE,eAAe;YAC3BC,QAAQ,EAAE,UAAU;YACpBC,QAAQ,EAAE;UACZ,CAAE;UACFC,UAAU,EAAGC,CAAC,IAAK;YACjBA,CAAC,CAAC9D,cAAc,CAAC,CAAC;YAClB8D,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACiC,WAAW,GAAG,SAAS;YAC7CF,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACC,UAAU,GAAG,mDAAmD;UACxF,CAAE;UACFiC,WAAW,EAAGH,CAAC,IAAK;YAClBA,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACiC,WAAW,GAAG,SAAS;YAC7CF,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACC,UAAU,GAAGtD,YAAY,GAAG,mDAAmD,GAAG,mDAAmD;UAC7J,CAAE;UACFwF,MAAM,EAAGJ,CAAC,IAAK;YACbA,CAAC,CAAC9D,cAAc,CAAC,CAAC;YAClB,MAAMF,KAAK,GAAGgE,CAAC,CAACK,YAAY,CAACrE,KAAK;YAClC,IAAIA,KAAK,CAACuB,MAAM,GAAG,CAAC,EAAE;cACpB1C,eAAe,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B;YACAgE,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACiC,WAAW,GAAG,SAAS;YAC7CF,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACC,UAAU,GAAG,mDAAmD;UACxF,CAAE;UAAAK,QAAA,gBAEFlE,OAAA;YACEiG,EAAE,EAAC,YAAY;YACfC,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,SAAS;YAChBC,QAAQ,EAAE7E,gBAAiB;YAC3BqC,KAAK,EAAE;cAAEY,OAAO,EAAE;YAAO;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,EAED/D,YAAY,gBACXP,OAAA;YAAK4D,KAAK,EAAE;cAAEyC,SAAS,EAAE,QAAQ;cAAEvC,OAAO,EAAE;YAAO,CAAE;YAAAI,QAAA,gBACnDlE,OAAA;cAAK4D,KAAK,EAAE;gBAAEe,QAAQ,EAAE,MAAM;gBAAEV,YAAY,EAAE,MAAM;gBAAES,KAAK,EAAE;cAAU,CAAE;cAAAR,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjFtE,OAAA;cAAI4D,KAAK,EAAE;gBAAE0C,MAAM,EAAE,WAAW;gBAAE5B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/EtE,OAAA;cAAG4D,KAAK,EAAE;gBAAE0C,MAAM,EAAE,WAAW;gBAAE5B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE,MAAM;gBAAEF,UAAU,EAAE;cAAM,CAAE;cAAAP,QAAA,EACnF3D,YAAY,CAACa;YAAI;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACJtE,OAAA;cAAG4D,KAAK,EAAE;gBAAE0C,MAAM,EAAE,GAAG;gBAAE5B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,GAAC,gBACtD,EAAC,CAAC3D,YAAY,CAACgG,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KACpD;YAAA;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJtE,OAAA;cACEkG,IAAI,EAAC,QAAQ;cACbrB,OAAO,EAAGc,CAAC,IAAK;gBACdA,CAAC,CAACc,eAAe,CAAC,CAAC;gBACnBjG,eAAe,CAAC,IAAI,CAAC;gBACrBsE,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAAC2B,KAAK,GAAG,EAAE;cAClD,CAAE;cACF9C,KAAK,EAAE;gBACL+C,SAAS,EAAE,MAAM;gBACjB9C,UAAU,EAAE,wBAAwB;gBACpCa,KAAK,EAAE,SAAS;gBAChBQ,MAAM,EAAE,kCAAkC;gBAC1CpB,OAAO,EAAE,UAAU;gBACnBC,YAAY,EAAE,KAAK;gBACnBY,QAAQ,EAAE,MAAM;gBAChBW,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE;cACd,CAAE;cACFqB,WAAW,EAAGjB,CAAC,IAAK;gBAClBA,CAAC,CAACjE,MAAM,CAACkC,KAAK,CAACC,UAAU,GAAG,wBAAwB;cACtD,CAAE;cACFgD,UAAU,EAAGlB,CAAC,IAAK;gBACjBA,CAAC,CAACjE,MAAM,CAACkC,KAAK,CAACC,UAAU,GAAG,wBAAwB;cACtD,CAAE;cAAAK,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAENtE,OAAA;YAAK4D,KAAK,EAAE;cAAEyC,SAAS,EAAE,QAAQ;cAAEvC,OAAO,EAAE;YAAO,CAAE;YAAAI,QAAA,gBACnDlE,OAAA;cAAK4D,KAAK,EAAE;gBAAEe,QAAQ,EAAE,MAAM;gBAAEV,YAAY,EAAE,MAAM;gBAAES,KAAK,EAAE;cAAU,CAAE;cAAAR,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClFtE,OAAA;cAAI4D,KAAK,EAAE;gBAAE0C,MAAM,EAAE,WAAW;gBAAE5B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnFtE,OAAA;cAAG4D,KAAK,EAAE;gBAAE0C,MAAM,EAAE,WAAW;gBAAE5B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJtE,OAAA;cAAG4D,KAAK,EAAE;gBAAE0C,MAAM,EAAE,GAAG;gBAAE5B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAC;YAE5D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJtE,OAAA;cAAK4D,KAAK,EAAE;gBACV+C,SAAS,EAAE,MAAM;gBACjB9C,UAAU,EAAE,mDAAmD;gBAC/Da,KAAK,EAAE,OAAO;gBACdZ,OAAO,EAAE,UAAU;gBACnBC,YAAY,EAAE,MAAM;gBACpBY,QAAQ,EAAE,MAAM;gBAChBF,UAAU,EAAE,KAAK;gBACjBD,OAAO,EAAE;cACX,CAAE;cAAAN,QAAA,EAAC;YAEH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtE,OAAA;QAAK4D,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnClE,OAAA;UAAO8G,OAAO,EAAC,aAAa;UAAClD,KAAK,EAAE;YAAEY,OAAO,EAAE,OAAO;YAAEP,YAAY,EAAE,KAAK;YAAEQ,UAAU,EAAE,KAAK;YAAEC,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAEnI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRtE,OAAA;UACEiG,EAAE,EAAC,aAAa;UAChBS,KAAK,EAAEjG,WAAY;UACnB2F,QAAQ,EAAGT,CAAC,IAAKjF,cAAc,CAACiF,CAAC,CAACjE,MAAM,CAACgF,KAAK,CAAE;UAChDK,WAAW,EAAC,iQAA+C;UAC3DC,IAAI,EAAE,CAAE;UACRpD,KAAK,EAAE;YACLqD,KAAK,EAAE,MAAM;YACbnD,OAAO,EAAE,MAAM;YACfoB,MAAM,EAAE,mBAAmB;YAC3BnB,YAAY,EAAE,KAAK;YACnBmD,MAAM,EAAE,UAAU;YAClBC,UAAU,EAAE,SAAS;YACrBxC,QAAQ,EAAE,MAAM;YAChByC,UAAU,EAAE,KAAK;YACjB7B,UAAU,EAAE,wBAAwB;YACpC1B,UAAU,EAAE;UACd,CAAE;UACFwD,OAAO,EAAG1B,CAAC,IAAK;YACdA,CAAC,CAACjE,MAAM,CAACkC,KAAK,CAACiC,WAAW,GAAG,SAAS;YACtCF,CAAC,CAACjE,MAAM,CAACkC,KAAK,CAACC,UAAU,GAAG,SAAS;UACvC,CAAE;UACFyD,MAAM,EAAG3B,CAAC,IAAK;YACbA,CAAC,CAACjE,MAAM,CAACkC,KAAK,CAACiC,WAAW,GAAG,SAAS;YACtCF,CAAC,CAACjE,MAAM,CAACkC,KAAK,CAACC,UAAU,GAAG,SAAS;UACvC,CAAE;UACF0D,QAAQ;QAAA;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFtE,OAAA;UAAK4D,KAAK,EAAE;YAAEyC,SAAS,EAAE,OAAO;YAAE1B,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE,MAAM;YAAEiC,SAAS,EAAE;UAAM,CAAE;UAAAzC,QAAA,GACnFzD,WAAW,CAACyC,MAAM,EAAC,MACtB;QAAA;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtE,OAAA;QAAK4D,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnClE,OAAA;UAAO4D,KAAK,EAAE;YAAEY,OAAO,EAAE,OAAO;YAAEP,YAAY,EAAE,KAAK;YAAEQ,UAAU,EAAE,KAAK;YAAEC,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE7G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRtE,OAAA;UAAK4D,KAAK,EAAE;YAAEY,OAAO,EAAE,MAAM;YAAEgD,mBAAmB,EAAE,SAAS;YAAEC,GAAG,EAAE,MAAM;YAAExD,YAAY,EAAE;UAAO,CAAE;UAAAC,QAAA,gBACjGlE,OAAA;YAAAkE,QAAA,gBACElE,OAAA;cAAO4D,KAAK,EAAE;gBAAEY,OAAO,EAAE,OAAO;gBAAEP,YAAY,EAAE,KAAK;gBAAEU,QAAQ,EAAE,MAAM;gBAAED,KAAK,EAAE;cAAO,CAAE;cAAAR,QAAA,EAAC;YAE1F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtE,OAAA;cACEkG,IAAI,EAAC,MAAM;cACXQ,KAAK,EAAEgB,cAAc,CAACC,WAAY;cAClCvB,QAAQ,EAAGT,CAAC,IAAKiC,iBAAiB,CAACzE,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEwE,WAAW,EAAEhC,CAAC,CAACjE,MAAM,CAACgF;cAAM,CAAC,CAAC,CAAE;cACvFK,WAAW,EAAC,sCAAQ;cACpBnD,KAAK,EAAE;gBACLqD,KAAK,EAAE,MAAM;gBACbnD,OAAO,EAAE,WAAW;gBACpBoB,MAAM,EAAE,mBAAmB;gBAC3BnB,YAAY,EAAE,KAAK;gBACnBY,QAAQ,EAAE,MAAM;gBAChBY,UAAU,EAAE,wBAAwB;gBACpC1B,UAAU,EAAE;cACd,CAAE;cACFwD,OAAO,EAAG1B,CAAC,IAAK;gBACdA,CAAC,CAACjE,MAAM,CAACkC,KAAK,CAACiC,WAAW,GAAG,SAAS;gBACtCF,CAAC,CAACjE,MAAM,CAACkC,KAAK,CAACC,UAAU,GAAG,SAAS;cACvC,CAAE;cACFyD,MAAM,EAAG3B,CAAC,IAAK;gBACbA,CAAC,CAACjE,MAAM,CAACkC,KAAK,CAACiC,WAAW,GAAG,SAAS;gBACtCF,CAAC,CAACjE,MAAM,CAACkC,KAAK,CAACC,UAAU,GAAG,SAAS;cACvC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNtE,OAAA;YAAAkE,QAAA,gBACElE,OAAA;cAAO4D,KAAK,EAAE;gBAAEY,OAAO,EAAE,OAAO;gBAAEP,YAAY,EAAE,KAAK;gBAAEU,QAAQ,EAAE,MAAM;gBAAED,KAAK,EAAE;cAAO,CAAE;cAAAR,QAAA,EAAC;YAE1F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtE,OAAA;cACE0G,KAAK,EAAEgB,cAAc,CAACG,QAAS;cAC/BzB,QAAQ,EAAGT,CAAC,IAAKiC,iBAAiB,CAACzE,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE0E,QAAQ,EAAElC,CAAC,CAACjE,MAAM,CAACgF;cAAM,CAAC,CAAC,CAAE;cACpF9C,KAAK,EAAE;gBACLqD,KAAK,EAAE,MAAM;gBACbnD,OAAO,EAAE,WAAW;gBACpBoB,MAAM,EAAE,mBAAmB;gBAC3BnB,YAAY,EAAE,KAAK;gBACnBY,QAAQ,EAAE,MAAM;gBAChBd,UAAU,EAAE,SAAS;gBACrByB,MAAM,EAAE;cACV,CAAE;cAAApB,QAAA,gBAEFlE,OAAA;gBAAQ0G,KAAK,EAAC,YAAY;gBAAAxC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCtE,OAAA;gBAAQ0G,KAAK,EAAC,IAAI;gBAAAxC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChCtE,OAAA;gBAAQ0G,KAAK,EAAC,aAAa;gBAAAxC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzCtE,OAAA;gBAAQ0G,KAAK,EAAC,YAAY;gBAAAxC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtE,OAAA;QAAK4D,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,eACnClE,OAAA;UAAK4D,KAAK,EAAE;YAAEY,OAAO,EAAE,MAAM;YAAEgD,mBAAmB,EAAE,SAAS;YAAEC,GAAG,EAAE,MAAM;YAAExD,YAAY,EAAE;UAAO,CAAE;UAAAC,QAAA,gBACjGlE,OAAA;YAAAkE,QAAA,gBACElE,OAAA;cAAO4D,KAAK,EAAE;gBAAEY,OAAO,EAAE,OAAO;gBAAEP,YAAY,EAAE,KAAK;gBAAEU,QAAQ,EAAE,MAAM;gBAAED,KAAK,EAAE;cAAO,CAAE;cAAAR,QAAA,EAAC;YAE1F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtE,OAAA;cACE0G,KAAK,EAAEgB,cAAc,CAACI,QAAS;cAC/B1B,QAAQ,EAAGT,CAAC,IAAKiC,iBAAiB,CAACzE,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE2E,QAAQ,EAAEnC,CAAC,CAACjE,MAAM,CAACgF;cAAM,CAAC,CAAC,CAAE;cACpF9C,KAAK,EAAE;gBACLqD,KAAK,EAAE,MAAM;gBACbnD,OAAO,EAAE,WAAW;gBACpBoB,MAAM,EAAE,mBAAmB;gBAC3BnB,YAAY,EAAE,KAAK;gBACnBY,QAAQ,EAAE,MAAM;gBAChBd,UAAU,EAAE,SAAS;gBACrByB,MAAM,EAAE;cACV,CAAE;cAAApB,QAAA,gBAEFlE,OAAA;gBAAQ0G,KAAK,EAAC,KAAK;gBAAAxC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BtE,OAAA;gBAAQ0G,KAAK,EAAC,QAAQ;gBAAAxC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACjCtE,OAAA;gBAAQ0G,KAAK,EAAC,MAAM;gBAAAxC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/BtE,OAAA;gBAAQ0G,KAAK,EAAC,UAAU;gBAAAxC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNtE,OAAA;YAAAkE,QAAA,gBACElE,OAAA;cAAO4D,KAAK,EAAE;gBAAEY,OAAO,EAAE,OAAO;gBAAEP,YAAY,EAAE,KAAK;gBAAEU,QAAQ,EAAE,MAAM;gBAAED,KAAK,EAAE;cAAO,CAAE;cAAAR,QAAA,EAAC;YAE1F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRtE,OAAA;cACEkG,IAAI,EAAC,MAAM;cACXQ,KAAK,EAAEgB,cAAc,CAACK,gBAAiB;cACvC3B,QAAQ,EAAGT,CAAC,IAAKiC,iBAAiB,CAACzE,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE4E,gBAAgB,EAAEpC,CAAC,CAACjE,MAAM,CAACgF;cAAM,CAAC,CAAC,CAAE;cAC5FK,WAAW,EAAC,yEAAkB;cAC9BnD,KAAK,EAAE;gBACLqD,KAAK,EAAE,MAAM;gBACbnD,OAAO,EAAE,WAAW;gBACpBoB,MAAM,EAAE,mBAAmB;gBAC3BnB,YAAY,EAAE,KAAK;gBACnBY,QAAQ,EAAE,MAAM;gBAChBY,UAAU,EAAE,wBAAwB;gBACpC1B,UAAU,EAAE;cACd,CAAE;cACFwD,OAAO,EAAG1B,CAAC,IAAK;gBACdA,CAAC,CAACjE,MAAM,CAACkC,KAAK,CAACiC,WAAW,GAAG,SAAS;gBACtCF,CAAC,CAACjE,MAAM,CAACkC,KAAK,CAACC,UAAU,GAAG,SAAS;cACvC,CAAE;cACFyD,MAAM,EAAG3B,CAAC,IAAK;gBACbA,CAAC,CAACjE,MAAM,CAACkC,KAAK,CAACiC,WAAW,GAAG,SAAS;gBACtCF,CAAC,CAACjE,MAAM,CAACkC,KAAK,CAACC,UAAU,GAAG,SAAS;cACvC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtE,OAAA;QAAK4D,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnClE,OAAA;UAAO4D,KAAK,EAAE;YAAEY,OAAO,EAAE,OAAO;YAAEP,YAAY,EAAE,KAAK;YAAEU,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE;UAAO,CAAE;UAAAR,QAAA,EAAC;QAE1F;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRtE,OAAA;UACE0G,KAAK,EAAEgB,cAAc,CAACM,mBAAoB;UAC1C5B,QAAQ,EAAGT,CAAC,IAAKiC,iBAAiB,CAACzE,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE6E,mBAAmB,EAAErC,CAAC,CAACjE,MAAM,CAACgF;UAAM,CAAC,CAAC,CAAE;UAC/FK,WAAW,EAAC,yLAAmC;UAC/CC,IAAI,EAAE,CAAE;UACRpD,KAAK,EAAE;YACLqD,KAAK,EAAE,MAAM;YACbnD,OAAO,EAAE,MAAM;YACfoB,MAAM,EAAE,mBAAmB;YAC3BnB,YAAY,EAAE,KAAK;YACnBmD,MAAM,EAAE,UAAU;YAClBC,UAAU,EAAE,SAAS;YACrBxC,QAAQ,EAAE,MAAM;YAChByC,UAAU,EAAE,KAAK;YACjB7B,UAAU,EAAE,wBAAwB;YACpC1B,UAAU,EAAE;UACd,CAAE;UACFwD,OAAO,EAAG1B,CAAC,IAAK;YACdA,CAAC,CAACjE,MAAM,CAACkC,KAAK,CAACiC,WAAW,GAAG,SAAS;YACtCF,CAAC,CAACjE,MAAM,CAACkC,KAAK,CAACC,UAAU,GAAG,SAAS;UACvC,CAAE;UACFyD,MAAM,EAAG3B,CAAC,IAAK;YACbA,CAAC,CAACjE,MAAM,CAACkC,KAAK,CAACiC,WAAW,GAAG,SAAS;YACtCF,CAAC,CAACjE,MAAM,CAACkC,KAAK,CAACC,UAAU,GAAG,SAAS;UACvC;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENtE,OAAA;QACEkG,IAAI,EAAC,QAAQ;QACb+B,QAAQ,EAAEtH,WAAW,IAAI,CAACJ,YAAY,IAAI,CAACE,WAAW,CAACqB,IAAI,CAAC,CAAE;QAC9D8B,KAAK,EAAE;UACLC,UAAU,EAAElD,WAAW,GAAG,SAAS,GAAG,SAAS;UAC/C+D,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdpB,OAAO,EAAE,WAAW;UACpBC,YAAY,EAAE,KAAK;UACnBY,QAAQ,EAAE,MAAM;UAChBF,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAE3E,WAAW,GAAG,aAAa,GAAG,SAAS;UAC/C4E,UAAU,EAAE,eAAe;UAC3Bf,OAAO,EAAE,MAAM;UACfY,UAAU,EAAE,QAAQ;UACpBqC,GAAG,EAAE;QACP,CAAE;QAAAvD,QAAA,EAEDvD,WAAW,gBACVX,OAAA,CAAAE,SAAA;UAAAgE,QAAA,gBACElE,OAAA;YAAM4D,KAAK,EAAE;cACXqD,KAAK,EAAE,MAAM;cACbhC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,uBAAuB;cAC/BgD,SAAS,EAAE,iBAAiB;cAC5BnE,YAAY,EAAE,KAAK;cACnBoE,SAAS,EAAE;YACb;UAAE;YAAAhE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,yBAEZ;QAAA,eAAE,CAAC,GAEH;MACD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGPtE,OAAA;MAAK4D,KAAK,EAAE;QACVY,OAAO,EAAE,MAAM;QACfY,UAAU,EAAE,QAAQ;QACpBkB,MAAM,EAAE,QAAQ;QAChB5B,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE;MACZ,CAAE;MAAAT,QAAA,gBACAlE,OAAA;QAAK4D,KAAK,EAAE;UAAEwE,IAAI,EAAE,CAAC;UAAEnD,MAAM,EAAE,KAAK;UAAEpB,UAAU,EAAE;QAAU;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrEtE,OAAA;QAAM4D,KAAK,EAAE;UAAEE,OAAO,EAAE;QAAS,CAAE;QAAAI,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7CtE,OAAA;QAAK4D,KAAK,EAAE;UAAEwE,IAAI,EAAE,CAAC;UAAEnD,MAAM,EAAE,KAAK;UAAEpB,UAAU,EAAE;QAAU;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE,CAAC,eAGNtE,OAAA;MAAK4D,KAAK,EAAE;QAAEyC,SAAS,EAAE;MAAS,CAAE;MAAAnC,QAAA,gBAClClE,OAAA;QAAI4D,KAAK,EAAE;UAAE0C,MAAM,EAAE,YAAY;UAAE5B,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAEtE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLtE,OAAA;QAAG4D,KAAK,EAAE;UAAE0C,MAAM,EAAE,YAAY;UAAE5B,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAErE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJtE,OAAA;QACEkG,IAAI,EAAC,QAAQ;QACbrB,OAAO,EAAElB,wBAAyB;QAClCsE,QAAQ,EAAEtH,WAAY;QACtBiD,KAAK,EAAE;UACLC,UAAU,EAAElD,WAAW,GAAG,SAAS,GAAG,SAAS;UAC/C+D,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdpB,OAAO,EAAE,WAAW;UACpBC,YAAY,EAAE,KAAK;UACnBY,QAAQ,EAAE,MAAM;UAChBF,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAE3E,WAAW,GAAG,aAAa,GAAG,SAAS;UAC/C4E,UAAU,EAAE,eAAe;UAC3Bf,OAAO,EAAE,MAAM;UACfY,UAAU,EAAE,QAAQ;UACpBqC,GAAG,EAAE,KAAK;UACVnB,MAAM,EAAE;QACV,CAAE;QAAApC,QAAA,EAEDvD,WAAW,gBACVX,OAAA,CAAAE,SAAA;UAAAgE,QAAA,gBACElE,OAAA;YAAM4D,KAAK,EAAE;cACXqD,KAAK,EAAE,MAAM;cACbhC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,uBAAuB;cAC/BgD,SAAS,EAAE,iBAAiB;cAC5BnE,YAAY,EAAE,KAAK;cACnBoE,SAAS,EAAE;YACb;UAAE;YAAAhE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,yBAEZ;QAAA,eAAE,CAAC,gBAEHtE,OAAA,CAAAE,SAAA;UAAAgE,QAAA,EAAE;QAEF,gBAAE;MACH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENtE,OAAA;MAAOqI,GAAG;MAAAnE,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAChE,EAAA,CAhiBIH,YAAY;AAAAmI,EAAA,GAAZnI,YAAY;AAkiBlB,eAAeA,YAAY;AAAC,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}