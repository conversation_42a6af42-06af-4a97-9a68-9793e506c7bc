# UI自动化测试平台使用指南

## 🚀 快速开始

### 1. 启动系统

**启动后端服务**:
```bash
cd backend
uvicorn app.main:app --reload --port 8000
```

**启动前端服务**:
```bash
cd frontend
npm start
```

**访问地址**: http://localhost:3000

### 2. 首次配置

1. 点击导航栏的 "⚙️ 设置" 进入设置页面
2. 在 "AI模型配置" 部分填入以下信息：
   - **API密钥**: 您的通义千问API密钥
   - **API基础URL**: `https://dashscope.aliyuncs.com/compatible-mode/v1`
   - **模型名称**: `qwen-vl-max-latest`
3. 点击 "保存AI配置" 完成配置

## 📋 功能使用

### UI界面分析

1. **进入分析页面**
   - 点击导航栏的 "🔍 UI分析" 或直接访问首页

2. **上传界面截图**
   - 点击上传区域或拖拽图片文件
   - 支持格式: JPG, PNG, GIF, BMP, WebP
   - 文件大小限制: 10MB

3. **填写分析参数**
   - **界面功能描述**: 详细描述界面的功能和用途
   - **项目名称**: (可选) 项目或模块名称
   - **测试类型**: 功能测试、回归测试等
   - **优先级**: 高、中、低
   - **预期元素数量**: (可选) 预期识别的元素数量
   - **特殊要求**: (可选) 特殊的测试要求

4. **开始分析**
   - 点击 "开始分析" 按钮
   - 系统将显示实时分析进度
   - 可以看到三个智能体的执行状态

5. **查看结果**
   - 分析完成后自动跳转到结果页面
   - 可以查看识别的UI元素、交互流程和生成的测试脚本

### 历史记录管理

1. **查看历史记录**
   - 点击导航栏的 "📚 历史记录"
   - 查看所有已生成的测试脚本

2. **脚本操作**
   - **运行脚本**: 点击绿色 "运行脚本" 按钮在线执行
   - **查看报告**: 点击蓝色 "查看报告" 按钮查看测试报告
   - **运行记录**: 点击黄色 "运行记录" 按钮查看历史运行记录
   - **下载脚本**: 点击蓝色 "下载脚本" 按钮下载YAML格式脚本

3. **查看详情**
   - 点击 "查看详情" 查看完整的分析结果
   - 包含UI元素列表、交互流程和脚本内容

## 🎯 最佳实践

### 界面截图建议

1. **清晰度要求**
   - 使用高分辨率截图 (建议1920x1080以上)
   - 确保文字和按钮清晰可见
   - 避免模糊或压缩过度的图片

2. **内容完整性**
   - 截图应包含完整的功能区域
   - 包含相关的导航和操作按钮
   - 避免截取部分界面

3. **状态一致性**
   - 使用典型的界面状态
   - 避免加载中或错误状态的截图
   - 确保界面元素处于可交互状态

### 描述编写技巧

1. **功能描述**
   ```
   ✅ 好的描述: "用户登录界面，包含用户名输入框、密码输入框和登录按钮，用户可以输入凭据进行系统登录"
   ❌ 简单描述: "登录页面"
   ```

2. **元素描述**
   ```
   ✅ 详细描述: "页面右上角的蓝色登录按钮，圆角矩形，白色文字'登录'"
   ❌ 模糊描述: "登录按钮"
   ```

3. **流程描述**
   ```
   ✅ 完整流程: "用户首先输入用户名，然后输入密码，最后点击登录按钮提交表单"
   ❌ 简单流程: "登录操作"
   ```

## 🔧 高级功能

### 脚本自定义

生成的脚本采用MidScene.js格式，支持以下操作类型：

- **aiTap**: 智能点击操作
- **aiInput**: 智能输入操作
- **aiAssert**: 智能断言验证
- **aiQuery**: 智能查询操作
- **aiKeyboardPress**: 键盘操作

### 批量处理

1. **多文件分析**
   - 可以连续上传多个界面截图
   - 系统会为每个文件生成独立的分析任务
   - 在历史记录中统一管理

2. **脚本合并**
   - 可以手动合并相关的测试脚本
   - 形成完整的用户流程测试

### 集成开发

1. **API调用**
   ```bash
   # 上传分析
   curl -X POST "http://localhost:8000/api/v1/upload" \
        -F "image_file=@screenshot.png" \
        -F "description=登录界面分析"
   
   # 获取脚本列表
   curl "http://localhost:8000/api/v1/scripts"
   
   # 运行脚本
   curl -X POST "http://localhost:8000/api/v1/scripts/{script_id}/run"
   ```

2. **Webhook集成**
   - 可以配置Webhook接收分析完成通知
   - 支持集成到CI/CD流水线

## 🐛 故障排除

### 常见问题

1. **AI模型调用失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 验证API配额是否充足
   - 系统会自动切换到模拟模式

2. **脚本运行失败**
   - 确保Playwright环境已正确安装
   - 检查浏览器驱动是否可用
   - 查看详细错误日志

3. **文件上传失败**
   - 检查文件格式是否支持
   - 确认文件大小不超过10MB
   - 验证网络连接稳定

4. **前端页面无法访问**
   - 确认前端服务已启动 (npm start)
   - 检查端口3000是否被占用
   - 清除浏览器缓存

5. **后端API无响应**
   - 确认后端服务已启动 (uvicorn)
   - 检查端口8000是否被占用
   - 查看后端日志输出

### 日志查看

1. **后端日志**
   - 在后端启动终端查看实时日志
   - 包含API调用、AI模型交互、错误信息

2. **前端日志**
   - 打开浏览器开发者工具 (F12)
   - 查看Console标签页的日志信息

3. **脚本运行日志**
   - 在历史记录页面查看运行记录
   - 包含详细的执行步骤和结果

## 📞 技术支持

如遇到问题，请按以下步骤操作：

1. 查看本使用指南的故障排除部分
2. 检查系统日志获取详细错误信息
3. 确认环境配置是否正确
4. 联系技术支持团队

---

**使用指南版本**: v1.0.0  
**最后更新**: 2024-12-18  
**适用系统版本**: UI自动化测试平台 v1.0.0
