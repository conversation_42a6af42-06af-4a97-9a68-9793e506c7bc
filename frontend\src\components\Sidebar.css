.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 280px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transition: all 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar.collapsed {
  width: 70px;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 32px;
  line-height: 1;
}

.logo-text h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.2;
}

.logo-text p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
  line-height: 1.2;
}

.collapse-btn {
  background: rgba(255,255,255,0.1);
  border: none;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 14px;
}

.collapse-btn:hover {
  background: rgba(255,255,255,0.2);
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin-bottom: 8px;
}

.nav-link {
  width: 100%;
  background: none;
  border: none;
  color: white;
  padding: 16px 20px;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s ease;
  border-radius: 0;
  position: relative;
}

.nav-link:hover {
  background: rgba(255,255,255,0.1);
}

.nav-link.active {
  background: rgba(255,255,255,0.2);
  border-right: 3px solid white;
}

.nav-icon {
  font-size: 20px;
  line-height: 1;
  min-width: 20px;
}

.nav-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.nav-name {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.2;
}

.nav-desc {
  font-size: 12px;
  opacity: 0.8;
  line-height: 1.2;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(255,255,255,0.1);
}

.version-info p {
  margin: 0;
  font-size: 12px;
  opacity: 0.7;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    transform: translateX(-100%);
  }
  
  .sidebar.collapsed {
    width: 100%;
    transform: translateX(-100%);
  }
  
  .sidebar.mobile-open {
    transform: translateX(0);
  }
}
