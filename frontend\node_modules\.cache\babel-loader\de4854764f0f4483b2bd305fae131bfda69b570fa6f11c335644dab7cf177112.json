{"ast": null, "code": "/**\n * 历史记录页面\n */import React,{useState,useEffect}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const HistoryPage=()=>{const[historyData,setHistoryData]=useState([]);const[loading,setLoading]=useState(true);const[selectedItem,setSelectedItem]=useState(null);useEffect(()=>{// 模拟加载历史数据\nsetTimeout(()=>{setHistoryData([{id:'1',taskId:'task-001',fileName:'登录界面.png',description:'用户登录界面分析',status:'completed',createdAt:'2024-12-17 10:30:00',elementsCount:5,flowsCount:2,scriptsCount:1},{id:'2',taskId:'task-002',fileName:'文件管理界面.png',description:'文件管理界面功能分析',status:'completed',createdAt:'2024-12-17 14:15:00',elementsCount:8,flowsCount:3,scriptsCount:2},{id:'3',taskId:'task-003',fileName:'购物车页面.png',description:'电商购物车界面分析',status:'failed',createdAt:'2024-12-17 16:45:00',elementsCount:0,flowsCount:0,scriptsCount:0}]);setLoading(false);},1000);},[]);const getStatusColor=status=>{switch(status){case'completed':return'#28a745';case'failed':return'#dc3545';case'processing':return'#007bff';default:return'#6c757d';}};const getStatusText=status=>{switch(status){case'completed':return'已完成';case'failed':return'失败';case'processing':return'处理中';default:return'未知';}};const handleViewDetails=item=>{setSelectedItem(item);};const handleDownloadScript=item=>{// 模拟下载脚本\nconst scriptContent=\"# \".concat(item.description,\"\\n# \\u751F\\u6210\\u65F6\\u95F4: \").concat(item.createdAt,\"\\n\\nname: \").concat(item.description,\"\\ndescription: \\u81EA\\u52A8\\u5316\\u6D4B\\u8BD5\\u811A\\u672C\\n\\nsteps:\\n  - name: \\u793A\\u4F8B\\u6B65\\u9AA4\\n    action: aiTap\\n    locate: \\u793A\\u4F8B\\u5143\\u7D20\\n    expect: \\u9884\\u671F\\u7ED3\\u679C\");const blob=new Blob([scriptContent],{type:'text/yaml'});const url=URL.createObjectURL(blob);const a=document.createElement('a');a.href=url;a.download=\"\".concat(item.fileName.replace('.png',''),\"_script.yaml\");document.body.appendChild(a);a.click();document.body.removeChild(a);URL.revokeObjectURL(url);};if(loading){return/*#__PURE__*/_jsxs(\"div\",{className:\"history-page\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"page-header\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"\\uD83D\\uDCCB \\u5206\\u6790\\u5386\\u53F2\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u67E5\\u770B\\u6240\\u6709UI\\u5206\\u6790\\u8BB0\\u5F55\\u548C\\u7ED3\\u679C\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"loading-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u52A0\\u8F7D\\u5386\\u53F2\\u8BB0\\u5F55\\u4E2D...\"})]})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"history-page\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"page-header\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"\\uD83D\\uDCCB \\u5206\\u6790\\u5386\\u53F2\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u67E5\\u770B\\u6240\\u6709UI\\u5206\\u6790\\u8BB0\\u5F55\\u548C\\u7ED3\\u679C\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"page-content\",children:historyData.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"empty-state\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"empty-icon\",children:\"\\uD83D\\uDCDD\"}),/*#__PURE__*/_jsx(\"h3\",{children:\"\\u6682\\u65E0\\u5206\\u6790\\u8BB0\\u5F55\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u5F00\\u59CB\\u60A8\\u7684\\u7B2C\\u4E00\\u6B21UI\\u5206\\u6790\\u5427\\uFF01\"})]}):/*#__PURE__*/_jsx(\"div\",{className:\"history-list\",children:historyData.map(item=>/*#__PURE__*/_jsxs(\"div\",{className:\"history-item\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"item-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"item-info\",children:[/*#__PURE__*/_jsx(\"h3\",{children:item.fileName}),/*#__PURE__*/_jsx(\"p\",{children:item.description})]}),/*#__PURE__*/_jsx(\"div\",{className:\"item-status\",children:/*#__PURE__*/_jsx(\"span\",{className:\"status-badge\",style:{backgroundColor:getStatusColor(item.status)},children:getStatusText(item.status)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"item-stats\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"UI\\u5143\\u7D20\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value\",children:item.elementsCount})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"\\u4EA4\\u4E92\\u6D41\\u7A0B\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value\",children:item.flowsCount})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"\\u6D4B\\u8BD5\\u811A\\u672C\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value\",children:item.scriptsCount})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"item-footer\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"item-time\",children:[\"\\u521B\\u5EFA\\u65F6\\u95F4: \",item.createdAt]}),/*#__PURE__*/_jsxs(\"div\",{className:\"item-actions\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"btn-secondary\",onClick:()=>handleViewDetails(item),children:\"\\u67E5\\u770B\\u8BE6\\u60C5\"}),item.status==='completed'&&/*#__PURE__*/_jsx(\"button\",{className:\"btn-primary\",onClick:()=>handleDownloadScript(item),children:\"\\u4E0B\\u8F7D\\u811A\\u672C\"})]})]})]},item.id))})}),/*#__PURE__*/_jsx(\"style\",{children:\"\\n        .history-page {\\n          min-height: 100vh;\\n          background: #f8f9fa;\\n        }\\n\\n        .page-header {\\n          background: white;\\n          border-bottom: 1px solid #e9ecef;\\n          padding: 24px 0;\\n          margin-bottom: 24px;\\n        }\\n\\n        .page-header h1 {\\n          margin: 0 0 8px 0;\\n          color: #333;\\n          font-size: 28px;\\n          font-weight: 600;\\n          max-width: 1200px;\\n          margin-left: auto;\\n          margin-right: auto;\\n          padding: 0 24px;\\n        }\\n\\n        .page-header p {\\n          margin: 0;\\n          color: #666;\\n          font-size: 16px;\\n          max-width: 1200px;\\n          margin-left: auto;\\n          margin-right: auto;\\n          padding: 0 24px;\\n        }\\n\\n        .page-content {\\n          max-width: 1200px;\\n          margin: 0 auto;\\n          padding: 0 24px;\\n        }\\n\\n        .loading-container {\\n          text-align: center;\\n          padding: 60px 20px;\\n        }\\n\\n        .loading-spinner {\\n          width: 40px;\\n          height: 40px;\\n          border: 4px solid #e9ecef;\\n          border-top: 4px solid #007bff;\\n          border-radius: 50%;\\n          animation: spin 1s linear infinite;\\n          margin: 0 auto 16px;\\n        }\\n\\n        .empty-state {\\n          text-align: center;\\n          padding: 60px 20px;\\n          background: white;\\n          border-radius: 12px;\\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\\n        }\\n\\n        .empty-icon {\\n          font-size: 48px;\\n          margin-bottom: 16px;\\n        }\\n\\n        .empty-state h3 {\\n          margin: 0 0 8px 0;\\n          color: #333;\\n          font-size: 20px;\\n        }\\n\\n        .empty-state p {\\n          margin: 0;\\n          color: #666;\\n          font-size: 16px;\\n        }\\n\\n        .history-list {\\n          display: grid;\\n          gap: 16px;\\n        }\\n\\n        .history-item {\\n          background: white;\\n          border-radius: 12px;\\n          padding: 20px;\\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\\n          transition: all 0.2s ease;\\n        }\\n\\n        .history-item:hover {\\n          box-shadow: 0 4px 20px rgba(0,0,0,0.15);\\n        }\\n\\n        .item-header {\\n          display: flex;\\n          justify-content: space-between;\\n          align-items: flex-start;\\n          margin-bottom: 16px;\\n        }\\n\\n        .item-info h3 {\\n          margin: 0 0 4px 0;\\n          color: #333;\\n          font-size: 18px;\\n          font-weight: 600;\\n        }\\n\\n        .item-info p {\\n          margin: 0;\\n          color: #666;\\n          font-size: 14px;\\n        }\\n\\n        .status-badge {\\n          color: white;\\n          padding: 4px 12px;\\n          border-radius: 20px;\\n          font-size: 12px;\\n          font-weight: 500;\\n        }\\n\\n        .item-stats {\\n          display: flex;\\n          gap: 24px;\\n          margin-bottom: 16px;\\n          padding: 16px 0;\\n          border-top: 1px solid #e9ecef;\\n          border-bottom: 1px solid #e9ecef;\\n        }\\n\\n        .stat {\\n          display: flex;\\n          flex-direction: column;\\n          align-items: center;\\n          gap: 4px;\\n        }\\n\\n        .stat-label {\\n          font-size: 12px;\\n          color: #666;\\n        }\\n\\n        .stat-value {\\n          font-size: 18px;\\n          font-weight: 600;\\n          color: #333;\\n        }\\n\\n        .item-footer {\\n          display: flex;\\n          justify-content: space-between;\\n          align-items: center;\\n        }\\n\\n        .item-time {\\n          font-size: 12px;\\n          color: #999;\\n        }\\n\\n        .item-actions {\\n          display: flex;\\n          gap: 8px;\\n        }\\n\\n        .btn-primary, .btn-secondary {\\n          border: none;\\n          padding: 8px 16px;\\n          border-radius: 6px;\\n          font-size: 14px;\\n          cursor: pointer;\\n          transition: all 0.2s ease;\\n        }\\n\\n        .btn-primary {\\n          background: #007bff;\\n          color: white;\\n        }\\n\\n        .btn-primary:hover {\\n          background: #0056b3;\\n        }\\n\\n        .btn-secondary {\\n          background: #6c757d;\\n          color: white;\\n        }\\n\\n        .btn-secondary:hover {\\n          background: #5a6268;\\n        }\\n\\n        @keyframes spin {\\n          0% { transform: rotate(0deg); }\\n          100% { transform: rotate(360deg); }\\n        }\\n\\n        /* \\u54CD\\u5E94\\u5F0F\\u8BBE\\u8BA1 */\\n        @media (max-width: 768px) {\\n          .page-header h1, .page-header p, .page-content {\\n            padding: 0 16px;\\n          }\\n\\n          .item-header {\\n            flex-direction: column;\\n            gap: 12px;\\n          }\\n\\n          .item-stats {\\n            justify-content: space-around;\\n          }\\n\\n          .item-footer {\\n            flex-direction: column;\\n            gap: 12px;\\n            align-items: flex-start;\\n          }\\n        }\\n      \"})]});};export default HistoryPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "HistoryPage", "historyData", "setHistoryData", "loading", "setLoading", "selectedItem", "setSelectedItem", "setTimeout", "id", "taskId", "fileName", "description", "status", "createdAt", "elementsCount", "flowsCount", "scriptsCount", "getStatusColor", "getStatusText", "handleViewDetails", "item", "handleDownloadScript", "scriptContent", "concat", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "replace", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "className", "children", "length", "map", "style", "backgroundColor", "onClick"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/pages/HistoryPage.js"], "sourcesContent": ["/**\n * 历史记录页面\n */\nimport React, { useState, useEffect } from 'react';\n\nconst HistoryPage = () => {\n  const [historyData, setHistoryData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedItem, setSelectedItem] = useState(null);\n\n  useEffect(() => {\n    // 模拟加载历史数据\n    setTimeout(() => {\n      setHistoryData([\n        {\n          id: '1',\n          taskId: 'task-001',\n          fileName: '登录界面.png',\n          description: '用户登录界面分析',\n          status: 'completed',\n          createdAt: '2024-12-17 10:30:00',\n          elementsCount: 5,\n          flowsCount: 2,\n          scriptsCount: 1\n        },\n        {\n          id: '2',\n          taskId: 'task-002',\n          fileName: '文件管理界面.png',\n          description: '文件管理界面功能分析',\n          status: 'completed',\n          createdAt: '2024-12-17 14:15:00',\n          elementsCount: 8,\n          flowsCount: 3,\n          scriptsCount: 2\n        },\n        {\n          id: '3',\n          taskId: 'task-003',\n          fileName: '购物车页面.png',\n          description: '电商购物车界面分析',\n          status: 'failed',\n          createdAt: '2024-12-17 16:45:00',\n          elementsCount: 0,\n          flowsCount: 0,\n          scriptsCount: 0\n        }\n      ]);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'completed':\n        return '#28a745';\n      case 'failed':\n        return '#dc3545';\n      case 'processing':\n        return '#007bff';\n      default:\n        return '#6c757d';\n    }\n  };\n\n  const getStatusText = (status) => {\n    switch (status) {\n      case 'completed':\n        return '已完成';\n      case 'failed':\n        return '失败';\n      case 'processing':\n        return '处理中';\n      default:\n        return '未知';\n    }\n  };\n\n  const handleViewDetails = (item) => {\n    setSelectedItem(item);\n  };\n\n  const handleDownloadScript = (item) => {\n    // 模拟下载脚本\n    const scriptContent = `# ${item.description}\\n# 生成时间: ${item.createdAt}\\n\\nname: ${item.description}\\ndescription: 自动化测试脚本\\n\\nsteps:\\n  - name: 示例步骤\\n    action: aiTap\\n    locate: 示例元素\\n    expect: 预期结果`;\n    \n    const blob = new Blob([scriptContent], { type: 'text/yaml' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `${item.fileName.replace('.png', '')}_script.yaml`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"history-page\">\n        <div className=\"page-header\">\n          <h1>📋 分析历史</h1>\n          <p>查看所有UI分析记录和结果</p>\n        </div>\n        <div className=\"loading-container\">\n          <div className=\"loading-spinner\"></div>\n          <p>加载历史记录中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"history-page\">\n      <div className=\"page-header\">\n        <h1>📋 分析历史</h1>\n        <p>查看所有UI分析记录和结果</p>\n      </div>\n\n      <div className=\"page-content\">\n        {historyData.length === 0 ? (\n          <div className=\"empty-state\">\n            <div className=\"empty-icon\">📝</div>\n            <h3>暂无分析记录</h3>\n            <p>开始您的第一次UI分析吧！</p>\n          </div>\n        ) : (\n          <div className=\"history-list\">\n            {historyData.map((item) => (\n              <div key={item.id} className=\"history-item\">\n                <div className=\"item-header\">\n                  <div className=\"item-info\">\n                    <h3>{item.fileName}</h3>\n                    <p>{item.description}</p>\n                  </div>\n                  <div className=\"item-status\">\n                    <span \n                      className=\"status-badge\"\n                      style={{ backgroundColor: getStatusColor(item.status) }}\n                    >\n                      {getStatusText(item.status)}\n                    </span>\n                  </div>\n                </div>\n                \n                <div className=\"item-stats\">\n                  <div className=\"stat\">\n                    <span className=\"stat-label\">UI元素</span>\n                    <span className=\"stat-value\">{item.elementsCount}</span>\n                  </div>\n                  <div className=\"stat\">\n                    <span className=\"stat-label\">交互流程</span>\n                    <span className=\"stat-value\">{item.flowsCount}</span>\n                  </div>\n                  <div className=\"stat\">\n                    <span className=\"stat-label\">测试脚本</span>\n                    <span className=\"stat-value\">{item.scriptsCount}</span>\n                  </div>\n                </div>\n\n                <div className=\"item-footer\">\n                  <span className=\"item-time\">创建时间: {item.createdAt}</span>\n                  <div className=\"item-actions\">\n                    <button \n                      className=\"btn-secondary\"\n                      onClick={() => handleViewDetails(item)}\n                    >\n                      查看详情\n                    </button>\n                    {item.status === 'completed' && (\n                      <button \n                        className=\"btn-primary\"\n                        onClick={() => handleDownloadScript(item)}\n                      >\n                        下载脚本\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      <style>{`\n        .history-page {\n          min-height: 100vh;\n          background: #f8f9fa;\n        }\n\n        .page-header {\n          background: white;\n          border-bottom: 1px solid #e9ecef;\n          padding: 24px 0;\n          margin-bottom: 24px;\n        }\n\n        .page-header h1 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 28px;\n          font-weight: 600;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-header p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-content {\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 0 24px;\n        }\n\n        .loading-container {\n          text-align: center;\n          padding: 60px 20px;\n        }\n\n        .loading-spinner {\n          width: 40px;\n          height: 40px;\n          border: 4px solid #e9ecef;\n          border-top: 4px solid #007bff;\n          border-radius: 50%;\n          animation: spin 1s linear infinite;\n          margin: 0 auto 16px;\n        }\n\n        .empty-state {\n          text-align: center;\n          padding: 60px 20px;\n          background: white;\n          border-radius: 12px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n        }\n\n        .empty-icon {\n          font-size: 48px;\n          margin-bottom: 16px;\n        }\n\n        .empty-state h3 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 20px;\n        }\n\n        .empty-state p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n        }\n\n        .history-list {\n          display: grid;\n          gap: 16px;\n        }\n\n        .history-item {\n          background: white;\n          border-radius: 12px;\n          padding: 20px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n          transition: all 0.2s ease;\n        }\n\n        .history-item:hover {\n          box-shadow: 0 4px 20px rgba(0,0,0,0.15);\n        }\n\n        .item-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 16px;\n        }\n\n        .item-info h3 {\n          margin: 0 0 4px 0;\n          color: #333;\n          font-size: 18px;\n          font-weight: 600;\n        }\n\n        .item-info p {\n          margin: 0;\n          color: #666;\n          font-size: 14px;\n        }\n\n        .status-badge {\n          color: white;\n          padding: 4px 12px;\n          border-radius: 20px;\n          font-size: 12px;\n          font-weight: 500;\n        }\n\n        .item-stats {\n          display: flex;\n          gap: 24px;\n          margin-bottom: 16px;\n          padding: 16px 0;\n          border-top: 1px solid #e9ecef;\n          border-bottom: 1px solid #e9ecef;\n        }\n\n        .stat {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          gap: 4px;\n        }\n\n        .stat-label {\n          font-size: 12px;\n          color: #666;\n        }\n\n        .stat-value {\n          font-size: 18px;\n          font-weight: 600;\n          color: #333;\n        }\n\n        .item-footer {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n        }\n\n        .item-time {\n          font-size: 12px;\n          color: #999;\n        }\n\n        .item-actions {\n          display: flex;\n          gap: 8px;\n        }\n\n        .btn-primary, .btn-secondary {\n          border: none;\n          padding: 8px 16px;\n          border-radius: 6px;\n          font-size: 14px;\n          cursor: pointer;\n          transition: all 0.2s ease;\n        }\n\n        .btn-primary {\n          background: #007bff;\n          color: white;\n        }\n\n        .btn-primary:hover {\n          background: #0056b3;\n        }\n\n        .btn-secondary {\n          background: #6c757d;\n          color: white;\n        }\n\n        .btn-secondary:hover {\n          background: #5a6268;\n        }\n\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n\n        /* 响应式设计 */\n        @media (max-width: 768px) {\n          .page-header h1, .page-header p, .page-content {\n            padding: 0 16px;\n          }\n\n          .item-header {\n            flex-direction: column;\n            gap: 12px;\n          }\n\n          .item-stats {\n            justify-content: space-around;\n          }\n\n          .item-footer {\n            flex-direction: column;\n            gap: 12px;\n            align-items: flex-start;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default HistoryPage;\n"], "mappings": "AAAA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnD,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGR,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACS,OAAO,CAAEC,UAAU,CAAC,CAAGV,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACW,YAAY,CAAEC,eAAe,CAAC,CAAGZ,QAAQ,CAAC,IAAI,CAAC,CAEtDC,SAAS,CAAC,IAAM,CACd;AACAY,UAAU,CAAC,IAAM,CACfL,cAAc,CAAC,CACb,CACEM,EAAE,CAAE,GAAG,CACPC,MAAM,CAAE,UAAU,CAClBC,QAAQ,CAAE,UAAU,CACpBC,WAAW,CAAE,UAAU,CACvBC,MAAM,CAAE,WAAW,CACnBC,SAAS,CAAE,qBAAqB,CAChCC,aAAa,CAAE,CAAC,CAChBC,UAAU,CAAE,CAAC,CACbC,YAAY,CAAE,CAChB,CAAC,CACD,CACER,EAAE,CAAE,GAAG,CACPC,MAAM,CAAE,UAAU,CAClBC,QAAQ,CAAE,YAAY,CACtBC,WAAW,CAAE,YAAY,CACzBC,MAAM,CAAE,WAAW,CACnBC,SAAS,CAAE,qBAAqB,CAChCC,aAAa,CAAE,CAAC,CAChBC,UAAU,CAAE,CAAC,CACbC,YAAY,CAAE,CAChB,CAAC,CACD,CACER,EAAE,CAAE,GAAG,CACPC,MAAM,CAAE,UAAU,CAClBC,QAAQ,CAAE,WAAW,CACrBC,WAAW,CAAE,WAAW,CACxBC,MAAM,CAAE,QAAQ,CAChBC,SAAS,CAAE,qBAAqB,CAChCC,aAAa,CAAE,CAAC,CAChBC,UAAU,CAAE,CAAC,CACbC,YAAY,CAAE,CAChB,CAAC,CACF,CAAC,CACFZ,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAa,cAAc,CAAIL,MAAM,EAAK,CACjC,OAAQA,MAAM,EACZ,IAAK,WAAW,CACd,MAAO,SAAS,CAClB,IAAK,QAAQ,CACX,MAAO,SAAS,CAClB,IAAK,YAAY,CACf,MAAO,SAAS,CAClB,QACE,MAAO,SAAS,CACpB,CACF,CAAC,CAED,KAAM,CAAAM,aAAa,CAAIN,MAAM,EAAK,CAChC,OAAQA,MAAM,EACZ,IAAK,WAAW,CACd,MAAO,KAAK,CACd,IAAK,QAAQ,CACX,MAAO,IAAI,CACb,IAAK,YAAY,CACf,MAAO,KAAK,CACd,QACE,MAAO,IAAI,CACf,CACF,CAAC,CAED,KAAM,CAAAO,iBAAiB,CAAIC,IAAI,EAAK,CAClCd,eAAe,CAACc,IAAI,CAAC,CACvB,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAID,IAAI,EAAK,CACrC;AACA,KAAM,CAAAE,aAAa,MAAAC,MAAA,CAAQH,IAAI,CAACT,WAAW,mCAAAY,MAAA,CAAaH,IAAI,CAACP,SAAS,eAAAU,MAAA,CAAaH,IAAI,CAACT,WAAW,0MAAyG,CAE5M,KAAM,CAAAa,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACH,aAAa,CAAC,CAAE,CAAEI,IAAI,CAAE,WAAY,CAAC,CAAC,CAC7D,KAAM,CAAAC,GAAG,CAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC,CACrC,KAAM,CAAAM,CAAC,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACrCF,CAAC,CAACG,IAAI,CAAGN,GAAG,CACZG,CAAC,CAACI,QAAQ,IAAAX,MAAA,CAAMH,IAAI,CAACV,QAAQ,CAACyB,OAAO,CAAC,MAAM,CAAE,EAAE,CAAC,gBAAc,CAC/DJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,CAAC,CAAC,CAC5BA,CAAC,CAACQ,KAAK,CAAC,CAAC,CACTP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,CAAC,CAAC,CAC5BF,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC,CAC1B,CAAC,CAED,GAAIxB,OAAO,CAAE,CACX,mBACEJ,KAAA,QAAK0C,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B3C,KAAA,QAAK0C,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B7C,IAAA,OAAA6C,QAAA,CAAI,uCAAO,CAAI,CAAC,cAChB7C,IAAA,MAAA6C,QAAA,CAAG,sEAAa,CAAG,CAAC,EACjB,CAAC,cACN3C,KAAA,QAAK0C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7C,IAAA,QAAK4C,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvC5C,IAAA,MAAA6C,QAAA,CAAG,+CAAU,CAAG,CAAC,EACd,CAAC,EACH,CAAC,CAEV,CAEA,mBACE3C,KAAA,QAAK0C,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B3C,KAAA,QAAK0C,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B7C,IAAA,OAAA6C,QAAA,CAAI,uCAAO,CAAI,CAAC,cAChB7C,IAAA,MAAA6C,QAAA,CAAG,sEAAa,CAAG,CAAC,EACjB,CAAC,cAEN7C,IAAA,QAAK4C,SAAS,CAAC,cAAc,CAAAC,QAAA,CAC1BzC,WAAW,CAAC0C,MAAM,GAAK,CAAC,cACvB5C,KAAA,QAAK0C,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B7C,IAAA,QAAK4C,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACpC7C,IAAA,OAAA6C,QAAA,CAAI,sCAAM,CAAI,CAAC,cACf7C,IAAA,MAAA6C,QAAA,CAAG,sEAAa,CAAG,CAAC,EACjB,CAAC,cAEN7C,IAAA,QAAK4C,SAAS,CAAC,cAAc,CAAAC,QAAA,CAC1BzC,WAAW,CAAC2C,GAAG,CAAExB,IAAI,eACpBrB,KAAA,QAAmB0C,SAAS,CAAC,cAAc,CAAAC,QAAA,eACzC3C,KAAA,QAAK0C,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3C,KAAA,QAAK0C,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB7C,IAAA,OAAA6C,QAAA,CAAKtB,IAAI,CAACV,QAAQ,CAAK,CAAC,cACxBb,IAAA,MAAA6C,QAAA,CAAItB,IAAI,CAACT,WAAW,CAAI,CAAC,EACtB,CAAC,cACNd,IAAA,QAAK4C,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1B7C,IAAA,SACE4C,SAAS,CAAC,cAAc,CACxBI,KAAK,CAAE,CAAEC,eAAe,CAAE7B,cAAc,CAACG,IAAI,CAACR,MAAM,CAAE,CAAE,CAAA8B,QAAA,CAEvDxB,aAAa,CAACE,IAAI,CAACR,MAAM,CAAC,CACvB,CAAC,CACJ,CAAC,EACH,CAAC,cAENb,KAAA,QAAK0C,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB3C,KAAA,QAAK0C,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB7C,IAAA,SAAM4C,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,gBAAI,CAAM,CAAC,cACxC7C,IAAA,SAAM4C,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEtB,IAAI,CAACN,aAAa,CAAO,CAAC,EACrD,CAAC,cACNf,KAAA,QAAK0C,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB7C,IAAA,SAAM4C,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,0BAAI,CAAM,CAAC,cACxC7C,IAAA,SAAM4C,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEtB,IAAI,CAACL,UAAU,CAAO,CAAC,EAClD,CAAC,cACNhB,KAAA,QAAK0C,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB7C,IAAA,SAAM4C,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,0BAAI,CAAM,CAAC,cACxC7C,IAAA,SAAM4C,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEtB,IAAI,CAACJ,YAAY,CAAO,CAAC,EACpD,CAAC,EACH,CAAC,cAENjB,KAAA,QAAK0C,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3C,KAAA,SAAM0C,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAC,4BAAM,CAACtB,IAAI,CAACP,SAAS,EAAO,CAAC,cACzDd,KAAA,QAAK0C,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B7C,IAAA,WACE4C,SAAS,CAAC,eAAe,CACzBM,OAAO,CAAEA,CAAA,GAAM5B,iBAAiB,CAACC,IAAI,CAAE,CAAAsB,QAAA,CACxC,0BAED,CAAQ,CAAC,CACRtB,IAAI,CAACR,MAAM,GAAK,WAAW,eAC1Bf,IAAA,WACE4C,SAAS,CAAC,aAAa,CACvBM,OAAO,CAAEA,CAAA,GAAM1B,oBAAoB,CAACD,IAAI,CAAE,CAAAsB,QAAA,CAC3C,0BAED,CAAQ,CACT,EACE,CAAC,EACH,CAAC,GAjDEtB,IAAI,CAACZ,EAkDV,CACN,CAAC,CACC,CACN,CACE,CAAC,cAENX,IAAA,UAAA6C,QAAA,w2JA6NS,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAA1C,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}