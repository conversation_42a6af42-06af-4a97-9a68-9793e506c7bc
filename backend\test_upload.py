#!/usr/bin/env python3
"""
测试上传功能的脚本
"""
import requests
import sys
from pathlib import Path
from PIL import Image
import io

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_upload_api():
    """测试上传API"""
    print("🧪 测试上传API...")
    
    try:
        # 创建测试图片
        img = Image.new('RGB', (800, 600), color='lightblue')
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG')
        img_bytes.seek(0)
        
        # 准备上传数据
        files = {
            'image_file': ('test_image.png', img_bytes, 'image/png')
        }
        data = {
            'description': '这是一个测试界面，包含登录按钮和输入框',
            'project_name': '测试项目',
            'test_type': 'functional',
            'priority': 'high'
        }
        
        print("  📤 发送上传请求...")
        print(f"    - 描述: {data['description']}")
        print(f"    - 项目: {data['project_name']}")
        
        # 发送请求
        response = requests.post(
            'http://localhost:8003/api/v1/upload',
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"  📨 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ 上传成功!")
            print(f"    - 任务ID: {result.get('task_id', 'N/A')}")
            print(f"    - 消息: {result.get('message', 'N/A')}")
            print(f"    - 流式URL: {result.get('stream_url', 'N/A')}")
            
            # 测试任务状态查询
            task_id = result.get('task_id')
            if task_id:
                print(f"  🔍 查询任务状态...")
                status_response = requests.get(f'http://localhost:8003/api/v1/tasks/{task_id}')
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    print(f"    - 任务状态: {status_data.get('status', 'N/A')}")
                else:
                    print(f"    - 状态查询失败: {status_response.status_code}")
            
            return True
        else:
            print(f"  ❌ 上传失败: {response.status_code}")
            print(f"    - 错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def test_health_check():
    """测试健康检查"""
    print("🏥 测试健康检查...")
    
    try:
        response = requests.get('http://localhost:8003/health', timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ 健康检查通过")
            print(f"    - 状态: {result.get('status', 'N/A')}")
            print(f"    - 消息: {result.get('message', 'N/A')}")
            return True
        else:
            print(f"  ❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 健康检查异常: {e}")
        return False

def test_demo_analyze():
    """测试演示分析接口"""
    print("🎭 测试演示分析接口...")
    
    try:
        # 创建测试图片
        img = Image.new('RGB', (800, 600), color='lightgreen')
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG')
        img_bytes.seek(0)
        
        # 准备上传数据
        files = {
            'image_file': ('demo_image.png', img_bytes, 'image/png')
        }
        data = {
            'description': '演示界面分析功能'
        }
        
        print("  📤 发送演示分析请求...")
        
        # 发送请求
        response = requests.post(
            'http://localhost:8003/api/v1/analyze',
            files=files,
            data=data,
            timeout=30
        )
        
        print(f"  📨 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ 演示分析成功!")
            print(f"    - 任务ID: {result.get('task_id', 'N/A')}")
            print(f"    - 消息: {result.get('message', 'N/A')}")
            
            # 显示分析结果
            analysis_result = result.get('result', {})
            elements = analysis_result.get('elements', [])
            flows = analysis_result.get('flows', [])
            scripts = analysis_result.get('automation_scripts', [])
            
            print(f"    - 识别元素: {len(elements)} 个")
            print(f"    - 交互流程: {len(flows)} 个")
            print(f"    - 自动化脚本: {len(scripts)} 个")
            
            return True
        else:
            print(f"  ❌ 演示分析失败: {response.status_code}")
            print(f"    - 错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"  ❌ 演示分析异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始API功能测试...")
    print("=" * 50)
    
    # 测试健康检查
    health_success = test_health_check()
    print()
    
    # 测试演示分析
    demo_success = test_demo_analyze()
    print()
    
    # 测试上传功能
    upload_success = test_upload_api()
    print()
    
    # 总结
    print("=" * 50)
    print("📊 测试结果总结:")
    print(f"  - 健康检查: {'✅ 通过' if health_success else '❌ 失败'}")
    print(f"  - 演示分析: {'✅ 通过' if demo_success else '❌ 失败'}")
    print(f"  - 上传功能: {'✅ 通过' if upload_success else '❌ 失败'}")
    
    if health_success and demo_success and upload_success:
        print("🎉 所有API测试通过!")
        return 0
    else:
        print("❌ 部分API测试失败!")
        return 1

if __name__ == "__main__":
    exit(main())
