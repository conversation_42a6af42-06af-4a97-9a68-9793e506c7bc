"""
元素识别智能体 - 基于UI-TARS技术识别界面元素
"""
import json
import base64
from typing import List, Dict, Any
from PIL import Image
import os

class ElementDetectionAgent:
    """元素识别智能体"""
    
    def __init__(self):
        self.prompt = self._load_prompt()
    
    def _load_prompt(self) -> str:
        """加载提示词"""
        return """你是UI元素识别专家，专门分析界面截图中的UI组件，为自动化测试提供精确的元素信息。

## 核心职责

### 1. 元素识别与分类
- **交互元素**: 按钮、链接、输入框、下拉菜单、复选框、单选按钮、开关
- **显示元素**: 文本、图片、图标、标签、提示信息
- **容器元素**: 表单、卡片、模态框、侧边栏、导航栏
- **列表元素**: 表格、列表项、菜单项、选项卡

### 2. 视觉特征描述标准
- **颜色**: 主色调、背景色、边框色（如"蓝色按钮"、"红色警告文字"）
- **尺寸**: 相对大小（大、中、小）和具体描述
- **形状**: 圆角、方形、圆形等
- **图标**: 具体图标类型（如"搜索图标"、"用户头像图标"）
- **文字**: 完整的文字内容和字体样式

### 3. 位置定位规范
- **绝对位置**: "页面左上角"、"右下角"、"中央区域"
- **相对位置**: "搜索框右侧"、"表单底部"、"导航栏下方"
- **层级关系**: "主容器内"、"弹窗中"、"侧边栏里"

### 4. 功能用途分析
- **操作类型**: 提交、取消、搜索、筛选、导航等
- **交互状态**: 可点击、禁用、选中、悬停等
- **业务功能**: 登录、注册、购买、编辑等

## 输出格式要求

请严格按照以下JSON格式输出，每个元素包含完整信息：

```json
[
  {
    "id": "element_001",
    "name": "登录按钮",
    "element_type": "button",
    "description": "页面右上角的蓝色圆角按钮，白色文字'登录'，位于搜索框右侧",
    "text_content": "登录",
    "position": {
      "area": "页面右上角",
      "relative_to": "搜索框右侧"
    },
    "visual_features": {
      "color": "蓝色背景，白色文字",
      "size": "中等尺寸",
      "shape": "圆角矩形"
    },
    "functionality": "用户登录入口",
    "interaction_state": "可点击",
    "confidence_score": 0.95
  }
]
```

## 质量标准

- **完整性**: 识别所有可见的交互元素（目标≥90%覆盖率）
- **准确性**: 元素类型和描述准确无误
- **详细性**: 每个元素包含足够的视觉特征用于自动化定位
- **结构化**: 严格遵循JSON格式，便于后续处理
"""
    
    def analyze(self, image_path: str, description: str) -> List[Dict[str, Any]]:
        """
        分析UI界面元素
        
        Args:
            image_path: 图片文件路径
            description: 界面功能描述
            
        Returns:
            识别到的UI元素列表
        """
        try:
            # 这里应该调用实际的AI模型API
            # 为了演示，我们返回模拟数据
            return self._mock_analysis(image_path, description)
            
        except Exception as e:
            print(f"元素识别失败: {e}")
            return []
    
    def _mock_analysis(self, image_path: str, description: str) -> List[Dict[str, Any]]:
        """模拟元素识别结果"""
        # 基于描述生成模拟元素
        mock_elements = [
            {
                "id": "element_001",
                "name": "主要操作按钮",
                "element_type": "button",
                "description": "页面中央的主要操作按钮，蓝色背景，白色文字",
                "text_content": "开始",
                "position": {
                    "area": "页面中央",
                    "relative_to": "主内容区域"
                },
                "visual_features": {
                    "color": "蓝色背景，白色文字",
                    "size": "大尺寸",
                    "shape": "圆角矩形"
                },
                "functionality": "触发主要业务流程",
                "interaction_state": "可点击",
                "confidence_score": 0.95
            },
            {
                "id": "element_002", 
                "name": "输入框",
                "element_type": "input",
                "description": "文本输入框，用于用户输入信息",
                "text_content": "",
                "position": {
                    "area": "页面上方",
                    "relative_to": "表单区域"
                },
                "visual_features": {
                    "color": "白色背景，灰色边框",
                    "size": "中等尺寸",
                    "shape": "矩形"
                },
                "functionality": "接收用户输入",
                "interaction_state": "可输入",
                "confidence_score": 0.90
            },
            {
                "id": "element_003",
                "name": "导航菜单",
                "element_type": "navigation",
                "description": "页面顶部的导航菜单栏",
                "text_content": "首页 | 产品 | 关于我们",
                "position": {
                    "area": "页面顶部",
                    "relative_to": "页面头部"
                },
                "visual_features": {
                    "color": "深色背景，白色文字",
                    "size": "全宽",
                    "shape": "水平条形"
                },
                "functionality": "页面导航",
                "interaction_state": "可点击",
                "confidence_score": 0.88
            }
        ]
        
        # 根据描述调整元素
        if "登录" in description:
            mock_elements.append({
                "id": "element_004",
                "name": "登录按钮",
                "element_type": "button",
                "description": "登录功能按钮",
                "text_content": "登录",
                "position": {
                    "area": "页面右上角",
                    "relative_to": "导航栏"
                },
                "visual_features": {
                    "color": "绿色背景，白色文字",
                    "size": "小尺寸",
                    "shape": "圆角矩形"
                },
                "functionality": "用户登录",
                "interaction_state": "可点击",
                "confidence_score": 0.92
            })
        
        return mock_elements
    
    def _encode_image(self, image_path: str) -> str:
        """将图片编码为base64"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            print(f"图片编码失败: {e}")
            return ""
