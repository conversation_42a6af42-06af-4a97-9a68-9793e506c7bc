"""
脚本存储管理模块
"""
import os
import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from .config import config

logger = logging.getLogger(__name__)

class ScriptManager:
    """脚本存储管理器"""
    
    def __init__(self):
        self.static_folder = config.STATIC_FOLDER
        self.reports_folder = config.REPORTS_FOLDER
        self.scripts_index_file = os.path.join(self.static_folder, 'scripts_index.json')
        self.run_history_file = os.path.join(self.static_folder, 'run_history.json')
        
        # 初始化索引文件
        self._init_index_files()
    
    def _init_index_files(self):
        """初始化索引文件"""
        if not os.path.exists(self.scripts_index_file):
            with open(self.scripts_index_file, 'w', encoding='utf-8') as f:
                json.dump([], f, ensure_ascii=False, indent=2)
        
        if not os.path.exists(self.run_history_file):
            with open(self.run_history_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
    
    def save_script(self, script_data: Dict[str, Any], task_id: str) -> str:
        """保存脚本文件"""
        try:
            script_name = script_data.get('script_name', 'unnamed_script.spec.ts')
            if not script_name.endswith('.spec.ts'):
                script_name += '.spec.ts'
            
            # 生成脚本内容
            script_content = self._generate_playwright_script(script_data)
            
            # 保存脚本文件
            script_path = os.path.join(self.static_folder, script_name)
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            # 更新索引
            self._update_scripts_index(script_data, task_id, script_path)
            
            logger.info(f"脚本已保存: {script_path}")
            return script_path
            
        except Exception as e:
            logger.error(f"保存脚本失败: {e}")
            raise e
    
    def _generate_playwright_script(self, script_data: Dict[str, Any]) -> str:
        """生成Playwright + MidScene.js脚本内容"""
        script_name = script_data.get('script_name', 'unnamed_script')
        description = script_data.get('description', '自动化测试脚本')
        test_steps = script_data.get('test_steps', [])
        yaml_content = script_data.get('yaml_content', '')
        
        # 生成脚本头部
        script_content = f'''import {{ test, expect }} from '@playwright/test';
import {{ MidscenePlaywright }} from '@midscene/playwright';

/**
 * {description}
 * 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
 */

test.describe('{script_name}', () => {{
  let midscene: MidscenePlaywright;

  test.beforeEach(async ({{ page }}) => {{
    midscene = new MidscenePlaywright(page);
  }});

  test('{description}', async ({{ page }}) => {{
    // 测试步骤
'''
        
        # 添加测试步骤
        for i, step in enumerate(test_steps, 1):
            action_type = step.get('action_type', 'ai')
            action_description = step.get('action_description', '')
            visual_target = step.get('visual_target', '')
            expected_result = step.get('expected_result', '')
            
            script_content += f'''
    // 步骤 {i}: {action_description}
    await midscene.{action_type}('{visual_target}');
    
    // 验证: {expected_result}
    await expect(page).toHaveTitle(/{expected_result}/);
'''
        
        # 添加脚本尾部
        script_content += '''
  });
});

'''
        
        # 如果有YAML内容，添加为注释
        if yaml_content:
            script_content += f'''
/*
MidScene.js YAML配置:
{yaml_content}
*/
'''
        
        return script_content
    
    def _update_scripts_index(self, script_data: Dict[str, Any], task_id: str, script_path: str):
        """更新脚本索引"""
        try:
            # 读取现有索引
            with open(self.scripts_index_file, 'r', encoding='utf-8') as f:
                scripts_index = json.load(f)
            
            # 添加新脚本信息
            script_info = {
                'id': task_id,
                'script_name': script_data.get('script_name', ''),
                'original_name': script_data.get('original_name', ''),
                'description': script_data.get('description', ''),
                'file_path': script_path,
                'created_at': datetime.now().isoformat(),
                'priority': script_data.get('priority', 'medium'),
                'estimated_duration': script_data.get('estimated_duration', ''),
                'status': 'created',
                'run_count': 0
            }
            
            scripts_index.append(script_info)
            
            # 保存索引
            with open(self.scripts_index_file, 'w', encoding='utf-8') as f:
                json.dump(scripts_index, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"更新脚本索引失败: {e}")
    
    def get_scripts_list(self) -> List[Dict[str, Any]]:
        """获取脚本列表"""
        try:
            with open(self.scripts_index_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"读取脚本列表失败: {e}")
            return []
    
    def get_script_by_id(self, script_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取脚本信息"""
        scripts = self.get_scripts_list()
        for script in scripts:
            if script.get('id') == script_id:
                return script
        return None
    
    def record_script_run(self, script_id: str, status: str, output: str = '', error: str = ''):
        """记录脚本运行历史"""
        try:
            # 读取运行历史
            with open(self.run_history_file, 'r', encoding='utf-8') as f:
                run_history = json.load(f)
            
            if script_id not in run_history:
                run_history[script_id] = []
            
            # 添加运行记录
            run_record = {
                'timestamp': datetime.now().isoformat(),
                'status': status,
                'output': output,
                'error': error
            }
            
            run_history[script_id].append(run_record)
            
            # 保存运行历史
            with open(self.run_history_file, 'w', encoding='utf-8') as f:
                json.dump(run_history, f, ensure_ascii=False, indent=2)
            
            # 更新脚本索引中的运行次数
            self._update_script_run_count(script_id)
            
        except Exception as e:
            logger.error(f"记录脚本运行历史失败: {e}")
    
    def _update_script_run_count(self, script_id: str):
        """更新脚本运行次数"""
        try:
            scripts_index = self.get_scripts_list()
            for script in scripts_index:
                if script.get('id') == script_id:
                    script['run_count'] = script.get('run_count', 0) + 1
                    break
            
            with open(self.scripts_index_file, 'w', encoding='utf-8') as f:
                json.dump(scripts_index, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"更新脚本运行次数失败: {e}")
    
    def get_script_run_history(self, script_id: str) -> List[Dict[str, Any]]:
        """获取脚本运行历史"""
        try:
            with open(self.run_history_file, 'r', encoding='utf-8') as f:
                run_history = json.load(f)
            return run_history.get(script_id, [])
        except Exception as e:
            logger.error(f"读取脚本运行历史失败: {e}")
            return []

# 全局脚本管理器实例
script_manager = ScriptManager()
