#!/usr/bin/env python3
"""
UI自动化测试平台系统测试脚本
"""
import requests
import json
import time
import os
from pathlib import Path

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def test_ai_config():
    """测试AI配置接口"""
    print("🔧 测试AI配置接口...")
    
    # 获取当前配置
    response = requests.get(f"{API_BASE}/config/ai")
    if response.status_code == 200:
        config = response.json()
        print(f"✅ 当前AI配置: {config}")
    else:
        print(f"❌ 获取AI配置失败: {response.status_code}")
        return False
    
    # 更新配置
    config_data = {
        'api_key': 'sk-3b9cb1dbb58a421082ba5c9f0d6c07d6',
        'base_url': 'https://dashscope.aliyuncs.com/compatible-mode/v1',
        'model_name': 'qwen-vl-max-latest'
    }
    
    response = requests.post(f"{API_BASE}/config/ai", data=config_data)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ AI配置更新成功: {result['message']}")
        return True
    else:
        print(f"❌ 更新AI配置失败: {response.status_code}")
        return False

def test_scripts_api():
    """测试脚本管理接口"""
    print("📝 测试脚本管理接口...")
    
    # 获取脚本列表
    response = requests.get(f"{API_BASE}/scripts")
    if response.status_code == 200:
        scripts = response.json()
        print(f"✅ 获取脚本列表成功，共 {scripts['count']} 个脚本")
        return scripts['scripts']
    else:
        print(f"❌ 获取脚本列表失败: {response.status_code}")
        return []

def test_upload_analysis():
    """测试图片上传和分析功能"""
    print("🖼️ 测试图片上传和分析功能...")
    
    # 创建一个测试图片文件
    test_image_path = "test_image.png"
    if not os.path.exists(test_image_path):
        # 创建一个简单的测试图片（实际应该是真实的UI截图）
        from PIL import Image
        img = Image.new('RGB', (800, 600), color='white')
        img.save(test_image_path)
        print(f"✅ 创建测试图片: {test_image_path}")
    
    # 上传图片进行分析
    with open(test_image_path, 'rb') as f:
        files = {'file': ('test_image.png', f, 'image/png')}
        data = {
            'description': '测试界面分析',
            'analysis_type': 'full'
        }
        
        response = requests.post(f"{API_BASE}/upload", files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 图片上传成功: {result['message']}")
            if 'task_id' in result:
                print(f"📋 任务ID: {result['task_id']}")
                return result['task_id']
            else:
                print("📊 直接返回分析结果")
                return None
        else:
            print(f"❌ 图片上传失败: {response.status_code}")
            try:
                error = response.json()
                print(f"错误详情: {error}")
            except:
                print(f"响应内容: {response.text}")
            return None

def test_element_detection():
    """测试元素识别智能体"""
    print("🔍 测试元素识别智能体...")
    
    try:
        from backend.app.agents.element_detection.agent import ElementDetectionAgent
        
        # 使用模拟模式测试
        agent = ElementDetectionAgent()
        
        # 创建测试图片
        test_image_path = "test_image.png"
        if not os.path.exists(test_image_path):
            from PIL import Image
            img = Image.new('RGB', (800, 600), color='white')
            img.save(test_image_path)
        
        # 执行分析
        elements = agent.analyze(test_image_path, "测试登录界面")
        
        if elements:
            print(f"✅ 元素识别成功，识别到 {len(elements)} 个元素")
            for i, element in enumerate(elements[:3]):  # 只显示前3个
                print(f"  {i+1}. {element.get('type', 'unknown')}: {element.get('description', 'N/A')}")
            return True
        else:
            print("❌ 元素识别失败")
            return False
            
    except Exception as e:
        print(f"❌ 元素识别测试异常: {e}")
        return False

def test_interaction_analysis():
    """测试交互分析智能体"""
    print("🔄 测试交互分析智能体...")
    
    try:
        from backend.app.agents.interaction_analysis.agent import InteractionAnalysisAgent
        
        # 使用模拟模式测试
        agent = InteractionAnalysisAgent()
        
        # 模拟元素数据
        elements = [
            {"type": "button", "description": "登录按钮", "position": {"x": 400, "y": 300}},
            {"type": "input", "description": "用户名输入框", "position": {"x": 400, "y": 200}},
            {"type": "input", "description": "密码输入框", "position": {"x": 400, "y": 250}}
        ]
        
        # 执行分析
        flows = agent.analyze(elements, "用户登录界面")
        
        if flows:
            print(f"✅ 交互分析成功，分析出 {len(flows)} 个交互流程")
            for i, flow in enumerate(flows[:2]):  # 只显示前2个
                print(f"  {i+1}. {flow.get('name', 'unknown')}: {flow.get('description', 'N/A')}")
            return True
        else:
            print("❌ 交互分析失败")
            return False
            
    except Exception as e:
        print(f"❌ 交互分析测试异常: {e}")
        return False

def test_script_generation():
    """测试脚本生成智能体"""
    print("📜 测试脚本生成智能体...")
    
    try:
        from backend.app.agents.test_generation.agent import TestGenerationAgent
        
        # 使用模拟模式测试
        agent = TestGenerationAgent()
        
        # 模拟数据
        elements = [
            {"type": "button", "description": "登录按钮", "position": {"x": 400, "y": 300}},
            {"type": "input", "description": "用户名输入框", "position": {"x": 400, "y": 200}}
        ]
        
        flows = [
            {"name": "用户登录流程", "description": "用户输入凭据并登录", "steps": [
                {"action": "input", "target": "用户名输入框", "value": "<EMAIL>"},
                {"action": "input", "target": "密码输入框", "value": "password"},
                {"action": "click", "target": "登录按钮"}
            ]}
        ]
        
        # 执行分析
        scripts = agent.analyze(elements, flows, "用户登录界面")
        
        if scripts:
            print(f"✅ 脚本生成成功，生成了 {len(scripts)} 个脚本")
            for i, script in enumerate(scripts[:2]):  # 只显示前2个
                print(f"  {i+1}. {script.get('script_name', 'unknown')}")
            return True
        else:
            print("❌ 脚本生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 脚本生成测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始UI自动化测试平台系统测试")
    print("=" * 50)
    
    # 测试结果统计
    results = []
    
    # 1. 测试AI配置
    results.append(("AI配置接口", test_ai_config()))
    
    # 2. 测试脚本管理
    results.append(("脚本管理接口", test_scripts_api()))
    
    # 3. 测试智能体
    results.append(("元素识别智能体", test_element_detection()))
    results.append(("交互分析智能体", test_interaction_analysis()))
    results.append(("脚本生成智能体", test_script_generation()))
    
    # 4. 测试完整流程
    results.append(("图片上传分析", test_upload_analysis()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
    else:
        print("⚠️ 部分测试失败，请检查系统配置。")
    
    # 清理测试文件
    if os.path.exists("test_image.png"):
        os.remove("test_image.png")
        print("🧹 清理测试文件完成")

if __name__ == "__main__":
    main()
