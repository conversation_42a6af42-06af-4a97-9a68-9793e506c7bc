{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\pages\\\\SettingsPage.js\",\n  _s = $RefreshSig$();\n/**\n * 设置页面\n */\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SettingsPage = () => {\n  _s();\n  const [settings, setSettings] = useState({\n    // API设置\n    apiEndpoint: 'http://localhost:8001',\n    timeout: 30,\n    // 分析设置\n    maxFileSize: 10,\n    supportedFormats: ['png', 'jpg', 'jpeg', 'gif'],\n    autoSave: true,\n    // 智能体设置\n    enableElementDetection: true,\n    enableInteractionAnalysis: true,\n    enableScriptGeneration: true,\n    // 界面设置\n    theme: 'light',\n    language: 'zh-CN',\n    showNotifications: true\n  });\n  const [saved, setSaved] = useState(false);\n  const handleSettingChange = (key, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n  const handleArraySettingChange = (key, index, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [key]: prev[key].map((item, i) => i === index ? value : item)\n    }));\n  };\n  const handleSaveSettings = () => {\n    // 模拟保存设置\n    localStorage.setItem('ui-automation-settings', JSON.stringify(settings));\n    setSaved(true);\n    setTimeout(() => setSaved(false), 3000);\n  };\n  const handleResetSettings = () => {\n    if (window.confirm('确定要重置所有设置吗？')) {\n      setSettings({\n        apiEndpoint: 'http://localhost:8001',\n        timeout: 30,\n        maxFileSize: 10,\n        supportedFormats: ['png', 'jpg', 'jpeg', 'gif'],\n        autoSave: true,\n        enableElementDetection: true,\n        enableInteractionAnalysis: true,\n        enableScriptGeneration: true,\n        theme: 'light',\n        language: 'zh-CN',\n        showNotifications: true\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"settings-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\u2699\\uFE0F \\u7CFB\\u7EDF\\u8BBE\\u7F6E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u914D\\u7F6E\\u7CFB\\u7EDF\\u53C2\\u6570\\u548C\\u4E2A\\u4EBA\\u504F\\u597D\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDD17 API\\u8BBE\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"API\\u7AEF\\u70B9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: settings.apiEndpoint,\n              onChange: e => handleSettingChange('apiEndpoint', e.target.value),\n              placeholder: \"http://localhost:8001\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u8BF7\\u6C42\\u8D85\\u65F6 (\\u79D2)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: settings.timeout,\n              onChange: e => handleSettingChange('timeout', parseInt(e.target.value)),\n              min: \"5\",\n              max: \"300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDD0D \\u5206\\u6790\\u8BBE\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u6700\\u5927\\u6587\\u4EF6\\u5927\\u5C0F (MB)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: settings.maxFileSize,\n              onChange: e => handleSettingChange('maxFileSize', parseInt(e.target.value)),\n              min: \"1\",\n              max: \"100\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u81EA\\u52A8\\u4FDD\\u5B58\\u7ED3\\u679C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: settings.autoSave,\n              onChange: e => handleSettingChange('autoSave', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83E\\uDD16 \\u667A\\u80FD\\u4F53\\u8BBE\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u542F\\u7528\\u5143\\u7D20\\u8BC6\\u522B\\u667A\\u80FD\\u4F53\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: settings.enableElementDetection,\n              onChange: e => handleSettingChange('enableElementDetection', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u542F\\u7528\\u4EA4\\u4E92\\u5206\\u6790\\u667A\\u80FD\\u4F53\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: settings.enableInteractionAnalysis,\n              onChange: e => handleSettingChange('enableInteractionAnalysis', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u542F\\u7528\\u811A\\u672C\\u751F\\u6210\\u667A\\u80FD\\u4F53\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: settings.enableScriptGeneration,\n              onChange: e => handleSettingChange('enableScriptGeneration', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83C\\uDFA8 \\u754C\\u9762\\u8BBE\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u4E3B\\u9898\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: settings.theme,\n              onChange: e => handleSettingChange('theme', e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"light\",\n                children: \"\\u6D45\\u8272\\u4E3B\\u9898\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"dark\",\n                children: \"\\u6DF1\\u8272\\u4E3B\\u9898\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"auto\",\n                children: \"\\u8DDF\\u968F\\u7CFB\\u7EDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u8BED\\u8A00\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: settings.language,\n              onChange: e => handleSettingChange('language', e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"zh-CN\",\n                children: \"\\u7B80\\u4F53\\u4E2D\\u6587\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"en-US\",\n                children: \"English\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"setting-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\u663E\\u793A\\u901A\\u77E5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: settings.showNotifications,\n              onChange: e => handleSettingChange('showNotifications', e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-primary\",\n            onClick: handleSaveSettings,\n            children: saved ? '✅ 已保存' : '💾 保存设置'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn-secondary\",\n            onClick: handleResetSettings,\n            children: \"\\uD83D\\uDD04 \\u91CD\\u7F6E\\u8BBE\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        .settings-page {\n          min-height: 100vh;\n          background: #f8f9fa;\n        }\n\n        .page-header {\n          background: white;\n          border-bottom: 1px solid #e9ecef;\n          padding: 24px 0;\n          margin-bottom: 24px;\n        }\n\n        .page-header h1 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 28px;\n          font-weight: 600;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-header p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-content {\n          max-width: 800px;\n          margin: 0 auto;\n          padding: 0 24px;\n        }\n\n        .settings-container {\n          background: white;\n          border-radius: 12px;\n          padding: 24px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n        }\n\n        .settings-section {\n          margin-bottom: 32px;\n          padding-bottom: 24px;\n          border-bottom: 1px solid #e9ecef;\n        }\n\n        .settings-section:last-of-type {\n          border-bottom: none;\n          margin-bottom: 24px;\n        }\n\n        .settings-section h3 {\n          margin: 0 0 20px 0;\n          color: #333;\n          font-size: 18px;\n          font-weight: 600;\n        }\n\n        .setting-item {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 16px;\n          padding: 12px 0;\n        }\n\n        .setting-item:last-child {\n          margin-bottom: 0;\n        }\n\n        .setting-item label {\n          font-weight: 500;\n          color: #333;\n          flex: 1;\n        }\n\n        .setting-item input,\n        .setting-item select {\n          width: 200px;\n          padding: 8px 12px;\n          border: 1px solid #ddd;\n          border-radius: 6px;\n          font-size: 14px;\n        }\n\n        .setting-item input[type=\"checkbox\"] {\n          width: auto;\n          transform: scale(1.2);\n        }\n\n        .setting-item input:focus,\n        .setting-item select:focus {\n          outline: none;\n          border-color: #007bff;\n          box-shadow: 0 0 0 2px rgba(0,123,255,0.25);\n        }\n\n        .settings-actions {\n          display: flex;\n          gap: 12px;\n          justify-content: center;\n          padding-top: 24px;\n          border-top: 1px solid #e9ecef;\n        }\n\n        .btn-primary,\n        .btn-secondary {\n          border: none;\n          padding: 12px 24px;\n          border-radius: 6px;\n          font-size: 16px;\n          font-weight: 500;\n          cursor: pointer;\n          transition: all 0.2s ease;\n          min-width: 120px;\n        }\n\n        .btn-primary {\n          background: #007bff;\n          color: white;\n        }\n\n        .btn-primary:hover {\n          background: #0056b3;\n        }\n\n        .btn-secondary {\n          background: #6c757d;\n          color: white;\n        }\n\n        .btn-secondary:hover {\n          background: #5a6268;\n        }\n\n        /* 响应式设计 */\n        @media (max-width: 768px) {\n          .page-header h1,\n          .page-header p,\n          .page-content {\n            padding: 0 16px;\n          }\n\n          .settings-container {\n            padding: 16px;\n          }\n\n          .setting-item {\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 8px;\n          }\n\n          .setting-item input,\n          .setting-item select {\n            width: 100%;\n          }\n\n          .settings-actions {\n            flex-direction: column;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_s(SettingsPage, \"m/S7XbTVbBcfJXSLIOrturAOq7w=\");\n_c = SettingsPage;\nexport default SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "SettingsPage", "_s", "settings", "setSettings", "apiEndpoint", "timeout", "maxFileSize", "supportedFormats", "autoSave", "enableElementDetection", "enableInteractionAnalysis", "enableScriptGeneration", "theme", "language", "showNotifications", "saved", "setSaved", "handleSettingChange", "key", "value", "prev", "handleArraySettingChange", "index", "map", "item", "i", "handleSaveSettings", "localStorage", "setItem", "JSON", "stringify", "setTimeout", "handleResetSettings", "window", "confirm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onChange", "e", "target", "placeholder", "parseInt", "min", "max", "checked", "onClick", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/pages/SettingsPage.js"], "sourcesContent": ["/**\n * 设置页面\n */\nimport React, { useState } from 'react';\n\nconst SettingsPage = () => {\n  const [settings, setSettings] = useState({\n    // API设置\n    apiEndpoint: 'http://localhost:8001',\n    timeout: 30,\n    \n    // 分析设置\n    maxFileSize: 10,\n    supportedFormats: ['png', 'jpg', 'jpeg', 'gif'],\n    autoSave: true,\n    \n    // 智能体设置\n    enableElementDetection: true,\n    enableInteractionAnalysis: true,\n    enableScriptGeneration: true,\n    \n    // 界面设置\n    theme: 'light',\n    language: 'zh-CN',\n    showNotifications: true\n  });\n\n  const [saved, setSaved] = useState(false);\n\n  const handleSettingChange = (key, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n\n  const handleArraySettingChange = (key, index, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [key]: prev[key].map((item, i) => i === index ? value : item)\n    }));\n  };\n\n  const handleSaveSettings = () => {\n    // 模拟保存设置\n    localStorage.setItem('ui-automation-settings', JSON.stringify(settings));\n    setSaved(true);\n    setTimeout(() => setSaved(false), 3000);\n  };\n\n  const handleResetSettings = () => {\n    if (window.confirm('确定要重置所有设置吗？')) {\n      setSettings({\n        apiEndpoint: 'http://localhost:8001',\n        timeout: 30,\n        maxFileSize: 10,\n        supportedFormats: ['png', 'jpg', 'jpeg', 'gif'],\n        autoSave: true,\n        enableElementDetection: true,\n        enableInteractionAnalysis: true,\n        enableScriptGeneration: true,\n        theme: 'light',\n        language: 'zh-CN',\n        showNotifications: true\n      });\n    }\n  };\n\n  return (\n    <div className=\"settings-page\">\n      <div className=\"page-header\">\n        <h1>⚙️ 系统设置</h1>\n        <p>配置系统参数和个人偏好</p>\n      </div>\n\n      <div className=\"page-content\">\n        <div className=\"settings-container\">\n          {/* API设置 */}\n          <div className=\"settings-section\">\n            <h3>🔗 API设置</h3>\n            <div className=\"setting-item\">\n              <label>API端点</label>\n              <input\n                type=\"text\"\n                value={settings.apiEndpoint}\n                onChange={(e) => handleSettingChange('apiEndpoint', e.target.value)}\n                placeholder=\"http://localhost:8001\"\n              />\n            </div>\n            <div className=\"setting-item\">\n              <label>请求超时 (秒)</label>\n              <input\n                type=\"number\"\n                value={settings.timeout}\n                onChange={(e) => handleSettingChange('timeout', parseInt(e.target.value))}\n                min=\"5\"\n                max=\"300\"\n              />\n            </div>\n          </div>\n\n          {/* 分析设置 */}\n          <div className=\"settings-section\">\n            <h3>🔍 分析设置</h3>\n            <div className=\"setting-item\">\n              <label>最大文件大小 (MB)</label>\n              <input\n                type=\"number\"\n                value={settings.maxFileSize}\n                onChange={(e) => handleSettingChange('maxFileSize', parseInt(e.target.value))}\n                min=\"1\"\n                max=\"100\"\n              />\n            </div>\n            <div className=\"setting-item\">\n              <label>自动保存结果</label>\n              <input\n                type=\"checkbox\"\n                checked={settings.autoSave}\n                onChange={(e) => handleSettingChange('autoSave', e.target.checked)}\n              />\n            </div>\n          </div>\n\n          {/* 智能体设置 */}\n          <div className=\"settings-section\">\n            <h3>🤖 智能体设置</h3>\n            <div className=\"setting-item\">\n              <label>启用元素识别智能体</label>\n              <input\n                type=\"checkbox\"\n                checked={settings.enableElementDetection}\n                onChange={(e) => handleSettingChange('enableElementDetection', e.target.checked)}\n              />\n            </div>\n            <div className=\"setting-item\">\n              <label>启用交互分析智能体</label>\n              <input\n                type=\"checkbox\"\n                checked={settings.enableInteractionAnalysis}\n                onChange={(e) => handleSettingChange('enableInteractionAnalysis', e.target.checked)}\n              />\n            </div>\n            <div className=\"setting-item\">\n              <label>启用脚本生成智能体</label>\n              <input\n                type=\"checkbox\"\n                checked={settings.enableScriptGeneration}\n                onChange={(e) => handleSettingChange('enableScriptGeneration', e.target.checked)}\n              />\n            </div>\n          </div>\n\n          {/* 界面设置 */}\n          <div className=\"settings-section\">\n            <h3>🎨 界面设置</h3>\n            <div className=\"setting-item\">\n              <label>主题</label>\n              <select\n                value={settings.theme}\n                onChange={(e) => handleSettingChange('theme', e.target.value)}\n              >\n                <option value=\"light\">浅色主题</option>\n                <option value=\"dark\">深色主题</option>\n                <option value=\"auto\">跟随系统</option>\n              </select>\n            </div>\n            <div className=\"setting-item\">\n              <label>语言</label>\n              <select\n                value={settings.language}\n                onChange={(e) => handleSettingChange('language', e.target.value)}\n              >\n                <option value=\"zh-CN\">简体中文</option>\n                <option value=\"en-US\">English</option>\n              </select>\n            </div>\n            <div className=\"setting-item\">\n              <label>显示通知</label>\n              <input\n                type=\"checkbox\"\n                checked={settings.showNotifications}\n                onChange={(e) => handleSettingChange('showNotifications', e.target.checked)}\n              />\n            </div>\n          </div>\n\n          {/* 操作按钮 */}\n          <div className=\"settings-actions\">\n            <button className=\"btn-primary\" onClick={handleSaveSettings}>\n              {saved ? '✅ 已保存' : '💾 保存设置'}\n            </button>\n            <button className=\"btn-secondary\" onClick={handleResetSettings}>\n              🔄 重置设置\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <style>{`\n        .settings-page {\n          min-height: 100vh;\n          background: #f8f9fa;\n        }\n\n        .page-header {\n          background: white;\n          border-bottom: 1px solid #e9ecef;\n          padding: 24px 0;\n          margin-bottom: 24px;\n        }\n\n        .page-header h1 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 28px;\n          font-weight: 600;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-header p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-content {\n          max-width: 800px;\n          margin: 0 auto;\n          padding: 0 24px;\n        }\n\n        .settings-container {\n          background: white;\n          border-radius: 12px;\n          padding: 24px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n        }\n\n        .settings-section {\n          margin-bottom: 32px;\n          padding-bottom: 24px;\n          border-bottom: 1px solid #e9ecef;\n        }\n\n        .settings-section:last-of-type {\n          border-bottom: none;\n          margin-bottom: 24px;\n        }\n\n        .settings-section h3 {\n          margin: 0 0 20px 0;\n          color: #333;\n          font-size: 18px;\n          font-weight: 600;\n        }\n\n        .setting-item {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 16px;\n          padding: 12px 0;\n        }\n\n        .setting-item:last-child {\n          margin-bottom: 0;\n        }\n\n        .setting-item label {\n          font-weight: 500;\n          color: #333;\n          flex: 1;\n        }\n\n        .setting-item input,\n        .setting-item select {\n          width: 200px;\n          padding: 8px 12px;\n          border: 1px solid #ddd;\n          border-radius: 6px;\n          font-size: 14px;\n        }\n\n        .setting-item input[type=\"checkbox\"] {\n          width: auto;\n          transform: scale(1.2);\n        }\n\n        .setting-item input:focus,\n        .setting-item select:focus {\n          outline: none;\n          border-color: #007bff;\n          box-shadow: 0 0 0 2px rgba(0,123,255,0.25);\n        }\n\n        .settings-actions {\n          display: flex;\n          gap: 12px;\n          justify-content: center;\n          padding-top: 24px;\n          border-top: 1px solid #e9ecef;\n        }\n\n        .btn-primary,\n        .btn-secondary {\n          border: none;\n          padding: 12px 24px;\n          border-radius: 6px;\n          font-size: 16px;\n          font-weight: 500;\n          cursor: pointer;\n          transition: all 0.2s ease;\n          min-width: 120px;\n        }\n\n        .btn-primary {\n          background: #007bff;\n          color: white;\n        }\n\n        .btn-primary:hover {\n          background: #0056b3;\n        }\n\n        .btn-secondary {\n          background: #6c757d;\n          color: white;\n        }\n\n        .btn-secondary:hover {\n          background: #5a6268;\n        }\n\n        /* 响应式设计 */\n        @media (max-width: 768px) {\n          .page-header h1,\n          .page-header p,\n          .page-content {\n            padding: 0 16px;\n          }\n\n          .settings-container {\n            padding: 16px;\n          }\n\n          .setting-item {\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 8px;\n          }\n\n          .setting-item input,\n          .setting-item select {\n            width: 100%;\n          }\n\n          .settings-actions {\n            flex-direction: column;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default SettingsPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGN,QAAQ,CAAC;IACvC;IACAO,WAAW,EAAE,uBAAuB;IACpCC,OAAO,EAAE,EAAE;IAEX;IACAC,WAAW,EAAE,EAAE;IACfC,gBAAgB,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;IAC/CC,QAAQ,EAAE,IAAI;IAEd;IACAC,sBAAsB,EAAE,IAAI;IAC5BC,yBAAyB,EAAE,IAAI;IAC/BC,sBAAsB,EAAE,IAAI;IAE5B;IACAC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,OAAO;IACjBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAEzC,MAAMoB,mBAAmB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IAC1ChB,WAAW,CAACiB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACF,GAAG,GAAGC;IACT,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,wBAAwB,GAAGA,CAACH,GAAG,EAAEI,KAAK,EAAEH,KAAK,KAAK;IACtDhB,WAAW,CAACiB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACF,GAAG,GAAGE,IAAI,CAACF,GAAG,CAAC,CAACK,GAAG,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAKA,CAAC,KAAKH,KAAK,GAAGH,KAAK,GAAGK,IAAI;IAC9D,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACAC,YAAY,CAACC,OAAO,CAAC,wBAAwB,EAAEC,IAAI,CAACC,SAAS,CAAC5B,QAAQ,CAAC,CAAC;IACxEc,QAAQ,CAAC,IAAI,CAAC;IACde,UAAU,CAAC,MAAMf,QAAQ,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;EACzC,CAAC;EAED,MAAMgB,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIC,MAAM,CAACC,OAAO,CAAC,aAAa,CAAC,EAAE;MACjC/B,WAAW,CAAC;QACVC,WAAW,EAAE,uBAAuB;QACpCC,OAAO,EAAE,EAAE;QACXC,WAAW,EAAE,EAAE;QACfC,gBAAgB,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;QAC/CC,QAAQ,EAAE,IAAI;QACdC,sBAAsB,EAAE,IAAI;QAC5BC,yBAAyB,EAAE,IAAI;QAC/BC,sBAAsB,EAAE,IAAI;QAC5BC,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE,OAAO;QACjBC,iBAAiB,EAAE;MACrB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACEf,OAAA;IAAKoC,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BrC,OAAA;MAAKoC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BrC,OAAA;QAAAqC,QAAA,EAAI;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChBzC,OAAA;QAAAqC,QAAA,EAAG;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAENzC,OAAA;MAAKoC,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BrC,OAAA;QAAKoC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBAEjCrC,OAAA;UAAKoC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BrC,OAAA;YAAAqC,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBzC,OAAA;YAAKoC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrC,OAAA;cAAAqC,QAAA,EAAO;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpBzC,OAAA;cACE0C,IAAI,EAAC,MAAM;cACXtB,KAAK,EAAEjB,QAAQ,CAACE,WAAY;cAC5BsC,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,aAAa,EAAE0B,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAE;cACpE0B,WAAW,EAAC;YAAuB;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzC,OAAA;YAAKoC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrC,OAAA;cAAAqC,QAAA,EAAO;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvBzC,OAAA;cACE0C,IAAI,EAAC,QAAQ;cACbtB,KAAK,EAAEjB,QAAQ,CAACG,OAAQ;cACxBqC,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,SAAS,EAAE6B,QAAQ,CAACH,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAC,CAAE;cAC1E4B,GAAG,EAAC,GAAG;cACPC,GAAG,EAAC;YAAK;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzC,OAAA;UAAKoC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BrC,OAAA;YAAAqC,QAAA,EAAI;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChBzC,OAAA;YAAKoC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrC,OAAA;cAAAqC,QAAA,EAAO;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1BzC,OAAA;cACE0C,IAAI,EAAC,QAAQ;cACbtB,KAAK,EAAEjB,QAAQ,CAACI,WAAY;cAC5BoC,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,aAAa,EAAE6B,QAAQ,CAACH,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAC,CAAE;cAC9E4B,GAAG,EAAC,GAAG;cACPC,GAAG,EAAC;YAAK;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzC,OAAA;YAAKoC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrC,OAAA;cAAAqC,QAAA,EAAO;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrBzC,OAAA;cACE0C,IAAI,EAAC,UAAU;cACfQ,OAAO,EAAE/C,QAAQ,CAACM,QAAS;cAC3BkC,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,UAAU,EAAE0B,CAAC,CAACC,MAAM,CAACK,OAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzC,OAAA;UAAKoC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BrC,OAAA;YAAAqC,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBzC,OAAA;YAAKoC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrC,OAAA;cAAAqC,QAAA,EAAO;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxBzC,OAAA;cACE0C,IAAI,EAAC,UAAU;cACfQ,OAAO,EAAE/C,QAAQ,CAACO,sBAAuB;cACzCiC,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,wBAAwB,EAAE0B,CAAC,CAACC,MAAM,CAACK,OAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzC,OAAA;YAAKoC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrC,OAAA;cAAAqC,QAAA,EAAO;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxBzC,OAAA;cACE0C,IAAI,EAAC,UAAU;cACfQ,OAAO,EAAE/C,QAAQ,CAACQ,yBAA0B;cAC5CgC,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,2BAA2B,EAAE0B,CAAC,CAACC,MAAM,CAACK,OAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzC,OAAA;YAAKoC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrC,OAAA;cAAAqC,QAAA,EAAO;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxBzC,OAAA;cACE0C,IAAI,EAAC,UAAU;cACfQ,OAAO,EAAE/C,QAAQ,CAACS,sBAAuB;cACzC+B,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,wBAAwB,EAAE0B,CAAC,CAACC,MAAM,CAACK,OAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzC,OAAA;UAAKoC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BrC,OAAA;YAAAqC,QAAA,EAAI;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChBzC,OAAA;YAAKoC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrC,OAAA;cAAAqC,QAAA,EAAO;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjBzC,OAAA;cACEoB,KAAK,EAAEjB,QAAQ,CAACU,KAAM;cACtB8B,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,OAAO,EAAE0B,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAE;cAAAiB,QAAA,gBAE9DrC,OAAA;gBAAQoB,KAAK,EAAC,OAAO;gBAAAiB,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCzC,OAAA;gBAAQoB,KAAK,EAAC,MAAM;gBAAAiB,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCzC,OAAA;gBAAQoB,KAAK,EAAC,MAAM;gBAAAiB,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNzC,OAAA;YAAKoC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrC,OAAA;cAAAqC,QAAA,EAAO;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjBzC,OAAA;cACEoB,KAAK,EAAEjB,QAAQ,CAACW,QAAS;cACzB6B,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,UAAU,EAAE0B,CAAC,CAACC,MAAM,CAACzB,KAAK,CAAE;cAAAiB,QAAA,gBAEjErC,OAAA;gBAAQoB,KAAK,EAAC,OAAO;gBAAAiB,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCzC,OAAA;gBAAQoB,KAAK,EAAC,OAAO;gBAAAiB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNzC,OAAA;YAAKoC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BrC,OAAA;cAAAqC,QAAA,EAAO;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnBzC,OAAA;cACE0C,IAAI,EAAC,UAAU;cACfQ,OAAO,EAAE/C,QAAQ,CAACY,iBAAkB;cACpC4B,QAAQ,EAAGC,CAAC,IAAK1B,mBAAmB,CAAC,mBAAmB,EAAE0B,CAAC,CAACC,MAAM,CAACK,OAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzC,OAAA;UAAKoC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BrC,OAAA;YAAQoC,SAAS,EAAC,aAAa;YAACe,OAAO,EAAExB,kBAAmB;YAAAU,QAAA,EACzDrB,KAAK,GAAG,OAAO,GAAG;UAAS;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACTzC,OAAA;YAAQoC,SAAS,EAAC,eAAe;YAACe,OAAO,EAAElB,mBAAoB;YAAAI,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENzC,OAAA;MAAAqC,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACvC,EAAA,CA9WID,YAAY;AAAAmD,EAAA,GAAZnD,YAAY;AAgXlB,eAAeA,YAAY;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}