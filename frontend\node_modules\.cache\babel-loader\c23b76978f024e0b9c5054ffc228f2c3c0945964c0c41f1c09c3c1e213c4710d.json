{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\pages\\\\AboutPage.js\";\n/**\n * 关于页面\n */\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AboutPage = () => {\n  const features = [{\n    icon: '🔍',\n    title: 'UI元素识别',\n    description: '基于AI-TARS技术，精确识别界面中的各种UI元素，包括按钮、输入框、链接等'\n  }, {\n    icon: '🔄',\n    title: '交互流程分析',\n    description: '智能分析用户操作路径，识别关键交互节点，构建完整的用户行为流程'\n  }, {\n    icon: '📝',\n    title: '自动化脚本生成',\n    description: '基于MidScene.js规范，自动生成可执行的UI自动化测试脚本'\n  }, {\n    icon: '⚡',\n    title: '实时进度展示',\n    description: '基于SSE技术的实时进度推送，让您随时了解分析状态'\n  }, {\n    icon: '🤖',\n    title: '多智能体协作',\n    description: '三个专业智能体协同工作，确保分析结果的准确性和完整性'\n  }, {\n    icon: '📊',\n    title: '结果可视化',\n    description: '直观的结果展示界面，支持脚本预览、编辑和下载'\n  }];\n  const techStack = [{\n    name: 'FastAPI',\n    description: '高性能Python Web框架',\n    category: '后端'\n  }, {\n    name: 'React',\n    description: '现代化前端框架',\n    category: '前端'\n  }, {\n    name: 'Server-Sent Events',\n    description: '实时数据推送',\n    category: '通信'\n  }, {\n    name: 'AI智能体',\n    description: '基于大语言模型的智能分析',\n    category: 'AI'\n  }, {\n    name: 'MidScene.js',\n    description: 'UI自动化测试框架',\n    category: '测试'\n  }, {\n    name: 'UI-TARS',\n    description: 'UI元素识别技术',\n    category: 'AI'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"about-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\u2139\\uFE0F \\u5173\\u4E8E\\u5E73\\u53F0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u4E86\\u89E3UI\\u81EA\\u52A8\\u5316\\u5206\\u6790\\u5E73\\u53F0\\u7684\\u529F\\u80FD\\u7279\\u6027\\u548C\\u6280\\u672F\\u67B6\\u6784\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"intro-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"intro-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83E\\uDD16 UI\\u81EA\\u52A8\\u5316\\u5206\\u6790\\u5E73\\u53F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"intro-text\",\n            children: \"\\u8FD9\\u662F\\u4E00\\u4E2A\\u57FA\\u4E8EAI\\u667A\\u80FD\\u4F53\\u7684UI\\u754C\\u9762\\u5206\\u6790\\u548C\\u81EA\\u52A8\\u5316\\u6D4B\\u8BD5\\u811A\\u672C\\u751F\\u6210\\u5E73\\u53F0\\u3002 \\u901A\\u8FC7\\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\\uFF0C\\u5E73\\u53F0\\u80FD\\u591F\\u81EA\\u52A8\\u8BC6\\u522B\\u754C\\u9762\\u5143\\u7D20\\u3001\\u5206\\u6790\\u4EA4\\u4E92\\u6D41\\u7A0B\\uFF0C \\u5E76\\u751F\\u6210\\u7B26\\u5408MidScene.js\\u89C4\\u8303\\u7684\\u81EA\\u52A8\\u5316\\u6D4B\\u8BD5\\u811A\\u672C\\u3002\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"version-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"version-badge\",\n              children: \"v1.0.0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"build-info\",\n              children: \"Build 2024.12.17\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"features-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\u2728 \\u6838\\u5FC3\\u529F\\u80FD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"features-grid\",\n          children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-icon\",\n              children: feature.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"tech-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\uD83D\\uDEE0\\uFE0F \\u6280\\u672F\\u6808\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tech-grid\",\n          children: techStack.map((tech, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tech-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"tech-name\",\n                children: tech.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"tech-category\",\n                children: tech.category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"tech-desc\",\n              children: tech.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"usage-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\uD83D\\uDCD6 \\u4F7F\\u7528\\u8BF4\\u660E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"usage-steps\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-number\",\n              children: \"1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u4E0A\\u4F20\\u622A\\u56FE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u5728\\u5206\\u6790\\u9875\\u9762\\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\\uFF0C\\u652F\\u6301PNG\\u3001JPG\\u7B49\\u683C\\u5F0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-number\",\n              children: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u63CF\\u8FF0\\u529F\\u80FD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u7B80\\u8981\\u63CF\\u8FF0\\u754C\\u9762\\u7684\\u4E3B\\u8981\\u529F\\u80FD\\uFF0C\\u5E2E\\u52A9AI\\u66F4\\u597D\\u5730\\u7406\\u89E3\\u754C\\u9762\\u7528\\u9014\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-number\",\n              children: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u5B9E\\u65F6\\u5206\\u6790\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u89C2\\u770B\\u4E09\\u4E2A\\u667A\\u80FD\\u4F53\\u534F\\u540C\\u5DE5\\u4F5C\\uFF0C\\u5B9E\\u65F6\\u5C55\\u793A\\u5206\\u6790\\u8FDB\\u5EA6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-number\",\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u83B7\\u53D6\\u7ED3\\u679C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u67E5\\u770B\\u5206\\u6790\\u7ED3\\u679C\\uFF0C\\u4E0B\\u8F7D\\u751F\\u6210\\u7684\\u81EA\\u52A8\\u5316\\u6D4B\\u8BD5\\u811A\\u672C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"contact-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\uD83D\\uDCDE \\u8054\\u7CFB\\u6211\\u4EEC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"contact-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u5F00\\u53D1\\u56E2\\u961F\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"UI\\u81EA\\u52A8\\u5316\\u5206\\u6790\\u5E73\\u53F0\\u5F00\\u53D1\\u7EC4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6280\\u672F\\u652F\\u6301\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"<EMAIL>\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u9879\\u76EE\\u5730\\u5740\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"https://github.com/ui-automation/platform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        .about-page {\n          min-height: 100vh;\n          background: #f8f9fa;\n        }\n\n        .page-header {\n          background: white;\n          border-bottom: 1px solid #e9ecef;\n          padding: 24px 0;\n          margin-bottom: 24px;\n        }\n\n        .page-header h1 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 28px;\n          font-weight: 600;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-header p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-content {\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 0 24px;\n        }\n\n        section {\n          background: white;\n          border-radius: 12px;\n          padding: 32px;\n          margin-bottom: 24px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n        }\n\n        section h2 {\n          margin: 0 0 24px 0;\n          color: #333;\n          font-size: 24px;\n          font-weight: 600;\n        }\n\n        .intro-content h2 {\n          margin-bottom: 16px;\n        }\n\n        .intro-text {\n          font-size: 16px;\n          line-height: 1.6;\n          color: #666;\n          margin-bottom: 20px;\n        }\n\n        .version-info {\n          display: flex;\n          gap: 12px;\n          align-items: center;\n        }\n\n        .version-badge {\n          background: #007bff;\n          color: white;\n          padding: 4px 12px;\n          border-radius: 20px;\n          font-size: 12px;\n          font-weight: 500;\n        }\n\n        .build-info {\n          color: #666;\n          font-size: 14px;\n        }\n\n        .features-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n          gap: 20px;\n        }\n\n        .feature-card {\n          padding: 20px;\n          border: 1px solid #e9ecef;\n          border-radius: 8px;\n          text-align: center;\n          transition: all 0.2s ease;\n        }\n\n        .feature-card:hover {\n          border-color: #007bff;\n          box-shadow: 0 4px 12px rgba(0,123,255,0.15);\n        }\n\n        .feature-icon {\n          font-size: 32px;\n          margin-bottom: 12px;\n        }\n\n        .feature-card h3 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 16px;\n          font-weight: 600;\n        }\n\n        .feature-card p {\n          margin: 0;\n          color: #666;\n          font-size: 14px;\n          line-height: 1.5;\n        }\n\n        .tech-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 16px;\n        }\n\n        .tech-item {\n          padding: 16px;\n          border: 1px solid #e9ecef;\n          border-radius: 8px;\n        }\n\n        .tech-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 8px;\n        }\n\n        .tech-name {\n          font-weight: 600;\n          color: #333;\n        }\n\n        .tech-category {\n          background: #f8f9fa;\n          color: #666;\n          padding: 2px 8px;\n          border-radius: 12px;\n          font-size: 12px;\n        }\n\n        .tech-desc {\n          margin: 0;\n          color: #666;\n          font-size: 14px;\n        }\n\n        .usage-steps {\n          display: grid;\n          gap: 20px;\n        }\n\n        .step {\n          display: flex;\n          gap: 16px;\n          align-items: flex-start;\n        }\n\n        .step-number {\n          width: 32px;\n          height: 32px;\n          background: #007bff;\n          color: white;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-weight: 600;\n          flex-shrink: 0;\n        }\n\n        .step-content h3 {\n          margin: 0 0 4px 0;\n          color: #333;\n          font-size: 16px;\n          font-weight: 600;\n        }\n\n        .step-content p {\n          margin: 0;\n          color: #666;\n          font-size: 14px;\n          line-height: 1.5;\n        }\n\n        .contact-info {\n          display: grid;\n          gap: 12px;\n        }\n\n        .contact-item {\n          display: flex;\n          gap: 12px;\n          align-items: center;\n        }\n\n        .contact-item strong {\n          min-width: 100px;\n          color: #333;\n        }\n\n        .contact-item span {\n          color: #666;\n        }\n\n        /* 响应式设计 */\n        @media (max-width: 768px) {\n          .page-header h1,\n          .page-header p,\n          .page-content {\n            padding: 0 16px;\n          }\n\n          section {\n            padding: 20px;\n          }\n\n          .features-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .tech-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .version-info {\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 8px;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_c = AboutPage;\nexport default AboutPage;\nvar _c;\n$RefreshReg$(_c, \"AboutPage\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "AboutPage", "features", "icon", "title", "description", "techStack", "name", "category", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "feature", "index", "tech", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/pages/AboutPage.js"], "sourcesContent": ["/**\n * 关于页面\n */\nimport React from 'react';\n\nconst AboutPage = () => {\n  const features = [\n    {\n      icon: '🔍',\n      title: 'UI元素识别',\n      description: '基于AI-TARS技术，精确识别界面中的各种UI元素，包括按钮、输入框、链接等'\n    },\n    {\n      icon: '🔄',\n      title: '交互流程分析',\n      description: '智能分析用户操作路径，识别关键交互节点，构建完整的用户行为流程'\n    },\n    {\n      icon: '📝',\n      title: '自动化脚本生成',\n      description: '基于MidScene.js规范，自动生成可执行的UI自动化测试脚本'\n    },\n    {\n      icon: '⚡',\n      title: '实时进度展示',\n      description: '基于SSE技术的实时进度推送，让您随时了解分析状态'\n    },\n    {\n      icon: '🤖',\n      title: '多智能体协作',\n      description: '三个专业智能体协同工作，确保分析结果的准确性和完整性'\n    },\n    {\n      icon: '📊',\n      title: '结果可视化',\n      description: '直观的结果展示界面，支持脚本预览、编辑和下载'\n    }\n  ];\n\n  const techStack = [\n    { name: 'FastAPI', description: '高性能Python Web框架', category: '后端' },\n    { name: 'React', description: '现代化前端框架', category: '前端' },\n    { name: 'Server-Sent Events', description: '实时数据推送', category: '通信' },\n    { name: 'AI智能体', description: '基于大语言模型的智能分析', category: 'AI' },\n    { name: 'MidScene.js', description: 'UI自动化测试框架', category: '测试' },\n    { name: 'UI-TARS', description: 'UI元素识别技术', category: 'AI' }\n  ];\n\n  return (\n    <div className=\"about-page\">\n      <div className=\"page-header\">\n        <h1>ℹ️ 关于平台</h1>\n        <p>了解UI自动化分析平台的功能特性和技术架构</p>\n      </div>\n\n      <div className=\"page-content\">\n        {/* 平台介绍 */}\n        <section className=\"intro-section\">\n          <div className=\"intro-content\">\n            <h2>🤖 UI自动化分析平台</h2>\n            <p className=\"intro-text\">\n              这是一个基于AI智能体的UI界面分析和自动化测试脚本生成平台。\n              通过上传UI界面截图，平台能够自动识别界面元素、分析交互流程，\n              并生成符合MidScene.js规范的自动化测试脚本。\n            </p>\n            <div className=\"version-info\">\n              <span className=\"version-badge\">v1.0.0</span>\n              <span className=\"build-info\">Build 2024.12.17</span>\n            </div>\n          </div>\n        </section>\n\n        {/* 核心功能 */}\n        <section className=\"features-section\">\n          <h2>✨ 核心功能</h2>\n          <div className=\"features-grid\">\n            {features.map((feature, index) => (\n              <div key={index} className=\"feature-card\">\n                <div className=\"feature-icon\">{feature.icon}</div>\n                <h3>{feature.title}</h3>\n                <p>{feature.description}</p>\n              </div>\n            ))}\n          </div>\n        </section>\n\n        {/* 技术栈 */}\n        <section className=\"tech-section\">\n          <h2>🛠️ 技术栈</h2>\n          <div className=\"tech-grid\">\n            {techStack.map((tech, index) => (\n              <div key={index} className=\"tech-item\">\n                <div className=\"tech-header\">\n                  <span className=\"tech-name\">{tech.name}</span>\n                  <span className=\"tech-category\">{tech.category}</span>\n                </div>\n                <p className=\"tech-desc\">{tech.description}</p>\n              </div>\n            ))}\n          </div>\n        </section>\n\n        {/* 使用说明 */}\n        <section className=\"usage-section\">\n          <h2>📖 使用说明</h2>\n          <div className=\"usage-steps\">\n            <div className=\"step\">\n              <div className=\"step-number\">1</div>\n              <div className=\"step-content\">\n                <h3>上传截图</h3>\n                <p>在分析页面上传UI界面截图，支持PNG、JPG等格式</p>\n              </div>\n            </div>\n            <div className=\"step\">\n              <div className=\"step-number\">2</div>\n              <div className=\"step-content\">\n                <h3>描述功能</h3>\n                <p>简要描述界面的主要功能，帮助AI更好地理解界面用途</p>\n              </div>\n            </div>\n            <div className=\"step\">\n              <div className=\"step-number\">3</div>\n              <div className=\"step-content\">\n                <h3>实时分析</h3>\n                <p>观看三个智能体协同工作，实时展示分析进度</p>\n              </div>\n            </div>\n            <div className=\"step\">\n              <div className=\"step-number\">4</div>\n              <div className=\"step-content\">\n                <h3>获取结果</h3>\n                <p>查看分析结果，下载生成的自动化测试脚本</p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* 联系信息 */}\n        <section className=\"contact-section\">\n          <h2>📞 联系我们</h2>\n          <div className=\"contact-info\">\n            <div className=\"contact-item\">\n              <strong>开发团队：</strong>\n              <span>UI自动化分析平台开发组</span>\n            </div>\n            <div className=\"contact-item\">\n              <strong>技术支持：</strong>\n              <span><EMAIL></span>\n            </div>\n            <div className=\"contact-item\">\n              <strong>项目地址：</strong>\n              <span>https://github.com/ui-automation/platform</span>\n            </div>\n          </div>\n        </section>\n      </div>\n\n      <style>{`\n        .about-page {\n          min-height: 100vh;\n          background: #f8f9fa;\n        }\n\n        .page-header {\n          background: white;\n          border-bottom: 1px solid #e9ecef;\n          padding: 24px 0;\n          margin-bottom: 24px;\n        }\n\n        .page-header h1 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 28px;\n          font-weight: 600;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-header p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-content {\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 0 24px;\n        }\n\n        section {\n          background: white;\n          border-radius: 12px;\n          padding: 32px;\n          margin-bottom: 24px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n        }\n\n        section h2 {\n          margin: 0 0 24px 0;\n          color: #333;\n          font-size: 24px;\n          font-weight: 600;\n        }\n\n        .intro-content h2 {\n          margin-bottom: 16px;\n        }\n\n        .intro-text {\n          font-size: 16px;\n          line-height: 1.6;\n          color: #666;\n          margin-bottom: 20px;\n        }\n\n        .version-info {\n          display: flex;\n          gap: 12px;\n          align-items: center;\n        }\n\n        .version-badge {\n          background: #007bff;\n          color: white;\n          padding: 4px 12px;\n          border-radius: 20px;\n          font-size: 12px;\n          font-weight: 500;\n        }\n\n        .build-info {\n          color: #666;\n          font-size: 14px;\n        }\n\n        .features-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n          gap: 20px;\n        }\n\n        .feature-card {\n          padding: 20px;\n          border: 1px solid #e9ecef;\n          border-radius: 8px;\n          text-align: center;\n          transition: all 0.2s ease;\n        }\n\n        .feature-card:hover {\n          border-color: #007bff;\n          box-shadow: 0 4px 12px rgba(0,123,255,0.15);\n        }\n\n        .feature-icon {\n          font-size: 32px;\n          margin-bottom: 12px;\n        }\n\n        .feature-card h3 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 16px;\n          font-weight: 600;\n        }\n\n        .feature-card p {\n          margin: 0;\n          color: #666;\n          font-size: 14px;\n          line-height: 1.5;\n        }\n\n        .tech-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 16px;\n        }\n\n        .tech-item {\n          padding: 16px;\n          border: 1px solid #e9ecef;\n          border-radius: 8px;\n        }\n\n        .tech-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 8px;\n        }\n\n        .tech-name {\n          font-weight: 600;\n          color: #333;\n        }\n\n        .tech-category {\n          background: #f8f9fa;\n          color: #666;\n          padding: 2px 8px;\n          border-radius: 12px;\n          font-size: 12px;\n        }\n\n        .tech-desc {\n          margin: 0;\n          color: #666;\n          font-size: 14px;\n        }\n\n        .usage-steps {\n          display: grid;\n          gap: 20px;\n        }\n\n        .step {\n          display: flex;\n          gap: 16px;\n          align-items: flex-start;\n        }\n\n        .step-number {\n          width: 32px;\n          height: 32px;\n          background: #007bff;\n          color: white;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-weight: 600;\n          flex-shrink: 0;\n        }\n\n        .step-content h3 {\n          margin: 0 0 4px 0;\n          color: #333;\n          font-size: 16px;\n          font-weight: 600;\n        }\n\n        .step-content p {\n          margin: 0;\n          color: #666;\n          font-size: 14px;\n          line-height: 1.5;\n        }\n\n        .contact-info {\n          display: grid;\n          gap: 12px;\n        }\n\n        .contact-item {\n          display: flex;\n          gap: 12px;\n          align-items: center;\n        }\n\n        .contact-item strong {\n          min-width: 100px;\n          color: #333;\n        }\n\n        .contact-item span {\n          color: #666;\n        }\n\n        /* 响应式设计 */\n        @media (max-width: 768px) {\n          .page-header h1,\n          .page-header p,\n          .page-content {\n            padding: 0 16px;\n          }\n\n          section {\n            padding: 20px;\n          }\n\n          .features-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .tech-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .version-info {\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 8px;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default AboutPage;\n"], "mappings": ";AAAA;AACA;AACA;AACA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtB,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,SAAS,GAAG,CAChB;IAAEC,IAAI,EAAE,SAAS;IAAEF,WAAW,EAAE,iBAAiB;IAAEG,QAAQ,EAAE;EAAK,CAAC,EACnE;IAAED,IAAI,EAAE,OAAO;IAAEF,WAAW,EAAE,SAAS;IAAEG,QAAQ,EAAE;EAAK,CAAC,EACzD;IAAED,IAAI,EAAE,oBAAoB;IAAEF,WAAW,EAAE,QAAQ;IAAEG,QAAQ,EAAE;EAAK,CAAC,EACrE;IAAED,IAAI,EAAE,OAAO;IAAEF,WAAW,EAAE,cAAc;IAAEG,QAAQ,EAAE;EAAK,CAAC,EAC9D;IAAED,IAAI,EAAE,aAAa;IAAEF,WAAW,EAAE,WAAW;IAAEG,QAAQ,EAAE;EAAK,CAAC,EACjE;IAAED,IAAI,EAAE,SAAS;IAAEF,WAAW,EAAE,UAAU;IAAEG,QAAQ,EAAE;EAAK,CAAC,CAC7D;EAED,oBACER,OAAA;IAAKS,SAAS,EAAC,YAAY;IAAAC,QAAA,gBACzBV,OAAA;MAAKS,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BV,OAAA;QAAAU,QAAA,EAAI;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChBd,OAAA;QAAAU,QAAA,EAAG;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAENd,OAAA;MAAKS,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAE3BV,OAAA;QAASS,SAAS,EAAC,eAAe;QAAAC,QAAA,eAChCV,OAAA;UAAKS,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BV,OAAA;YAAAU,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBd,OAAA;YAAGS,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAI1B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJd,OAAA;YAAKS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BV,OAAA;cAAMS,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7Cd,OAAA;cAAMS,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVd,OAAA;QAASS,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBACnCV,OAAA;UAAAU,QAAA,EAAI;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACfd,OAAA;UAAKS,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BR,QAAQ,CAACa,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BjB,OAAA;YAAiBS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACvCV,OAAA;cAAKS,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEM,OAAO,CAACb;YAAI;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDd,OAAA;cAAAU,QAAA,EAAKM,OAAO,CAACZ;YAAK;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxBd,OAAA;cAAAU,QAAA,EAAIM,OAAO,CAACX;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAHpBG,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVd,OAAA;QAASS,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC/BV,OAAA;UAAAU,QAAA,EAAI;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChBd,OAAA;UAAKS,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBJ,SAAS,CAACS,GAAG,CAAC,CAACG,IAAI,EAAED,KAAK,kBACzBjB,OAAA;YAAiBS,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACpCV,OAAA;cAAKS,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BV,OAAA;gBAAMS,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEQ,IAAI,CAACX;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9Cd,OAAA;gBAAMS,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEQ,IAAI,CAACV;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNd,OAAA;cAAGS,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEQ,IAAI,CAACb;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GALvCG,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVd,OAAA;QAASS,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAChCV,OAAA;UAAAU,QAAA,EAAI;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChBd,OAAA;UAAKS,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BV,OAAA;YAAKS,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBV,OAAA;cAAKS,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpCd,OAAA;cAAKS,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BV,OAAA;gBAAAU,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbd,OAAA;gBAAAU,QAAA,EAAG;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBV,OAAA;cAAKS,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpCd,OAAA;cAAKS,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BV,OAAA;gBAAAU,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbd,OAAA;gBAAAU,QAAA,EAAG;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBV,OAAA;cAAKS,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpCd,OAAA;cAAKS,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BV,OAAA;gBAAAU,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbd,OAAA;gBAAAU,QAAA,EAAG;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBV,OAAA;cAAKS,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpCd,OAAA;cAAKS,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BV,OAAA;gBAAAU,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbd,OAAA;gBAAAU,QAAA,EAAG;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVd,OAAA;QAASS,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAClCV,OAAA;UAAAU,QAAA,EAAI;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChBd,OAAA;UAAKS,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BV,OAAA;YAAKS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BV,OAAA;cAAAU,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtBd,OAAA;cAAAU,QAAA,EAAM;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BV,OAAA;cAAAU,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtBd,OAAA;cAAAU,QAAA,EAAM;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BV,OAAA;cAAAU,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtBd,OAAA;cAAAU,QAAA,EAAM;YAAyC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAENd,OAAA;MAAAU,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACK,EAAA,GAjZIlB,SAAS;AAmZf,eAAeA,SAAS;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}