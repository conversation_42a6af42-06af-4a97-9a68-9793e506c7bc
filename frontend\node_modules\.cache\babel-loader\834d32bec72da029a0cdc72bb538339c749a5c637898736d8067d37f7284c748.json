{"ast": null, "code": "/**\n * 关于页面\n */import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AboutPage=()=>{const features=[{icon:'🔍',title:'UI元素识别',description:'基于AI-TARS技术，精确识别界面中的各种UI元素，包括按钮、输入框、链接等'},{icon:'🔄',title:'交互流程分析',description:'智能分析用户操作路径，识别关键交互节点，构建完整的用户行为流程'},{icon:'📝',title:'自动化脚本生成',description:'基于MidScene.js规范，自动生成可执行的UI自动化测试脚本'},{icon:'⚡',title:'实时进度展示',description:'基于SSE技术的实时进度推送，让您随时了解分析状态'},{icon:'🤖',title:'多智能体协作',description:'三个专业智能体协同工作，确保分析结果的准确性和完整性'},{icon:'📊',title:'结果可视化',description:'直观的结果展示界面，支持脚本预览、编辑和下载'}];const techStack=[{name:'FastAPI',description:'高性能Python Web框架',category:'后端'},{name:'React',description:'现代化前端框架',category:'前端'},{name:'Server-Sent Events',description:'实时数据推送',category:'通信'},{name:'AI智能体',description:'基于大语言模型的智能分析',category:'AI'},{name:'MidScene.js',description:'UI自动化测试框架',category:'测试'},{name:'UI-TARS',description:'UI元素识别技术',category:'AI'}];return/*#__PURE__*/_jsxs(\"div\",{className:\"about-page\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"page-header\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"\\u2139\\uFE0F \\u5173\\u4E8E\\u5E73\\u53F0\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u4E86\\u89E3UI\\u81EA\\u52A8\\u5316\\u5206\\u6790\\u5E73\\u53F0\\u7684\\u529F\\u80FD\\u7279\\u6027\\u548C\\u6280\\u672F\\u67B6\\u6784\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"page-content\",children:[/*#__PURE__*/_jsx(\"section\",{className:\"intro-section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"intro-content\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\uD83E\\uDD16 UI\\u81EA\\u52A8\\u5316\\u5206\\u6790\\u5E73\\u53F0\"}),/*#__PURE__*/_jsx(\"p\",{className:\"intro-text\",children:\"\\u8FD9\\u662F\\u4E00\\u4E2A\\u57FA\\u4E8EAI\\u667A\\u80FD\\u4F53\\u7684UI\\u754C\\u9762\\u5206\\u6790\\u548C\\u81EA\\u52A8\\u5316\\u6D4B\\u8BD5\\u811A\\u672C\\u751F\\u6210\\u5E73\\u53F0\\u3002 \\u901A\\u8FC7\\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\\uFF0C\\u5E73\\u53F0\\u80FD\\u591F\\u81EA\\u52A8\\u8BC6\\u522B\\u754C\\u9762\\u5143\\u7D20\\u3001\\u5206\\u6790\\u4EA4\\u4E92\\u6D41\\u7A0B\\uFF0C \\u5E76\\u751F\\u6210\\u7B26\\u5408MidScene.js\\u89C4\\u8303\\u7684\\u81EA\\u52A8\\u5316\\u6D4B\\u8BD5\\u811A\\u672C\\u3002\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"version-info\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"version-badge\",children:\"v1.0.0\"}),/*#__PURE__*/_jsx(\"span\",{className:\"build-info\",children:\"Build 2024.12.17\"})]})]})}),/*#__PURE__*/_jsxs(\"section\",{className:\"features-section\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\u2728 \\u6838\\u5FC3\\u529F\\u80FD\"}),/*#__PURE__*/_jsx(\"div\",{className:\"features-grid\",children:features.map((feature,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"feature-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"feature-icon\",children:feature.icon}),/*#__PURE__*/_jsx(\"h3\",{children:feature.title}),/*#__PURE__*/_jsx(\"p\",{children:feature.description})]},index))})]}),/*#__PURE__*/_jsxs(\"section\",{className:\"tech-section\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\uD83D\\uDEE0\\uFE0F \\u6280\\u672F\\u6808\"}),/*#__PURE__*/_jsx(\"div\",{className:\"tech-grid\",children:techStack.map((tech,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"tech-item\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"tech-header\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"tech-name\",children:tech.name}),/*#__PURE__*/_jsx(\"span\",{className:\"tech-category\",children:tech.category})]}),/*#__PURE__*/_jsx(\"p\",{className:\"tech-desc\",children:tech.description})]},index))})]}),/*#__PURE__*/_jsxs(\"section\",{className:\"usage-section\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\uD83D\\uDCD6 \\u4F7F\\u7528\\u8BF4\\u660E\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"usage-steps\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"step\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"step-number\",children:\"1\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"step-content\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u4E0A\\u4F20\\u622A\\u56FE\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u5728\\u5206\\u6790\\u9875\\u9762\\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\\uFF0C\\u652F\\u6301PNG\\u3001JPG\\u7B49\\u683C\\u5F0F\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"step\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"step-number\",children:\"2\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"step-content\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u63CF\\u8FF0\\u529F\\u80FD\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u7B80\\u8981\\u63CF\\u8FF0\\u754C\\u9762\\u7684\\u4E3B\\u8981\\u529F\\u80FD\\uFF0C\\u5E2E\\u52A9AI\\u66F4\\u597D\\u5730\\u7406\\u89E3\\u754C\\u9762\\u7528\\u9014\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"step\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"step-number\",children:\"3\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"step-content\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u5B9E\\u65F6\\u5206\\u6790\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u89C2\\u770B\\u4E09\\u4E2A\\u667A\\u80FD\\u4F53\\u534F\\u540C\\u5DE5\\u4F5C\\uFF0C\\u5B9E\\u65F6\\u5C55\\u793A\\u5206\\u6790\\u8FDB\\u5EA6\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"step\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"step-number\",children:\"4\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"step-content\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u83B7\\u53D6\\u7ED3\\u679C\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u67E5\\u770B\\u5206\\u6790\\u7ED3\\u679C\\uFF0C\\u4E0B\\u8F7D\\u751F\\u6210\\u7684\\u81EA\\u52A8\\u5316\\u6D4B\\u8BD5\\u811A\\u672C\"})]})]})]})]}),/*#__PURE__*/_jsxs(\"section\",{className:\"contact-section\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\uD83D\\uDCDE \\u8054\\u7CFB\\u6211\\u4EEC\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact-info\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"contact-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u5F00\\u53D1\\u56E2\\u961F\\uFF1A\"}),/*#__PURE__*/_jsx(\"span\",{children:\"UI\\u81EA\\u52A8\\u5316\\u5206\\u6790\\u5E73\\u53F0\\u5F00\\u53D1\\u7EC4\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u6280\\u672F\\u652F\\u6301\\uFF1A\"}),/*#__PURE__*/_jsx(\"span\",{children:\"<EMAIL>\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"contact-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u9879\\u76EE\\u5730\\u5740\\uFF1A\"}),/*#__PURE__*/_jsx(\"span\",{children:\"https://github.com/ui-automation/platform\"})]})]})]})]}),/*#__PURE__*/_jsx(\"style\",{children:\"\\n        .about-page {\\n          min-height: 100vh;\\n          background: #f8f9fa;\\n        }\\n\\n        .page-header {\\n          background: white;\\n          border-bottom: 1px solid #e9ecef;\\n          padding: 24px 0;\\n          margin-bottom: 24px;\\n        }\\n\\n        .page-header h1 {\\n          margin: 0 0 8px 0;\\n          color: #333;\\n          font-size: 28px;\\n          font-weight: 600;\\n          max-width: 1200px;\\n          margin-left: auto;\\n          margin-right: auto;\\n          padding: 0 24px;\\n        }\\n\\n        .page-header p {\\n          margin: 0;\\n          color: #666;\\n          font-size: 16px;\\n          max-width: 1200px;\\n          margin-left: auto;\\n          margin-right: auto;\\n          padding: 0 24px;\\n        }\\n\\n        .page-content {\\n          max-width: 1200px;\\n          margin: 0 auto;\\n          padding: 0 24px;\\n        }\\n\\n        section {\\n          background: white;\\n          border-radius: 12px;\\n          padding: 32px;\\n          margin-bottom: 24px;\\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\\n        }\\n\\n        section h2 {\\n          margin: 0 0 24px 0;\\n          color: #333;\\n          font-size: 24px;\\n          font-weight: 600;\\n        }\\n\\n        .intro-content h2 {\\n          margin-bottom: 16px;\\n        }\\n\\n        .intro-text {\\n          font-size: 16px;\\n          line-height: 1.6;\\n          color: #666;\\n          margin-bottom: 20px;\\n        }\\n\\n        .version-info {\\n          display: flex;\\n          gap: 12px;\\n          align-items: center;\\n        }\\n\\n        .version-badge {\\n          background: #007bff;\\n          color: white;\\n          padding: 4px 12px;\\n          border-radius: 20px;\\n          font-size: 12px;\\n          font-weight: 500;\\n        }\\n\\n        .build-info {\\n          color: #666;\\n          font-size: 14px;\\n        }\\n\\n        .features-grid {\\n          display: grid;\\n          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n          gap: 20px;\\n        }\\n\\n        .feature-card {\\n          padding: 20px;\\n          border: 1px solid #e9ecef;\\n          border-radius: 8px;\\n          text-align: center;\\n          transition: all 0.2s ease;\\n        }\\n\\n        .feature-card:hover {\\n          border-color: #007bff;\\n          box-shadow: 0 4px 12px rgba(0,123,255,0.15);\\n        }\\n\\n        .feature-icon {\\n          font-size: 32px;\\n          margin-bottom: 12px;\\n        }\\n\\n        .feature-card h3 {\\n          margin: 0 0 8px 0;\\n          color: #333;\\n          font-size: 16px;\\n          font-weight: 600;\\n        }\\n\\n        .feature-card p {\\n          margin: 0;\\n          color: #666;\\n          font-size: 14px;\\n          line-height: 1.5;\\n        }\\n\\n        .tech-grid {\\n          display: grid;\\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n          gap: 16px;\\n        }\\n\\n        .tech-item {\\n          padding: 16px;\\n          border: 1px solid #e9ecef;\\n          border-radius: 8px;\\n        }\\n\\n        .tech-header {\\n          display: flex;\\n          justify-content: space-between;\\n          align-items: center;\\n          margin-bottom: 8px;\\n        }\\n\\n        .tech-name {\\n          font-weight: 600;\\n          color: #333;\\n        }\\n\\n        .tech-category {\\n          background: #f8f9fa;\\n          color: #666;\\n          padding: 2px 8px;\\n          border-radius: 12px;\\n          font-size: 12px;\\n        }\\n\\n        .tech-desc {\\n          margin: 0;\\n          color: #666;\\n          font-size: 14px;\\n        }\\n\\n        .usage-steps {\\n          display: grid;\\n          gap: 20px;\\n        }\\n\\n        .step {\\n          display: flex;\\n          gap: 16px;\\n          align-items: flex-start;\\n        }\\n\\n        .step-number {\\n          width: 32px;\\n          height: 32px;\\n          background: #007bff;\\n          color: white;\\n          border-radius: 50%;\\n          display: flex;\\n          align-items: center;\\n          justify-content: center;\\n          font-weight: 600;\\n          flex-shrink: 0;\\n        }\\n\\n        .step-content h3 {\\n          margin: 0 0 4px 0;\\n          color: #333;\\n          font-size: 16px;\\n          font-weight: 600;\\n        }\\n\\n        .step-content p {\\n          margin: 0;\\n          color: #666;\\n          font-size: 14px;\\n          line-height: 1.5;\\n        }\\n\\n        .contact-info {\\n          display: grid;\\n          gap: 12px;\\n        }\\n\\n        .contact-item {\\n          display: flex;\\n          gap: 12px;\\n          align-items: center;\\n        }\\n\\n        .contact-item strong {\\n          min-width: 100px;\\n          color: #333;\\n        }\\n\\n        .contact-item span {\\n          color: #666;\\n        }\\n\\n        /* \\u54CD\\u5E94\\u5F0F\\u8BBE\\u8BA1 */\\n        @media (max-width: 768px) {\\n          .page-header h1,\\n          .page-header p,\\n          .page-content {\\n            padding: 0 16px;\\n          }\\n\\n          section {\\n            padding: 20px;\\n          }\\n\\n          .features-grid {\\n            grid-template-columns: 1fr;\\n          }\\n\\n          .tech-grid {\\n            grid-template-columns: 1fr;\\n          }\\n\\n          .version-info {\\n            flex-direction: column;\\n            align-items: flex-start;\\n            gap: 8px;\\n          }\\n        }\\n      \"})]});};export default AboutPage;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "AboutPage", "features", "icon", "title", "description", "techStack", "name", "category", "className", "children", "map", "feature", "index", "tech"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/pages/AboutPage.js"], "sourcesContent": ["/**\n * 关于页面\n */\nimport React from 'react';\n\nconst AboutPage = () => {\n  const features = [\n    {\n      icon: '🔍',\n      title: 'UI元素识别',\n      description: '基于AI-TARS技术，精确识别界面中的各种UI元素，包括按钮、输入框、链接等'\n    },\n    {\n      icon: '🔄',\n      title: '交互流程分析',\n      description: '智能分析用户操作路径，识别关键交互节点，构建完整的用户行为流程'\n    },\n    {\n      icon: '📝',\n      title: '自动化脚本生成',\n      description: '基于MidScene.js规范，自动生成可执行的UI自动化测试脚本'\n    },\n    {\n      icon: '⚡',\n      title: '实时进度展示',\n      description: '基于SSE技术的实时进度推送，让您随时了解分析状态'\n    },\n    {\n      icon: '🤖',\n      title: '多智能体协作',\n      description: '三个专业智能体协同工作，确保分析结果的准确性和完整性'\n    },\n    {\n      icon: '📊',\n      title: '结果可视化',\n      description: '直观的结果展示界面，支持脚本预览、编辑和下载'\n    }\n  ];\n\n  const techStack = [\n    { name: 'FastAPI', description: '高性能Python Web框架', category: '后端' },\n    { name: 'React', description: '现代化前端框架', category: '前端' },\n    { name: 'Server-Sent Events', description: '实时数据推送', category: '通信' },\n    { name: 'AI智能体', description: '基于大语言模型的智能分析', category: 'AI' },\n    { name: 'MidScene.js', description: 'UI自动化测试框架', category: '测试' },\n    { name: 'UI-TARS', description: 'UI元素识别技术', category: 'AI' }\n  ];\n\n  return (\n    <div className=\"about-page\">\n      <div className=\"page-header\">\n        <h1>ℹ️ 关于平台</h1>\n        <p>了解UI自动化分析平台的功能特性和技术架构</p>\n      </div>\n\n      <div className=\"page-content\">\n        {/* 平台介绍 */}\n        <section className=\"intro-section\">\n          <div className=\"intro-content\">\n            <h2>🤖 UI自动化分析平台</h2>\n            <p className=\"intro-text\">\n              这是一个基于AI智能体的UI界面分析和自动化测试脚本生成平台。\n              通过上传UI界面截图，平台能够自动识别界面元素、分析交互流程，\n              并生成符合MidScene.js规范的自动化测试脚本。\n            </p>\n            <div className=\"version-info\">\n              <span className=\"version-badge\">v1.0.0</span>\n              <span className=\"build-info\">Build 2024.12.17</span>\n            </div>\n          </div>\n        </section>\n\n        {/* 核心功能 */}\n        <section className=\"features-section\">\n          <h2>✨ 核心功能</h2>\n          <div className=\"features-grid\">\n            {features.map((feature, index) => (\n              <div key={index} className=\"feature-card\">\n                <div className=\"feature-icon\">{feature.icon}</div>\n                <h3>{feature.title}</h3>\n                <p>{feature.description}</p>\n              </div>\n            ))}\n          </div>\n        </section>\n\n        {/* 技术栈 */}\n        <section className=\"tech-section\">\n          <h2>🛠️ 技术栈</h2>\n          <div className=\"tech-grid\">\n            {techStack.map((tech, index) => (\n              <div key={index} className=\"tech-item\">\n                <div className=\"tech-header\">\n                  <span className=\"tech-name\">{tech.name}</span>\n                  <span className=\"tech-category\">{tech.category}</span>\n                </div>\n                <p className=\"tech-desc\">{tech.description}</p>\n              </div>\n            ))}\n          </div>\n        </section>\n\n        {/* 使用说明 */}\n        <section className=\"usage-section\">\n          <h2>📖 使用说明</h2>\n          <div className=\"usage-steps\">\n            <div className=\"step\">\n              <div className=\"step-number\">1</div>\n              <div className=\"step-content\">\n                <h3>上传截图</h3>\n                <p>在分析页面上传UI界面截图，支持PNG、JPG等格式</p>\n              </div>\n            </div>\n            <div className=\"step\">\n              <div className=\"step-number\">2</div>\n              <div className=\"step-content\">\n                <h3>描述功能</h3>\n                <p>简要描述界面的主要功能，帮助AI更好地理解界面用途</p>\n              </div>\n            </div>\n            <div className=\"step\">\n              <div className=\"step-number\">3</div>\n              <div className=\"step-content\">\n                <h3>实时分析</h3>\n                <p>观看三个智能体协同工作，实时展示分析进度</p>\n              </div>\n            </div>\n            <div className=\"step\">\n              <div className=\"step-number\">4</div>\n              <div className=\"step-content\">\n                <h3>获取结果</h3>\n                <p>查看分析结果，下载生成的自动化测试脚本</p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* 联系信息 */}\n        <section className=\"contact-section\">\n          <h2>📞 联系我们</h2>\n          <div className=\"contact-info\">\n            <div className=\"contact-item\">\n              <strong>开发团队：</strong>\n              <span>UI自动化分析平台开发组</span>\n            </div>\n            <div className=\"contact-item\">\n              <strong>技术支持：</strong>\n              <span><EMAIL></span>\n            </div>\n            <div className=\"contact-item\">\n              <strong>项目地址：</strong>\n              <span>https://github.com/ui-automation/platform</span>\n            </div>\n          </div>\n        </section>\n      </div>\n\n      <style>{`\n        .about-page {\n          min-height: 100vh;\n          background: #f8f9fa;\n        }\n\n        .page-header {\n          background: white;\n          border-bottom: 1px solid #e9ecef;\n          padding: 24px 0;\n          margin-bottom: 24px;\n        }\n\n        .page-header h1 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 28px;\n          font-weight: 600;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-header p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-content {\n          max-width: 1200px;\n          margin: 0 auto;\n          padding: 0 24px;\n        }\n\n        section {\n          background: white;\n          border-radius: 12px;\n          padding: 32px;\n          margin-bottom: 24px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n        }\n\n        section h2 {\n          margin: 0 0 24px 0;\n          color: #333;\n          font-size: 24px;\n          font-weight: 600;\n        }\n\n        .intro-content h2 {\n          margin-bottom: 16px;\n        }\n\n        .intro-text {\n          font-size: 16px;\n          line-height: 1.6;\n          color: #666;\n          margin-bottom: 20px;\n        }\n\n        .version-info {\n          display: flex;\n          gap: 12px;\n          align-items: center;\n        }\n\n        .version-badge {\n          background: #007bff;\n          color: white;\n          padding: 4px 12px;\n          border-radius: 20px;\n          font-size: 12px;\n          font-weight: 500;\n        }\n\n        .build-info {\n          color: #666;\n          font-size: 14px;\n        }\n\n        .features-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n          gap: 20px;\n        }\n\n        .feature-card {\n          padding: 20px;\n          border: 1px solid #e9ecef;\n          border-radius: 8px;\n          text-align: center;\n          transition: all 0.2s ease;\n        }\n\n        .feature-card:hover {\n          border-color: #007bff;\n          box-shadow: 0 4px 12px rgba(0,123,255,0.15);\n        }\n\n        .feature-icon {\n          font-size: 32px;\n          margin-bottom: 12px;\n        }\n\n        .feature-card h3 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 16px;\n          font-weight: 600;\n        }\n\n        .feature-card p {\n          margin: 0;\n          color: #666;\n          font-size: 14px;\n          line-height: 1.5;\n        }\n\n        .tech-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 16px;\n        }\n\n        .tech-item {\n          padding: 16px;\n          border: 1px solid #e9ecef;\n          border-radius: 8px;\n        }\n\n        .tech-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 8px;\n        }\n\n        .tech-name {\n          font-weight: 600;\n          color: #333;\n        }\n\n        .tech-category {\n          background: #f8f9fa;\n          color: #666;\n          padding: 2px 8px;\n          border-radius: 12px;\n          font-size: 12px;\n        }\n\n        .tech-desc {\n          margin: 0;\n          color: #666;\n          font-size: 14px;\n        }\n\n        .usage-steps {\n          display: grid;\n          gap: 20px;\n        }\n\n        .step {\n          display: flex;\n          gap: 16px;\n          align-items: flex-start;\n        }\n\n        .step-number {\n          width: 32px;\n          height: 32px;\n          background: #007bff;\n          color: white;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-weight: 600;\n          flex-shrink: 0;\n        }\n\n        .step-content h3 {\n          margin: 0 0 4px 0;\n          color: #333;\n          font-size: 16px;\n          font-weight: 600;\n        }\n\n        .step-content p {\n          margin: 0;\n          color: #666;\n          font-size: 14px;\n          line-height: 1.5;\n        }\n\n        .contact-info {\n          display: grid;\n          gap: 12px;\n        }\n\n        .contact-item {\n          display: flex;\n          gap: 12px;\n          align-items: center;\n        }\n\n        .contact-item strong {\n          min-width: 100px;\n          color: #333;\n        }\n\n        .contact-item span {\n          color: #666;\n        }\n\n        /* 响应式设计 */\n        @media (max-width: 768px) {\n          .page-header h1,\n          .page-header p,\n          .page-content {\n            padding: 0 16px;\n          }\n\n          section {\n            padding: 20px;\n          }\n\n          .features-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .tech-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .version-info {\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 8px;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default AboutPage;\n"], "mappings": "AAAA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAAAC,QAAQ,CAAG,CACf,CACEC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,QAAQ,CACfC,WAAW,CAAE,yCACf,CAAC,CACD,CACEF,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,QAAQ,CACfC,WAAW,CAAE,iCACf,CAAC,CACD,CACEF,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,SAAS,CAChBC,WAAW,CAAE,mCACf,CAAC,CACD,CACEF,IAAI,CAAE,GAAG,CACTC,KAAK,CAAE,QAAQ,CACfC,WAAW,CAAE,2BACf,CAAC,CACD,CACEF,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,QAAQ,CACfC,WAAW,CAAE,4BACf,CAAC,CACD,CACEF,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,OAAO,CACdC,WAAW,CAAE,wBACf,CAAC,CACF,CAED,KAAM,CAAAC,SAAS,CAAG,CAChB,CAAEC,IAAI,CAAE,SAAS,CAAEF,WAAW,CAAE,iBAAiB,CAAEG,QAAQ,CAAE,IAAK,CAAC,CACnE,CAAED,IAAI,CAAE,OAAO,CAAEF,WAAW,CAAE,SAAS,CAAEG,QAAQ,CAAE,IAAK,CAAC,CACzD,CAAED,IAAI,CAAE,oBAAoB,CAAEF,WAAW,CAAE,QAAQ,CAAEG,QAAQ,CAAE,IAAK,CAAC,CACrE,CAAED,IAAI,CAAE,OAAO,CAAEF,WAAW,CAAE,cAAc,CAAEG,QAAQ,CAAE,IAAK,CAAC,CAC9D,CAAED,IAAI,CAAE,aAAa,CAAEF,WAAW,CAAE,WAAW,CAAEG,QAAQ,CAAE,IAAK,CAAC,CACjE,CAAED,IAAI,CAAE,SAAS,CAAEF,WAAW,CAAE,UAAU,CAAEG,QAAQ,CAAE,IAAK,CAAC,CAC7D,CAED,mBACER,KAAA,QAAKS,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBV,KAAA,QAAKS,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BZ,IAAA,OAAAY,QAAA,CAAI,uCAAO,CAAI,CAAC,cAChBZ,IAAA,MAAAY,QAAA,CAAG,sHAAqB,CAAG,CAAC,EACzB,CAAC,cAENV,KAAA,QAAKS,SAAS,CAAC,cAAc,CAAAC,QAAA,eAE3BZ,IAAA,YAASW,SAAS,CAAC,eAAe,CAAAC,QAAA,cAChCV,KAAA,QAAKS,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BZ,IAAA,OAAAY,QAAA,CAAI,2DAAY,CAAI,CAAC,cACrBZ,IAAA,MAAGW,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,qcAI1B,CAAG,CAAC,cACJV,KAAA,QAAKS,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BZ,IAAA,SAAMW,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,cAC7CZ,IAAA,SAAMW,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,EACjD,CAAC,EACH,CAAC,CACC,CAAC,cAGVV,KAAA,YAASS,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eACnCZ,IAAA,OAAAY,QAAA,CAAI,iCAAM,CAAI,CAAC,cACfZ,IAAA,QAAKW,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC3BR,QAAQ,CAACS,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,gBAC3Bb,KAAA,QAAiBS,SAAS,CAAC,cAAc,CAAAC,QAAA,eACvCZ,IAAA,QAAKW,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEE,OAAO,CAACT,IAAI,CAAM,CAAC,cAClDL,IAAA,OAAAY,QAAA,CAAKE,OAAO,CAACR,KAAK,CAAK,CAAC,cACxBN,IAAA,MAAAY,QAAA,CAAIE,OAAO,CAACP,WAAW,CAAI,CAAC,GAHpBQ,KAIL,CACN,CAAC,CACC,CAAC,EACC,CAAC,cAGVb,KAAA,YAASS,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC/BZ,IAAA,OAAAY,QAAA,CAAI,uCAAO,CAAI,CAAC,cAChBZ,IAAA,QAAKW,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBJ,SAAS,CAACK,GAAG,CAAC,CAACG,IAAI,CAAED,KAAK,gBACzBb,KAAA,QAAiBS,SAAS,CAAC,WAAW,CAAAC,QAAA,eACpCV,KAAA,QAAKS,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BZ,IAAA,SAAMW,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEI,IAAI,CAACP,IAAI,CAAO,CAAC,cAC9CT,IAAA,SAAMW,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEI,IAAI,CAACN,QAAQ,CAAO,CAAC,EACnD,CAAC,cACNV,IAAA,MAAGW,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEI,IAAI,CAACT,WAAW,CAAI,CAAC,GALvCQ,KAML,CACN,CAAC,CACC,CAAC,EACC,CAAC,cAGVb,KAAA,YAASS,SAAS,CAAC,eAAe,CAAAC,QAAA,eAChCZ,IAAA,OAAAY,QAAA,CAAI,uCAAO,CAAI,CAAC,cAChBV,KAAA,QAAKS,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BV,KAAA,QAAKS,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBZ,IAAA,QAAKW,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAK,CAAC,cACpCV,KAAA,QAAKS,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BZ,IAAA,OAAAY,QAAA,CAAI,0BAAI,CAAI,CAAC,cACbZ,IAAA,MAAAY,QAAA,CAAG,sHAA0B,CAAG,CAAC,EAC9B,CAAC,EACH,CAAC,cACNV,KAAA,QAAKS,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBZ,IAAA,QAAKW,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAK,CAAC,cACpCV,KAAA,QAAKS,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BZ,IAAA,OAAAY,QAAA,CAAI,0BAAI,CAAI,CAAC,cACbZ,IAAA,MAAAY,QAAA,CAAG,8IAAyB,CAAG,CAAC,EAC7B,CAAC,EACH,CAAC,cACNV,KAAA,QAAKS,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBZ,IAAA,QAAKW,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAK,CAAC,cACpCV,KAAA,QAAKS,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BZ,IAAA,OAAAY,QAAA,CAAI,0BAAI,CAAI,CAAC,cACbZ,IAAA,MAAAY,QAAA,CAAG,0HAAoB,CAAG,CAAC,EACxB,CAAC,EACH,CAAC,cACNV,KAAA,QAAKS,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBZ,IAAA,QAAKW,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAK,CAAC,cACpCV,KAAA,QAAKS,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BZ,IAAA,OAAAY,QAAA,CAAI,0BAAI,CAAI,CAAC,cACbZ,IAAA,MAAAY,QAAA,CAAG,oHAAmB,CAAG,CAAC,EACvB,CAAC,EACH,CAAC,EACH,CAAC,EACC,CAAC,cAGVV,KAAA,YAASS,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAClCZ,IAAA,OAAAY,QAAA,CAAI,uCAAO,CAAI,CAAC,cAChBV,KAAA,QAAKS,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BV,KAAA,QAAKS,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BZ,IAAA,WAAAY,QAAA,CAAQ,gCAAK,CAAQ,CAAC,cACtBZ,IAAA,SAAAY,QAAA,CAAM,gEAAY,CAAM,CAAC,EACtB,CAAC,cACNV,KAAA,QAAKS,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BZ,IAAA,WAAAY,QAAA,CAAQ,gCAAK,CAAQ,CAAC,cACtBZ,IAAA,SAAAY,QAAA,CAAM,2BAAyB,CAAM,CAAC,EACnC,CAAC,cACNV,KAAA,QAAKS,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BZ,IAAA,WAAAY,QAAA,CAAQ,gCAAK,CAAQ,CAAC,cACtBZ,IAAA,SAAAY,QAAA,CAAM,2CAAyC,CAAM,CAAC,EACnD,CAAC,EACH,CAAC,EACC,CAAC,EACP,CAAC,cAENZ,IAAA,UAAAY,QAAA,sxKAsPS,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAAT,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}