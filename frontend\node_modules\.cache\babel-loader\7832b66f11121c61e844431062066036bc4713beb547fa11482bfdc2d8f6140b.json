{"ast": null, "code": "/**\n * UI自动化分析平台 - 主应用组件\n */import React,{useState}from'react';import Sidebar from'./components/Sidebar';import AnalysisPage from'./pages/AnalysisPage';import HistoryPage from'./pages/HistoryPage';import SettingsPage from'./pages/SettingsPage';import AboutPage from'./pages/AboutPage';import'./App.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){const[currentPage,setCurrentPage]=useState('analysis');const[sidebarCollapsed,setSidebarCollapsed]=useState(false);const renderCurrentPage=()=>{switch(currentPage){case'analysis':return/*#__PURE__*/_jsx(AnalysisPage,{});case'history':return/*#__PURE__*/_jsx(HistoryPage,{});case'settings':return/*#__PURE__*/_jsx(SettingsPage,{});case'about':return/*#__PURE__*/_jsx(AboutPage,{});default:return/*#__PURE__*/_jsx(AnalysisPage,{});}};return/*#__PURE__*/_jsxs(\"div\",{className:\"app\",children:[/*#__PURE__*/_jsx(Sidebar,{currentPage:currentPage,onPageChange:setCurrentPage,collapsed:sidebarCollapsed,onToggleCollapse:()=>setSidebarCollapsed(!sidebarCollapsed)}),/*#__PURE__*/_jsx(\"main\",{className:\"app-main \".concat(sidebarCollapsed?'sidebar-collapsed':''),children:/*#__PURE__*/_jsx(\"div\",{className:\"page-container\",children:renderCurrentPage()})})]});}export default App;", "map": {"version": 3, "names": ["React", "useState", "Sidebar", "AnalysisPage", "HistoryPage", "SettingsPage", "AboutPage", "jsx", "_jsx", "jsxs", "_jsxs", "App", "currentPage", "setCurrentPage", "sidebarCollapsed", "setSidebarCollapsed", "renderCurrentPage", "className", "children", "onPageChange", "collapsed", "onToggleCollapse", "concat"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/App.js"], "sourcesContent": ["/**\n * UI自动化分析平台 - 主应用组件\n */\nimport React, { useState } from 'react';\nimport Sidebar from './components/Sidebar';\nimport AnalysisPage from './pages/AnalysisPage';\nimport HistoryPage from './pages/HistoryPage';\nimport SettingsPage from './pages/SettingsPage';\nimport AboutPage from './pages/AboutPage';\nimport './App.css';\n\nfunction App() {\n  const [currentPage, setCurrentPage] = useState('analysis');\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n\n  const renderCurrentPage = () => {\n    switch (currentPage) {\n      case 'analysis':\n        return <AnalysisPage />;\n      case 'history':\n        return <HistoryPage />;\n      case 'settings':\n        return <SettingsPage />;\n      case 'about':\n        return <AboutPage />;\n      default:\n        return <AnalysisPage />;\n    }\n  };\n\n  return (\n    <div className=\"app\">\n      <Sidebar\n        currentPage={currentPage}\n        onPageChange={setCurrentPage}\n        collapsed={sidebarCollapsed}\n        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}\n      />\n\n      <main className={`app-main ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>\n        <div className=\"page-container\">\n          {renderCurrentPage()}\n        </div>\n      </main>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,OAAO,KAAM,sBAAsB,CAC1C,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnB,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGZ,QAAQ,CAAC,UAAU,CAAC,CAC1D,KAAM,CAACa,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CAE/D,KAAM,CAAAe,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,OAAQJ,WAAW,EACjB,IAAK,UAAU,CACb,mBAAOJ,IAAA,CAACL,YAAY,GAAE,CAAC,CACzB,IAAK,SAAS,CACZ,mBAAOK,IAAA,CAACJ,WAAW,GAAE,CAAC,CACxB,IAAK,UAAU,CACb,mBAAOI,IAAA,CAACH,YAAY,GAAE,CAAC,CACzB,IAAK,OAAO,CACV,mBAAOG,IAAA,CAACF,SAAS,GAAE,CAAC,CACtB,QACE,mBAAOE,IAAA,CAACL,YAAY,GAAE,CAAC,CAC3B,CACF,CAAC,CAED,mBACEO,KAAA,QAAKO,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBV,IAAA,CAACN,OAAO,EACNU,WAAW,CAAEA,WAAY,CACzBO,YAAY,CAAEN,cAAe,CAC7BO,SAAS,CAAEN,gBAAiB,CAC5BO,gBAAgB,CAAEA,CAAA,GAAMN,mBAAmB,CAAC,CAACD,gBAAgB,CAAE,CAChE,CAAC,cAEFN,IAAA,SAAMS,SAAS,aAAAK,MAAA,CAAcR,gBAAgB,CAAG,mBAAmB,CAAG,EAAE,CAAG,CAAAI,QAAA,cACzEV,IAAA,QAAKS,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC5BF,iBAAiB,CAAC,CAAC,CACjB,CAAC,CACF,CAAC,EACJ,CAAC,CAEV,CAEA,cAAe,CAAAL,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}