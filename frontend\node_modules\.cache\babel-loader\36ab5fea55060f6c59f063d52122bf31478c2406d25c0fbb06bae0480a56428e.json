{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\n/**\n * UI自动化分析平台 - 主应用组件\n */\nimport React, { useState } from 'react';\nimport SimpleUpload from './components/SimpleUpload';\nimport SimpleResults from './components/SimpleResults';\nimport RealTimeAnalysis from './components/RealTimeAnalysis';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [appState, setAppState] = useState('upload'); // 'upload' | 'analyzing' | 'results'\n  const [currentTaskId, setCurrentTaskId] = useState('');\n  const [analysisResult, setAnalysisResult] = useState(null);\n  const [error, setError] = useState('');\n  const handleUploadSuccess = result => {\n    if (result.task_id) {\n      // 如果有任务ID，进入实时分析模式\n      setCurrentTaskId(result.task_id);\n      setAppState('analyzing');\n    } else {\n      // 如果直接返回结果，进入结果展示模式\n      setAnalysisResult(result);\n      setAppState('results');\n    }\n    setError('');\n  };\n  const handleUploadError = errorMessage => {\n    setError(errorMessage);\n  };\n  const handleAnalysisComplete = (taskData = null) => {\n    // 实时分析完成，设置最终结果\n    setAppState('results');\n    if (taskData) {\n      // 使用从API获取的真实数据\n      setAnalysisResult({\n        task_id: currentTaskId,\n        message: \"分析完成\",\n        result: taskData\n      });\n    } else {\n      // 使用默认数据\n      setAnalysisResult({\n        task_id: currentTaskId,\n        message: \"分析完成\",\n        result: {\n          status: \"completed\",\n          elements: [],\n          flows: [],\n          automation_scripts: []\n        }\n      });\n    }\n  };\n  const handleAnalysisError = errorMessage => {\n    setError(errorMessage);\n    setAppState('upload');\n  };\n  const handleReset = () => {\n    setAppState('upload');\n    setCurrentTaskId('');\n    setAnalysisResult(null);\n    setError('');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"app-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83E\\uDD16 UI\\u81EA\\u52A8\\u5316\\u5206\\u6790\\u5E73\\u53F0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u57FA\\u4E8EAI\\u667A\\u80FD\\u4F53\\u7684UI\\u754C\\u9762\\u5206\\u6790\\u548C\\u81EA\\u52A8\\u5316\\u6D4B\\u8BD5\\u811A\\u672C\\u751F\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), appState !== 'upload' && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"reset-button\",\n          onClick: handleReset,\n          children: \"\\u2190 \\u91CD\\u65B0\\u5F00\\u59CB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"app-main\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-banner\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"error-icon\",\n            children: \"\\u26A0\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"error-text\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"error-close\",\n            onClick: () => setError(''),\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this), appState === 'upload' && /*#__PURE__*/_jsxDEV(SimpleUpload, {\n          onUploadSuccess: handleUploadSuccess,\n          onUploadError: handleUploadError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 13\n        }, this), appState === 'analyzing' && currentTaskId && /*#__PURE__*/_jsxDEV(RealTimeAnalysis, {\n          taskId: currentTaskId,\n          onAnalysisComplete: handleAnalysisComplete,\n          onAnalysisError: handleAnalysisError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 13\n        }, this), appState === 'results' && analysisResult && /*#__PURE__*/_jsxDEV(SimpleResults, {\n          result: analysisResult\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"app-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\xA9 2024 UI\\u81EA\\u52A8\\u5316\\u5206\\u6790\\u5E73\\u53F0 - \\u57FA\\u4E8EFastAPI + React + AI\\u667A\\u80FD\\u4F53\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"3TI9Vtr+WUqebxz+MEMIiBjCZ08=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "SimpleUpload", "SimpleResults", "RealTimeAnalysis", "jsxDEV", "_jsxDEV", "App", "_s", "appState", "setAppState", "currentTaskId", "setCurrentTaskId", "analysisResult", "setAnalysisResult", "error", "setError", "handleUploadSuccess", "result", "task_id", "handleUploadError", "errorMessage", "handleAnalysisComplete", "taskData", "message", "status", "elements", "flows", "automation_scripts", "handleAnalysisError", "handleReset", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onUploadSuccess", "onUploadError", "taskId", "onAnalysisComplete", "onAnalysisError", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/App.js"], "sourcesContent": ["/**\n * UI自动化分析平台 - 主应用组件\n */\nimport React, { useState } from 'react';\nimport SimpleUpload from './components/SimpleUpload';\nimport SimpleResults from './components/SimpleResults';\nimport RealTimeAnalysis from './components/RealTimeAnalysis';\nimport './App.css';\n\nfunction App() {\n  const [appState, setAppState] = useState('upload'); // 'upload' | 'analyzing' | 'results'\n  const [currentTaskId, setCurrentTaskId] = useState('');\n  const [analysisResult, setAnalysisResult] = useState(null);\n  const [error, setError] = useState('');\n\n  const handleUploadSuccess = (result) => {\n    if (result.task_id) {\n      // 如果有任务ID，进入实时分析模式\n      setCurrentTaskId(result.task_id);\n      setAppState('analyzing');\n    } else {\n      // 如果直接返回结果，进入结果展示模式\n      setAnalysisResult(result);\n      setAppState('results');\n    }\n    setError('');\n  };\n\n  const handleUploadError = (errorMessage) => {\n    setError(errorMessage);\n  };\n\n  const handleAnalysisComplete = (taskData = null) => {\n    // 实时分析完成，设置最终结果\n    setAppState('results');\n\n    if (taskData) {\n      // 使用从API获取的真实数据\n      setAnalysisResult({\n        task_id: currentTaskId,\n        message: \"分析完成\",\n        result: taskData\n      });\n    } else {\n      // 使用默认数据\n      setAnalysisResult({\n        task_id: currentTaskId,\n        message: \"分析完成\",\n        result: {\n          status: \"completed\",\n          elements: [],\n          flows: [],\n          automation_scripts: []\n        }\n      });\n    }\n  };\n\n  const handleAnalysisError = (errorMessage) => {\n    setError(errorMessage);\n    setAppState('upload');\n  };\n\n  const handleReset = () => {\n    setAppState('upload');\n    setCurrentTaskId('');\n    setAnalysisResult(null);\n    setError('');\n  };\n\n  return (\n    <div className=\"app\">\n      <header className=\"app-header\">\n        <div className=\"container\">\n          <h1>🤖 UI自动化分析平台</h1>\n          <p>基于AI智能体的UI界面分析和自动化测试脚本生成</p>\n          {appState !== 'upload' && (\n            <button className=\"reset-button\" onClick={handleReset}>\n              ← 重新开始\n            </button>\n          )}\n        </div>\n      </header>\n\n      <main className=\"app-main\">\n        <div className=\"container\">\n          {error && (\n            <div className=\"error-banner\">\n              <span className=\"error-icon\">⚠️</span>\n              <span className=\"error-text\">{error}</span>\n              <button className=\"error-close\" onClick={() => setError('')}>\n                ✕\n              </button>\n            </div>\n          )}\n\n          {/* 实际组件 */}\n          {appState === 'upload' && (\n            <SimpleUpload\n              onUploadSuccess={handleUploadSuccess}\n              onUploadError={handleUploadError}\n            />\n          )}\n\n          {appState === 'analyzing' && currentTaskId && (\n            <RealTimeAnalysis\n              taskId={currentTaskId}\n              onAnalysisComplete={handleAnalysisComplete}\n              onAnalysisError={handleAnalysisError}\n            />\n          )}\n\n          {appState === 'results' && analysisResult && (\n            <SimpleResults result={analysisResult} />\n          )}\n        </div>\n      </main>\n\n      <footer className=\"app-footer\">\n        <div className=\"container\">\n          <p>© 2024 UI自动化分析平台 - 基于FastAPI + React + AI智能体</p>\n        </div>\n      </footer>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACpD,MAAM,CAACU,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACY,cAAc,EAAEC,iBAAiB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMgB,mBAAmB,GAAIC,MAAM,IAAK;IACtC,IAAIA,MAAM,CAACC,OAAO,EAAE;MAClB;MACAP,gBAAgB,CAACM,MAAM,CAACC,OAAO,CAAC;MAChCT,WAAW,CAAC,WAAW,CAAC;IAC1B,CAAC,MAAM;MACL;MACAI,iBAAiB,CAACI,MAAM,CAAC;MACzBR,WAAW,CAAC,SAAS,CAAC;IACxB;IACAM,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMI,iBAAiB,GAAIC,YAAY,IAAK;IAC1CL,QAAQ,CAACK,YAAY,CAAC;EACxB,CAAC;EAED,MAAMC,sBAAsB,GAAGA,CAACC,QAAQ,GAAG,IAAI,KAAK;IAClD;IACAb,WAAW,CAAC,SAAS,CAAC;IAEtB,IAAIa,QAAQ,EAAE;MACZ;MACAT,iBAAiB,CAAC;QAChBK,OAAO,EAAER,aAAa;QACtBa,OAAO,EAAE,MAAM;QACfN,MAAM,EAAEK;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAT,iBAAiB,CAAC;QAChBK,OAAO,EAAER,aAAa;QACtBa,OAAO,EAAE,MAAM;QACfN,MAAM,EAAE;UACNO,MAAM,EAAE,WAAW;UACnBC,QAAQ,EAAE,EAAE;UACZC,KAAK,EAAE,EAAE;UACTC,kBAAkB,EAAE;QACtB;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAIR,YAAY,IAAK;IAC5CL,QAAQ,CAACK,YAAY,CAAC;IACtBX,WAAW,CAAC,QAAQ,CAAC;EACvB,CAAC;EAED,MAAMoB,WAAW,GAAGA,CAAA,KAAM;IACxBpB,WAAW,CAAC,QAAQ,CAAC;IACrBE,gBAAgB,CAAC,EAAE,CAAC;IACpBE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,oBACEV,OAAA;IAAKyB,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClB1B,OAAA;MAAQyB,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC5B1B,OAAA;QAAKyB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB1B,OAAA;UAAA0B,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrB9B,OAAA;UAAA0B,QAAA,EAAG;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAC9B3B,QAAQ,KAAK,QAAQ,iBACpBH,OAAA;UAAQyB,SAAS,EAAC,cAAc;UAACM,OAAO,EAAEP,WAAY;UAAAE,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAET9B,OAAA;MAAMyB,SAAS,EAAC,UAAU;MAAAC,QAAA,eACxB1B,OAAA;QAAKyB,SAAS,EAAC,WAAW;QAAAC,QAAA,GACvBjB,KAAK,iBACJT,OAAA;UAAKyB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B1B,OAAA;YAAMyB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtC9B,OAAA;YAAMyB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEjB;UAAK;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3C9B,OAAA;YAAQyB,SAAS,EAAC,aAAa;YAACM,OAAO,EAAEA,CAAA,KAAMrB,QAAQ,CAAC,EAAE,CAAE;YAAAgB,QAAA,EAAC;UAE7D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,EAGA3B,QAAQ,KAAK,QAAQ,iBACpBH,OAAA,CAACJ,YAAY;UACXoC,eAAe,EAAErB,mBAAoB;UACrCsB,aAAa,EAAEnB;QAAkB;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CACF,EAEA3B,QAAQ,KAAK,WAAW,IAAIE,aAAa,iBACxCL,OAAA,CAACF,gBAAgB;UACfoC,MAAM,EAAE7B,aAAc;UACtB8B,kBAAkB,EAAEnB,sBAAuB;UAC3CoB,eAAe,EAAEb;QAAoB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CACF,EAEA3B,QAAQ,KAAK,SAAS,IAAII,cAAc,iBACvCP,OAAA,CAACH,aAAa;UAACe,MAAM,EAAEL;QAAe;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACzC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEP9B,OAAA;MAAQyB,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC5B1B,OAAA;QAAKyB,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB1B,OAAA;UAAA0B,QAAA,EAAG;QAA4C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAAC5B,EAAA,CApHQD,GAAG;AAAAoC,EAAA,GAAHpC,GAAG;AAsHZ,eAAeA,GAAG;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}