/**
 * 侧边导航栏组件
 */
import React from 'react';
import './Sidebar.css';

const Sidebar = ({ currentPage, onPageChange, collapsed, onToggleCollapse }) => {
  const menuItems = [
    {
      id: 'analysis',
      name: 'UI分析',
      icon: '🔍',
      description: '上传界面截图进行AI分析'
    },
    {
      id: 'history',
      name: '历史记录',
      icon: '📋',
      description: '查看分析历史和结果'
    },
    {
      id: 'settings',
      name: '设置',
      icon: '⚙️',
      description: '系统配置和参数设置'
    },
    {
      id: 'about',
      name: '关于',
      icon: 'ℹ️',
      description: '平台信息和使用说明'
    }
  ];

  return (
    <div className={`sidebar ${collapsed ? 'collapsed' : ''}`}>
      {/* 头部 */}
      <div className="sidebar-header">
        <div className="logo">
          <span className="logo-icon">🤖</span>
          {!collapsed && (
            <div className="logo-text">
              <h2>UI自动化</h2>
              <p>分析平台</p>
            </div>
          )}
        </div>
        <button 
          className="collapse-btn"
          onClick={onToggleCollapse}
          title={collapsed ? '展开侧边栏' : '收起侧边栏'}
        >
          {collapsed ? '→' : '←'}
        </button>
      </div>

      {/* 导航菜单 */}
      <nav className="sidebar-nav">
        <ul className="nav-list">
          {menuItems.map((item) => (
            <li key={item.id} className="nav-item">
              <button
                className={`nav-link ${currentPage === item.id ? 'active' : ''}`}
                onClick={() => onPageChange(item.id)}
                title={collapsed ? item.name : item.description}
              >
                <span className="nav-icon">{item.icon}</span>
                {!collapsed && (
                  <div className="nav-content">
                    <span className="nav-name">{item.name}</span>
                    <span className="nav-desc">{item.description}</span>
                  </div>
                )}
              </button>
            </li>
          ))}
        </ul>
      </nav>

      {/* 底部信息 */}
      {!collapsed && (
        <div className="sidebar-footer">
          <div className="version-info">
            <p>版本 v1.0.0</p>
            <p>基于AI智能体</p>
          </div>
        </div>
      )}


    </div>
  );
};

export default Sidebar;
