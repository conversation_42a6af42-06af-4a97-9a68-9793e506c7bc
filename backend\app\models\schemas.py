"""
数据模型定义
"""
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from enum import Enum

class AgentType(str, Enum):
    """智能体类型"""
    ELEMENT_DETECTION = "元素识别"
    INTERACTION_ANALYSIS = "交互分析"
    TEST_GENERATION = "用例生成"

class TaskStatus(str, Enum):
    """任务状态"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class AgentProgress(BaseModel):
    """智能体进度消息"""
    agent: AgentType
    stage: TaskStatus
    message: str
    progress: Optional[float] = None
    data: Optional[Dict[str, Any]] = None

class UIElement(BaseModel):
    """UI元素模型"""
    id: str
    name: str
    element_type: str
    description: str
    text_content: Optional[str] = None
    position: Dict[str, str]
    visual_features: Dict[str, str]
    functionality: str
    interaction_state: str
    confidence_score: float

class InteractionStep(BaseModel):
    """交互步骤模型"""
    step_id: int
    action: str
    target_element: str
    expected_result: str
    precondition: Optional[str] = None
    validation: Optional[str] = None

class InteractionFlow(BaseModel):
    """交互流程模型"""
    flow_name: str
    description: str
    steps: List[InteractionStep]
    success_criteria: str
    error_scenarios: List[str]

class TestStep(BaseModel):
    """测试步骤模型"""
    step_id: int
    action_type: str
    action_description: str
    visual_target: str
    expected_result: str
    validation_step: str

class AutomationScript(BaseModel):
    """UI自动化测试脚本模型"""
    script_name: str
    description: str
    priority: str
    estimated_duration: str
    preconditions: List[str]
    test_steps: List[TestStep]
    validation_points: List[str]
    yaml_content: Optional[str] = None  # MidScene.js YAML格式脚本内容

class AnalysisResult(BaseModel):
    """分析结果模型"""
    task_id: str
    elements: List[UIElement] = []
    flows: List[InteractionFlow] = []
    automation_scripts: List[AutomationScript] = []
    status: TaskStatus
    created_at: str
    completed_at: Optional[str] = None
    error_message: Optional[str] = None

class UploadRequest(BaseModel):
    """上传请求模型"""
    description: str

class AnalyzeResponse(BaseModel):
    """分析响应模型"""
    task_id: str
    message: str
    stream_url: str
