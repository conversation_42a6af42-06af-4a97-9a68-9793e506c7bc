/**
 * API 服务 - 与后端通信
 */
import axios from 'axios';

// API 基础配置
const API_BASE_URL = 'http://localhost:8001/api/v1';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.status, error.message);
    return Promise.reject(error);
  }
);

// 数据类型定义
export interface UploadResponse {
  task_id: string;
  message: string;
  stream_url: string;
}

export interface TaskStatus {
  task_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  elements: UIElement[];
  flows: InteractionFlow[];
  test_scenarios: TestScenario[];
  created_at: string;
  completed_at?: string;
  error_message?: string;
}

export interface UIElement {
  id: string;
  name: string;
  element_type: string;
  description: string;
  text_content?: string;
  position: {
    area: string;
    relative_to: string;
  };
  visual_features: {
    color: string;
    size: string;
    shape: string;
  };
  functionality: string;
  interaction_state: string;
  confidence_score: number;
}

export interface InteractionFlow {
  flow_name: string;
  description: string;
  steps: InteractionStep[];
  success_criteria: string;
  error_scenarios: string[];
}

export interface InteractionStep {
  step_id: number;
  action: string;
  target_element: string;
  expected_result: string;
  precondition?: string;
  validation?: string;
}

export interface TestScenario {
  scenario_name: string;
  description: string;
  priority: string;
  estimated_duration: string;
  preconditions: string[];
  test_steps: TestStep[];
  validation_points: string[];
}

export interface TestStep {
  step_id: number;
  action_type: string;
  action_description: string;
  visual_target: string;
  expected_result: string;
  validation_step: string;
}

export interface AgentProgress {
  agent: string;
  stage: string;
  message: string;
  progress?: number;
  data?: any;
  timestamp?: string;
}

// API 方法
export const apiService = {
  /**
   * 上传图片并启动分析
   */
  async uploadAndAnalyze(imageFile: File, description: string): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('image_file', imageFile);
    formData.append('description', description);

    const response = await apiClient.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  /**
   * 获取任务状态
   */
  async getTaskStatus(taskId: string): Promise<TaskStatus> {
    const response = await apiClient.get(`/tasks/${taskId}`);
    return response.data;
  },

  /**
   * 创建SSE连接获取实时进度
   */
  createEventSource(taskId: string): EventSource {
    const url = `${API_BASE_URL}/stream/${taskId}`;
    return new EventSource(url);
  },

  /**
   * 测试SSE连接
   */
  createTestEventSource(taskId: string): EventSource {
    const url = `${API_BASE_URL}/stream/${taskId}/test`;
    return new EventSource(url);
  },

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{ status: string; message: string }> {
    const response = await axios.get('http://localhost:8001/health');
    return response.data;
  },
};

export default apiService;
