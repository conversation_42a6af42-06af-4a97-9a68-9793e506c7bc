{"ast": null, "code": "import _objectSpread from\"E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";/**\n * 实时分析组件 - 基于SSE的实时进度展示\n */import React,{useState,useEffect,useRef}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const RealTimeAnalysis=_ref=>{let{taskId,onAnalysisComplete,onAnalysisError}=_ref;const[agents,setAgents]=useState([{name:'元素识别智能体',status:'pending',message:'等待开始...',progress:0,duration:null},{name:'交互分析智能体',status:'pending',message:'等待开始...',progress:0,duration:null},{name:'脚本生成智能体',status:'pending',message:'等待开始...',progress:0,duration:null}]);const[messages,setMessages]=useState([]);const[isConnected,setIsConnected]=useState(false);const[connectionError,setConnectionError]=useState(null);const[startTime,setStartTime]=useState(null);const eventSourceRef=useRef(null);useEffect(()=>{if(!taskId)return;// 创建SSE连接\nconst eventSource=new EventSource(\"http://localhost:8001/api/v1/stream/\".concat(taskId));eventSourceRef.current=eventSource;setStartTime(Date.now());eventSource.onopen=()=>{console.log('SSE连接已建立');setIsConnected(true);setConnectionError(null);};eventSource.addEventListener('connected',event=>{const data=JSON.parse(event.data);console.log('连接确认:',data);addMessage({type:'system',message:data.data.message,timestamp:new Date().toISOString()});});eventSource.addEventListener('agent_progress',event=>{const data=JSON.parse(event.data);console.log('智能体进度:',data);addMessage({type:'progress',agent:data.data.agent,message:data.data.message,progress:data.data.progress,timestamp:data.timestamp});updateAgentStatus(data.data);});eventSource.addEventListener('task_complete',event=>{const data=JSON.parse(event.data);console.log('任务完成:',data);addMessage({type:'complete',message:data.data.message||'所有智能体执行完成！',timestamp:data.timestamp});// 标记所有智能体为完成状态\nsetAgents(prev=>prev.map(agent=>_objectSpread(_objectSpread({},agent),{},{status:'completed',progress:100,duration:Date.now()-startTime})));// 获取完整的分析结果\nsetTimeout(async()=>{try{const response=await fetch(\"http://localhost:8001/api/v1/tasks/\".concat(taskId));if(response.ok){const taskData=await response.json();onAnalysisComplete(taskData);}else{onAnalysisComplete();}}catch(error){console.error('获取任务结果失败:',error);onAnalysisComplete();}},1000);});eventSource.addEventListener('error',event=>{const data=JSON.parse(event.data);console.error('SSE错误:',data);onAnalysisError(data.data.message||'分析过程中发生错误');});eventSource.addEventListener('heartbeat',event=>{const data=JSON.parse(event.data);console.log('心跳:',data);});eventSource.onerror=error=>{console.error('SSE连接错误:',error);setIsConnected(false);setConnectionError('连接中断，请刷新页面重试');};return()=>{if(eventSourceRef.current){eventSourceRef.current.close();}};},[taskId,onAnalysisComplete,onAnalysisError,startTime]);const addMessage=message=>{setMessages(prev=>[...prev,_objectSpread(_objectSpread({},message),{},{id:Date.now()+Math.random()})]);};const updateAgentStatus=progressData=>{setAgents(prev=>prev.map(agent=>{if(agent.name===progressData.agent){return _objectSpread(_objectSpread({},agent),{},{status:progressData.status,message:progressData.message,progress:progressData.progress||agent.progress,duration:progressData.duration||agent.duration});}return agent;}));};const getStatusIcon=status=>{switch(status){case'pending':return'⏳';case'processing':return'🔄';case'completed':return'✅';case'failed':return'❌';default:return'⏳';}};const getStatusColor=status=>{switch(status){case'pending':return'#6c757d';case'processing':return'#007bff';case'completed':return'#28a745';case'failed':return'#dc3545';default:return'#6c757d';}};const formatDuration=duration=>{if(!duration)return'';return\"\".concat((duration/1000).toFixed(1),\"s\");};return/*#__PURE__*/_jsxs(\"div\",{style:{background:'white',borderRadius:'12px',padding:'24px',boxShadow:'0 2px 12px rgba(0,0,0,0.1)',marginBottom:'24px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'20px'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0',color:'#333',fontSize:'20px',fontWeight:'600'},children:\"\\uD83D\\uDD04 \\u5B9E\\u65F6\\u5206\\u6790\\u8FDB\\u5EA6\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'8px',fontSize:'14px',fontWeight:'500',color:isConnected?'#28a745':'#dc3545'},children:[/*#__PURE__*/_jsx(\"span\",{style:{width:'8px',height:'8px',borderRadius:'50%',backgroundColor:'currentColor',animation:isConnected?'pulse 2s infinite':'none'}}),isConnected?'实时连接中':'连接中断']})]}),connectionError&&/*#__PURE__*/_jsx(\"div\",{style:{background:'#f8d7da',color:'#721c24',padding:'12px',borderRadius:'6px',marginBottom:'20px',border:'1px solid #f5c6cb'},children:connectionError}),/*#__PURE__*/_jsx(\"div\",{style:{display:'grid',gap:'16px',marginBottom:'24px'},children:agents.map((agent,index)=>/*#__PURE__*/_jsxs(\"div\",{style:{border:'1px solid #e9ecef',borderRadius:'8px',padding:'16px',background:'#f8f9fa'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'12px',marginBottom:'12px'},children:[/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'20px'},children:getStatusIcon(agent.status)}),/*#__PURE__*/_jsx(\"span\",{style:{fontWeight:'600',color:'#333',flex:1},children:agent.name}),/*#__PURE__*/_jsxs(\"span\",{style:{fontSize:'14px',fontWeight:'500',color:getStatusColor(agent.status)},children:[agent.status==='pending'&&'等待中',agent.status==='processing'&&'处理中',agent.status==='completed'&&'已完成',agent.status==='failed'&&'失败']}),agent.duration&&/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'12px',color:'#999'},children:formatDuration(agent.duration)})]}),/*#__PURE__*/_jsx(\"div\",{style:{width:'100%',height:'8px',background:'#e9ecef',borderRadius:'4px',overflow:'hidden',marginBottom:'8px'},children:/*#__PURE__*/_jsx(\"div\",{style:{height:'100%',width:\"\".concat(agent.progress,\"%\"),backgroundColor:getStatusColor(agent.status),transition:'width 0.3s ease',borderRadius:'4px'}})}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:'#666'},children:agent.message})]},agent.name))}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{style:{margin:'0 0 12px 0',color:'#333',fontSize:'16px',fontWeight:'600'},children:\"\\uD83D\\uDCCB \\u6267\\u884C\\u65E5\\u5FD7\"}),/*#__PURE__*/_jsxs(\"div\",{style:{maxHeight:'200px',overflowY:'auto',border:'1px solid #e9ecef',borderRadius:'6px',padding:'12px',background:'#f8f9fa'},children:[messages.map(message=>/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'8px',marginBottom:'8px',fontSize:'13px',lineHeight:'1.4'},children:[/*#__PURE__*/_jsx(\"span\",{style:{color:'#999',fontFamily:'monospace',minWidth:'80px'},children:new Date(message.timestamp).toLocaleTimeString()}),/*#__PURE__*/_jsx(\"span\",{style:{color:message.type==='system'?'#007bff':message.type==='complete'?'#28a745':'#333',fontWeight:message.type==='complete'?'500':'normal',minWidth:'80px'},children:message.agent?\"[\".concat(message.agent,\"]\"):'[系统]'}),/*#__PURE__*/_jsx(\"span\",{style:{color:'#333',flex:1},children:message.message})]},message.id)),messages.length===0&&/*#__PURE__*/_jsx(\"div\",{style:{color:'#999',textAlign:'center',padding:'20px'},children:\"\\u7B49\\u5F85\\u6D88\\u606F...\"})]})]}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:\"\\n        @keyframes pulse {\\n          0% { opacity: 1; }\\n          50% { opacity: 0.5; }\\n          100% { opacity: 1; }\\n        }\\n      \"})]});};export default RealTimeAnalysis;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsx", "_jsx", "jsxs", "_jsxs", "RealTimeAnalysis", "_ref", "taskId", "onAnalysisComplete", "onAnalysisError", "agents", "setAgents", "name", "status", "message", "progress", "duration", "messages", "setMessages", "isConnected", "setIsConnected", "connectionError", "setConnectionError", "startTime", "setStartTime", "eventSourceRef", "eventSource", "EventSource", "concat", "current", "Date", "now", "onopen", "console", "log", "addEventListener", "event", "data", "JSON", "parse", "addMessage", "type", "timestamp", "toISOString", "agent", "updateAgentStatus", "prev", "map", "_objectSpread", "setTimeout", "response", "fetch", "ok", "taskData", "json", "error", "onerror", "close", "id", "Math", "random", "progressData", "getStatusIcon", "getStatusColor", "formatDuration", "toFixed", "style", "background", "borderRadius", "padding", "boxShadow", "marginBottom", "children", "display", "justifyContent", "alignItems", "margin", "color", "fontSize", "fontWeight", "gap", "width", "height", "backgroundColor", "animation", "border", "index", "flex", "overflow", "transition", "maxHeight", "overflowY", "lineHeight", "fontFamily", "min<PERSON><PERSON><PERSON>", "toLocaleTimeString", "length", "textAlign"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/components/RealTimeAnalysis.js"], "sourcesContent": ["/**\n * 实时分析组件 - 基于SSE的实时进度展示\n */\nimport React, { useState, useEffect, useRef } from 'react';\n\nconst RealTimeAnalysis = ({ taskId, onAnalysisComplete, onAnalysisError }) => {\n  const [agents, setAgents] = useState([\n    { name: '元素识别智能体', status: 'pending', message: '等待开始...', progress: 0, duration: null },\n    { name: '交互分析智能体', status: 'pending', message: '等待开始...', progress: 0, duration: null },\n    { name: '脚本生成智能体', status: 'pending', message: '等待开始...', progress: 0, duration: null },\n  ]);\n  const [messages, setMessages] = useState([]);\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionError, setConnectionError] = useState(null);\n  const [startTime, setStartTime] = useState(null);\n  const eventSourceRef = useRef(null);\n\n  useEffect(() => {\n    if (!taskId) return;\n\n    // 创建SSE连接\n    const eventSource = new EventSource(`http://localhost:8001/api/v1/stream/${taskId}`);\n    eventSourceRef.current = eventSource;\n    setStartTime(Date.now());\n\n    eventSource.onopen = () => {\n      console.log('SSE连接已建立');\n      setIsConnected(true);\n      setConnectionError(null);\n    };\n\n    eventSource.addEventListener('connected', (event) => {\n      const data = JSON.parse(event.data);\n      console.log('连接确认:', data);\n      addMessage({\n        type: 'system',\n        message: data.data.message,\n        timestamp: new Date().toISOString(),\n      });\n    });\n\n    eventSource.addEventListener('agent_progress', (event) => {\n      const data = JSON.parse(event.data);\n      console.log('智能体进度:', data);\n      \n      addMessage({\n        type: 'progress',\n        agent: data.data.agent,\n        message: data.data.message,\n        progress: data.data.progress,\n        timestamp: data.timestamp,\n      });\n      \n      updateAgentStatus(data.data);\n    });\n\n    eventSource.addEventListener('task_complete', (event) => {\n      const data = JSON.parse(event.data);\n      console.log('任务完成:', data);\n      \n      addMessage({\n        type: 'complete',\n        message: data.data.message || '所有智能体执行完成！',\n        timestamp: data.timestamp,\n      });\n\n      // 标记所有智能体为完成状态\n      setAgents(prev => prev.map(agent => ({\n        ...agent,\n        status: 'completed',\n        progress: 100,\n        duration: Date.now() - startTime,\n      })));\n\n      // 获取完整的分析结果\n      setTimeout(async () => {\n        try {\n          const response = await fetch(`http://localhost:8001/api/v1/tasks/${taskId}`);\n          if (response.ok) {\n            const taskData = await response.json();\n            onAnalysisComplete(taskData);\n          } else {\n            onAnalysisComplete();\n          }\n        } catch (error) {\n          console.error('获取任务结果失败:', error);\n          onAnalysisComplete();\n        }\n      }, 1000);\n    });\n\n    eventSource.addEventListener('error', (event) => {\n      const data = JSON.parse(event.data);\n      console.error('SSE错误:', data);\n      onAnalysisError(data.data.message || '分析过程中发生错误');\n    });\n\n    eventSource.addEventListener('heartbeat', (event) => {\n      const data = JSON.parse(event.data);\n      console.log('心跳:', data);\n    });\n\n    eventSource.onerror = (error) => {\n      console.error('SSE连接错误:', error);\n      setIsConnected(false);\n      setConnectionError('连接中断，请刷新页面重试');\n    };\n\n    return () => {\n      if (eventSourceRef.current) {\n        eventSourceRef.current.close();\n      }\n    };\n  }, [taskId, onAnalysisComplete, onAnalysisError, startTime]);\n\n  const addMessage = (message) => {\n    setMessages(prev => [...prev, { ...message, id: Date.now() + Math.random() }]);\n  };\n\n  const updateAgentStatus = (progressData) => {\n    setAgents(prev => prev.map(agent => {\n      if (agent.name === progressData.agent) {\n        return {\n          ...agent,\n          status: progressData.status,\n          message: progressData.message,\n          progress: progressData.progress || agent.progress,\n          duration: progressData.duration || agent.duration,\n        };\n      }\n      return agent;\n    }));\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'pending':\n        return '⏳';\n      case 'processing':\n        return '🔄';\n      case 'completed':\n        return '✅';\n      case 'failed':\n        return '❌';\n      default:\n        return '⏳';\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'pending':\n        return '#6c757d';\n      case 'processing':\n        return '#007bff';\n      case 'completed':\n        return '#28a745';\n      case 'failed':\n        return '#dc3545';\n      default:\n        return '#6c757d';\n    }\n  };\n\n  const formatDuration = (duration) => {\n    if (!duration) return '';\n    return `${(duration / 1000).toFixed(1)}s`;\n  };\n\n  return (\n    <div style={{ \n      background: 'white', \n      borderRadius: '12px', \n      padding: '24px', \n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n      marginBottom: '24px'\n    }}>\n      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>\n        <h3 style={{ margin: '0', color: '#333', fontSize: '20px', fontWeight: '600' }}>\n          🔄 实时分析进度\n        </h3>\n        <div style={{\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px',\n          fontSize: '14px',\n          fontWeight: '500',\n          color: isConnected ? '#28a745' : '#dc3545'\n        }}>\n          <span style={{\n            width: '8px',\n            height: '8px',\n            borderRadius: '50%',\n            backgroundColor: 'currentColor',\n            animation: isConnected ? 'pulse 2s infinite' : 'none'\n          }}></span>\n          {isConnected ? '实时连接中' : '连接中断'}\n        </div>\n      </div>\n\n      {connectionError && (\n        <div style={{\n          background: '#f8d7da',\n          color: '#721c24',\n          padding: '12px',\n          borderRadius: '6px',\n          marginBottom: '20px',\n          border: '1px solid #f5c6cb'\n        }}>\n          {connectionError}\n        </div>\n      )}\n\n      {/* 智能体状态 */}\n      <div style={{ display: 'grid', gap: '16px', marginBottom: '24px' }}>\n        {agents.map((agent, index) => (\n          <div key={agent.name} style={{\n            border: '1px solid #e9ecef',\n            borderRadius: '8px',\n            padding: '16px',\n            background: '#f8f9fa'\n          }}>\n            <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>\n              <span style={{ fontSize: '20px' }}>{getStatusIcon(agent.status)}</span>\n              <span style={{ fontWeight: '600', color: '#333', flex: 1 }}>{agent.name}</span>\n              <span style={{ \n                fontSize: '14px', \n                fontWeight: '500',\n                color: getStatusColor(agent.status)\n              }}>\n                {agent.status === 'pending' && '等待中'}\n                {agent.status === 'processing' && '处理中'}\n                {agent.status === 'completed' && '已完成'}\n                {agent.status === 'failed' && '失败'}\n              </span>\n              {agent.duration && (\n                <span style={{ fontSize: '12px', color: '#999' }}>\n                  {formatDuration(agent.duration)}\n                </span>\n              )}\n            </div>\n            \n            <div style={{\n              width: '100%',\n              height: '8px',\n              background: '#e9ecef',\n              borderRadius: '4px',\n              overflow: 'hidden',\n              marginBottom: '8px'\n            }}>\n              <div style={{\n                height: '100%',\n                width: `${agent.progress}%`,\n                backgroundColor: getStatusColor(agent.status),\n                transition: 'width 0.3s ease',\n                borderRadius: '4px'\n              }}></div>\n            </div>\n            \n            <div style={{ fontSize: '14px', color: '#666' }}>{agent.message}</div>\n          </div>\n        ))}\n      </div>\n\n      {/* 消息流 */}\n      <div>\n        <h4 style={{ margin: '0 0 12px 0', color: '#333', fontSize: '16px', fontWeight: '600' }}>\n          📋 执行日志\n        </h4>\n        <div style={{\n          maxHeight: '200px',\n          overflowY: 'auto',\n          border: '1px solid #e9ecef',\n          borderRadius: '6px',\n          padding: '12px',\n          background: '#f8f9fa'\n        }}>\n          {messages.map((message) => (\n            <div key={message.id} style={{\n              display: 'flex',\n              gap: '8px',\n              marginBottom: '8px',\n              fontSize: '13px',\n              lineHeight: '1.4'\n            }}>\n              <span style={{ color: '#999', fontFamily: 'monospace', minWidth: '80px' }}>\n                {new Date(message.timestamp).toLocaleTimeString()}\n              </span>\n              <span style={{\n                color: message.type === 'system' ? '#007bff' : \n                       message.type === 'complete' ? '#28a745' : '#333',\n                fontWeight: message.type === 'complete' ? '500' : 'normal',\n                minWidth: '80px'\n              }}>\n                {message.agent ? `[${message.agent}]` : '[系统]'}\n              </span>\n              <span style={{ color: '#333', flex: 1 }}>{message.message}</span>\n            </div>\n          ))}\n          {messages.length === 0 && (\n            <div style={{ color: '#999', textAlign: 'center', padding: '20px' }}>\n              等待消息...\n            </div>\n          )}\n        </div>\n      </div>\n\n      <style jsx>{`\n        @keyframes pulse {\n          0% { opacity: 1; }\n          50% { opacity: 0.5; }\n          100% { opacity: 1; }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default RealTimeAnalysis;\n"], "mappings": "wJAAA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3D,KAAM,CAAAC,gBAAgB,CAAGC,IAAA,EAAqD,IAApD,CAAEC,MAAM,CAAEC,kBAAkB,CAAEC,eAAgB,CAAC,CAAAH,IAAA,CACvE,KAAM,CAACI,MAAM,CAAEC,SAAS,CAAC,CAAGb,QAAQ,CAAC,CACnC,CAAEc,IAAI,CAAE,SAAS,CAAEC,MAAM,CAAE,SAAS,CAAEC,OAAO,CAAE,SAAS,CAAEC,QAAQ,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAK,CAAC,CACvF,CAAEJ,IAAI,CAAE,SAAS,CAAEC,MAAM,CAAE,SAAS,CAAEC,OAAO,CAAE,SAAS,CAAEC,QAAQ,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAK,CAAC,CACvF,CAAEJ,IAAI,CAAE,SAAS,CAAEC,MAAM,CAAE,SAAS,CAAEC,OAAO,CAAE,SAAS,CAAEC,QAAQ,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAK,CAAC,CACxF,CAAC,CACF,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGpB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACqB,WAAW,CAAEC,cAAc,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACuB,eAAe,CAAEC,kBAAkB,CAAC,CAAGxB,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAACyB,SAAS,CAAEC,YAAY,CAAC,CAAG1B,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAAA2B,cAAc,CAAGzB,MAAM,CAAC,IAAI,CAAC,CAEnCD,SAAS,CAAC,IAAM,CACd,GAAI,CAACQ,MAAM,CAAE,OAEb;AACA,KAAM,CAAAmB,WAAW,CAAG,GAAI,CAAAC,WAAW,wCAAAC,MAAA,CAAwCrB,MAAM,CAAE,CAAC,CACpFkB,cAAc,CAACI,OAAO,CAAGH,WAAW,CACpCF,YAAY,CAACM,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAExBL,WAAW,CAACM,MAAM,CAAG,IAAM,CACzBC,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC,CACvBd,cAAc,CAAC,IAAI,CAAC,CACpBE,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAEDI,WAAW,CAACS,gBAAgB,CAAC,WAAW,CAAGC,KAAK,EAAK,CACnD,KAAM,CAAAC,IAAI,CAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC,CACnCJ,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEG,IAAI,CAAC,CAC1BG,UAAU,CAAC,CACTC,IAAI,CAAE,QAAQ,CACd3B,OAAO,CAAEuB,IAAI,CAACA,IAAI,CAACvB,OAAO,CAC1B4B,SAAS,CAAE,GAAI,CAAAZ,IAAI,CAAC,CAAC,CAACa,WAAW,CAAC,CACpC,CAAC,CAAC,CACJ,CAAC,CAAC,CAEFjB,WAAW,CAACS,gBAAgB,CAAC,gBAAgB,CAAGC,KAAK,EAAK,CACxD,KAAM,CAAAC,IAAI,CAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC,CACnCJ,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAEG,IAAI,CAAC,CAE3BG,UAAU,CAAC,CACTC,IAAI,CAAE,UAAU,CAChBG,KAAK,CAAEP,IAAI,CAACA,IAAI,CAACO,KAAK,CACtB9B,OAAO,CAAEuB,IAAI,CAACA,IAAI,CAACvB,OAAO,CAC1BC,QAAQ,CAAEsB,IAAI,CAACA,IAAI,CAACtB,QAAQ,CAC5B2B,SAAS,CAAEL,IAAI,CAACK,SAClB,CAAC,CAAC,CAEFG,iBAAiB,CAACR,IAAI,CAACA,IAAI,CAAC,CAC9B,CAAC,CAAC,CAEFX,WAAW,CAACS,gBAAgB,CAAC,eAAe,CAAGC,KAAK,EAAK,CACvD,KAAM,CAAAC,IAAI,CAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC,CACnCJ,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEG,IAAI,CAAC,CAE1BG,UAAU,CAAC,CACTC,IAAI,CAAE,UAAU,CAChB3B,OAAO,CAAEuB,IAAI,CAACA,IAAI,CAACvB,OAAO,EAAI,YAAY,CAC1C4B,SAAS,CAAEL,IAAI,CAACK,SAClB,CAAC,CAAC,CAEF;AACA/B,SAAS,CAACmC,IAAI,EAAIA,IAAI,CAACC,GAAG,CAACH,KAAK,EAAAI,aAAA,CAAAA,aAAA,IAC3BJ,KAAK,MACR/B,MAAM,CAAE,WAAW,CACnBE,QAAQ,CAAE,GAAG,CACbC,QAAQ,CAAEc,IAAI,CAACC,GAAG,CAAC,CAAC,CAAGR,SAAS,EAChC,CAAC,CAAC,CAEJ;AACA0B,UAAU,CAAC,SAAY,CACrB,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,uCAAAvB,MAAA,CAAuCrB,MAAM,CAAE,CAAC,CAC5E,GAAI2C,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CACtC9C,kBAAkB,CAAC6C,QAAQ,CAAC,CAC9B,CAAC,IAAM,CACL7C,kBAAkB,CAAC,CAAC,CACtB,CACF,CAAE,MAAO+C,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC/C,kBAAkB,CAAC,CAAC,CACtB,CACF,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAAC,CAEFkB,WAAW,CAACS,gBAAgB,CAAC,OAAO,CAAGC,KAAK,EAAK,CAC/C,KAAM,CAAAC,IAAI,CAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC,CACnCJ,OAAO,CAACsB,KAAK,CAAC,QAAQ,CAAElB,IAAI,CAAC,CAC7B5B,eAAe,CAAC4B,IAAI,CAACA,IAAI,CAACvB,OAAO,EAAI,WAAW,CAAC,CACnD,CAAC,CAAC,CAEFY,WAAW,CAACS,gBAAgB,CAAC,WAAW,CAAGC,KAAK,EAAK,CACnD,KAAM,CAAAC,IAAI,CAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC,CACnCJ,OAAO,CAACC,GAAG,CAAC,KAAK,CAAEG,IAAI,CAAC,CAC1B,CAAC,CAAC,CAEFX,WAAW,CAAC8B,OAAO,CAAID,KAAK,EAAK,CAC/BtB,OAAO,CAACsB,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAChCnC,cAAc,CAAC,KAAK,CAAC,CACrBE,kBAAkB,CAAC,cAAc,CAAC,CACpC,CAAC,CAED,MAAO,IAAM,CACX,GAAIG,cAAc,CAACI,OAAO,CAAE,CAC1BJ,cAAc,CAACI,OAAO,CAAC4B,KAAK,CAAC,CAAC,CAChC,CACF,CAAC,CACH,CAAC,CAAE,CAAClD,MAAM,CAAEC,kBAAkB,CAAEC,eAAe,CAAEc,SAAS,CAAC,CAAC,CAE5D,KAAM,CAAAiB,UAAU,CAAI1B,OAAO,EAAK,CAC9BI,WAAW,CAAC4B,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAAE,aAAA,CAAAA,aAAA,IAAOlC,OAAO,MAAE4C,EAAE,CAAE5B,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG4B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAChF,CAAC,CAED,KAAM,CAAAf,iBAAiB,CAAIgB,YAAY,EAAK,CAC1ClD,SAAS,CAACmC,IAAI,EAAIA,IAAI,CAACC,GAAG,CAACH,KAAK,EAAI,CAClC,GAAIA,KAAK,CAAChC,IAAI,GAAKiD,YAAY,CAACjB,KAAK,CAAE,CACrC,OAAAI,aAAA,CAAAA,aAAA,IACKJ,KAAK,MACR/B,MAAM,CAAEgD,YAAY,CAAChD,MAAM,CAC3BC,OAAO,CAAE+C,YAAY,CAAC/C,OAAO,CAC7BC,QAAQ,CAAE8C,YAAY,CAAC9C,QAAQ,EAAI6B,KAAK,CAAC7B,QAAQ,CACjDC,QAAQ,CAAE6C,YAAY,CAAC7C,QAAQ,EAAI4B,KAAK,CAAC5B,QAAQ,GAErD,CACA,MAAO,CAAA4B,KAAK,CACd,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAkB,aAAa,CAAIjD,MAAM,EAAK,CAChC,OAAQA,MAAM,EACZ,IAAK,SAAS,CACZ,MAAO,GAAG,CACZ,IAAK,YAAY,CACf,MAAO,IAAI,CACb,IAAK,WAAW,CACd,MAAO,GAAG,CACZ,IAAK,QAAQ,CACX,MAAO,GAAG,CACZ,QACE,MAAO,GAAG,CACd,CACF,CAAC,CAED,KAAM,CAAAkD,cAAc,CAAIlD,MAAM,EAAK,CACjC,OAAQA,MAAM,EACZ,IAAK,SAAS,CACZ,MAAO,SAAS,CAClB,IAAK,YAAY,CACf,MAAO,SAAS,CAClB,IAAK,WAAW,CACd,MAAO,SAAS,CAClB,IAAK,QAAQ,CACX,MAAO,SAAS,CAClB,QACE,MAAO,SAAS,CACpB,CACF,CAAC,CAED,KAAM,CAAAmD,cAAc,CAAIhD,QAAQ,EAAK,CACnC,GAAI,CAACA,QAAQ,CAAE,MAAO,EAAE,CACxB,SAAAY,MAAA,CAAU,CAACZ,QAAQ,CAAG,IAAI,EAAEiD,OAAO,CAAC,CAAC,CAAC,MACxC,CAAC,CAED,mBACE7D,KAAA,QAAK8D,KAAK,CAAE,CACVC,UAAU,CAAE,OAAO,CACnBC,YAAY,CAAE,MAAM,CACpBC,OAAO,CAAE,MAAM,CACfC,SAAS,CAAE,4BAA4B,CACvCC,YAAY,CAAE,MAChB,CAAE,CAAAC,QAAA,eACApE,KAAA,QAAK8D,KAAK,CAAE,CAAEO,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEJ,YAAY,CAAE,MAAO,CAAE,CAAAC,QAAA,eAC3GtE,IAAA,OAAIgE,KAAK,CAAE,CAAEU,MAAM,CAAE,GAAG,CAAEC,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,KAAM,CAAE,CAAAP,QAAA,CAAC,mDAEhF,CAAI,CAAC,cACLpE,KAAA,QAAK8D,KAAK,CAAE,CACVO,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBK,GAAG,CAAE,KAAK,CACVF,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KAAK,CACjBF,KAAK,CAAE1D,WAAW,CAAG,SAAS,CAAG,SACnC,CAAE,CAAAqD,QAAA,eACAtE,IAAA,SAAMgE,KAAK,CAAE,CACXe,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE,KAAK,CACbd,YAAY,CAAE,KAAK,CACnBe,eAAe,CAAE,cAAc,CAC/BC,SAAS,CAAEjE,WAAW,CAAG,mBAAmB,CAAG,MACjD,CAAE,CAAO,CAAC,CACTA,WAAW,CAAG,OAAO,CAAG,MAAM,EAC5B,CAAC,EACH,CAAC,CAELE,eAAe,eACdnB,IAAA,QAAKgE,KAAK,CAAE,CACVC,UAAU,CAAE,SAAS,CACrBU,KAAK,CAAE,SAAS,CAChBR,OAAO,CAAE,MAAM,CACfD,YAAY,CAAE,KAAK,CACnBG,YAAY,CAAE,MAAM,CACpBc,MAAM,CAAE,mBACV,CAAE,CAAAb,QAAA,CACCnD,eAAe,CACb,CACN,cAGDnB,IAAA,QAAKgE,KAAK,CAAE,CAAEO,OAAO,CAAE,MAAM,CAAEO,GAAG,CAAE,MAAM,CAAET,YAAY,CAAE,MAAO,CAAE,CAAAC,QAAA,CAChE9D,MAAM,CAACqC,GAAG,CAAC,CAACH,KAAK,CAAE0C,KAAK,gBACvBlF,KAAA,QAAsB8D,KAAK,CAAE,CAC3BmB,MAAM,CAAE,mBAAmB,CAC3BjB,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,MAAM,CACfF,UAAU,CAAE,SACd,CAAE,CAAAK,QAAA,eACApE,KAAA,QAAK8D,KAAK,CAAE,CAAEO,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEK,GAAG,CAAE,MAAM,CAAET,YAAY,CAAE,MAAO,CAAE,CAAAC,QAAA,eACvFtE,IAAA,SAAMgE,KAAK,CAAE,CAAEY,QAAQ,CAAE,MAAO,CAAE,CAAAN,QAAA,CAAEV,aAAa,CAAClB,KAAK,CAAC/B,MAAM,CAAC,CAAO,CAAC,cACvEX,IAAA,SAAMgE,KAAK,CAAE,CAAEa,UAAU,CAAE,KAAK,CAAEF,KAAK,CAAE,MAAM,CAAEU,IAAI,CAAE,CAAE,CAAE,CAAAf,QAAA,CAAE5B,KAAK,CAAChC,IAAI,CAAO,CAAC,cAC/ER,KAAA,SAAM8D,KAAK,CAAE,CACXY,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KAAK,CACjBF,KAAK,CAAEd,cAAc,CAACnB,KAAK,CAAC/B,MAAM,CACpC,CAAE,CAAA2D,QAAA,EACC5B,KAAK,CAAC/B,MAAM,GAAK,SAAS,EAAI,KAAK,CACnC+B,KAAK,CAAC/B,MAAM,GAAK,YAAY,EAAI,KAAK,CACtC+B,KAAK,CAAC/B,MAAM,GAAK,WAAW,EAAI,KAAK,CACrC+B,KAAK,CAAC/B,MAAM,GAAK,QAAQ,EAAI,IAAI,EAC9B,CAAC,CACN+B,KAAK,CAAC5B,QAAQ,eACbd,IAAA,SAAMgE,KAAK,CAAE,CAAEY,QAAQ,CAAE,MAAM,CAAED,KAAK,CAAE,MAAO,CAAE,CAAAL,QAAA,CAC9CR,cAAc,CAACpB,KAAK,CAAC5B,QAAQ,CAAC,CAC3B,CACP,EACE,CAAC,cAENd,IAAA,QAAKgE,KAAK,CAAE,CACVe,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,KAAK,CACbf,UAAU,CAAE,SAAS,CACrBC,YAAY,CAAE,KAAK,CACnBoB,QAAQ,CAAE,QAAQ,CAClBjB,YAAY,CAAE,KAChB,CAAE,CAAAC,QAAA,cACAtE,IAAA,QAAKgE,KAAK,CAAE,CACVgB,MAAM,CAAE,MAAM,CACdD,KAAK,IAAArD,MAAA,CAAKgB,KAAK,CAAC7B,QAAQ,KAAG,CAC3BoE,eAAe,CAAEpB,cAAc,CAACnB,KAAK,CAAC/B,MAAM,CAAC,CAC7C4E,UAAU,CAAE,iBAAiB,CAC7BrB,YAAY,CAAE,KAChB,CAAE,CAAM,CAAC,CACN,CAAC,cAENlE,IAAA,QAAKgE,KAAK,CAAE,CAAEY,QAAQ,CAAE,MAAM,CAAED,KAAK,CAAE,MAAO,CAAE,CAAAL,QAAA,CAAE5B,KAAK,CAAC9B,OAAO,CAAM,CAAC,GA3C9D8B,KAAK,CAAChC,IA4CX,CACN,CAAC,CACC,CAAC,cAGNR,KAAA,QAAAoE,QAAA,eACEtE,IAAA,OAAIgE,KAAK,CAAE,CAAEU,MAAM,CAAE,YAAY,CAAEC,KAAK,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,KAAM,CAAE,CAAAP,QAAA,CAAC,uCAEzF,CAAI,CAAC,cACLpE,KAAA,QAAK8D,KAAK,CAAE,CACVwB,SAAS,CAAE,OAAO,CAClBC,SAAS,CAAE,MAAM,CACjBN,MAAM,CAAE,mBAAmB,CAC3BjB,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,MAAM,CACfF,UAAU,CAAE,SACd,CAAE,CAAAK,QAAA,EACCvD,QAAQ,CAAC8B,GAAG,CAAEjC,OAAO,eACpBV,KAAA,QAAsB8D,KAAK,CAAE,CAC3BO,OAAO,CAAE,MAAM,CACfO,GAAG,CAAE,KAAK,CACVT,YAAY,CAAE,KAAK,CACnBO,QAAQ,CAAE,MAAM,CAChBc,UAAU,CAAE,KACd,CAAE,CAAApB,QAAA,eACAtE,IAAA,SAAMgE,KAAK,CAAE,CAAEW,KAAK,CAAE,MAAM,CAAEgB,UAAU,CAAE,WAAW,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAtB,QAAA,CACvE,GAAI,CAAA1C,IAAI,CAAChB,OAAO,CAAC4B,SAAS,CAAC,CAACqD,kBAAkB,CAAC,CAAC,CAC7C,CAAC,cACP7F,IAAA,SAAMgE,KAAK,CAAE,CACXW,KAAK,CAAE/D,OAAO,CAAC2B,IAAI,GAAK,QAAQ,CAAG,SAAS,CACrC3B,OAAO,CAAC2B,IAAI,GAAK,UAAU,CAAG,SAAS,CAAG,MAAM,CACvDsC,UAAU,CAAEjE,OAAO,CAAC2B,IAAI,GAAK,UAAU,CAAG,KAAK,CAAG,QAAQ,CAC1DqD,QAAQ,CAAE,MACZ,CAAE,CAAAtB,QAAA,CACC1D,OAAO,CAAC8B,KAAK,KAAAhB,MAAA,CAAOd,OAAO,CAAC8B,KAAK,MAAM,MAAM,CAC1C,CAAC,cACP1C,IAAA,SAAMgE,KAAK,CAAE,CAAEW,KAAK,CAAE,MAAM,CAAEU,IAAI,CAAE,CAAE,CAAE,CAAAf,QAAA,CAAE1D,OAAO,CAACA,OAAO,CAAO,CAAC,GAlBzDA,OAAO,CAAC4C,EAmBb,CACN,CAAC,CACDzC,QAAQ,CAAC+E,MAAM,GAAK,CAAC,eACpB9F,IAAA,QAAKgE,KAAK,CAAE,CAAEW,KAAK,CAAE,MAAM,CAAEoB,SAAS,CAAE,QAAQ,CAAE5B,OAAO,CAAE,MAAO,CAAE,CAAAG,QAAA,CAAC,6BAErE,CAAK,CACN,EACE,CAAC,EACH,CAAC,cAENtE,IAAA,UAAOD,GAAG,MAAAuE,QAAA,kJAMD,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnE,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}