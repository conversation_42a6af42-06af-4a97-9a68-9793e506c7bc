{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\components\\\\SimpleUpload.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SimpleUpload = ({\n  onUploadSuccess,\n  onUploadError\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const [showAnalysis, setShowAnalysis] = useState(true); // 默认显示进度框以便查看效果\n  const [analysisProgress, setAnalysisProgress] = useState({\n    overall: 0,\n    currentStep: '准备开始分析...',\n    steps: [{\n      name: '初始化',\n      status: 'pending',\n      progress: 0\n    }, {\n      name: '元素分析和智能识别',\n      status: 'pending',\n      progress: 0\n    }, {\n      name: '生成自动化测试脚本',\n      status: 'pending',\n      progress: 0\n    }]\n  });\n  const [analysisLogs, setAnalysisLogs] = useState([]);\n  const logContainerRef = useRef(null);\n\n  // 自动滚动到最新日志\n  useEffect(() => {\n    if (logContainerRef.current) {\n      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;\n    }\n  }, [analysisLogs]);\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (file) {\n      console.log('📁 文件选择:', {\n        name: file.name,\n        size: file.size,\n        type: file.type,\n        lastModified: new Date(file.lastModified).toLocaleString()\n      });\n      setSelectedFile(file);\n    }\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n    console.log('🚀 开始提交表单...');\n    console.log('  - 文件:', selectedFile === null || selectedFile === void 0 ? void 0 : selectedFile.name);\n    console.log('  - 描述:', description.trim());\n    if (!selectedFile) {\n      console.log('  ❌ 验证失败: 未选择文件');\n      onUploadError('请选择图片文件');\n      return;\n    }\n    if (!description.trim()) {\n      console.log('  ❌ 验证失败: 描述为空');\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n    console.log('  ✅ 表单验证通过');\n    setIsUploading(true);\n    setShowAnalysis(true);\n    console.log('  📊 显示分析界面');\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n      console.log('  📦 构建FormData完成');\n      console.log('  🎯 开始模拟分析进度...');\n\n      // 开始模拟分析进度\n      simulateAnalysisProgress();\n      console.log('  📡 发送上传请求到后端...');\n      const response = await fetch('http://localhost:8000/api/v1/upload', {\n        method: 'POST',\n        body: formData,\n        mode: 'cors'\n      });\n      console.log('  📨 收到后端响应:', response.status, response.statusText);\n      if (!response.ok) {\n        const errorData = await response.json();\n        console.log('  ❌ 后端返回错误:', errorData);\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n      const result = await response.json();\n      console.log('  ✅ 上传成功:', result);\n      onUploadSuccess(result);\n    } catch (error) {\n      console.error('  ❌ 上传失败:', error);\n      setShowAnalysis(false);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n      console.log('  🏁 上传流程结束');\n    }\n  };\n  const addLog = (message, type = 'info') => {\n    const timestamp = new Date().toLocaleTimeString();\n    console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`);\n    setAnalysisLogs(prev => [...prev, {\n      id: Date.now() + Math.random(),\n      timestamp,\n      message,\n      type\n    }]);\n  };\n  const simulateAnalysisProgress = () => {\n    let step = 0;\n    const steps = ['初始化', '元素分析和智能识别', '生成自动化测试脚本'];\n\n    // 清空之前的日志\n    setAnalysisLogs([]);\n\n    // 详细的步骤日志\n    const stepLogs = {\n      0: [\n      // 初始化\n      '🚀 开始分析流程...', '📋 检查上传文件格式和大小', '🔧 初始化AI分析引擎', '📊 加载UI元素识别模型', '✅ 初始化完成'],\n      1: [\n      // 元素分析和智能识别\n      '🔍 开始图像预处理...', '🎯 检测UI界面元素', '📝 识别文本内容和标签', '🔘 分析按钮和交互元素', '📋 识别输入框和表单元素', '🎨 分析布局和样式信息', '🧠 AI智能分类UI组件', '✅ 元素分析完成'],\n      2: [\n      // 生成自动化测试脚本\n      '📝 开始生成测试脚本...', '🔧 构建MidScene.js测试框架', '📋 生成元素定位策略', '⚡ 创建交互操作脚本', '🧪 添加断言和验证逻辑', '📄 格式化YAML输出', '✅ 测试脚本生成完成']\n    };\n    const updateProgress = () => {\n      if (step < steps.length) {\n        // 添加步骤开始日志\n        addLog(`开始执行: ${steps[step]}`, 'step');\n\n        // 更新进度状态\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: Math.round((step + 1) / steps.length * 100),\n          currentStep: `正在执行: ${steps[step]}`,\n          steps: prev.steps.map((s, index) => ({\n            ...s,\n            status: index < step ? 'completed' : index === step ? 'processing' : 'pending',\n            progress: index < step ? 100 : index === step ? 0 : 0\n          }))\n        }));\n\n        // 模拟步骤内的详细日志\n        const currentStepLogs = stepLogs[step];\n        let logIndex = 0;\n        const addStepLogs = () => {\n          if (logIndex < currentStepLogs.length) {\n            addLog(currentStepLogs[logIndex], 'info');\n\n            // 更新当前步骤进度\n            const progress = Math.round((logIndex + 1) / currentStepLogs.length * 100);\n            setAnalysisProgress(prev => ({\n              ...prev,\n              steps: prev.steps.map((s, index) => index === step ? {\n                ...s,\n                progress\n              } : s)\n            }));\n            logIndex++;\n            setTimeout(addStepLogs, 800 + Math.random() * 1200);\n          } else {\n            // 当前步骤完成\n            setAnalysisProgress(prev => ({\n              ...prev,\n              steps: prev.steps.map((s, index) => index === step ? {\n                ...s,\n                status: 'completed',\n                progress: 100\n              } : s)\n            }));\n            step++;\n            setTimeout(updateProgress, 1000);\n          }\n        };\n        setTimeout(addStepLogs, 500);\n      } else {\n        // 所有步骤完成\n        addLog('🎉 所有分析步骤已完成！', 'success');\n        addLog('📄 测试脚本已生成，可以下载使用', 'success');\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: 100,\n          currentStep: '分析完成',\n          steps: prev.steps.map(s => ({\n            ...s,\n            status: 'completed',\n            progress: 100\n          }))\n        }));\n      }\n    };\n\n    // 开始分析\n    addLog('🔥 启动UI自动化测试脚本分析...', 'start');\n    setTimeout(updateProgress, 1000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '24px',\n      height: '100vh',\n      padding: '24px',\n      width: '100%',\n      boxSizing: 'border-box',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: showAnalysis ? '0 0 400px' : '1',\n        background: 'white',\n        padding: '24px',\n        borderRadius: '12px',\n        boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n        transition: 'all 0.3s ease'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '24px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              marginBottom: '12px',\n              fontWeight: '600',\n              color: '#333',\n              fontSize: '16px'\n            },\n            children: \"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"file-upload-area\",\n            onClick: () => document.getElementById('file-input').click(),\n            style: {\n              height: '240px',\n              border: '2px dashed #667eea',\n              borderRadius: '12px',\n              background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.3s ease',\n              position: 'relative',\n              overflow: 'hidden'\n            },\n            onDragOver: e => {\n              e.preventDefault();\n              e.currentTarget.style.borderColor = '#4f46e5';\n              e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';\n            },\n            onDragLeave: e => {\n              e.currentTarget.style.borderColor = '#667eea';\n              e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';\n            },\n            onDrop: e => {\n              e.preventDefault();\n              const files = e.dataTransfer.files;\n              if (files.length > 0) {\n                setSelectedFile(files[0]);\n              }\n              e.currentTarget.style.borderColor = '#667eea';\n              e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"file-input\",\n              type: \"file\",\n              accept: \"image/*\",\n              onChange: handleFileSelect,\n              style: {\n                display: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this), selectedFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: '20px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '48px',\n                  marginBottom: '16px',\n                  color: '#667eea'\n                },\n                children: \"\\u2705\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 8px 0',\n                  color: '#333',\n                  fontSize: '18px'\n                },\n                children: \"\\u6587\\u4EF6\\u5DF2\\u9009\\u62E9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0 0 8px 0',\n                  color: '#666',\n                  fontSize: '14px',\n                  fontWeight: '500'\n                },\n                children: selectedFile.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0',\n                  color: '#999',\n                  fontSize: '12px'\n                },\n                children: [\"\\u5927\\u5C0F: \", (selectedFile.size / 1024 / 1024).toFixed(2), \" MB\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: e => {\n                  e.stopPropagation();\n                  setSelectedFile(null);\n                  document.getElementById('file-input').value = '';\n                },\n                style: {\n                  marginTop: '12px',\n                  background: 'rgba(239, 68, 68, 0.1)',\n                  color: '#ef4444',\n                  border: '1px solid rgba(239, 68, 68, 0.3)',\n                  padding: '6px 12px',\n                  borderRadius: '6px',\n                  fontSize: '12px',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                },\n                children: \"\\u91CD\\u65B0\\u9009\\u62E9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: '20px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '48px',\n                  marginBottom: '16px',\n                  color: '#667eea'\n                },\n                children: \"\\uD83D\\uDCE4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 8px 0',\n                  color: '#333',\n                  fontSize: '18px'\n                },\n                children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u4E0A\\u4F20\\u56FE\\u7247\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0 0 4px 0',\n                  color: '#666',\n                  fontSize: '14px'\n                },\n                children: \"\\u652F\\u6301 PNG\\u3001JPG\\u3001JPEG\\u3001GIF \\u683C\\u5F0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0',\n                  color: '#999',\n                  fontSize: '12px'\n                },\n                children: \"\\u6587\\u4EF6\\u5927\\u5C0F\\u4E0D\\u8D85\\u8FC7 10MB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: '16px',\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  color: 'white',\n                  padding: '8px 20px',\n                  borderRadius: '20px',\n                  fontSize: '14px',\n                  fontWeight: '500',\n                  display: 'inline-block'\n                },\n                children: \"\\u9009\\u62E9\\u6587\\u4EF6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"description\",\n            style: {\n              display: 'block',\n              marginBottom: '8px',\n              fontWeight: '600',\n              color: '#333',\n              fontSize: '16px'\n            },\n            children: \"\\uD83C\\uDFAF \\u754C\\u9762\\u529F\\u80FD\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"description\",\n            value: description,\n            onChange: e => setDescription(e.target.value),\n            placeholder: \"\\u8BF7\\u8BE6\\u7EC6\\u63CF\\u8FF0\\u8FD9\\u4E2A\\u754C\\u9762\\u7684\\u4E3B\\u8981\\u529F\\u80FD\\uFF0C\\u4F8B\\u5982\\uFF1A\\u8FD9\\u662F\\u4E00\\u4E2A\\u767B\\u5F55\\u9875\\u9762\\uFF0C\\u5305\\u542B\\u7528\\u6237\\u540D\\u5BC6\\u7801\\u8F93\\u5165\\u6846\\u548C\\u767B\\u5F55\\u6309\\u94AE...\",\n            rows: 4,\n            style: {\n              width: '100%',\n              padding: '12px',\n              border: '2px solid #e5e7eb',\n              borderRadius: '8px',\n              resize: 'vertical',\n              fontFamily: 'inherit',\n              fontSize: '14px',\n              lineHeight: '1.5',\n              transition: 'border-color 0.2s ease',\n              background: '#fafafa'\n            },\n            onFocus: e => {\n              e.target.style.borderColor = '#667eea';\n              e.target.style.background = '#ffffff';\n            },\n            onBlur: e => {\n              e.target.style.borderColor = '#e5e7eb';\n              e.target.style.background = '#fafafa';\n            },\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'right',\n              fontSize: '12px',\n              color: '#666',\n              marginTop: '4px'\n            },\n            children: [description.length, \"/500\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: isUploading || !selectedFile || !description.trim(),\n          style: {\n            background: isUploading ? '#6c757d' : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '8px',\n            fontSize: '16px',\n            fontWeight: '500',\n            cursor: isUploading ? 'not-allowed' : 'pointer',\n            transition: 'all 0.3s ease',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px',\n            width: '100%',\n            justifyContent: 'center',\n            marginBottom: '12px'\n          },\n          children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                width: '16px',\n                height: '16px',\n                border: '2px solid transparent',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this), \"\\u5F00\\u59CB\\u5206\\u6790\\u4E2D...\"]\n          }, void 0, true) : '🚀 开始分析'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => {\n            setShowAnalysis(true);\n            simulateAnalysisProgress();\n          },\n          style: {\n            background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',\n            color: 'white',\n            border: 'none',\n            padding: '8px 16px',\n            borderRadius: '6px',\n            fontSize: '14px',\n            fontWeight: '500',\n            cursor: 'pointer',\n            transition: 'all 0.3s ease',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '6px',\n            width: '100%',\n            justifyContent: 'center'\n          },\n          children: \"\\uD83E\\uDDEA \\u6D4B\\u8BD5\\u8FDB\\u5EA6\\u6846\\u6548\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this), showAnalysis && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: '1',\n        background: '#f8f9fa',\n        padding: '20px',\n        borderRadius: '12px',\n        overflow: 'hidden',\n        display: 'flex',\n        flexDirection: 'column',\n        minWidth: '400px',\n        maxWidth: '500px',\n        border: '1px solid #e9ecef'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          marginBottom: '20px',\n          paddingBottom: '12px',\n          borderBottom: '1px solid #dee2e6'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: 0,\n            color: '#495057',\n            fontSize: '16px',\n            fontWeight: '600',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          },\n          children: \"\\u5B9E\\u65F6\\u5206\\u6790\\u8FDB\\u5EA6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#e9ecef',\n            color: '#6c757d',\n            padding: '4px 12px',\n            borderRadius: '12px',\n            fontSize: '12px',\n            fontWeight: '500'\n          },\n          children: [analysisProgress.overall, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          border: '1px solid #dee2e6',\n          borderRadius: '8px',\n          padding: '16px',\n          marginBottom: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#6c757d',\n            marginBottom: '8px'\n          },\n          children: \"\\u5206\\u6790\\u72B6\\u6001\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '14px',\n            fontWeight: '500',\n            color: '#495057'\n          },\n          children: analysisProgress.currentStep\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '16px'\n        },\n        children: analysisProgress.steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            border: '1px solid #dee2e6',\n            borderRadius: '8px',\n            padding: '12px 16px',\n            marginBottom: '8px',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '24px',\n              height: '24px',\n              borderRadius: '50%',\n              background: step.status === 'completed' ? '#28a745' : step.status === 'processing' ? '#007bff' : '#e9ecef',\n              color: step.status === 'completed' || step.status === 'processing' ? 'white' : '#6c757d',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontSize: '12px',\n              fontWeight: '600'\n            },\n            children: step.status === 'completed' ? '✓' : step.status === 'processing' ? '⟳' : index + 1\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '14px',\n                fontWeight: '500',\n                marginBottom: '4px',\n                color: step.status === 'processing' ? '#007bff' : '#495057'\n              },\n              children: step.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '100%',\n                height: '4px',\n                background: '#e9ecef',\n                borderRadius: '2px',\n                overflow: 'hidden'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: `${step.progress}%`,\n                  height: '100%',\n                  background: step.status === 'completed' ? '#28a745' : step.status === 'processing' ? '#007bff' : '#e9ecef',\n                  transition: 'width 0.3s ease'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 519,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          border: '1px solid #dee2e6',\n          borderRadius: '8px',\n          padding: '16px',\n          marginBottom: '16px',\n          flex: '1',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#6c757d',\n            marginBottom: '12px',\n            fontWeight: '600',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '6px'\n          },\n          children: \"\\uD83D\\uDCCB \\u5B9E\\u65F6\\u5206\\u6790\\u65E5\\u5FD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: logContainerRef,\n          style: {\n            flex: '1',\n            maxHeight: '200px',\n            overflowY: 'auto',\n            fontSize: '12px',\n            lineHeight: '1.4',\n            fontFamily: 'Monaco, Consolas, \"Courier New\", monospace'\n          },\n          children: analysisLogs.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6c757d',\n              fontStyle: 'italic'\n            },\n            children: \"\\u7B49\\u5F85\\u5206\\u6790\\u5F00\\u59CB...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 17\n          }, this) : analysisLogs.map(log => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '4px',\n              padding: '4px 8px',\n              borderRadius: '4px',\n              background: log.type === 'start' ? '#e3f2fd' : log.type === 'step' ? '#f3e5f5' : log.type === 'success' ? '#e8f5e8' : log.type === 'error' ? '#ffebee' : '#f8f9fa',\n              color: log.type === 'start' ? '#1976d2' : log.type === 'step' ? '#7b1fa2' : log.type === 'success' ? '#388e3c' : log.type === 'error' ? '#d32f2f' : '#495057'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#6c757d',\n                marginRight: '8px'\n              },\n              children: [\"[\", log.timestamp, \"]\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 21\n            }, this), log.message]\n          }, log.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 'auto',\n          paddingTop: '16px',\n          borderTop: '1px solid #dee2e6'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowAnalysis(false),\n          style: {\n            width: '100%',\n            background: '#6c757d',\n            color: 'white',\n            border: 'none',\n            padding: '10px 16px',\n            borderRadius: '6px',\n            fontSize: '14px',\n            fontWeight: '500',\n            cursor: 'pointer',\n            transition: 'background-color 0.2s ease',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            gap: '8px'\n          },\n          onMouseOver: e => e.target.style.background = '#5a6268',\n          onMouseOut: e => e.target.style.background = '#6c757d',\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 15\n          }, this), \"\\u7EE7\\u7EED\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 643,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 458,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 676,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 225,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleUpload, \"q/GrJjBdgAS3e1YEI+7sc8aPlqk=\");\n_c = SimpleUpload;\nexport default SimpleUpload;\nvar _c;\n$RefreshReg$(_c, \"SimpleUpload\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SimpleUpload", "onUploadSuccess", "onUploadError", "_s", "selectedFile", "setSelectedFile", "description", "setDescription", "isUploading", "setIsUploading", "showAnalysis", "setShowAnalysis", "analysisProgress", "setAnalysisProgress", "overall", "currentStep", "steps", "name", "status", "progress", "analysisLogs", "setAnalysisLogs", "logContainerRef", "current", "scrollTop", "scrollHeight", "handleFileSelect", "event", "file", "target", "files", "console", "log", "size", "type", "lastModified", "Date", "toLocaleString", "handleSubmit", "preventDefault", "trim", "formData", "FormData", "append", "simulateAnalysisProgress", "response", "fetch", "method", "body", "mode", "statusText", "ok", "errorData", "json", "Error", "detail", "result", "error", "message", "addLog", "timestamp", "toLocaleTimeString", "toUpperCase", "prev", "id", "now", "Math", "random", "step", "stepLogs", "updateProgress", "length", "round", "map", "s", "index", "currentStepLogs", "logIndex", "addStepLogs", "setTimeout", "style", "display", "gap", "height", "padding", "width", "boxSizing", "overflow", "children", "flex", "background", "borderRadius", "boxShadow", "transition", "onSubmit", "marginBottom", "fontWeight", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onClick", "document", "getElementById", "click", "border", "flexDirection", "alignItems", "justifyContent", "cursor", "position", "onDragOver", "e", "currentTarget", "borderColor", "onDragLeave", "onDrop", "dataTransfer", "accept", "onChange", "textAlign", "margin", "toFixed", "stopPropagation", "value", "marginTop", "htmlFor", "placeholder", "rows", "resize", "fontFamily", "lineHeight", "onFocus", "onBlur", "required", "disabled", "borderTop", "animation", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "paddingBottom", "borderBottom", "ref", "maxHeight", "overflowY", "fontStyle", "marginRight", "paddingTop", "onMouseOver", "onMouseOut", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/components/SimpleUpload.js"], "sourcesContent": ["\nimport { useState, useEffect, useRef } from 'react';\n\nconst SimpleUpload = ({ onUploadSuccess, onUploadError }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const [showAnalysis, setShowAnalysis] = useState(true); // 默认显示进度框以便查看效果\n  const [analysisProgress, setAnalysisProgress] = useState({\n    overall: 0,\n    currentStep: '准备开始分析...',\n    steps: [\n      { name: '初始化', status: 'pending', progress: 0 },\n      { name: '元素分析和智能识别', status: 'pending', progress: 0 },\n      { name: '生成自动化测试脚本', status: 'pending', progress: 0 }\n    ]\n  });\n  const [analysisLogs, setAnalysisLogs] = useState([]);\n  const logContainerRef = useRef(null);\n\n  // 自动滚动到最新日志\n  useEffect(() => {\n    if (logContainerRef.current) {\n      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;\n    }\n  }, [analysisLogs]);\n\n  const handleFileSelect = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      console.log('📁 文件选择:', {\n        name: file.name,\n        size: file.size,\n        type: file.type,\n        lastModified: new Date(file.lastModified).toLocaleString()\n      });\n      setSelectedFile(file);\n    }\n  };\n\n  const handleSubmit = async (event) => {\n    event.preventDefault();\n\n    console.log('🚀 开始提交表单...');\n    console.log('  - 文件:', selectedFile?.name);\n    console.log('  - 描述:', description.trim());\n\n    if (!selectedFile) {\n      console.log('  ❌ 验证失败: 未选择文件');\n      onUploadError('请选择图片文件');\n      return;\n    }\n\n    if (!description.trim()) {\n      console.log('  ❌ 验证失败: 描述为空');\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n\n    console.log('  ✅ 表单验证通过');\n    setIsUploading(true);\n    setShowAnalysis(true);\n    console.log('  📊 显示分析界面');\n\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n\n      console.log('  📦 构建FormData完成');\n      console.log('  🎯 开始模拟分析进度...');\n\n      // 开始模拟分析进度\n      simulateAnalysisProgress();\n\n      console.log('  📡 发送上传请求到后端...');\n      const response = await fetch('http://localhost:8000/api/v1/upload', {\n        method: 'POST',\n        body: formData,\n        mode: 'cors',\n      });\n\n      console.log('  📨 收到后端响应:', response.status, response.statusText);\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        console.log('  ❌ 后端返回错误:', errorData);\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      console.log('  ✅ 上传成功:', result);\n      onUploadSuccess(result);\n\n    } catch (error) {\n      console.error('  ❌ 上传失败:', error);\n      setShowAnalysis(false);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n      console.log('  🏁 上传流程结束');\n    }\n  };\n\n  const addLog = (message, type = 'info') => {\n    const timestamp = new Date().toLocaleTimeString();\n    console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`);\n    setAnalysisLogs(prev => [...prev, {\n      id: Date.now() + Math.random(),\n      timestamp,\n      message,\n      type\n    }]);\n  };\n\n  const simulateAnalysisProgress = () => {\n    let step = 0;\n    const steps = ['初始化', '元素分析和智能识别', '生成自动化测试脚本'];\n\n    // 清空之前的日志\n    setAnalysisLogs([]);\n\n    // 详细的步骤日志\n    const stepLogs = {\n      0: [ // 初始化\n        '🚀 开始分析流程...',\n        '📋 检查上传文件格式和大小',\n        '🔧 初始化AI分析引擎',\n        '📊 加载UI元素识别模型',\n        '✅ 初始化完成'\n      ],\n      1: [ // 元素分析和智能识别\n        '🔍 开始图像预处理...',\n        '🎯 检测UI界面元素',\n        '📝 识别文本内容和标签',\n        '🔘 分析按钮和交互元素',\n        '📋 识别输入框和表单元素',\n        '🎨 分析布局和样式信息',\n        '🧠 AI智能分类UI组件',\n        '✅ 元素分析完成'\n      ],\n      2: [ // 生成自动化测试脚本\n        '📝 开始生成测试脚本...',\n        '🔧 构建MidScene.js测试框架',\n        '📋 生成元素定位策略',\n        '⚡ 创建交互操作脚本',\n        '🧪 添加断言和验证逻辑',\n        '📄 格式化YAML输出',\n        '✅ 测试脚本生成完成'\n      ]\n    };\n\n    const updateProgress = () => {\n      if (step < steps.length) {\n        // 添加步骤开始日志\n        addLog(`开始执行: ${steps[step]}`, 'step');\n\n        // 更新进度状态\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: Math.round((step + 1) / steps.length * 100),\n          currentStep: `正在执行: ${steps[step]}`,\n          steps: prev.steps.map((s, index) => ({\n            ...s,\n            status: index < step ? 'completed' : index === step ? 'processing' : 'pending',\n            progress: index < step ? 100 : index === step ? 0 : 0\n          }))\n        }));\n\n        // 模拟步骤内的详细日志\n        const currentStepLogs = stepLogs[step];\n        let logIndex = 0;\n\n        const addStepLogs = () => {\n          if (logIndex < currentStepLogs.length) {\n            addLog(currentStepLogs[logIndex], 'info');\n\n            // 更新当前步骤进度\n            const progress = Math.round(((logIndex + 1) / currentStepLogs.length) * 100);\n            setAnalysisProgress(prev => ({\n              ...prev,\n              steps: prev.steps.map((s, index) =>\n                index === step ? { ...s, progress } : s\n              )\n            }));\n\n            logIndex++;\n            setTimeout(addStepLogs, 800 + Math.random() * 1200);\n          } else {\n            // 当前步骤完成\n            setAnalysisProgress(prev => ({\n              ...prev,\n              steps: prev.steps.map((s, index) =>\n                index === step ? { ...s, status: 'completed', progress: 100 } : s\n              )\n            }));\n\n            step++;\n            setTimeout(updateProgress, 1000);\n          }\n        };\n\n        setTimeout(addStepLogs, 500);\n\n      } else {\n        // 所有步骤完成\n        addLog('🎉 所有分析步骤已完成！', 'success');\n        addLog('📄 测试脚本已生成，可以下载使用', 'success');\n\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: 100,\n          currentStep: '分析完成',\n          steps: prev.steps.map(s => ({ ...s, status: 'completed', progress: 100 }))\n        }));\n      }\n    };\n\n    // 开始分析\n    addLog('🔥 启动UI自动化测试脚本分析...', 'start');\n    setTimeout(updateProgress, 1000);\n  };\n\n  return (\n    <div style={{ \n      display: 'flex', \n      gap: '24px', \n      height: '100vh', \n      padding: '24px',\n      width: '100%',\n      boxSizing: 'border-box',\n      overflow: 'hidden'\n    }}>\n      {/* Left upload area - no duplicate header */}\n      <div style={{ \n        flex: showAnalysis ? '0 0 400px' : '1',\n        background: 'white',\n        padding: '24px',\n        borderRadius: '12px',\n        boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n        transition: 'all 0.3s ease'\n      }}>\n        <form onSubmit={handleSubmit}>\n          <div style={{ marginBottom: '24px' }}>\n            <label style={{ display: 'block', marginBottom: '12px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n              📁 上传UI界面截图\n            </label>\n            <div\n              className=\"file-upload-area\"\n              onClick={() => document.getElementById('file-input').click()}\n              style={{\n                height: '240px',\n                border: '2px dashed #667eea',\n                borderRadius: '12px',\n                background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                justifyContent: 'center',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease',\n                position: 'relative',\n                overflow: 'hidden'\n              }}\n              onDragOver={(e) => {\n                e.preventDefault();\n                e.currentTarget.style.borderColor = '#4f46e5';\n                e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';\n              }}\n              onDragLeave={(e) => {\n                e.currentTarget.style.borderColor = '#667eea';\n                e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';\n              }}\n              onDrop={(e) => {\n                e.preventDefault();\n                const files = e.dataTransfer.files;\n                if (files.length > 0) {\n                  setSelectedFile(files[0]);\n                }\n                e.currentTarget.style.borderColor = '#667eea';\n                e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';\n              }}\n            >\n              <input\n                id=\"file-input\"\n                type=\"file\"\n                accept=\"image/*\"\n                onChange={handleFileSelect}\n                style={{ display: 'none' }}\n              />\n\n              {selectedFile ? (\n                <div style={{ textAlign: 'center', padding: '20px' }}>\n                  <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>✅</div>\n                  <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>文件已选择</h3>\n                  <p style={{ margin: '0 0 8px 0', color: '#666', fontSize: '14px', fontWeight: '500' }}>\n                    {selectedFile.name}\n                  </p>\n                  <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>\n                    大小: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB\n                  </p>\n                  <button\n                    type=\"button\"\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      setSelectedFile(null);\n                      document.getElementById('file-input').value = '';\n                    }}\n                    style={{\n                      marginTop: '12px',\n                      background: 'rgba(239, 68, 68, 0.1)',\n                      color: '#ef4444',\n                      border: '1px solid rgba(239, 68, 68, 0.3)',\n                      padding: '6px 12px',\n                      borderRadius: '6px',\n                      fontSize: '12px',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    }}\n                  >\n                    重新选择\n                  </button>\n                </div>\n              ) : (\n                <div style={{ textAlign: 'center', padding: '20px' }}>\n                  <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>📤</div>\n                  <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>点击或拖拽上传图片</h3>\n                  <p style={{ margin: '0 0 4px 0', color: '#666', fontSize: '14px' }}>\n                    支持 PNG、JPG、JPEG、GIF 格式\n                  </p>\n                  <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>\n                    文件大小不超过 10MB\n                  </p>\n                  <div style={{\n                    marginTop: '16px',\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    color: 'white',\n                    padding: '8px 20px',\n                    borderRadius: '20px',\n                    fontSize: '14px',\n                    fontWeight: '500',\n                    display: 'inline-block'\n                  }}>\n                    选择文件\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* 界面功能描述 */}\n          <div style={{ marginBottom: '20px' }}>\n            <label htmlFor=\"description\" style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n              🎯 界面功能描述\n            </label>\n            <textarea\n              id=\"description\"\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              placeholder=\"请详细描述这个界面的主要功能，例如：这是一个登录页面，包含用户名密码输入框和登录按钮...\"\n              rows={4}\n              style={{\n                width: '100%',\n                padding: '12px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '8px',\n                resize: 'vertical',\n                fontFamily: 'inherit',\n                fontSize: '14px',\n                lineHeight: '1.5',\n                transition: 'border-color 0.2s ease',\n                background: '#fafafa'\n              }}\n              onFocus={(e) => {\n                e.target.style.borderColor = '#667eea';\n                e.target.style.background = '#ffffff';\n              }}\n              onBlur={(e) => {\n                e.target.style.borderColor = '#e5e7eb';\n                e.target.style.background = '#fafafa';\n              }}\n              required\n            />\n            <div style={{ textAlign: 'right', fontSize: '12px', color: '#666', marginTop: '4px' }}>\n              {description.length}/500\n            </div>\n          </div>\n\n          <button\n            type=\"submit\"\n            disabled={isUploading || !selectedFile || !description.trim()}\n            style={{\n              background: isUploading ? '#6c757d' : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '8px',\n              fontSize: '16px',\n              fontWeight: '500',\n              cursor: isUploading ? 'not-allowed' : 'pointer',\n              transition: 'all 0.3s ease',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px',\n              width: '100%',\n              justifyContent: 'center',\n              marginBottom: '12px'\n            }}\n          >\n            {isUploading ? (\n              <>\n                <span style={{\n                  width: '16px',\n                  height: '16px',\n                  border: '2px solid transparent',\n                  borderTop: '2px solid white',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}></span>\n                开始分析中...\n              </>\n            ) : (\n              '🚀 开始分析'\n            )}\n          </button>\n\n          {/* 测试按钮 - 用于演示进度框效果 */}\n          <button\n            type=\"button\"\n            onClick={() => {\n              setShowAnalysis(true);\n              simulateAnalysisProgress();\n            }}\n            style={{\n              background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',\n              color: 'white',\n              border: 'none',\n              padding: '8px 16px',\n              borderRadius: '6px',\n              fontSize: '14px',\n              fontWeight: '500',\n              cursor: 'pointer',\n              transition: 'all 0.3s ease',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '6px',\n              width: '100%',\n              justifyContent: 'center'\n            }}\n          >\n            🧪 测试进度框效果\n          </button>\n        </form>\n      </div>\n\n      {/* 右侧实时分析界面 - 按照截图样式设计 */}\n      {showAnalysis && (\n        <div style={{\n          flex: '1',\n          background: '#f8f9fa',\n          padding: '20px',\n          borderRadius: '12px',\n          overflow: 'hidden',\n          display: 'flex',\n          flexDirection: 'column',\n          minWidth: '400px',\n          maxWidth: '500px',\n          border: '1px solid #e9ecef'\n        }}>\n          {/* 标题区域 */}\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            marginBottom: '20px',\n            paddingBottom: '12px',\n            borderBottom: '1px solid #dee2e6'\n          }}>\n            <h3 style={{\n              margin: 0,\n              color: '#495057',\n              fontSize: '16px',\n              fontWeight: '600',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px'\n            }}>\n              实时分析进度\n            </h3>\n            <div style={{\n              background: '#e9ecef',\n              color: '#6c757d',\n              padding: '4px 12px',\n              borderRadius: '12px',\n              fontSize: '12px',\n              fontWeight: '500'\n            }}>\n              {analysisProgress.overall}%\n            </div>\n          </div>\n\n          {/* 当前分析状态 */}\n          <div style={{\n            background: 'white',\n            border: '1px solid #dee2e6',\n            borderRadius: '8px',\n            padding: '16px',\n            marginBottom: '16px'\n          }}>\n            <div style={{ fontSize: '12px', color: '#6c757d', marginBottom: '8px' }}>\n              分析状态\n            </div>\n            <div style={{ fontSize: '14px', fontWeight: '500', color: '#495057' }}>\n              {analysisProgress.currentStep}\n            </div>\n          </div>\n\n          {/* 分析步骤列表 */}\n          <div style={{ marginBottom: '16px' }}>\n            {analysisProgress.steps.map((step, index) => (\n              <div key={index} style={{\n                background: 'white',\n                border: '1px solid #dee2e6',\n                borderRadius: '8px',\n                padding: '12px 16px',\n                marginBottom: '8px',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '12px'\n              }}>\n                {/* 步骤图标 */}\n                <div style={{\n                  width: '24px',\n                  height: '24px',\n                  borderRadius: '50%',\n                  background: step.status === 'completed' ? '#28a745' :\n                             step.status === 'processing' ? '#007bff' : '#e9ecef',\n                  color: step.status === 'completed' || step.status === 'processing' ? 'white' : '#6c757d',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '12px',\n                  fontWeight: '600'\n                }}>\n                  {step.status === 'completed' ? '✓' :\n                   step.status === 'processing' ? '⟳' : index + 1}\n                </div>\n\n                {/* 步骤内容和进度条 */}\n                <div style={{ flex: 1 }}>\n                  <div style={{\n                    fontSize: '14px',\n                    fontWeight: '500',\n                    marginBottom: '4px',\n                    color: step.status === 'processing' ? '#007bff' : '#495057'\n                  }}>\n                    {step.name}\n                  </div>\n                  {/* 进度条 */}\n                  <div style={{\n                    width: '100%',\n                    height: '4px',\n                    background: '#e9ecef',\n                    borderRadius: '2px',\n                    overflow: 'hidden'\n                  }}>\n                    <div style={{\n                      width: `${step.progress}%`,\n                      height: '100%',\n                      background: step.status === 'completed' ? '#28a745' :\n                                 step.status === 'processing' ? '#007bff' : '#e9ecef',\n                      transition: 'width 0.3s ease'\n                    }}></div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* 实时日志区域 */}\n          <div style={{\n            background: 'white',\n            border: '1px solid #dee2e6',\n            borderRadius: '8px',\n            padding: '16px',\n            marginBottom: '16px',\n            flex: '1',\n            display: 'flex',\n            flexDirection: 'column'\n          }}>\n            <div style={{\n              fontSize: '12px',\n              color: '#6c757d',\n              marginBottom: '12px',\n              fontWeight: '600',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '6px'\n            }}>\n              📋 实时分析日志\n            </div>\n            <div\n              ref={logContainerRef}\n              style={{\n                flex: '1',\n                maxHeight: '200px',\n                overflowY: 'auto',\n                fontSize: '12px',\n                lineHeight: '1.4',\n                fontFamily: 'Monaco, Consolas, \"Courier New\", monospace'\n              }}\n            >\n              {analysisLogs.length === 0 ? (\n                <div style={{ color: '#6c757d', fontStyle: 'italic' }}>\n                  等待分析开始...\n                </div>\n              ) : (\n                analysisLogs.map((log) => (\n                  <div key={log.id} style={{\n                    marginBottom: '4px',\n                    padding: '4px 8px',\n                    borderRadius: '4px',\n                    background: log.type === 'start' ? '#e3f2fd' :\n                               log.type === 'step' ? '#f3e5f5' :\n                               log.type === 'success' ? '#e8f5e8' :\n                               log.type === 'error' ? '#ffebee' : '#f8f9fa',\n                    color: log.type === 'start' ? '#1976d2' :\n                           log.type === 'step' ? '#7b1fa2' :\n                           log.type === 'success' ? '#388e3c' :\n                           log.type === 'error' ? '#d32f2f' : '#495057'\n                  }}>\n                    <span style={{ color: '#6c757d', marginRight: '8px' }}>\n                      [{log.timestamp}]\n                    </span>\n                    {log.message}\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n\n          {/* 底部操作按钮 */}\n          <div style={{\n            marginTop: 'auto',\n            paddingTop: '16px',\n            borderTop: '1px solid #dee2e6'\n          }}>\n            <button\n              onClick={() => setShowAnalysis(false)}\n              style={{\n                width: '100%',\n                background: '#6c757d',\n                color: 'white',\n                border: 'none',\n                padding: '10px 16px',\n                borderRadius: '6px',\n                fontSize: '14px',\n                fontWeight: '500',\n                cursor: 'pointer',\n                transition: 'background-color 0.2s ease',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                gap: '8px'\n              }}\n              onMouseOver={(e) => e.target.style.background = '#5a6268'}\n              onMouseOut={(e) => e.target.style.background = '#6c757d'}\n            >\n              <span>→</span>\n              继续\n            </button>\n          </div>\n        </div>\n      )}\n\n      <style>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default SimpleUpload;\n\n\n\n"], "mappings": ";;AACA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,YAAY,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACxD,MAAM,CAACmB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpB,QAAQ,CAAC;IACvDqB,OAAO,EAAE,CAAC;IACVC,WAAW,EAAE,WAAW;IACxBC,KAAK,EAAE,CACL;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAE,CAAC,EAC/C;MAAEF,IAAI,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAE,CAAC,EACrD;MAAEF,IAAI,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAE,CAAC;EAEzD,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM6B,eAAe,GAAG3B,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACAD,SAAS,CAAC,MAAM;IACd,IAAI4B,eAAe,CAACC,OAAO,EAAE;MAC3BD,eAAe,CAACC,OAAO,CAACC,SAAS,GAAGF,eAAe,CAACC,OAAO,CAACE,YAAY;IAC1E;EACF,CAAC,EAAE,CAACL,YAAY,CAAC,CAAC;EAElB,MAAMM,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACRG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;QACtBf,IAAI,EAAEW,IAAI,CAACX,IAAI;QACfgB,IAAI,EAAEL,IAAI,CAACK,IAAI;QACfC,IAAI,EAAEN,IAAI,CAACM,IAAI;QACfC,YAAY,EAAE,IAAIC,IAAI,CAACR,IAAI,CAACO,YAAY,CAAC,CAACE,cAAc,CAAC;MAC3D,CAAC,CAAC;MACFhC,eAAe,CAACuB,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAMU,YAAY,GAAG,MAAOX,KAAK,IAAK;IACpCA,KAAK,CAACY,cAAc,CAAC,CAAC;IAEtBR,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3BD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE5B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEa,IAAI,CAAC;IAC1Cc,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE1B,WAAW,CAACkC,IAAI,CAAC,CAAC,CAAC;IAE1C,IAAI,CAACpC,YAAY,EAAE;MACjB2B,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B9B,aAAa,CAAC,SAAS,CAAC;MACxB;IACF;IAEA,IAAI,CAACI,WAAW,CAACkC,IAAI,CAAC,CAAC,EAAE;MACvBT,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;MAC7B9B,aAAa,CAAC,WAAW,CAAC;MAC1B;IACF;IAEA6B,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;IACzBvB,cAAc,CAAC,IAAI,CAAC;IACpBE,eAAe,CAAC,IAAI,CAAC;IACrBoB,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAE1B,IAAI;MACF,MAAMS,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEvC,YAAY,CAAC;MAC3CqC,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAErC,WAAW,CAACkC,IAAI,CAAC,CAAC,CAAC;MAElDT,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChCD,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;;MAE/B;MACAY,wBAAwB,CAAC,CAAC;MAE1Bb,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,MAAMa,QAAQ,GAAG,MAAMC,KAAK,CAAC,qCAAqC,EAAE;QAClEC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEP,QAAQ;QACdQ,IAAI,EAAE;MACR,CAAC,CAAC;MAEFlB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEa,QAAQ,CAAC3B,MAAM,EAAE2B,QAAQ,CAACK,UAAU,CAAC;MAEjE,IAAI,CAACL,QAAQ,CAACM,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;QACvCtB,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEoB,SAAS,CAAC;QACrC,MAAM,IAAIE,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,uBAAuBV,QAAQ,CAAC3B,MAAM,EAAE,CAAC;MAC/E;MAEA,MAAMsC,MAAM,GAAG,MAAMX,QAAQ,CAACQ,IAAI,CAAC,CAAC;MACpCtB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEwB,MAAM,CAAC;MAChCvD,eAAe,CAACuD,MAAM,CAAC;IAEzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd1B,OAAO,CAAC0B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC9C,eAAe,CAAC,KAAK,CAAC;MACtBT,aAAa,CAACuD,KAAK,CAACC,OAAO,IAAI,UAAU,CAAC;IAC5C,CAAC,SAAS;MACRjD,cAAc,CAAC,KAAK,CAAC;MACrBsB,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC5B;EACF,CAAC;EAED,MAAM2B,MAAM,GAAGA,CAACD,OAAO,EAAExB,IAAI,GAAG,MAAM,KAAK;IACzC,MAAM0B,SAAS,GAAG,IAAIxB,IAAI,CAAC,CAAC,CAACyB,kBAAkB,CAAC,CAAC;IACjD9B,OAAO,CAACC,GAAG,CAAC,IAAI4B,SAAS,KAAK1B,IAAI,CAAC4B,WAAW,CAAC,CAAC,KAAKJ,OAAO,EAAE,CAAC;IAC/DrC,eAAe,CAAC0C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAChCC,EAAE,EAAE5B,IAAI,CAAC6B,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC;MAC9BP,SAAS;MACTF,OAAO;MACPxB;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMU,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAIwB,IAAI,GAAG,CAAC;IACZ,MAAMpD,KAAK,GAAG,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,CAAC;;IAE/C;IACAK,eAAe,CAAC,EAAE,CAAC;;IAEnB;IACA,MAAMgD,QAAQ,GAAG;MACf,CAAC,EAAE;MAAE;MACH,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,eAAe,EACf,SAAS,CACV;MACD,CAAC,EAAE;MAAE;MACH,eAAe,EACf,aAAa,EACb,cAAc,EACd,cAAc,EACd,eAAe,EACf,cAAc,EACd,eAAe,EACf,UAAU,CACX;MACD,CAAC,EAAE;MAAE;MACH,gBAAgB,EAChB,sBAAsB,EACtB,aAAa,EACb,YAAY,EACZ,cAAc,EACd,cAAc,EACd,YAAY;IAEhB,CAAC;IAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAIF,IAAI,GAAGpD,KAAK,CAACuD,MAAM,EAAE;QACvB;QACAZ,MAAM,CAAC,SAAS3C,KAAK,CAACoD,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC;;QAEtC;QACAvD,mBAAmB,CAACkD,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACPjD,OAAO,EAAEoD,IAAI,CAACM,KAAK,CAAC,CAACJ,IAAI,GAAG,CAAC,IAAIpD,KAAK,CAACuD,MAAM,GAAG,GAAG,CAAC;UACpDxD,WAAW,EAAE,SAASC,KAAK,CAACoD,IAAI,CAAC,EAAE;UACnCpD,KAAK,EAAE+C,IAAI,CAAC/C,KAAK,CAACyD,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,MAAM;YACnC,GAAGD,CAAC;YACJxD,MAAM,EAAEyD,KAAK,GAAGP,IAAI,GAAG,WAAW,GAAGO,KAAK,KAAKP,IAAI,GAAG,YAAY,GAAG,SAAS;YAC9EjD,QAAQ,EAAEwD,KAAK,GAAGP,IAAI,GAAG,GAAG,GAAGO,KAAK,KAAKP,IAAI,GAAG,CAAC,GAAG;UACtD,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMQ,eAAe,GAAGP,QAAQ,CAACD,IAAI,CAAC;QACtC,IAAIS,QAAQ,GAAG,CAAC;QAEhB,MAAMC,WAAW,GAAGA,CAAA,KAAM;UACxB,IAAID,QAAQ,GAAGD,eAAe,CAACL,MAAM,EAAE;YACrCZ,MAAM,CAACiB,eAAe,CAACC,QAAQ,CAAC,EAAE,MAAM,CAAC;;YAEzC;YACA,MAAM1D,QAAQ,GAAG+C,IAAI,CAACM,KAAK,CAAE,CAACK,QAAQ,GAAG,CAAC,IAAID,eAAe,CAACL,MAAM,GAAI,GAAG,CAAC;YAC5E1D,mBAAmB,CAACkD,IAAI,KAAK;cAC3B,GAAGA,IAAI;cACP/C,KAAK,EAAE+C,IAAI,CAAC/C,KAAK,CAACyD,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAC7BA,KAAK,KAAKP,IAAI,GAAG;gBAAE,GAAGM,CAAC;gBAAEvD;cAAS,CAAC,GAAGuD,CACxC;YACF,CAAC,CAAC,CAAC;YAEHG,QAAQ,EAAE;YACVE,UAAU,CAACD,WAAW,EAAE,GAAG,GAAGZ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;UACrD,CAAC,MAAM;YACL;YACAtD,mBAAmB,CAACkD,IAAI,KAAK;cAC3B,GAAGA,IAAI;cACP/C,KAAK,EAAE+C,IAAI,CAAC/C,KAAK,CAACyD,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAC7BA,KAAK,KAAKP,IAAI,GAAG;gBAAE,GAAGM,CAAC;gBAAExD,MAAM,EAAE,WAAW;gBAAEC,QAAQ,EAAE;cAAI,CAAC,GAAGuD,CAClE;YACF,CAAC,CAAC,CAAC;YAEHN,IAAI,EAAE;YACNW,UAAU,CAACT,cAAc,EAAE,IAAI,CAAC;UAClC;QACF,CAAC;QAEDS,UAAU,CAACD,WAAW,EAAE,GAAG,CAAC;MAE9B,CAAC,MAAM;QACL;QACAnB,MAAM,CAAC,eAAe,EAAE,SAAS,CAAC;QAClCA,MAAM,CAAC,mBAAmB,EAAE,SAAS,CAAC;QAEtC9C,mBAAmB,CAACkD,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACPjD,OAAO,EAAE,GAAG;UACZC,WAAW,EAAE,MAAM;UACnBC,KAAK,EAAE+C,IAAI,CAAC/C,KAAK,CAACyD,GAAG,CAACC,CAAC,KAAK;YAAE,GAAGA,CAAC;YAAExD,MAAM,EAAE,WAAW;YAAEC,QAAQ,EAAE;UAAI,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;MACL;IACF,CAAC;;IAED;IACAwC,MAAM,CAAC,qBAAqB,EAAE,OAAO,CAAC;IACtCoB,UAAU,CAACT,cAAc,EAAE,IAAI,CAAC;EAClC,CAAC;EAED,oBACEzE,OAAA;IAAKmF,KAAK,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,GAAG,EAAE,MAAM;MACXC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE,MAAM;MACfC,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE,YAAY;MACvBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEA3F,OAAA;MAAKmF,KAAK,EAAE;QACVS,IAAI,EAAE/E,YAAY,GAAG,WAAW,GAAG,GAAG;QACtCgF,UAAU,EAAE,OAAO;QACnBN,OAAO,EAAE,MAAM;QACfO,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE,4BAA4B;QACvCC,UAAU,EAAE;MACd,CAAE;MAAAL,QAAA,eACA3F,OAAA;QAAMiG,QAAQ,EAAExD,YAAa;QAAAkD,QAAA,gBAC3B3F,OAAA;UAAKmF,KAAK,EAAE;YAAEe,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,gBACnC3F,OAAA;YAAOmF,KAAK,EAAE;cAAEC,OAAO,EAAE,OAAO;cAAEc,YAAY,EAAE,MAAM;cAAEC,UAAU,EAAE,KAAK;cAAEC,KAAK,EAAE,MAAM;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAV,QAAA,EAAC;UAE9G;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRzG,OAAA;YACE0G,SAAS,EAAC,kBAAkB;YAC5BC,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAACC,KAAK,CAAC,CAAE;YAC7D3B,KAAK,EAAE;cACLG,MAAM,EAAE,OAAO;cACfyB,MAAM,EAAE,oBAAoB;cAC5BjB,YAAY,EAAE,MAAM;cACpBD,UAAU,EAAEtF,YAAY,GAAG,mDAAmD,GAAG,mDAAmD;cACpI6E,OAAO,EAAE,MAAM;cACf4B,aAAa,EAAE,QAAQ;cACvBC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,MAAM,EAAE,SAAS;cACjBnB,UAAU,EAAE,eAAe;cAC3BoB,QAAQ,EAAE,UAAU;cACpB1B,QAAQ,EAAE;YACZ,CAAE;YACF2B,UAAU,EAAGC,CAAC,IAAK;cACjBA,CAAC,CAAC5E,cAAc,CAAC,CAAC;cAClB4E,CAAC,CAACC,aAAa,CAACpC,KAAK,CAACqC,WAAW,GAAG,SAAS;cAC7CF,CAAC,CAACC,aAAa,CAACpC,KAAK,CAACU,UAAU,GAAG,mDAAmD;YACxF,CAAE;YACF4B,WAAW,EAAGH,CAAC,IAAK;cAClBA,CAAC,CAACC,aAAa,CAACpC,KAAK,CAACqC,WAAW,GAAG,SAAS;cAC7CF,CAAC,CAACC,aAAa,CAACpC,KAAK,CAACU,UAAU,GAAGtF,YAAY,GAAG,mDAAmD,GAAG,mDAAmD;YAC7J,CAAE;YACFmH,MAAM,EAAGJ,CAAC,IAAK;cACbA,CAAC,CAAC5E,cAAc,CAAC,CAAC;cAClB,MAAMT,KAAK,GAAGqF,CAAC,CAACK,YAAY,CAAC1F,KAAK;cAClC,IAAIA,KAAK,CAACyC,MAAM,GAAG,CAAC,EAAE;gBACpBlE,eAAe,CAACyB,KAAK,CAAC,CAAC,CAAC,CAAC;cAC3B;cACAqF,CAAC,CAACC,aAAa,CAACpC,KAAK,CAACqC,WAAW,GAAG,SAAS;cAC7CF,CAAC,CAACC,aAAa,CAACpC,KAAK,CAACU,UAAU,GAAG,mDAAmD;YACxF,CAAE;YAAAF,QAAA,gBAEF3F,OAAA;cACEmE,EAAE,EAAC,YAAY;cACf9B,IAAI,EAAC,MAAM;cACXuF,MAAM,EAAC,SAAS;cAChBC,QAAQ,EAAEhG,gBAAiB;cAC3BsD,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAO;YAAE;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,EAEDlG,YAAY,gBACXP,OAAA;cAAKmF,KAAK,EAAE;gBAAE2C,SAAS,EAAE,QAAQ;gBAAEvC,OAAO,EAAE;cAAO,CAAE;cAAAI,QAAA,gBACnD3F,OAAA;gBAAKmF,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,MAAM;kBAAEH,YAAY,EAAE,MAAM;kBAAEE,KAAK,EAAE;gBAAU,CAAE;gBAAAT,QAAA,EAAC;cAAC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjFzG,OAAA;gBAAImF,KAAK,EAAE;kBAAE4C,MAAM,EAAE,WAAW;kBAAE3B,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAV,QAAA,EAAC;cAAK;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/EzG,OAAA;gBAAGmF,KAAK,EAAE;kBAAE4C,MAAM,EAAE,WAAW;kBAAE3B,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,MAAM;kBAAEF,UAAU,EAAE;gBAAM,CAAE;gBAAAR,QAAA,EACnFpF,YAAY,CAACa;cAAI;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACJzG,OAAA;gBAAGmF,KAAK,EAAE;kBAAE4C,MAAM,EAAE,GAAG;kBAAE3B,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAV,QAAA,GAAC,gBACtD,EAAC,CAACpF,YAAY,CAAC6B,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE4F,OAAO,CAAC,CAAC,CAAC,EAAC,KACpD;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJzG,OAAA;gBACEqC,IAAI,EAAC,QAAQ;gBACbsE,OAAO,EAAGW,CAAC,IAAK;kBACdA,CAAC,CAACW,eAAe,CAAC,CAAC;kBACnBzH,eAAe,CAAC,IAAI,CAAC;kBACrBoG,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAACqB,KAAK,GAAG,EAAE;gBAClD,CAAE;gBACF/C,KAAK,EAAE;kBACLgD,SAAS,EAAE,MAAM;kBACjBtC,UAAU,EAAE,wBAAwB;kBACpCO,KAAK,EAAE,SAAS;kBAChBW,MAAM,EAAE,kCAAkC;kBAC1CxB,OAAO,EAAE,UAAU;kBACnBO,YAAY,EAAE,KAAK;kBACnBO,QAAQ,EAAE,MAAM;kBAChBc,MAAM,EAAE,SAAS;kBACjBnB,UAAU,EAAE;gBACd,CAAE;gBAAAL,QAAA,EACH;cAED;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,gBAENzG,OAAA;cAAKmF,KAAK,EAAE;gBAAE2C,SAAS,EAAE,QAAQ;gBAAEvC,OAAO,EAAE;cAAO,CAAE;cAAAI,QAAA,gBACnD3F,OAAA;gBAAKmF,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,MAAM;kBAAEH,YAAY,EAAE,MAAM;kBAAEE,KAAK,EAAE;gBAAU,CAAE;gBAAAT,QAAA,EAAC;cAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClFzG,OAAA;gBAAImF,KAAK,EAAE;kBAAE4C,MAAM,EAAE,WAAW;kBAAE3B,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAV,QAAA,EAAC;cAAS;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnFzG,OAAA;gBAAGmF,KAAK,EAAE;kBAAE4C,MAAM,EAAE,WAAW;kBAAE3B,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAV,QAAA,EAAC;cAEpE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJzG,OAAA;gBAAGmF,KAAK,EAAE;kBAAE4C,MAAM,EAAE,GAAG;kBAAE3B,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAV,QAAA,EAAC;cAE5D;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJzG,OAAA;gBAAKmF,KAAK,EAAE;kBACVgD,SAAS,EAAE,MAAM;kBACjBtC,UAAU,EAAE,mDAAmD;kBAC/DO,KAAK,EAAE,OAAO;kBACdb,OAAO,EAAE,UAAU;kBACnBO,YAAY,EAAE,MAAM;kBACpBO,QAAQ,EAAE,MAAM;kBAChBF,UAAU,EAAE,KAAK;kBACjBf,OAAO,EAAE;gBACX,CAAE;gBAAAO,QAAA,EAAC;cAEH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzG,OAAA;UAAKmF,KAAK,EAAE;YAAEe,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,gBACnC3F,OAAA;YAAOoI,OAAO,EAAC,aAAa;YAACjD,KAAK,EAAE;cAAEC,OAAO,EAAE,OAAO;cAAEc,YAAY,EAAE,KAAK;cAAEC,UAAU,EAAE,KAAK;cAAEC,KAAK,EAAE,MAAM;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAV,QAAA,EAAC;UAEnI;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRzG,OAAA;YACEmE,EAAE,EAAC,aAAa;YAChB+D,KAAK,EAAEzH,WAAY;YACnBoH,QAAQ,EAAGP,CAAC,IAAK5G,cAAc,CAAC4G,CAAC,CAACtF,MAAM,CAACkG,KAAK,CAAE;YAChDG,WAAW,EAAC,iQAA+C;YAC3DC,IAAI,EAAE,CAAE;YACRnD,KAAK,EAAE;cACLK,KAAK,EAAE,MAAM;cACbD,OAAO,EAAE,MAAM;cACfwB,MAAM,EAAE,mBAAmB;cAC3BjB,YAAY,EAAE,KAAK;cACnByC,MAAM,EAAE,UAAU;cAClBC,UAAU,EAAE,SAAS;cACrBnC,QAAQ,EAAE,MAAM;cAChBoC,UAAU,EAAE,KAAK;cACjBzC,UAAU,EAAE,wBAAwB;cACpCH,UAAU,EAAE;YACd,CAAE;YACF6C,OAAO,EAAGpB,CAAC,IAAK;cACdA,CAAC,CAACtF,MAAM,CAACmD,KAAK,CAACqC,WAAW,GAAG,SAAS;cACtCF,CAAC,CAACtF,MAAM,CAACmD,KAAK,CAACU,UAAU,GAAG,SAAS;YACvC,CAAE;YACF8C,MAAM,EAAGrB,CAAC,IAAK;cACbA,CAAC,CAACtF,MAAM,CAACmD,KAAK,CAACqC,WAAW,GAAG,SAAS;cACtCF,CAAC,CAACtF,MAAM,CAACmD,KAAK,CAACU,UAAU,GAAG,SAAS;YACvC,CAAE;YACF+C,QAAQ;UAAA;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFzG,OAAA;YAAKmF,KAAK,EAAE;cAAE2C,SAAS,EAAE,OAAO;cAAEzB,QAAQ,EAAE,MAAM;cAAED,KAAK,EAAE,MAAM;cAAE+B,SAAS,EAAE;YAAM,CAAE;YAAAxC,QAAA,GACnFlF,WAAW,CAACiE,MAAM,EAAC,MACtB;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzG,OAAA;UACEqC,IAAI,EAAC,QAAQ;UACbwG,QAAQ,EAAElI,WAAW,IAAI,CAACJ,YAAY,IAAI,CAACE,WAAW,CAACkC,IAAI,CAAC,CAAE;UAC9DwC,KAAK,EAAE;YACLU,UAAU,EAAElF,WAAW,GAAG,SAAS,GAAG,mDAAmD;YACzFyF,KAAK,EAAE,OAAO;YACdW,MAAM,EAAE,MAAM;YACdxB,OAAO,EAAE,WAAW;YACpBO,YAAY,EAAE,KAAK;YACnBO,QAAQ,EAAE,MAAM;YAChBF,UAAU,EAAE,KAAK;YACjBgB,MAAM,EAAExG,WAAW,GAAG,aAAa,GAAG,SAAS;YAC/CqF,UAAU,EAAE,eAAe;YAC3BZ,OAAO,EAAE,MAAM;YACf6B,UAAU,EAAE,QAAQ;YACpB5B,GAAG,EAAE,KAAK;YACVG,KAAK,EAAE,MAAM;YACb0B,cAAc,EAAE,QAAQ;YACxBhB,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,EAEDhF,WAAW,gBACVX,OAAA,CAAAE,SAAA;YAAAyF,QAAA,gBACE3F,OAAA;cAAMmF,KAAK,EAAE;gBACXK,KAAK,EAAE,MAAM;gBACbF,MAAM,EAAE,MAAM;gBACdyB,MAAM,EAAE,uBAAuB;gBAC/B+B,SAAS,EAAE,iBAAiB;gBAC5BhD,YAAY,EAAE,KAAK;gBACnBiD,SAAS,EAAE;cACb;YAAE;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,qCAEZ;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAGTzG,OAAA;UACEqC,IAAI,EAAC,QAAQ;UACbsE,OAAO,EAAEA,CAAA,KAAM;YACb7F,eAAe,CAAC,IAAI,CAAC;YACrBiC,wBAAwB,CAAC,CAAC;UAC5B,CAAE;UACFoC,KAAK,EAAE;YACLU,UAAU,EAAE,mDAAmD;YAC/DO,KAAK,EAAE,OAAO;YACdW,MAAM,EAAE,MAAM;YACdxB,OAAO,EAAE,UAAU;YACnBO,YAAY,EAAE,KAAK;YACnBO,QAAQ,EAAE,MAAM;YAChBF,UAAU,EAAE,KAAK;YACjBgB,MAAM,EAAE,SAAS;YACjBnB,UAAU,EAAE,eAAe;YAC3BZ,OAAO,EAAE,MAAM;YACf6B,UAAU,EAAE,QAAQ;YACpB5B,GAAG,EAAE,KAAK;YACVG,KAAK,EAAE,MAAM;YACb0B,cAAc,EAAE;UAClB,CAAE;UAAAvB,QAAA,EACH;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGL5F,YAAY,iBACXb,OAAA;MAAKmF,KAAK,EAAE;QACVS,IAAI,EAAE,GAAG;QACTC,UAAU,EAAE,SAAS;QACrBN,OAAO,EAAE,MAAM;QACfO,YAAY,EAAE,MAAM;QACpBJ,QAAQ,EAAE,QAAQ;QAClBN,OAAO,EAAE,MAAM;QACf4B,aAAa,EAAE,QAAQ;QACvBgC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,OAAO;QACjBlC,MAAM,EAAE;MACV,CAAE;MAAApB,QAAA,gBAEA3F,OAAA;QAAKmF,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACf6B,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,eAAe;UAC/BhB,YAAY,EAAE,MAAM;UACpBgD,aAAa,EAAE,MAAM;UACrBC,YAAY,EAAE;QAChB,CAAE;QAAAxD,QAAA,gBACA3F,OAAA;UAAImF,KAAK,EAAE;YACT4C,MAAM,EAAE,CAAC;YACT3B,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,MAAM;YAChBF,UAAU,EAAE,KAAK;YACjBf,OAAO,EAAE,MAAM;YACf6B,UAAU,EAAE,QAAQ;YACpB5B,GAAG,EAAE;UACP,CAAE;UAAAM,QAAA,EAAC;QAEH;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLzG,OAAA;UAAKmF,KAAK,EAAE;YACVU,UAAU,EAAE,SAAS;YACrBO,KAAK,EAAE,SAAS;YAChBb,OAAO,EAAE,UAAU;YACnBO,YAAY,EAAE,MAAM;YACpBO,QAAQ,EAAE,MAAM;YAChBF,UAAU,EAAE;UACd,CAAE;UAAAR,QAAA,GACC5E,gBAAgB,CAACE,OAAO,EAAC,GAC5B;QAAA;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzG,OAAA;QAAKmF,KAAK,EAAE;UACVU,UAAU,EAAE,OAAO;UACnBkB,MAAM,EAAE,mBAAmB;UAC3BjB,YAAY,EAAE,KAAK;UACnBP,OAAO,EAAE,MAAM;UACfW,YAAY,EAAE;QAChB,CAAE;QAAAP,QAAA,gBACA3F,OAAA;UAAKmF,KAAK,EAAE;YAAEkB,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE,SAAS;YAAEF,YAAY,EAAE;UAAM,CAAE;UAAAP,QAAA,EAAC;QAEzE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNzG,OAAA;UAAKmF,KAAK,EAAE;YAAEkB,QAAQ,EAAE,MAAM;YAAEF,UAAU,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAT,QAAA,EACnE5E,gBAAgB,CAACG;QAAW;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzG,OAAA;QAAKmF,KAAK,EAAE;UAAEe,YAAY,EAAE;QAAO,CAAE;QAAAP,QAAA,EAClC5E,gBAAgB,CAACI,KAAK,CAACyD,GAAG,CAAC,CAACL,IAAI,EAAEO,KAAK,kBACtC9E,OAAA;UAAiBmF,KAAK,EAAE;YACtBU,UAAU,EAAE,OAAO;YACnBkB,MAAM,EAAE,mBAAmB;YAC3BjB,YAAY,EAAE,KAAK;YACnBP,OAAO,EAAE,WAAW;YACpBW,YAAY,EAAE,KAAK;YACnBd,OAAO,EAAE,MAAM;YACf6B,UAAU,EAAE,QAAQ;YACpB5B,GAAG,EAAE;UACP,CAAE;UAAAM,QAAA,gBAEA3F,OAAA;YAAKmF,KAAK,EAAE;cACVK,KAAK,EAAE,MAAM;cACbF,MAAM,EAAE,MAAM;cACdQ,YAAY,EAAE,KAAK;cACnBD,UAAU,EAAEtB,IAAI,CAAClD,MAAM,KAAK,WAAW,GAAG,SAAS,GACxCkD,IAAI,CAAClD,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG,SAAS;cAC/D+E,KAAK,EAAE7B,IAAI,CAAClD,MAAM,KAAK,WAAW,IAAIkD,IAAI,CAAClD,MAAM,KAAK,YAAY,GAAG,OAAO,GAAG,SAAS;cACxF+D,OAAO,EAAE,MAAM;cACf6B,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBb,QAAQ,EAAE,MAAM;cAChBF,UAAU,EAAE;YACd,CAAE;YAAAR,QAAA,EACCpB,IAAI,CAAClD,MAAM,KAAK,WAAW,GAAG,GAAG,GACjCkD,IAAI,CAAClD,MAAM,KAAK,YAAY,GAAG,GAAG,GAAGyD,KAAK,GAAG;UAAC;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eAGNzG,OAAA;YAAKmF,KAAK,EAAE;cAAES,IAAI,EAAE;YAAE,CAAE;YAAAD,QAAA,gBACtB3F,OAAA;cAAKmF,KAAK,EAAE;gBACVkB,QAAQ,EAAE,MAAM;gBAChBF,UAAU,EAAE,KAAK;gBACjBD,YAAY,EAAE,KAAK;gBACnBE,KAAK,EAAE7B,IAAI,CAAClD,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG;cACpD,CAAE;cAAAsE,QAAA,EACCpB,IAAI,CAACnD;YAAI;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eAENzG,OAAA;cAAKmF,KAAK,EAAE;gBACVK,KAAK,EAAE,MAAM;gBACbF,MAAM,EAAE,KAAK;gBACbO,UAAU,EAAE,SAAS;gBACrBC,YAAY,EAAE,KAAK;gBACnBJ,QAAQ,EAAE;cACZ,CAAE;cAAAC,QAAA,eACA3F,OAAA;gBAAKmF,KAAK,EAAE;kBACVK,KAAK,EAAE,GAAGjB,IAAI,CAACjD,QAAQ,GAAG;kBAC1BgE,MAAM,EAAE,MAAM;kBACdO,UAAU,EAAEtB,IAAI,CAAClD,MAAM,KAAK,WAAW,GAAG,SAAS,GACxCkD,IAAI,CAAClD,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG,SAAS;kBAC/D2E,UAAU,EAAE;gBACd;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAtDE3B,KAAK;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuDV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNzG,OAAA;QAAKmF,KAAK,EAAE;UACVU,UAAU,EAAE,OAAO;UACnBkB,MAAM,EAAE,mBAAmB;UAC3BjB,YAAY,EAAE,KAAK;UACnBP,OAAO,EAAE,MAAM;UACfW,YAAY,EAAE,MAAM;UACpBN,IAAI,EAAE,GAAG;UACTR,OAAO,EAAE,MAAM;UACf4B,aAAa,EAAE;QACjB,CAAE;QAAArB,QAAA,gBACA3F,OAAA;UAAKmF,KAAK,EAAE;YACVkB,QAAQ,EAAE,MAAM;YAChBD,KAAK,EAAE,SAAS;YAChBF,YAAY,EAAE,MAAM;YACpBC,UAAU,EAAE,KAAK;YACjBf,OAAO,EAAE,MAAM;YACf6B,UAAU,EAAE,QAAQ;YACpB5B,GAAG,EAAE;UACP,CAAE;UAAAM,QAAA,EAAC;QAEH;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNzG,OAAA;UACEoJ,GAAG,EAAE3H,eAAgB;UACrB0D,KAAK,EAAE;YACLS,IAAI,EAAE,GAAG;YACTyD,SAAS,EAAE,OAAO;YAClBC,SAAS,EAAE,MAAM;YACjBjD,QAAQ,EAAE,MAAM;YAChBoC,UAAU,EAAE,KAAK;YACjBD,UAAU,EAAE;UACd,CAAE;UAAA7C,QAAA,EAEDpE,YAAY,CAACmD,MAAM,KAAK,CAAC,gBACxB1E,OAAA;YAAKmF,KAAK,EAAE;cAAEiB,KAAK,EAAE,SAAS;cAAEmD,SAAS,EAAE;YAAS,CAAE;YAAA5D,QAAA,EAAC;UAEvD;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAENlF,YAAY,CAACqD,GAAG,CAAEzC,GAAG,iBACnBnC,OAAA;YAAkBmF,KAAK,EAAE;cACvBe,YAAY,EAAE,KAAK;cACnBX,OAAO,EAAE,SAAS;cAClBO,YAAY,EAAE,KAAK;cACnBD,UAAU,EAAE1D,GAAG,CAACE,IAAI,KAAK,OAAO,GAAG,SAAS,GACjCF,GAAG,CAACE,IAAI,KAAK,MAAM,GAAG,SAAS,GAC/BF,GAAG,CAACE,IAAI,KAAK,SAAS,GAAG,SAAS,GAClCF,GAAG,CAACE,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;cACvD+D,KAAK,EAAEjE,GAAG,CAACE,IAAI,KAAK,OAAO,GAAG,SAAS,GAChCF,GAAG,CAACE,IAAI,KAAK,MAAM,GAAG,SAAS,GAC/BF,GAAG,CAACE,IAAI,KAAK,SAAS,GAAG,SAAS,GAClCF,GAAG,CAACE,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG;YAC5C,CAAE;YAAAsD,QAAA,gBACA3F,OAAA;cAAMmF,KAAK,EAAE;gBAAEiB,KAAK,EAAE,SAAS;gBAAEoD,WAAW,EAAE;cAAM,CAAE;cAAA7D,QAAA,GAAC,GACpD,EAACxD,GAAG,CAAC4B,SAAS,EAAC,GAClB;YAAA;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACNtE,GAAG,CAAC0B,OAAO;UAAA,GAhBJ1B,GAAG,CAACgC,EAAE;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiBX,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzG,OAAA;QAAKmF,KAAK,EAAE;UACVgD,SAAS,EAAE,MAAM;UACjBsB,UAAU,EAAE,MAAM;UAClBX,SAAS,EAAE;QACb,CAAE;QAAAnD,QAAA,eACA3F,OAAA;UACE2G,OAAO,EAAEA,CAAA,KAAM7F,eAAe,CAAC,KAAK,CAAE;UACtCqE,KAAK,EAAE;YACLK,KAAK,EAAE,MAAM;YACbK,UAAU,EAAE,SAAS;YACrBO,KAAK,EAAE,OAAO;YACdW,MAAM,EAAE,MAAM;YACdxB,OAAO,EAAE,WAAW;YACpBO,YAAY,EAAE,KAAK;YACnBO,QAAQ,EAAE,MAAM;YAChBF,UAAU,EAAE,KAAK;YACjBgB,MAAM,EAAE,SAAS;YACjBnB,UAAU,EAAE,4BAA4B;YACxCZ,OAAO,EAAE,MAAM;YACf6B,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxB7B,GAAG,EAAE;UACP,CAAE;UACFqE,WAAW,EAAGpC,CAAC,IAAKA,CAAC,CAACtF,MAAM,CAACmD,KAAK,CAACU,UAAU,GAAG,SAAU;UAC1D8D,UAAU,EAAGrC,CAAC,IAAKA,CAAC,CAACtF,MAAM,CAACmD,KAAK,CAACU,UAAU,GAAG,SAAU;UAAAF,QAAA,gBAEzD3F,OAAA;YAAA2F,QAAA,EAAM;UAAC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,gBAEhB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDzG,OAAA;MAAA2F,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;IAAO;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACnG,EAAA,CAxqBIH,YAAY;AAAAyJ,EAAA,GAAZzJ,YAAY;AA0qBlB,eAAeA,YAAY;AAAC,IAAAyJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}