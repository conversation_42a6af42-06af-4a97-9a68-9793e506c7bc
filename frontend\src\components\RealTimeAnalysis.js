/**
 * 实时分析组件 - 基于SSE的实时进度展示
 */
import React, { useState, useEffect, useRef } from 'react';

const RealTimeAnalysis = ({ taskId, onAnalysisComplete, onAnalysisError }) => {
  const [agents, setAgents] = useState([
    { name: '元素识别智能体', status: 'pending', message: '等待开始...', progress: 0, duration: null },
    { name: '交互分析智能体', status: 'pending', message: '等待开始...', progress: 0, duration: null },
    { name: '脚本生成智能体', status: 'pending', message: '等待开始...', progress: 0, duration: null },
  ]);
  const [messages, setMessages] = useState([]);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState(null);
  const [startTime, setStartTime] = useState(null);
  const eventSourceRef = useRef(null);

  useEffect(() => {
    if (!taskId) return;

    // 创建SSE连接
    const eventSource = new EventSource(`http://localhost:8001/api/v1/stream/${taskId}`);
    eventSourceRef.current = eventSource;
    setStartTime(Date.now());

    eventSource.onopen = () => {
      console.log('SSE连接已建立');
      setIsConnected(true);
      setConnectionError(null);
    };

    eventSource.addEventListener('connected', (event) => {
      const data = JSON.parse(event.data);
      console.log('连接确认:', data);
      addMessage({
        type: 'system',
        message: data.data.message,
        timestamp: new Date().toISOString(),
      });
    });

    eventSource.addEventListener('agent_progress', (event) => {
      const data = JSON.parse(event.data);
      console.log('智能体进度:', data);
      
      addMessage({
        type: 'progress',
        agent: data.data.agent,
        message: data.data.message,
        progress: data.data.progress,
        timestamp: data.timestamp,
      });
      
      updateAgentStatus(data.data);
    });

    eventSource.addEventListener('task_complete', (event) => {
      const data = JSON.parse(event.data);
      console.log('任务完成:', data);
      
      addMessage({
        type: 'complete',
        message: data.data.message || '所有智能体执行完成！',
        timestamp: data.timestamp,
      });

      // 标记所有智能体为完成状态
      setAgents(prev => prev.map(agent => ({
        ...agent,
        status: 'completed',
        progress: 100,
        duration: Date.now() - startTime,
      })));

      // 获取完整的分析结果
      setTimeout(async () => {
        try {
          const response = await fetch(`http://localhost:8001/api/v1/tasks/${taskId}`);
          if (response.ok) {
            const taskData = await response.json();
            onAnalysisComplete(taskData);
          } else {
            onAnalysisComplete();
          }
        } catch (error) {
          console.error('获取任务结果失败:', error);
          onAnalysisComplete();
        }
      }, 1000);
    });

    eventSource.addEventListener('error', (event) => {
      const data = JSON.parse(event.data);
      console.error('SSE错误:', data);
      onAnalysisError(data.data.message || '分析过程中发生错误');
    });

    eventSource.addEventListener('heartbeat', (event) => {
      const data = JSON.parse(event.data);
      console.log('心跳:', data);
    });

    eventSource.onerror = (error) => {
      console.error('SSE连接错误:', error);
      setIsConnected(false);
      setConnectionError('连接中断，请刷新页面重试');
    };

    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, [taskId, onAnalysisComplete, onAnalysisError]);

  const addMessage = (message) => {
    setMessages(prev => [...prev, { ...message, id: Date.now() + Math.random() }]);
  };

  const updateAgentStatus = (progressData) => {
    setAgents(prev => prev.map(agent => {
      if (agent.name === progressData.agent) {
        return {
          ...agent,
          status: progressData.status,
          message: progressData.message,
          progress: progressData.progress || agent.progress,
          duration: progressData.duration || agent.duration,
        };
      }
      return agent;
    }));
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return '⏳';
      case 'processing':
        return '🔄';
      case 'completed':
        return '✅';
      case 'failed':
        return '❌';
      default:
        return '⏳';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return '#6c757d';
      case 'processing':
        return '#007bff';
      case 'completed':
        return '#28a745';
      case 'failed':
        return '#dc3545';
      default:
        return '#6c757d';
    }
  };

  const formatDuration = (duration) => {
    if (!duration) return '';
    return `${(duration / 1000).toFixed(1)}s`;
  };

  return (
    <div style={{ 
      background: 'white', 
      borderRadius: '12px', 
      padding: '24px', 
      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',
      marginBottom: '24px'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h3 style={{ margin: '0', color: '#333', fontSize: '20px', fontWeight: '600' }}>
          🔄 实时分析进度
        </h3>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          fontSize: '14px',
          fontWeight: '500',
          color: isConnected ? '#28a745' : '#dc3545'
        }}>
          <span style={{
            width: '8px',
            height: '8px',
            borderRadius: '50%',
            backgroundColor: 'currentColor',
            animation: isConnected ? 'pulse 2s infinite' : 'none'
          }}></span>
          {isConnected ? '实时连接中' : '连接中断'}
        </div>
      </div>

      {connectionError && (
        <div style={{
          background: '#f8d7da',
          color: '#721c24',
          padding: '12px',
          borderRadius: '6px',
          marginBottom: '20px',
          border: '1px solid #f5c6cb'
        }}>
          {connectionError}
        </div>
      )}

      {/* 智能体状态 */}
      <div style={{ display: 'grid', gap: '16px', marginBottom: '24px' }}>
        {agents.map((agent, index) => (
          <div key={agent.name} style={{
            border: '1px solid #e9ecef',
            borderRadius: '8px',
            padding: '16px',
            background: '#f8f9fa'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
              <span style={{ fontSize: '20px' }}>{getStatusIcon(agent.status)}</span>
              <span style={{ fontWeight: '600', color: '#333', flex: 1 }}>{agent.name}</span>
              <span style={{ 
                fontSize: '14px', 
                fontWeight: '500',
                color: getStatusColor(agent.status)
              }}>
                {agent.status === 'pending' && '等待中'}
                {agent.status === 'processing' && '处理中'}
                {agent.status === 'completed' && '已完成'}
                {agent.status === 'failed' && '失败'}
              </span>
              {agent.duration && (
                <span style={{ fontSize: '12px', color: '#999' }}>
                  {formatDuration(agent.duration)}
                </span>
              )}
            </div>
            
            <div style={{
              width: '100%',
              height: '8px',
              background: '#e9ecef',
              borderRadius: '4px',
              overflow: 'hidden',
              marginBottom: '8px'
            }}>
              <div style={{
                height: '100%',
                width: `${agent.progress}%`,
                backgroundColor: getStatusColor(agent.status),
                transition: 'width 0.3s ease',
                borderRadius: '4px'
              }}></div>
            </div>
            
            <div style={{ fontSize: '14px', color: '#666' }}>{agent.message}</div>
          </div>
        ))}
      </div>

      {/* 消息流 */}
      <div>
        <h4 style={{ margin: '0 0 12px 0', color: '#333', fontSize: '16px', fontWeight: '600' }}>
          📋 执行日志
        </h4>
        <div style={{
          maxHeight: '200px',
          overflowY: 'auto',
          border: '1px solid #e9ecef',
          borderRadius: '6px',
          padding: '12px',
          background: '#f8f9fa'
        }}>
          {messages.map((message) => (
            <div key={message.id} style={{
              display: 'flex',
              gap: '8px',
              marginBottom: '8px',
              fontSize: '13px',
              lineHeight: '1.4'
            }}>
              <span style={{ color: '#999', fontFamily: 'monospace', minWidth: '80px' }}>
                {new Date(message.timestamp).toLocaleTimeString()}
              </span>
              <span style={{
                color: message.type === 'system' ? '#007bff' : 
                       message.type === 'complete' ? '#28a745' : '#333',
                fontWeight: message.type === 'complete' ? '500' : 'normal',
                minWidth: '80px'
              }}>
                {message.agent ? `[${message.agent}]` : '[系统]'}
              </span>
              <span style={{ color: '#333', flex: 1 }}>{message.message}</span>
            </div>
          ))}
          {messages.length === 0 && (
            <div style={{ color: '#999', textAlign: 'center', padding: '20px' }}>
              等待消息...
            </div>
          )}
        </div>
      </div>

      <style jsx>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
      `}</style>
    </div>
  );
};

export default RealTimeAnalysis;
