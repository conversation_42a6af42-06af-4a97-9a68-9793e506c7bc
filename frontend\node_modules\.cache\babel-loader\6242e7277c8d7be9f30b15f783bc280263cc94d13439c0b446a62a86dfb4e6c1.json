{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\components\\\\SimpleUpload.js\",\n  _s = $RefreshSig$();\n/**\n * 简化的上传组件 - 移除项目信息字段，添加实时分析界面\n */\nimport React, { useState, useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SimpleUpload = ({\n  onUploadSuccess,\n  onUploadError\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const [showAnalysis, setShowAnalysis] = useState(false);\n  const [analysisProgress, setAnalysisProgress] = useState({\n    overall: 0,\n    currentStep: '准备开始分析...',\n    steps: [{\n      name: '初始化',\n      status: 'pending',\n      progress: 0\n    }, {\n      name: '元素分析和智能识别',\n      status: 'pending',\n      progress: 0\n    }, {\n      name: '生成自动化测试脚本',\n      status: 'pending',\n      progress: 0\n    }]\n  });\n  const [analysisLogs, setAnalysisLogs] = useState([]);\n  const logContainerRef = useRef(null);\n\n  // 自动滚动到最新日志\n  useEffect(() => {\n    if (logContainerRef.current) {\n      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;\n    }\n  }, [analysisLogs]);\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n    setIsUploading(true);\n    setShowAnalysis(true);\n    console.log('Analysis started, showAnalysis set to true');\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n\n      // 开始模拟分析进度\n      simulateAnalysisProgress();\n      const response = await fetch('http://localhost:8001/api/v1/upload', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n      const result = await response.json();\n      onUploadSuccess(result);\n    } catch (error) {\n      console.error('Upload error:', error);\n      setShowAnalysis(false);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  const addLog = (message, type = 'info') => {\n    const timestamp = new Date().toLocaleTimeString();\n    setAnalysisLogs(prev => [...prev, {\n      id: Date.now() + Math.random(),\n      timestamp,\n      message,\n      type\n    }]);\n  };\n  const simulateAnalysisProgress = () => {\n    let step = 0;\n    const steps = ['初始化', '元素分析和智能识别', '生成自动化测试脚本'];\n\n    // 清空之前的日志\n    setAnalysisLogs([]);\n\n    // 详细的步骤日志\n    const stepLogs = {\n      0: [\n      // 初始化\n      '🚀 开始分析流程...', '📋 检查上传文件格式和大小', '🔧 初始化AI分析引擎', '📊 加载UI元素识别模型', '✅ 初始化完成'],\n      1: [\n      // 元素分析和智能识别\n      '🔍 开始图像预处理...', '🎯 检测UI界面元素', '📝 识别文本内容和标签', '🔘 分析按钮和交互元素', '📋 识别输入框和表单元素', '🎨 分析布局和样式信息', '🧠 AI智能分类UI组件', '✅ 元素分析完成'],\n      2: [\n      // 生成自动化测试脚本\n      '📝 开始生成测试脚本...', '🔧 构建MidScene.js测试框架', '📋 生成元素定位策略', '⚡ 创建交互操作脚本', '🧪 添加断言和验证逻辑', '📄 格式化YAML输出', '✅ 测试脚本生成完成']\n    };\n    const updateProgress = () => {\n      if (step < steps.length) {\n        // 添加步骤开始日志\n        addLog(`开始执行: ${steps[step]}`, 'step');\n\n        // 更新进度状态\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: Math.round((step + 1) / steps.length * 100),\n          currentStep: `正在执行: ${steps[step]}`,\n          steps: prev.steps.map((s, index) => ({\n            ...s,\n            status: index < step ? 'completed' : index === step ? 'processing' : 'pending',\n            progress: index < step ? 100 : index === step ? 0 : 0\n          }))\n        }));\n\n        // 模拟步骤内的详细日志\n        const currentStepLogs = stepLogs[step];\n        let logIndex = 0;\n        const addStepLogs = () => {\n          if (logIndex < currentStepLogs.length) {\n            addLog(currentStepLogs[logIndex], 'info');\n\n            // 更新当前步骤进度\n            const progress = Math.round((logIndex + 1) / currentStepLogs.length * 100);\n            setAnalysisProgress(prev => ({\n              ...prev,\n              steps: prev.steps.map((s, index) => index === step ? {\n                ...s,\n                progress\n              } : s)\n            }));\n            logIndex++;\n            setTimeout(addStepLogs, 800 + Math.random() * 1200);\n          } else {\n            // 当前步骤完成\n            setAnalysisProgress(prev => ({\n              ...prev,\n              steps: prev.steps.map((s, index) => index === step ? {\n                ...s,\n                status: 'completed',\n                progress: 100\n              } : s)\n            }));\n            step++;\n            setTimeout(updateProgress, 1000);\n          }\n        };\n        setTimeout(addStepLogs, 500);\n      } else {\n        // 所有步骤完成\n        addLog('🎉 所有分析步骤已完成！', 'success');\n        addLog('📄 测试脚本已生成，可以下载使用', 'success');\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: 100,\n          currentStep: '分析完成',\n          steps: prev.steps.map(s => ({\n            ...s,\n            status: 'completed',\n            progress: 100\n          }))\n        }));\n      }\n    };\n\n    // 开始分析\n    addLog('🔥 启动UI自动化测试脚本分析...', 'start');\n    setTimeout(updateProgress, 1000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '24px',\n      height: '100vh',\n      padding: '24px',\n      width: '100%',\n      boxSizing: 'border-box',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: showAnalysis ? '0 0 400px' : '1',\n        background: 'white',\n        padding: '24px',\n        borderRadius: '12px',\n        boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n        transition: 'all 0.3s ease'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '24px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              marginBottom: '12px',\n              fontWeight: '600',\n              color: '#333',\n              fontSize: '16px'\n            },\n            children: \"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"file-upload-area\",\n            onClick: () => document.getElementById('file-input').click(),\n            style: {\n              height: '240px',\n              border: '2px dashed #667eea',\n              borderRadius: '12px',\n              background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.3s ease',\n              position: 'relative',\n              overflow: 'hidden'\n            },\n            onDragOver: e => {\n              e.preventDefault();\n              e.currentTarget.style.borderColor = '#4f46e5';\n              e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';\n            },\n            onDragLeave: e => {\n              e.currentTarget.style.borderColor = '#667eea';\n              e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';\n            },\n            onDrop: e => {\n              e.preventDefault();\n              const files = e.dataTransfer.files;\n              if (files.length > 0) {\n                setSelectedFile(files[0]);\n              }\n              e.currentTarget.style.borderColor = '#667eea';\n              e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"file-input\",\n              type: \"file\",\n              accept: \"image/*\",\n              onChange: handleFileSelect,\n              style: {\n                display: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this), selectedFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: '20px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '48px',\n                  marginBottom: '16px',\n                  color: '#667eea'\n                },\n                children: \"\\u2705\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 8px 0',\n                  color: '#333',\n                  fontSize: '18px'\n                },\n                children: \"\\u6587\\u4EF6\\u5DF2\\u9009\\u62E9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0 0 8px 0',\n                  color: '#666',\n                  fontSize: '14px',\n                  fontWeight: '500'\n                },\n                children: selectedFile.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0',\n                  color: '#999',\n                  fontSize: '12px'\n                },\n                children: [\"\\u5927\\u5C0F: \", (selectedFile.size / 1024 / 1024).toFixed(2), \" MB\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: e => {\n                  e.stopPropagation();\n                  setSelectedFile(null);\n                  document.getElementById('file-input').value = '';\n                },\n                style: {\n                  marginTop: '12px',\n                  background: 'rgba(239, 68, 68, 0.1)',\n                  color: '#ef4444',\n                  border: '1px solid rgba(239, 68, 68, 0.3)',\n                  padding: '6px 12px',\n                  borderRadius: '6px',\n                  fontSize: '12px',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                },\n                children: \"\\u91CD\\u65B0\\u9009\\u62E9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: '20px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '48px',\n                  marginBottom: '16px',\n                  color: '#667eea'\n                },\n                children: \"\\uD83D\\uDCE4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 8px 0',\n                  color: '#333',\n                  fontSize: '18px'\n                },\n                children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u4E0A\\u4F20\\u56FE\\u7247\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0 0 4px 0',\n                  color: '#666',\n                  fontSize: '14px'\n                },\n                children: \"\\u652F\\u6301 PNG\\u3001JPG\\u3001JPEG\\u3001GIF \\u683C\\u5F0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0',\n                  color: '#999',\n                  fontSize: '12px'\n                },\n                children: \"\\u6587\\u4EF6\\u5927\\u5C0F\\u4E0D\\u8D85\\u8FC7 10MB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: '16px',\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  color: 'white',\n                  padding: '8px 20px',\n                  borderRadius: '20px',\n                  fontSize: '14px',\n                  fontWeight: '500',\n                  display: 'inline-block'\n                },\n                children: \"\\u9009\\u62E9\\u6587\\u4EF6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"description\",\n            style: {\n              display: 'block',\n              marginBottom: '8px',\n              fontWeight: '600',\n              color: '#333',\n              fontSize: '16px'\n            },\n            children: \"\\uD83C\\uDFAF \\u754C\\u9762\\u529F\\u80FD\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"description\",\n            value: description,\n            onChange: e => setDescription(e.target.value),\n            placeholder: \"\\u8BF7\\u8BE6\\u7EC6\\u63CF\\u8FF0\\u8FD9\\u4E2A\\u754C\\u9762\\u7684\\u4E3B\\u8981\\u529F\\u80FD\\uFF0C\\u4F8B\\u5982\\uFF1A\\u8FD9\\u662F\\u4E00\\u4E2A\\u767B\\u5F55\\u9875\\u9762\\uFF0C\\u5305\\u542B\\u7528\\u6237\\u540D\\u5BC6\\u7801\\u8F93\\u5165\\u6846\\u548C\\u767B\\u5F55\\u6309\\u94AE...\",\n            rows: 4,\n            style: {\n              width: '100%',\n              padding: '12px',\n              border: '2px solid #e5e7eb',\n              borderRadius: '8px',\n              resize: 'vertical',\n              fontFamily: 'inherit',\n              fontSize: '14px',\n              lineHeight: '1.5',\n              transition: 'border-color 0.2s ease',\n              background: '#fafafa'\n            },\n            onFocus: e => {\n              e.target.style.borderColor = '#667eea';\n              e.target.style.background = '#ffffff';\n            },\n            onBlur: e => {\n              e.target.style.borderColor = '#e5e7eb';\n              e.target.style.background = '#fafafa';\n            },\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'right',\n              fontSize: '12px',\n              color: '#666',\n              marginTop: '4px'\n            },\n            children: [description.length, \"/500\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: isUploading || !selectedFile || !description.trim(),\n          style: {\n            background: isUploading ? '#6c757d' : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '8px',\n            fontSize: '16px',\n            fontWeight: '500',\n            cursor: isUploading ? 'not-allowed' : 'pointer',\n            transition: 'all 0.3s ease',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px',\n            width: '100%',\n            justifyContent: 'center'\n          },\n          children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                width: '16px',\n                height: '16px',\n                border: '2px solid transparent',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this), \"\\u5F00\\u59CB\\u5206\\u6790\\u4E2D...\"]\n          }, void 0, true) : '🚀 开始分析'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this), showAnalysis && (console.log('Rendering analysis panel, showAnalysis:', showAnalysis) || true) && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: '1',\n        background: 'white',\n        padding: '24px',\n        borderRadius: '12px',\n        boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n        overflow: 'hidden',\n        display: 'flex',\n        flexDirection: 'column',\n        minWidth: '400px',\n        maxWidth: '600px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          marginBottom: '24px',\n          paddingBottom: '16px',\n          borderBottom: '2px solid #f1f3f4'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: 0,\n            color: '#333',\n            fontSize: '20px',\n            fontWeight: '600'\n          },\n          children: \"\\uD83D\\uDD0D \\u5B9E\\u65F6\\u5206\\u6790\\u8FDB\\u5EA6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: analysisProgress.overall === 100 ? '#d4edda' : '#f8f9fa',\n            color: analysisProgress.overall === 100 ? '#155724' : '#666',\n            padding: '8px 16px',\n            borderRadius: '20px',\n            fontSize: '14px',\n            fontWeight: '500',\n            border: analysisProgress.overall === 100 ? '1px solid #c3e6cb' : 'none'\n          },\n          children: [analysisProgress.overall, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: '#f8f9ff',\n          border: '1px solid #e5e7eb',\n          borderRadius: '8px',\n          padding: '16px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '14px',\n            color: '#666',\n            marginBottom: '8px'\n          },\n          children: \"\\u5206\\u6790\\u72B6\\u6001\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '16px',\n            fontWeight: '600',\n            color: '#333'\n          },\n          children: analysisProgress.currentStep\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '16px',\n            fontWeight: '600',\n            color: '#333',\n            marginBottom: '12px'\n          },\n          children: \"\\uD83D\\uDCCA \\u5206\\u6790\\u6B65\\u9AA4\\u6982\\u89C8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '8px'\n          },\n          children: analysisProgress.steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1,\n              padding: '12px 8px',\n              background: step.status === 'completed' ? '#d4edda' : step.status === 'processing' ? '#e3f2fd' : '#f8f9fa',\n              border: `1px solid ${step.status === 'completed' ? '#c3e6cb' : step.status === 'processing' ? '#bbdefb' : '#e9ecef'}`,\n              borderRadius: '6px',\n              textAlign: 'center',\n              fontSize: '12px',\n              transition: 'all 0.3s ease'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '16px',\n                marginBottom: '4px',\n                color: step.status === 'completed' ? '#28a745' : step.status === 'processing' ? '#1976d2' : '#6c757d'\n              },\n              children: step.status === 'completed' ? '✅' : step.status === 'processing' ? '⚡' : '⏳'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: '500',\n                color: '#333',\n                fontSize: '11px'\n              },\n              children: step.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '100%',\n                height: '3px',\n                background: '#e9ecef',\n                borderRadius: '2px',\n                overflow: 'hidden',\n                marginTop: '6px'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  height: '100%',\n                  width: `${step.progress}%`,\n                  background: step.status === 'completed' ? '#28a745' : step.status === 'processing' ? '#1976d2' : '#e9ecef',\n                  transition: 'width 0.5s ease',\n                  borderRadius: '2px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            marginBottom: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '16px',\n              fontWeight: '600',\n              color: '#333'\n            },\n            children: \"\\uD83D\\uDCDD \\u5B9E\\u65F6\\u5206\\u6790\\u65E5\\u5FD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              color: '#666',\n              background: '#f8f9fa',\n              padding: '4px 8px',\n              borderRadius: '12px'\n            },\n            children: [analysisLogs.length, \" \\u6761\\u65E5\\u5FD7\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: logContainerRef,\n          style: {\n            flex: 1,\n            background: '#1a1a1a',\n            borderRadius: '8px',\n            padding: '16px',\n            overflow: 'auto',\n            fontFamily: 'Monaco, Consolas, \"Courier New\", monospace',\n            fontSize: '13px',\n            lineHeight: '1.5',\n            maxHeight: '400px'\n          },\n          children: analysisLogs.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#666',\n              textAlign: 'center',\n              padding: '20px'\n            },\n            children: \"\\u7B49\\u5F85\\u5206\\u6790\\u5F00\\u59CB...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 17\n          }, this) : analysisLogs.map(log => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '8px',\n              padding: '6px 0',\n              borderBottom: '1px solid #333'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'flex-start',\n                gap: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#888',\n                  fontSize: '11px',\n                  minWidth: '60px',\n                  fontWeight: '500'\n                },\n                children: log.timestamp\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: log.type === 'start' ? '#4fc3f7' : log.type === 'step' ? '#ffb74d' : log.type === 'success' ? '#81c784' : log.type === 'error' ? '#e57373' : '#e0e0e0',\n                  fontWeight: log.type === 'step' || log.type === 'start' || log.type === 'success' ? '600' : '400'\n                },\n                children: log.message\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 21\n            }, this)\n          }, log.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 590,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 203,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleUpload, \"IdtSkHBBfxWSYby+eMtHi/CdD6o=\");\n_c = SimpleUpload;\nexport default SimpleUpload;\nvar _c;\n$RefreshReg$(_c, \"SimpleUpload\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SimpleUpload", "onUploadSuccess", "onUploadError", "_s", "selectedFile", "setSelectedFile", "description", "setDescription", "isUploading", "setIsUploading", "showAnalysis", "setShowAnalysis", "analysisProgress", "setAnalysisProgress", "overall", "currentStep", "steps", "name", "status", "progress", "analysisLogs", "setAnalysisLogs", "logContainerRef", "current", "scrollTop", "scrollHeight", "handleFileSelect", "event", "file", "target", "files", "handleSubmit", "preventDefault", "trim", "console", "log", "formData", "FormData", "append", "simulateAnalysisProgress", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "result", "error", "message", "addLog", "type", "timestamp", "Date", "toLocaleTimeString", "prev", "id", "now", "Math", "random", "step", "stepLogs", "updateProgress", "length", "round", "map", "s", "index", "currentStepLogs", "logIndex", "addStepLogs", "setTimeout", "style", "display", "gap", "height", "padding", "width", "boxSizing", "overflow", "children", "flex", "background", "borderRadius", "boxShadow", "transition", "onSubmit", "marginBottom", "fontWeight", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onClick", "document", "getElementById", "click", "border", "flexDirection", "alignItems", "justifyContent", "cursor", "position", "onDragOver", "e", "currentTarget", "borderColor", "onDragLeave", "onDrop", "dataTransfer", "accept", "onChange", "textAlign", "margin", "size", "toFixed", "stopPropagation", "value", "marginTop", "htmlFor", "placeholder", "rows", "resize", "fontFamily", "lineHeight", "onFocus", "onBlur", "required", "disabled", "borderTop", "animation", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "paddingBottom", "borderBottom", "ref", "maxHeight", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/components/SimpleUpload.js"], "sourcesContent": ["/**\n * 简化的上传组件 - 移除项目信息字段，添加实时分析界面\n */\nimport React, { useState, useEffect, useRef } from 'react';\n\nconst SimpleUpload = ({ onUploadSuccess, onUploadError }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const [showAnalysis, setShowAnalysis] = useState(false);\n  const [analysisProgress, setAnalysisProgress] = useState({\n    overall: 0,\n    currentStep: '准备开始分析...',\n    steps: [\n      { name: '初始化', status: 'pending', progress: 0 },\n      { name: '元素分析和智能识别', status: 'pending', progress: 0 },\n      { name: '生成自动化测试脚本', status: 'pending', progress: 0 }\n    ]\n  });\n  const [analysisLogs, setAnalysisLogs] = useState([]);\n  const logContainerRef = useRef(null);\n\n  // 自动滚动到最新日志\n  useEffect(() => {\n    if (logContainerRef.current) {\n      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;\n    }\n  }, [analysisLogs]);\n\n  const handleFileSelect = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n\n  const handleSubmit = async (event) => {\n    event.preventDefault();\n\n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n\n    setIsUploading(true);\n    setShowAnalysis(true);\n    console.log('Analysis started, showAnalysis set to true');\n\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n\n      // 开始模拟分析进度\n      simulateAnalysisProgress();\n\n      const response = await fetch('http://localhost:8001/api/v1/upload', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      onUploadSuccess(result);\n\n    } catch (error) {\n      console.error('Upload error:', error);\n      setShowAnalysis(false);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const addLog = (message, type = 'info') => {\n    const timestamp = new Date().toLocaleTimeString();\n    setAnalysisLogs(prev => [...prev, {\n      id: Date.now() + Math.random(),\n      timestamp,\n      message,\n      type\n    }]);\n  };\n\n  const simulateAnalysisProgress = () => {\n    let step = 0;\n    const steps = ['初始化', '元素分析和智能识别', '生成自动化测试脚本'];\n\n    // 清空之前的日志\n    setAnalysisLogs([]);\n\n    // 详细的步骤日志\n    const stepLogs = {\n      0: [ // 初始化\n        '🚀 开始分析流程...',\n        '📋 检查上传文件格式和大小',\n        '🔧 初始化AI分析引擎',\n        '📊 加载UI元素识别模型',\n        '✅ 初始化完成'\n      ],\n      1: [ // 元素分析和智能识别\n        '🔍 开始图像预处理...',\n        '🎯 检测UI界面元素',\n        '📝 识别文本内容和标签',\n        '🔘 分析按钮和交互元素',\n        '📋 识别输入框和表单元素',\n        '🎨 分析布局和样式信息',\n        '🧠 AI智能分类UI组件',\n        '✅ 元素分析完成'\n      ],\n      2: [ // 生成自动化测试脚本\n        '📝 开始生成测试脚本...',\n        '🔧 构建MidScene.js测试框架',\n        '📋 生成元素定位策略',\n        '⚡ 创建交互操作脚本',\n        '🧪 添加断言和验证逻辑',\n        '📄 格式化YAML输出',\n        '✅ 测试脚本生成完成'\n      ]\n    };\n\n    const updateProgress = () => {\n      if (step < steps.length) {\n        // 添加步骤开始日志\n        addLog(`开始执行: ${steps[step]}`, 'step');\n\n        // 更新进度状态\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: Math.round((step + 1) / steps.length * 100),\n          currentStep: `正在执行: ${steps[step]}`,\n          steps: prev.steps.map((s, index) => ({\n            ...s,\n            status: index < step ? 'completed' : index === step ? 'processing' : 'pending',\n            progress: index < step ? 100 : index === step ? 0 : 0\n          }))\n        }));\n\n        // 模拟步骤内的详细日志\n        const currentStepLogs = stepLogs[step];\n        let logIndex = 0;\n\n        const addStepLogs = () => {\n          if (logIndex < currentStepLogs.length) {\n            addLog(currentStepLogs[logIndex], 'info');\n\n            // 更新当前步骤进度\n            const progress = Math.round(((logIndex + 1) / currentStepLogs.length) * 100);\n            setAnalysisProgress(prev => ({\n              ...prev,\n              steps: prev.steps.map((s, index) =>\n                index === step ? { ...s, progress } : s\n              )\n            }));\n\n            logIndex++;\n            setTimeout(addStepLogs, 800 + Math.random() * 1200);\n          } else {\n            // 当前步骤完成\n            setAnalysisProgress(prev => ({\n              ...prev,\n              steps: prev.steps.map((s, index) =>\n                index === step ? { ...s, status: 'completed', progress: 100 } : s\n              )\n            }));\n\n            step++;\n            setTimeout(updateProgress, 1000);\n          }\n        };\n\n        setTimeout(addStepLogs, 500);\n\n      } else {\n        // 所有步骤完成\n        addLog('🎉 所有分析步骤已完成！', 'success');\n        addLog('📄 测试脚本已生成，可以下载使用', 'success');\n\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: 100,\n          currentStep: '分析完成',\n          steps: prev.steps.map(s => ({ ...s, status: 'completed', progress: 100 }))\n        }));\n      }\n    };\n\n    // 开始分析\n    addLog('🔥 启动UI自动化测试脚本分析...', 'start');\n    setTimeout(updateProgress, 1000);\n  };\n\n  return (\n    <div style={{\n      display: 'flex',\n      gap: '24px',\n      height: '100vh',\n      padding: '24px',\n      width: '100%',\n      boxSizing: 'border-box',\n      overflow: 'hidden'\n    }}>\n      {/* 左侧上传区域 */}\n      <div style={{\n        flex: showAnalysis ? '0 0 400px' : '1',\n        background: 'white',\n        padding: '24px',\n        borderRadius: '12px',\n        boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n        transition: 'all 0.3s ease'\n      }}>\n        <form onSubmit={handleSubmit}>\n          <div style={{ marginBottom: '24px' }}>\n            <label style={{ display: 'block', marginBottom: '12px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n              📁 上传UI界面截图\n            </label>\n            <div\n              className=\"file-upload-area\"\n              onClick={() => document.getElementById('file-input').click()}\n              style={{\n                height: '240px',\n                border: '2px dashed #667eea',\n                borderRadius: '12px',\n                background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                justifyContent: 'center',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease',\n                position: 'relative',\n                overflow: 'hidden'\n              }}\n              onDragOver={(e) => {\n                e.preventDefault();\n                e.currentTarget.style.borderColor = '#4f46e5';\n                e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';\n              }}\n              onDragLeave={(e) => {\n                e.currentTarget.style.borderColor = '#667eea';\n                e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';\n              }}\n              onDrop={(e) => {\n                e.preventDefault();\n                const files = e.dataTransfer.files;\n                if (files.length > 0) {\n                  setSelectedFile(files[0]);\n                }\n                e.currentTarget.style.borderColor = '#667eea';\n                e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';\n              }}\n            >\n              <input\n                id=\"file-input\"\n                type=\"file\"\n                accept=\"image/*\"\n                onChange={handleFileSelect}\n                style={{ display: 'none' }}\n              />\n\n              {selectedFile ? (\n                <div style={{ textAlign: 'center', padding: '20px' }}>\n                  <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>✅</div>\n                  <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>文件已选择</h3>\n                  <p style={{ margin: '0 0 8px 0', color: '#666', fontSize: '14px', fontWeight: '500' }}>\n                    {selectedFile.name}\n                  </p>\n                  <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>\n                    大小: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB\n                  </p>\n                  <button\n                    type=\"button\"\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      setSelectedFile(null);\n                      document.getElementById('file-input').value = '';\n                    }}\n                    style={{\n                      marginTop: '12px',\n                      background: 'rgba(239, 68, 68, 0.1)',\n                      color: '#ef4444',\n                      border: '1px solid rgba(239, 68, 68, 0.3)',\n                      padding: '6px 12px',\n                      borderRadius: '6px',\n                      fontSize: '12px',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    }}\n                  >\n                    重新选择\n                  </button>\n                </div>\n              ) : (\n                <div style={{ textAlign: 'center', padding: '20px' }}>\n                  <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>📤</div>\n                  <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>点击或拖拽上传图片</h3>\n                  <p style={{ margin: '0 0 4px 0', color: '#666', fontSize: '14px' }}>\n                    支持 PNG、JPG、JPEG、GIF 格式\n                  </p>\n                  <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>\n                    文件大小不超过 10MB\n                  </p>\n                  <div style={{\n                    marginTop: '16px',\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    color: 'white',\n                    padding: '8px 20px',\n                    borderRadius: '20px',\n                    fontSize: '14px',\n                    fontWeight: '500',\n                    display: 'inline-block'\n                  }}>\n                    选择文件\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* 界面功能描述 */}\n          <div style={{ marginBottom: '20px' }}>\n            <label htmlFor=\"description\" style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n              🎯 界面功能描述\n            </label>\n            <textarea\n              id=\"description\"\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              placeholder=\"请详细描述这个界面的主要功能，例如：这是一个登录页面，包含用户名密码输入框和登录按钮...\"\n              rows={4}\n              style={{\n                width: '100%',\n                padding: '12px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '8px',\n                resize: 'vertical',\n                fontFamily: 'inherit',\n                fontSize: '14px',\n                lineHeight: '1.5',\n                transition: 'border-color 0.2s ease',\n                background: '#fafafa'\n              }}\n              onFocus={(e) => {\n                e.target.style.borderColor = '#667eea';\n                e.target.style.background = '#ffffff';\n              }}\n              onBlur={(e) => {\n                e.target.style.borderColor = '#e5e7eb';\n                e.target.style.background = '#fafafa';\n              }}\n              required\n            />\n            <div style={{ textAlign: 'right', fontSize: '12px', color: '#666', marginTop: '4px' }}>\n              {description.length}/500\n            </div>\n          </div>\n\n          <button\n            type=\"submit\"\n            disabled={isUploading || !selectedFile || !description.trim()}\n            style={{\n              background: isUploading ? '#6c757d' : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '8px',\n              fontSize: '16px',\n              fontWeight: '500',\n              cursor: isUploading ? 'not-allowed' : 'pointer',\n              transition: 'all 0.3s ease',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px',\n              width: '100%',\n              justifyContent: 'center'\n            }}\n          >\n            {isUploading ? (\n              <>\n                <span style={{\n                  width: '16px',\n                  height: '16px',\n                  border: '2px solid transparent',\n                  borderTop: '2px solid white',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}></span>\n                开始分析中...\n              </>\n            ) : (\n              '🚀 开始分析'\n            )}\n          </button>\n        </form>\n      </div>\n\n      {/* 右侧实时分析界面 */}\n      {showAnalysis && (console.log('Rendering analysis panel, showAnalysis:', showAnalysis) || true) && (\n        <div style={{\n          flex: '1',\n          background: 'white',\n          padding: '24px',\n          borderRadius: '12px',\n          boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n          overflow: 'hidden',\n          display: 'flex',\n          flexDirection: 'column',\n          minWidth: '400px',\n          maxWidth: '600px'\n        }}>\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            marginBottom: '24px',\n            paddingBottom: '16px',\n            borderBottom: '2px solid #f1f3f4'\n          }}>\n            <h3 style={{ margin: 0, color: '#333', fontSize: '20px', fontWeight: '600' }}>\n              🔍 实时分析进度\n            </h3>\n            <div style={{\n              background: analysisProgress.overall === 100 ? '#d4edda' : '#f8f9fa',\n              color: analysisProgress.overall === 100 ? '#155724' : '#666',\n              padding: '8px 16px',\n              borderRadius: '20px',\n              fontSize: '14px',\n              fontWeight: '500',\n              border: analysisProgress.overall === 100 ? '1px solid #c3e6cb' : 'none'\n            }}>\n              {analysisProgress.overall}%\n            </div>\n          </div>\n\n          {/* 当前步骤 */}\n          <div style={{\n            background: '#f8f9ff',\n            border: '1px solid #e5e7eb',\n            borderRadius: '8px',\n            padding: '16px',\n            marginBottom: '20px'\n          }}>\n            <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>\n              分析状态\n            </div>\n            <div style={{ fontSize: '16px', fontWeight: '600', color: '#333' }}>\n              {analysisProgress.currentStep}\n            </div>\n          </div>\n\n          {/* 分析步骤概览 */}\n          <div style={{ marginBottom: '20px' }}>\n            <div style={{ fontSize: '16px', fontWeight: '600', color: '#333', marginBottom: '12px' }}>\n              📊 分析步骤概览\n            </div>\n            <div style={{ display: 'flex', gap: '8px' }}>\n              {analysisProgress.steps.map((step, index) => (\n                <div key={index} style={{\n                  flex: 1,\n                  padding: '12px 8px',\n                  background: step.status === 'completed' ? '#d4edda' :\n                             step.status === 'processing' ? '#e3f2fd' : '#f8f9fa',\n                  border: `1px solid ${step.status === 'completed' ? '#c3e6cb' :\n                                      step.status === 'processing' ? '#bbdefb' : '#e9ecef'}`,\n                  borderRadius: '6px',\n                  textAlign: 'center',\n                  fontSize: '12px',\n                  transition: 'all 0.3s ease'\n                }}>\n                  <div style={{\n                    fontSize: '16px',\n                    marginBottom: '4px',\n                    color: step.status === 'completed' ? '#28a745' :\n                           step.status === 'processing' ? '#1976d2' : '#6c757d'\n                  }}>\n                    {step.status === 'completed' ? '✅' :\n                     step.status === 'processing' ? '⚡' : '⏳'}\n                  </div>\n                  <div style={{ fontWeight: '500', color: '#333', fontSize: '11px' }}>\n                    {step.name}\n                  </div>\n                  <div style={{\n                    width: '100%',\n                    height: '3px',\n                    background: '#e9ecef',\n                    borderRadius: '2px',\n                    overflow: 'hidden',\n                    marginTop: '6px'\n                  }}>\n                    <div style={{\n                      height: '100%',\n                      width: `${step.progress}%`,\n                      background: step.status === 'completed' ? '#28a745' :\n                                 step.status === 'processing' ? '#1976d2' : '#e9ecef',\n                      transition: 'width 0.5s ease',\n                      borderRadius: '2px'\n                    }}></div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* 实时日志区域 */}\n          <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              marginBottom: '12px'\n            }}>\n              <div style={{ fontSize: '16px', fontWeight: '600', color: '#333' }}>\n                📝 实时分析日志\n              </div>\n              <div style={{\n                fontSize: '12px',\n                color: '#666',\n                background: '#f8f9fa',\n                padding: '4px 8px',\n                borderRadius: '12px'\n              }}>\n                {analysisLogs.length} 条日志\n              </div>\n            </div>\n\n            <div\n              ref={logContainerRef}\n              style={{\n                flex: 1,\n                background: '#1a1a1a',\n                borderRadius: '8px',\n                padding: '16px',\n                overflow: 'auto',\n                fontFamily: 'Monaco, Consolas, \"Courier New\", monospace',\n                fontSize: '13px',\n                lineHeight: '1.5',\n                maxHeight: '400px'\n              }}>\n              {analysisLogs.length === 0 ? (\n                <div style={{ color: '#666', textAlign: 'center', padding: '20px' }}>\n                  等待分析开始...\n                </div>\n              ) : (\n                analysisLogs.map((log) => (\n                  <div key={log.id} style={{\n                    marginBottom: '8px',\n                    padding: '6px 0',\n                    borderBottom: '1px solid #333'\n                  }}>\n                    <div style={{\n                      display: 'flex',\n                      alignItems: 'flex-start',\n                      gap: '8px'\n                    }}>\n                      <span style={{\n                        color: '#888',\n                        fontSize: '11px',\n                        minWidth: '60px',\n                        fontWeight: '500'\n                      }}>\n                        {log.timestamp}\n                      </span>\n                      <span style={{\n                        color: log.type === 'start' ? '#4fc3f7' :\n                               log.type === 'step' ? '#ffb74d' :\n                               log.type === 'success' ? '#81c784' :\n                               log.type === 'error' ? '#e57373' : '#e0e0e0',\n                        fontWeight: log.type === 'step' || log.type === 'start' || log.type === 'success' ? '600' : '400'\n                      }}>\n                        {log.message}\n                      </span>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n\n      <style>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default SimpleUpload;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3D,MAAMC,YAAY,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpB,QAAQ,CAAC;IACvDqB,OAAO,EAAE,CAAC;IACVC,WAAW,EAAE,WAAW;IACxBC,KAAK,EAAE,CACL;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAE,CAAC,EAC/C;MAAEF,IAAI,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAE,CAAC,EACrD;MAAEF,IAAI,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAE,CAAC;EAEzD,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM6B,eAAe,GAAG3B,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACAD,SAAS,CAAC,MAAM;IACd,IAAI4B,eAAe,CAACC,OAAO,EAAE;MAC3BD,eAAe,CAACC,OAAO,CAACC,SAAS,GAAGF,eAAe,CAACC,OAAO,CAACE,YAAY;IAC1E;EACF,CAAC,EAAE,CAACL,YAAY,CAAC,CAAC;EAElB,MAAMM,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACRvB,eAAe,CAACuB,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOJ,KAAK,IAAK;IACpCA,KAAK,CAACK,cAAc,CAAC,CAAC;IAEtB,IAAI,CAAC5B,YAAY,EAAE;MACjBF,aAAa,CAAC,SAAS,CAAC;MACxB;IACF;IAEA,IAAI,CAACI,WAAW,CAAC2B,IAAI,CAAC,CAAC,EAAE;MACvB/B,aAAa,CAAC,WAAW,CAAC;MAC1B;IACF;IAEAO,cAAc,CAAC,IAAI,CAAC;IACpBE,eAAe,CAAC,IAAI,CAAC;IACrBuB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IAEzD,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAElC,YAAY,CAAC;MAC3CgC,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEhC,WAAW,CAAC2B,IAAI,CAAC,CAAC,CAAC;;MAElD;MACAM,wBAAwB,CAAC,CAAC;MAE1B,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,qCAAqC,EAAE;QAClEC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEP;MACR,CAAC,CAAC;MAEF,IAAI,CAACI,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,uBAAuBR,QAAQ,CAACtB,MAAM,EAAE,CAAC;MAC/E;MAEA,MAAM+B,MAAM,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MACpC7C,eAAe,CAACgD,MAAM,CAAC;IAEzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCvC,eAAe,CAAC,KAAK,CAAC;MACtBT,aAAa,CAACgD,KAAK,CAACC,OAAO,IAAI,UAAU,CAAC;IAC5C,CAAC,SAAS;MACR1C,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM2C,MAAM,GAAGA,CAACD,OAAO,EAAEE,IAAI,GAAG,MAAM,KAAK;IACzC,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;IACjDnC,eAAe,CAACoC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAChCC,EAAE,EAAEH,IAAI,CAACI,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC;MAC9BP,SAAS;MACTH,OAAO;MACPE;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMd,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAIuB,IAAI,GAAG,CAAC;IACZ,MAAM9C,KAAK,GAAG,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,CAAC;;IAE/C;IACAK,eAAe,CAAC,EAAE,CAAC;;IAEnB;IACA,MAAM0C,QAAQ,GAAG;MACf,CAAC,EAAE;MAAE;MACH,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,eAAe,EACf,SAAS,CACV;MACD,CAAC,EAAE;MAAE;MACH,eAAe,EACf,aAAa,EACb,cAAc,EACd,cAAc,EACd,eAAe,EACf,cAAc,EACd,eAAe,EACf,UAAU,CACX;MACD,CAAC,EAAE;MAAE;MACH,gBAAgB,EAChB,sBAAsB,EACtB,aAAa,EACb,YAAY,EACZ,cAAc,EACd,cAAc,EACd,YAAY;IAEhB,CAAC;IAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAIF,IAAI,GAAG9C,KAAK,CAACiD,MAAM,EAAE;QACvB;QACAb,MAAM,CAAC,SAASpC,KAAK,CAAC8C,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC;;QAEtC;QACAjD,mBAAmB,CAAC4C,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACP3C,OAAO,EAAE8C,IAAI,CAACM,KAAK,CAAC,CAACJ,IAAI,GAAG,CAAC,IAAI9C,KAAK,CAACiD,MAAM,GAAG,GAAG,CAAC;UACpDlD,WAAW,EAAE,SAASC,KAAK,CAAC8C,IAAI,CAAC,EAAE;UACnC9C,KAAK,EAAEyC,IAAI,CAACzC,KAAK,CAACmD,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,MAAM;YACnC,GAAGD,CAAC;YACJlD,MAAM,EAAEmD,KAAK,GAAGP,IAAI,GAAG,WAAW,GAAGO,KAAK,KAAKP,IAAI,GAAG,YAAY,GAAG,SAAS;YAC9E3C,QAAQ,EAAEkD,KAAK,GAAGP,IAAI,GAAG,GAAG,GAAGO,KAAK,KAAKP,IAAI,GAAG,CAAC,GAAG;UACtD,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMQ,eAAe,GAAGP,QAAQ,CAACD,IAAI,CAAC;QACtC,IAAIS,QAAQ,GAAG,CAAC;QAEhB,MAAMC,WAAW,GAAGA,CAAA,KAAM;UACxB,IAAID,QAAQ,GAAGD,eAAe,CAACL,MAAM,EAAE;YACrCb,MAAM,CAACkB,eAAe,CAACC,QAAQ,CAAC,EAAE,MAAM,CAAC;;YAEzC;YACA,MAAMpD,QAAQ,GAAGyC,IAAI,CAACM,KAAK,CAAE,CAACK,QAAQ,GAAG,CAAC,IAAID,eAAe,CAACL,MAAM,GAAI,GAAG,CAAC;YAC5EpD,mBAAmB,CAAC4C,IAAI,KAAK;cAC3B,GAAGA,IAAI;cACPzC,KAAK,EAAEyC,IAAI,CAACzC,KAAK,CAACmD,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAC7BA,KAAK,KAAKP,IAAI,GAAG;gBAAE,GAAGM,CAAC;gBAAEjD;cAAS,CAAC,GAAGiD,CACxC;YACF,CAAC,CAAC,CAAC;YAEHG,QAAQ,EAAE;YACVE,UAAU,CAACD,WAAW,EAAE,GAAG,GAAGZ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;UACrD,CAAC,MAAM;YACL;YACAhD,mBAAmB,CAAC4C,IAAI,KAAK;cAC3B,GAAGA,IAAI;cACPzC,KAAK,EAAEyC,IAAI,CAACzC,KAAK,CAACmD,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAC7BA,KAAK,KAAKP,IAAI,GAAG;gBAAE,GAAGM,CAAC;gBAAElD,MAAM,EAAE,WAAW;gBAAEC,QAAQ,EAAE;cAAI,CAAC,GAAGiD,CAClE;YACF,CAAC,CAAC,CAAC;YAEHN,IAAI,EAAE;YACNW,UAAU,CAACT,cAAc,EAAE,IAAI,CAAC;UAClC;QACF,CAAC;QAEDS,UAAU,CAACD,WAAW,EAAE,GAAG,CAAC;MAE9B,CAAC,MAAM;QACL;QACApB,MAAM,CAAC,eAAe,EAAE,SAAS,CAAC;QAClCA,MAAM,CAAC,mBAAmB,EAAE,SAAS,CAAC;QAEtCvC,mBAAmB,CAAC4C,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACP3C,OAAO,EAAE,GAAG;UACZC,WAAW,EAAE,MAAM;UACnBC,KAAK,EAAEyC,IAAI,CAACzC,KAAK,CAACmD,GAAG,CAACC,CAAC,KAAK;YAAE,GAAGA,CAAC;YAAElD,MAAM,EAAE,WAAW;YAAEC,QAAQ,EAAE;UAAI,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;MACL;IACF,CAAC;;IAED;IACAiC,MAAM,CAAC,qBAAqB,EAAE,OAAO,CAAC;IACtCqB,UAAU,CAACT,cAAc,EAAE,IAAI,CAAC;EAClC,CAAC;EAED,oBACEnE,OAAA;IAAK6E,KAAK,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,GAAG,EAAE,MAAM;MACXC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE,MAAM;MACfC,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE,YAAY;MACvBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEArF,OAAA;MAAK6E,KAAK,EAAE;QACVS,IAAI,EAAEzE,YAAY,GAAG,WAAW,GAAG,GAAG;QACtC0E,UAAU,EAAE,OAAO;QACnBN,OAAO,EAAE,MAAM;QACfO,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE,4BAA4B;QACvCC,UAAU,EAAE;MACd,CAAE;MAAAL,QAAA,eACArF,OAAA;QAAM2F,QAAQ,EAAEzD,YAAa;QAAAmD,QAAA,gBAC3BrF,OAAA;UAAK6E,KAAK,EAAE;YAAEe,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,gBACnCrF,OAAA;YAAO6E,KAAK,EAAE;cAAEC,OAAO,EAAE,OAAO;cAAEc,YAAY,EAAE,MAAM;cAAEC,UAAU,EAAE,KAAK;cAAEC,KAAK,EAAE,MAAM;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAV,QAAA,EAAC;UAE9G;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRnG,OAAA;YACEoG,SAAS,EAAC,kBAAkB;YAC5BC,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAACC,KAAK,CAAC,CAAE;YAC7D3B,KAAK,EAAE;cACLG,MAAM,EAAE,OAAO;cACfyB,MAAM,EAAE,oBAAoB;cAC5BjB,YAAY,EAAE,MAAM;cACpBD,UAAU,EAAEhF,YAAY,GAAG,mDAAmD,GAAG,mDAAmD;cACpIuE,OAAO,EAAE,MAAM;cACf4B,aAAa,EAAE,QAAQ;cACvBC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,MAAM,EAAE,SAAS;cACjBnB,UAAU,EAAE,eAAe;cAC3BoB,QAAQ,EAAE,UAAU;cACpB1B,QAAQ,EAAE;YACZ,CAAE;YACF2B,UAAU,EAAGC,CAAC,IAAK;cACjBA,CAAC,CAAC7E,cAAc,CAAC,CAAC;cAClB6E,CAAC,CAACC,aAAa,CAACpC,KAAK,CAACqC,WAAW,GAAG,SAAS;cAC7CF,CAAC,CAACC,aAAa,CAACpC,KAAK,CAACU,UAAU,GAAG,mDAAmD;YACxF,CAAE;YACF4B,WAAW,EAAGH,CAAC,IAAK;cAClBA,CAAC,CAACC,aAAa,CAACpC,KAAK,CAACqC,WAAW,GAAG,SAAS;cAC7CF,CAAC,CAACC,aAAa,CAACpC,KAAK,CAACU,UAAU,GAAGhF,YAAY,GAAG,mDAAmD,GAAG,mDAAmD;YAC7J,CAAE;YACF6G,MAAM,EAAGJ,CAAC,IAAK;cACbA,CAAC,CAAC7E,cAAc,CAAC,CAAC;cAClB,MAAMF,KAAK,GAAG+E,CAAC,CAACK,YAAY,CAACpF,KAAK;cAClC,IAAIA,KAAK,CAACmC,MAAM,GAAG,CAAC,EAAE;gBACpB5D,eAAe,CAACyB,KAAK,CAAC,CAAC,CAAC,CAAC;cAC3B;cACA+E,CAAC,CAACC,aAAa,CAACpC,KAAK,CAACqC,WAAW,GAAG,SAAS;cAC7CF,CAAC,CAACC,aAAa,CAACpC,KAAK,CAACU,UAAU,GAAG,mDAAmD;YACxF,CAAE;YAAAF,QAAA,gBAEFrF,OAAA;cACE6D,EAAE,EAAC,YAAY;cACfL,IAAI,EAAC,MAAM;cACX8D,MAAM,EAAC,SAAS;cAChBC,QAAQ,EAAE1F,gBAAiB;cAC3BgD,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAO;YAAE;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,EAED5F,YAAY,gBACXP,OAAA;cAAK6E,KAAK,EAAE;gBAAE2C,SAAS,EAAE,QAAQ;gBAAEvC,OAAO,EAAE;cAAO,CAAE;cAAAI,QAAA,gBACnDrF,OAAA;gBAAK6E,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,MAAM;kBAAEH,YAAY,EAAE,MAAM;kBAAEE,KAAK,EAAE;gBAAU,CAAE;gBAAAT,QAAA,EAAC;cAAC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjFnG,OAAA;gBAAI6E,KAAK,EAAE;kBAAE4C,MAAM,EAAE,WAAW;kBAAE3B,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAV,QAAA,EAAC;cAAK;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/EnG,OAAA;gBAAG6E,KAAK,EAAE;kBAAE4C,MAAM,EAAE,WAAW;kBAAE3B,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,MAAM;kBAAEF,UAAU,EAAE;gBAAM,CAAE;gBAAAR,QAAA,EACnF9E,YAAY,CAACa;cAAI;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACJnG,OAAA;gBAAG6E,KAAK,EAAE;kBAAE4C,MAAM,EAAE,GAAG;kBAAE3B,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAV,QAAA,GAAC,gBACtD,EAAC,CAAC9E,YAAY,CAACmH,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KACpD;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJnG,OAAA;gBACEwD,IAAI,EAAC,QAAQ;gBACb6C,OAAO,EAAGW,CAAC,IAAK;kBACdA,CAAC,CAACY,eAAe,CAAC,CAAC;kBACnBpH,eAAe,CAAC,IAAI,CAAC;kBACrB8F,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAACsB,KAAK,GAAG,EAAE;gBAClD,CAAE;gBACFhD,KAAK,EAAE;kBACLiD,SAAS,EAAE,MAAM;kBACjBvC,UAAU,EAAE,wBAAwB;kBACpCO,KAAK,EAAE,SAAS;kBAChBW,MAAM,EAAE,kCAAkC;kBAC1CxB,OAAO,EAAE,UAAU;kBACnBO,YAAY,EAAE,KAAK;kBACnBO,QAAQ,EAAE,MAAM;kBAChBc,MAAM,EAAE,SAAS;kBACjBnB,UAAU,EAAE;gBACd,CAAE;gBAAAL,QAAA,EACH;cAED;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,gBAENnG,OAAA;cAAK6E,KAAK,EAAE;gBAAE2C,SAAS,EAAE,QAAQ;gBAAEvC,OAAO,EAAE;cAAO,CAAE;cAAAI,QAAA,gBACnDrF,OAAA;gBAAK6E,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,MAAM;kBAAEH,YAAY,EAAE,MAAM;kBAAEE,KAAK,EAAE;gBAAU,CAAE;gBAAAT,QAAA,EAAC;cAAE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClFnG,OAAA;gBAAI6E,KAAK,EAAE;kBAAE4C,MAAM,EAAE,WAAW;kBAAE3B,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAV,QAAA,EAAC;cAAS;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnFnG,OAAA;gBAAG6E,KAAK,EAAE;kBAAE4C,MAAM,EAAE,WAAW;kBAAE3B,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAV,QAAA,EAAC;cAEpE;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJnG,OAAA;gBAAG6E,KAAK,EAAE;kBAAE4C,MAAM,EAAE,GAAG;kBAAE3B,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAV,QAAA,EAAC;cAE5D;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJnG,OAAA;gBAAK6E,KAAK,EAAE;kBACViD,SAAS,EAAE,MAAM;kBACjBvC,UAAU,EAAE,mDAAmD;kBAC/DO,KAAK,EAAE,OAAO;kBACdb,OAAO,EAAE,UAAU;kBACnBO,YAAY,EAAE,MAAM;kBACpBO,QAAQ,EAAE,MAAM;kBAChBF,UAAU,EAAE,KAAK;kBACjBf,OAAO,EAAE;gBACX,CAAE;gBAAAO,QAAA,EAAC;cAEH;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnG,OAAA;UAAK6E,KAAK,EAAE;YAAEe,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,gBACnCrF,OAAA;YAAO+H,OAAO,EAAC,aAAa;YAAClD,KAAK,EAAE;cAAEC,OAAO,EAAE,OAAO;cAAEc,YAAY,EAAE,KAAK;cAAEC,UAAU,EAAE,KAAK;cAAEC,KAAK,EAAE,MAAM;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAV,QAAA,EAAC;UAEnI;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRnG,OAAA;YACE6D,EAAE,EAAC,aAAa;YAChBgE,KAAK,EAAEpH,WAAY;YACnB8G,QAAQ,EAAGP,CAAC,IAAKtG,cAAc,CAACsG,CAAC,CAAChF,MAAM,CAAC6F,KAAK,CAAE;YAChDG,WAAW,EAAC,iQAA+C;YAC3DC,IAAI,EAAE,CAAE;YACRpD,KAAK,EAAE;cACLK,KAAK,EAAE,MAAM;cACbD,OAAO,EAAE,MAAM;cACfwB,MAAM,EAAE,mBAAmB;cAC3BjB,YAAY,EAAE,KAAK;cACnB0C,MAAM,EAAE,UAAU;cAClBC,UAAU,EAAE,SAAS;cACrBpC,QAAQ,EAAE,MAAM;cAChBqC,UAAU,EAAE,KAAK;cACjB1C,UAAU,EAAE,wBAAwB;cACpCH,UAAU,EAAE;YACd,CAAE;YACF8C,OAAO,EAAGrB,CAAC,IAAK;cACdA,CAAC,CAAChF,MAAM,CAAC6C,KAAK,CAACqC,WAAW,GAAG,SAAS;cACtCF,CAAC,CAAChF,MAAM,CAAC6C,KAAK,CAACU,UAAU,GAAG,SAAS;YACvC,CAAE;YACF+C,MAAM,EAAGtB,CAAC,IAAK;cACbA,CAAC,CAAChF,MAAM,CAAC6C,KAAK,CAACqC,WAAW,GAAG,SAAS;cACtCF,CAAC,CAAChF,MAAM,CAAC6C,KAAK,CAACU,UAAU,GAAG,SAAS;YACvC,CAAE;YACFgD,QAAQ;UAAA;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFnG,OAAA;YAAK6E,KAAK,EAAE;cAAE2C,SAAS,EAAE,OAAO;cAAEzB,QAAQ,EAAE,MAAM;cAAED,KAAK,EAAE,MAAM;cAAEgC,SAAS,EAAE;YAAM,CAAE;YAAAzC,QAAA,GACnF5E,WAAW,CAAC2D,MAAM,EAAC,MACtB;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnG,OAAA;UACEwD,IAAI,EAAC,QAAQ;UACbgF,QAAQ,EAAE7H,WAAW,IAAI,CAACJ,YAAY,IAAI,CAACE,WAAW,CAAC2B,IAAI,CAAC,CAAE;UAC9DyC,KAAK,EAAE;YACLU,UAAU,EAAE5E,WAAW,GAAG,SAAS,GAAG,mDAAmD;YACzFmF,KAAK,EAAE,OAAO;YACdW,MAAM,EAAE,MAAM;YACdxB,OAAO,EAAE,WAAW;YACpBO,YAAY,EAAE,KAAK;YACnBO,QAAQ,EAAE,MAAM;YAChBF,UAAU,EAAE,KAAK;YACjBgB,MAAM,EAAElG,WAAW,GAAG,aAAa,GAAG,SAAS;YAC/C+E,UAAU,EAAE,eAAe;YAC3BZ,OAAO,EAAE,MAAM;YACf6B,UAAU,EAAE,QAAQ;YACpB5B,GAAG,EAAE,KAAK;YACVG,KAAK,EAAE,MAAM;YACb0B,cAAc,EAAE;UAClB,CAAE;UAAAvB,QAAA,EAED1E,WAAW,gBACVX,OAAA,CAAAE,SAAA;YAAAmF,QAAA,gBACErF,OAAA;cAAM6E,KAAK,EAAE;gBACXK,KAAK,EAAE,MAAM;gBACbF,MAAM,EAAE,MAAM;gBACdyB,MAAM,EAAE,uBAAuB;gBAC/BgC,SAAS,EAAE,iBAAiB;gBAC5BjD,YAAY,EAAE,KAAK;gBACnBkD,SAAS,EAAE;cACb;YAAE;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,qCAEZ;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGLtF,YAAY,KAAKwB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEzB,YAAY,CAAC,IAAI,IAAI,CAAC,iBAC7Fb,OAAA;MAAK6E,KAAK,EAAE;QACVS,IAAI,EAAE,GAAG;QACTC,UAAU,EAAE,OAAO;QACnBN,OAAO,EAAE,MAAM;QACfO,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE,4BAA4B;QACvCL,QAAQ,EAAE,QAAQ;QAClBN,OAAO,EAAE,MAAM;QACf4B,aAAa,EAAE,QAAQ;QACvBiC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE;MACZ,CAAE;MAAAvD,QAAA,gBACArF,OAAA;QAAK6E,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACf6B,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,eAAe;UAC/BhB,YAAY,EAAE,MAAM;UACpBiD,aAAa,EAAE,MAAM;UACrBC,YAAY,EAAE;QAChB,CAAE;QAAAzD,QAAA,gBACArF,OAAA;UAAI6E,KAAK,EAAE;YAAE4C,MAAM,EAAE,CAAC;YAAE3B,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE,MAAM;YAAEF,UAAU,EAAE;UAAM,CAAE;UAAAR,QAAA,EAAC;QAE9E;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnG,OAAA;UAAK6E,KAAK,EAAE;YACVU,UAAU,EAAExE,gBAAgB,CAACE,OAAO,KAAK,GAAG,GAAG,SAAS,GAAG,SAAS;YACpE6E,KAAK,EAAE/E,gBAAgB,CAACE,OAAO,KAAK,GAAG,GAAG,SAAS,GAAG,MAAM;YAC5DgE,OAAO,EAAE,UAAU;YACnBO,YAAY,EAAE,MAAM;YACpBO,QAAQ,EAAE,MAAM;YAChBF,UAAU,EAAE,KAAK;YACjBY,MAAM,EAAE1F,gBAAgB,CAACE,OAAO,KAAK,GAAG,GAAG,mBAAmB,GAAG;UACnE,CAAE;UAAAoE,QAAA,GACCtE,gBAAgB,CAACE,OAAO,EAAC,GAC5B;QAAA;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnG,OAAA;QAAK6E,KAAK,EAAE;UACVU,UAAU,EAAE,SAAS;UACrBkB,MAAM,EAAE,mBAAmB;UAC3BjB,YAAY,EAAE,KAAK;UACnBP,OAAO,EAAE,MAAM;UACfW,YAAY,EAAE;QAChB,CAAE;QAAAP,QAAA,gBACArF,OAAA;UAAK6E,KAAK,EAAE;YAAEkB,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE,MAAM;YAAEF,YAAY,EAAE;UAAM,CAAE;UAAAP,QAAA,EAAC;QAEtE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNnG,OAAA;UAAK6E,KAAK,EAAE;YAAEkB,QAAQ,EAAE,MAAM;YAAEF,UAAU,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAT,QAAA,EAChEtE,gBAAgB,CAACG;QAAW;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnG,OAAA;QAAK6E,KAAK,EAAE;UAAEe,YAAY,EAAE;QAAO,CAAE;QAAAP,QAAA,gBACnCrF,OAAA;UAAK6E,KAAK,EAAE;YAAEkB,QAAQ,EAAE,MAAM;YAAEF,UAAU,EAAE,KAAK;YAAEC,KAAK,EAAE,MAAM;YAAEF,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,EAAC;QAE1F;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNnG,OAAA;UAAK6E,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAM,CAAE;UAAAM,QAAA,EACzCtE,gBAAgB,CAACI,KAAK,CAACmD,GAAG,CAAC,CAACL,IAAI,EAAEO,KAAK,kBACtCxE,OAAA;YAAiB6E,KAAK,EAAE;cACtBS,IAAI,EAAE,CAAC;cACPL,OAAO,EAAE,UAAU;cACnBM,UAAU,EAAEtB,IAAI,CAAC5C,MAAM,KAAK,WAAW,GAAG,SAAS,GACxC4C,IAAI,CAAC5C,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG,SAAS;cAC/DoF,MAAM,EAAE,aAAaxC,IAAI,CAAC5C,MAAM,KAAK,WAAW,GAAG,SAAS,GACxC4C,IAAI,CAAC5C,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG,SAAS,EAAE;cAC1EmE,YAAY,EAAE,KAAK;cACnBgC,SAAS,EAAE,QAAQ;cACnBzB,QAAQ,EAAE,MAAM;cAChBL,UAAU,EAAE;YACd,CAAE;YAAAL,QAAA,gBACArF,OAAA;cAAK6E,KAAK,EAAE;gBACVkB,QAAQ,EAAE,MAAM;gBAChBH,YAAY,EAAE,KAAK;gBACnBE,KAAK,EAAE7B,IAAI,CAAC5C,MAAM,KAAK,WAAW,GAAG,SAAS,GACvC4C,IAAI,CAAC5C,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG;cACpD,CAAE;cAAAgE,QAAA,EACCpB,IAAI,CAAC5C,MAAM,KAAK,WAAW,GAAG,GAAG,GACjC4C,IAAI,CAAC5C,MAAM,KAAK,YAAY,GAAG,GAAG,GAAG;YAAG;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNnG,OAAA;cAAK6E,KAAK,EAAE;gBAAEgB,UAAU,EAAE,KAAK;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAV,QAAA,EAChEpB,IAAI,CAAC7C;YAAI;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACNnG,OAAA;cAAK6E,KAAK,EAAE;gBACVK,KAAK,EAAE,MAAM;gBACbF,MAAM,EAAE,KAAK;gBACbO,UAAU,EAAE,SAAS;gBACrBC,YAAY,EAAE,KAAK;gBACnBJ,QAAQ,EAAE,QAAQ;gBAClB0C,SAAS,EAAE;cACb,CAAE;cAAAzC,QAAA,eACArF,OAAA;gBAAK6E,KAAK,EAAE;kBACVG,MAAM,EAAE,MAAM;kBACdE,KAAK,EAAE,GAAGjB,IAAI,CAAC3C,QAAQ,GAAG;kBAC1BiE,UAAU,EAAEtB,IAAI,CAAC5C,MAAM,KAAK,WAAW,GAAG,SAAS,GACxC4C,IAAI,CAAC5C,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG,SAAS;kBAC/DqE,UAAU,EAAE,iBAAiB;kBAC7BF,YAAY,EAAE;gBAChB;cAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA,GAxCE3B,KAAK;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyCV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnG,OAAA;QAAK6E,KAAK,EAAE;UAAES,IAAI,EAAE,CAAC;UAAER,OAAO,EAAE,MAAM;UAAE4B,aAAa,EAAE;QAAS,CAAE;QAAArB,QAAA,gBAChErF,OAAA;UAAK6E,KAAK,EAAE;YACVC,OAAO,EAAE,MAAM;YACf6B,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,eAAe;YAC/BhB,YAAY,EAAE;UAChB,CAAE;UAAAP,QAAA,gBACArF,OAAA;YAAK6E,KAAK,EAAE;cAAEkB,QAAQ,EAAE,MAAM;cAAEF,UAAU,EAAE,KAAK;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAT,QAAA,EAAC;UAEpE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNnG,OAAA;YAAK6E,KAAK,EAAE;cACVkB,QAAQ,EAAE,MAAM;cAChBD,KAAK,EAAE,MAAM;cACbP,UAAU,EAAE,SAAS;cACrBN,OAAO,EAAE,SAAS;cAClBO,YAAY,EAAE;YAChB,CAAE;YAAAH,QAAA,GACC9D,YAAY,CAAC6C,MAAM,EAAC,qBACvB;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnG,OAAA;UACE+I,GAAG,EAAEtH,eAAgB;UACrBoD,KAAK,EAAE;YACLS,IAAI,EAAE,CAAC;YACPC,UAAU,EAAE,SAAS;YACrBC,YAAY,EAAE,KAAK;YACnBP,OAAO,EAAE,MAAM;YACfG,QAAQ,EAAE,MAAM;YAChB+C,UAAU,EAAE,4CAA4C;YACxDpC,QAAQ,EAAE,MAAM;YAChBqC,UAAU,EAAE,KAAK;YACjBY,SAAS,EAAE;UACb,CAAE;UAAA3D,QAAA,EACD9D,YAAY,CAAC6C,MAAM,KAAK,CAAC,gBACxBpE,OAAA;YAAK6E,KAAK,EAAE;cAAEiB,KAAK,EAAE,MAAM;cAAE0B,SAAS,EAAE,QAAQ;cAAEvC,OAAO,EAAE;YAAO,CAAE;YAAAI,QAAA,EAAC;UAErE;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAEN5E,YAAY,CAAC+C,GAAG,CAAEhC,GAAG,iBACnBtC,OAAA;YAAkB6E,KAAK,EAAE;cACvBe,YAAY,EAAE,KAAK;cACnBX,OAAO,EAAE,OAAO;cAChB6D,YAAY,EAAE;YAChB,CAAE;YAAAzD,QAAA,eACArF,OAAA;cAAK6E,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACf6B,UAAU,EAAE,YAAY;gBACxB5B,GAAG,EAAE;cACP,CAAE;cAAAM,QAAA,gBACArF,OAAA;gBAAM6E,KAAK,EAAE;kBACXiB,KAAK,EAAE,MAAM;kBACbC,QAAQ,EAAE,MAAM;kBAChB4C,QAAQ,EAAE,MAAM;kBAChB9C,UAAU,EAAE;gBACd,CAAE;gBAAAR,QAAA,EACC/C,GAAG,CAACmB;cAAS;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACPnG,OAAA;gBAAM6E,KAAK,EAAE;kBACXiB,KAAK,EAAExD,GAAG,CAACkB,IAAI,KAAK,OAAO,GAAG,SAAS,GAChClB,GAAG,CAACkB,IAAI,KAAK,MAAM,GAAG,SAAS,GAC/BlB,GAAG,CAACkB,IAAI,KAAK,SAAS,GAAG,SAAS,GAClClB,GAAG,CAACkB,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;kBACnDqC,UAAU,EAAEvD,GAAG,CAACkB,IAAI,KAAK,MAAM,IAAIlB,GAAG,CAACkB,IAAI,KAAK,OAAO,IAAIlB,GAAG,CAACkB,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG;gBAC9F,CAAE;gBAAA6B,QAAA,EACC/C,GAAG,CAACgB;cAAO;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC,GA3BE7D,GAAG,CAACuB,EAAE;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4BX,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDnG,OAAA;MAAAqF,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;IAAO;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC7F,EAAA,CAhlBIH,YAAY;AAAA8I,EAAA,GAAZ9I,YAAY;AAklBlB,eAAeA,YAAY;AAAC,IAAA8I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}