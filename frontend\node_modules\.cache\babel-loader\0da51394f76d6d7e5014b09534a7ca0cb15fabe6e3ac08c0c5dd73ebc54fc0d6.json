{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\components\\\\SimpleUpload.js\",\n  _s = $RefreshSig$();\n/**\n * 简化的上传组件\n */\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SimpleUpload = ({\n  onUploadSuccess,\n  onUploadError\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const [additionalInfo, setAdditionalInfo] = useState({\n    projectName: '',\n    testType: 'functional',\n    priority: 'medium',\n    expectedElements: '',\n    specialRequirements: ''\n  });\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n    setIsUploading(true);\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n      const response = await fetch('http://localhost:8001/api/v1/upload', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n      const result = await response.json();\n      onUploadSuccess(result);\n\n      // 重置表单\n      setSelectedFile(null);\n      setDescription('');\n      event.target.reset();\n    } catch (error) {\n      console.error('Upload error:', error);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  const handleFileManagementDemo = async () => {\n    setIsUploading(true);\n    try {\n      const formData = new FormData();\n      formData.append('description', '文件管理界面功能分析 - 基于提供的截图进行专项分析');\n      const response = await fetch('http://localhost:8001/api/v1/analyze/file-management', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n      const result = await response.json();\n      onUploadSuccess(result);\n    } catch (error) {\n      console.error('File management demo error:', error);\n      onUploadError(error.message || '文件管理界面分析失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'white',\n      padding: '24px',\n      borderRadius: '12px',\n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n      marginBottom: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: 'block',\n            marginBottom: '12px',\n            fontWeight: '600',\n            color: '#333',\n            fontSize: '16px'\n          },\n          children: \"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"file-upload-area\",\n          onClick: () => document.getElementById('file-input').click(),\n          style: {\n            height: '240px',\n            // 调高3倍（原来约80px）\n            border: '2px dashed #667eea',\n            borderRadius: '12px',\n            background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            transition: 'all 0.3s ease',\n            position: 'relative',\n            overflow: 'hidden'\n          },\n          onDragOver: e => {\n            e.preventDefault();\n            e.currentTarget.style.borderColor = '#4f46e5';\n            e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';\n          },\n          onDragLeave: e => {\n            e.currentTarget.style.borderColor = '#667eea';\n            e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';\n          },\n          onDrop: e => {\n            e.preventDefault();\n            const files = e.dataTransfer.files;\n            if (files.length > 0) {\n              setSelectedFile(files[0]);\n            }\n            e.currentTarget.style.borderColor = '#667eea';\n            e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"file-input\",\n            type: \"file\",\n            accept: \"image/*\",\n            onChange: handleFileSelect,\n            style: {\n              display: 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), selectedFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '16px',\n                color: '#667eea'\n              },\n              children: \"\\u2705\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: '0 0 8px 0',\n                color: '#333',\n                fontSize: '18px'\n              },\n              children: \"\\u6587\\u4EF6\\u5DF2\\u9009\\u62E9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0 0 8px 0',\n                color: '#666',\n                fontSize: '14px',\n                fontWeight: '500'\n              },\n              children: selectedFile.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0',\n                color: '#999',\n                fontSize: '12px'\n              },\n              children: [\"\\u5927\\u5C0F: \", (selectedFile.size / 1024 / 1024).toFixed(2), \" MB\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: e => {\n                e.stopPropagation();\n                setSelectedFile(null);\n                document.getElementById('file-input').value = '';\n              },\n              style: {\n                marginTop: '12px',\n                background: 'rgba(239, 68, 68, 0.1)',\n                color: '#ef4444',\n                border: '1px solid rgba(239, 68, 68, 0.3)',\n                padding: '6px 12px',\n                borderRadius: '6px',\n                fontSize: '12px',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              },\n              onMouseOver: e => {\n                e.target.style.background = 'rgba(239, 68, 68, 0.2)';\n              },\n              onMouseOut: e => {\n                e.target.style.background = 'rgba(239, 68, 68, 0.1)';\n              },\n              children: \"\\u91CD\\u65B0\\u9009\\u62E9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '16px',\n                color: '#667eea'\n              },\n              children: \"\\uD83D\\uDCE4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: '0 0 8px 0',\n                color: '#333',\n                fontSize: '18px'\n              },\n              children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u4E0A\\u4F20\\u56FE\\u7247\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0 0 4px 0',\n                color: '#666',\n                fontSize: '14px'\n              },\n              children: \"\\u652F\\u6301 PNG\\u3001JPG\\u3001JPEG\\u3001GIF \\u683C\\u5F0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0',\n                color: '#999',\n                fontSize: '12px'\n              },\n              children: \"\\u6587\\u4EF6\\u5927\\u5C0F\\u4E0D\\u8D85\\u8FC7 10MB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '16px',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                color: 'white',\n                padding: '8px 20px',\n                borderRadius: '20px',\n                fontSize: '14px',\n                fontWeight: '500',\n                display: 'inline-block'\n              },\n              children: \"\\u9009\\u62E9\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"description\",\n          style: {\n            display: 'block',\n            marginBottom: '8px',\n            fontWeight: '600',\n            color: '#333',\n            fontSize: '16px'\n          },\n          children: \"\\uD83C\\uDFAF \\u754C\\u9762\\u529F\\u80FD\\u63CF\\u8FF0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          id: \"description\",\n          value: description,\n          onChange: e => setDescription(e.target.value),\n          placeholder: \"\\u8BF7\\u8BE6\\u7EC6\\u63CF\\u8FF0\\u8FD9\\u4E2A\\u754C\\u9762\\u7684\\u4E3B\\u8981\\u529F\\u80FD\\uFF0C\\u4F8B\\u5982\\uFF1A\\u8FD9\\u662F\\u4E00\\u4E2A\\u767B\\u5F55\\u9875\\u9762\\uFF0C\\u5305\\u542B\\u7528\\u6237\\u540D\\u5BC6\\u7801\\u8F93\\u5165\\u6846\\u548C\\u767B\\u5F55\\u6309\\u94AE...\",\n          rows: 4,\n          style: {\n            width: '100%',\n            padding: '12px',\n            border: '2px solid #e5e7eb',\n            borderRadius: '8px',\n            resize: 'vertical',\n            fontFamily: 'inherit',\n            fontSize: '14px',\n            lineHeight: '1.5',\n            transition: 'border-color 0.2s ease',\n            background: '#fafafa'\n          },\n          onFocus: e => {\n            e.target.style.borderColor = '#667eea';\n            e.target.style.background = '#ffffff';\n          },\n          onBlur: e => {\n            e.target.style.borderColor = '#e5e7eb';\n            e.target.style.background = '#fafafa';\n          },\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'right',\n            fontSize: '12px',\n            color: '#666',\n            marginTop: '4px'\n          },\n          children: [description.length, \"/500\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: 'block',\n            marginBottom: '8px',\n            fontWeight: '600',\n            color: '#333',\n            fontSize: '16px'\n          },\n          children: \"\\uD83D\\uDCCB \\u9879\\u76EE\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: '16px',\n            marginBottom: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '4px',\n                fontSize: '14px',\n                color: '#555'\n              },\n              children: \"\\u9879\\u76EE\\u540D\\u79F0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: additionalInfo.projectName,\n              onChange: e => setAdditionalInfo(prev => ({\n                ...prev,\n                projectName: e.target.value\n              })),\n              placeholder: \"\\u8F93\\u5165\\u9879\\u76EE\\u540D\\u79F0\",\n              style: {\n                width: '100%',\n                padding: '10px 12px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '6px',\n                fontSize: '14px',\n                transition: 'border-color 0.2s ease',\n                background: '#fafafa'\n              },\n              onFocus: e => {\n                e.target.style.borderColor = '#667eea';\n                e.target.style.background = '#ffffff';\n              },\n              onBlur: e => {\n                e.target.style.borderColor = '#e5e7eb';\n                e.target.style.background = '#fafafa';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '4px',\n                fontSize: '14px',\n                color: '#555'\n              },\n              children: \"\\u6D4B\\u8BD5\\u7C7B\\u578B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: additionalInfo.testType,\n              onChange: e => setAdditionalInfo(prev => ({\n                ...prev,\n                testType: e.target.value\n              })),\n              style: {\n                width: '100%',\n                padding: '10px 12px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '6px',\n                fontSize: '14px',\n                background: '#fafafa',\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"functional\",\n                children: \"\\u529F\\u80FD\\u6D4B\\u8BD5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"ui\",\n                children: \"UI\\u6D4B\\u8BD5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"integration\",\n                children: \"\\u96C6\\u6210\\u6D4B\\u8BD5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"regression\",\n                children: \"\\u56DE\\u5F52\\u6D4B\\u8BD5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: '16px',\n            marginBottom: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '4px',\n                fontSize: '14px',\n                color: '#555'\n              },\n              children: \"\\u4F18\\u5148\\u7EA7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: additionalInfo.priority,\n              onChange: e => setAdditionalInfo(prev => ({\n                ...prev,\n                priority: e.target.value\n              })),\n              style: {\n                width: '100%',\n                padding: '10px 12px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '6px',\n                fontSize: '14px',\n                background: '#fafafa',\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"low\",\n                children: \"\\u4F4E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"medium\",\n                children: \"\\u4E2D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"high\",\n                children: \"\\u9AD8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"critical\",\n                children: \"\\u7D27\\u6025\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '4px',\n                fontSize: '14px',\n                color: '#555'\n              },\n              children: \"\\u9884\\u671F\\u5143\\u7D20\\u6570\\u91CF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: additionalInfo.expectedElements,\n              onChange: e => setAdditionalInfo(prev => ({\n                ...prev,\n                expectedElements: e.target.value\n              })),\n              placeholder: \"\\u4F8B\\u5982\\uFF1A5-10\\u4E2A\\u6309\\u94AE\\uFF0C2\\u4E2A\\u8F93\\u5165\\u6846\",\n              style: {\n                width: '100%',\n                padding: '10px 12px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '6px',\n                fontSize: '14px',\n                transition: 'border-color 0.2s ease',\n                background: '#fafafa'\n              },\n              onFocus: e => {\n                e.target.style.borderColor = '#667eea';\n                e.target.style.background = '#ffffff';\n              },\n              onBlur: e => {\n                e.target.style.borderColor = '#e5e7eb';\n                e.target.style.background = '#fafafa';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: 'block',\n            marginBottom: '8px',\n            fontSize: '14px',\n            color: '#555'\n          },\n          children: \"\\u7279\\u6B8A\\u8981\\u6C42\\u6216\\u6CE8\\u610F\\u4E8B\\u9879\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: additionalInfo.specialRequirements,\n          onChange: e => setAdditionalInfo(prev => ({\n            ...prev,\n            specialRequirements: e.target.value\n          })),\n          placeholder: \"\\u4F8B\\u5982\\uFF1A\\u9700\\u8981\\u7279\\u522B\\u5173\\u6CE8\\u54CD\\u5E94\\u5F0F\\u8BBE\\u8BA1\\u3001\\u65E0\\u969C\\u788D\\u8BBF\\u95EE\\u3001\\u7279\\u5B9A\\u6D4F\\u89C8\\u5668\\u517C\\u5BB9\\u6027\\u7B49...\",\n          rows: 3,\n          style: {\n            width: '100%',\n            padding: '12px',\n            border: '2px solid #e5e7eb',\n            borderRadius: '8px',\n            resize: 'vertical',\n            fontFamily: 'inherit',\n            fontSize: '14px',\n            lineHeight: '1.5',\n            transition: 'border-color 0.2s ease',\n            background: '#fafafa'\n          },\n          onFocus: e => {\n            e.target.style.borderColor = '#667eea';\n            e.target.style.background = '#ffffff';\n          },\n          onBlur: e => {\n            e.target.style.borderColor = '#e5e7eb';\n            e.target.style.background = '#fafafa';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: isUploading || !selectedFile || !description.trim(),\n        style: {\n          background: isUploading ? '#6c757d' : '#007bff',\n          color: 'white',\n          border: 'none',\n          padding: '12px 24px',\n          borderRadius: '6px',\n          fontSize: '16px',\n          fontWeight: '500',\n          cursor: isUploading ? 'not-allowed' : 'pointer',\n          transition: 'all 0.3s ease',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        },\n        children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: '16px',\n              height: '16px',\n              border: '2px solid transparent',\n              borderTop: '2px solid white',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 15\n          }, this), \"\\u5206\\u6790\\u4E2D...\"]\n        }, void 0, true) : '🚀 开始分析'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        margin: '24px 0',\n        color: '#666',\n        fontSize: '14px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          height: '1px',\n          background: '#e9ecef'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          padding: '0 16px'\n        },\n        children: \"\\u6216\\u8005\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          height: '1px',\n          background: '#e9ecef'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          margin: '0 0 12px 0',\n          color: '#333',\n          fontSize: '16px'\n        },\n        children: \"\\uD83D\\uDCC1 \\u4E13\\u9879\\u5206\\u6790\\u6F14\\u793A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: '0 0 16px 0',\n          color: '#666',\n          fontSize: '14px'\n        },\n        children: \"\\u57FA\\u4E8E\\u60A8\\u63D0\\u4F9B\\u7684\\u6587\\u4EF6\\u7BA1\\u7406\\u754C\\u9762\\u622A\\u56FE\\uFF0C\\u751F\\u6210\\u4E13\\u95E8\\u7684UI\\u81EA\\u52A8\\u5316\\u6D4B\\u8BD5\\u811A\\u672C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: handleFileManagementDemo,\n        disabled: isUploading,\n        style: {\n          background: isUploading ? '#6c757d' : '#28a745',\n          color: 'white',\n          border: 'none',\n          padding: '12px 32px',\n          borderRadius: '6px',\n          fontSize: '16px',\n          fontWeight: '500',\n          cursor: isUploading ? 'not-allowed' : 'pointer',\n          transition: 'all 0.3s ease',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px',\n          margin: '0 auto'\n        },\n        children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: '16px',\n              height: '16px',\n              border: '2px solid transparent',\n              borderTop: '2px solid white',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 15\n          }, this), \"\\u5206\\u6790\\u4E2D...\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: \"\\uD83D\\uDCCA \\u5206\\u6790\\u6587\\u4EF6\\u7BA1\\u7406\\u754C\\u9762\"\n        }, void 0, false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 459,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 506,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleUpload, \"H382Ql6lZpzQ2uMIQk286CVeERY=\");\n_c = SimpleUpload;\nexport default SimpleUpload;\nvar _c;\n$RefreshReg$(_c, \"SimpleUpload\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SimpleUpload", "onUploadSuccess", "onUploadError", "_s", "selectedFile", "setSelectedFile", "description", "setDescription", "isUploading", "setIsUploading", "additionalInfo", "setAdditionalInfo", "projectName", "testType", "priority", "expectedElements", "specialRequirements", "handleFileSelect", "event", "file", "target", "files", "handleSubmit", "preventDefault", "trim", "formData", "FormData", "append", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "status", "result", "reset", "error", "console", "message", "handleFileManagementDemo", "style", "background", "padding", "borderRadius", "boxShadow", "marginBottom", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "display", "fontWeight", "color", "fontSize", "className", "onClick", "document", "getElementById", "click", "height", "border", "flexDirection", "alignItems", "justifyContent", "cursor", "transition", "position", "overflow", "onDragOver", "e", "currentTarget", "borderColor", "onDragLeave", "onDrop", "dataTransfer", "length", "id", "type", "accept", "onChange", "textAlign", "margin", "name", "size", "toFixed", "stopPropagation", "value", "marginTop", "onMouseOver", "onMouseOut", "htmlFor", "placeholder", "rows", "width", "resize", "fontFamily", "lineHeight", "onFocus", "onBlur", "required", "gridTemplateColumns", "gap", "prev", "disabled", "borderTop", "animation", "flex", "jsx", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/components/SimpleUpload.js"], "sourcesContent": ["/**\n * 简化的上传组件\n */\nimport React, { useState } from 'react';\n\nconst SimpleUpload = ({ onUploadSuccess, onUploadError }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const [additionalInfo, setAdditionalInfo] = useState({\n    projectName: '',\n    testType: 'functional',\n    priority: 'medium',\n    expectedElements: '',\n    specialRequirements: ''\n  });\n\n  const handleFileSelect = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n\n  const handleSubmit = async (event) => {\n    event.preventDefault();\n\n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n\n    setIsUploading(true);\n\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n\n      const response = await fetch('http://localhost:8001/api/v1/upload', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      onUploadSuccess(result);\n\n      // 重置表单\n      setSelectedFile(null);\n      setDescription('');\n      event.target.reset();\n    } catch (error) {\n      console.error('Upload error:', error);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const handleFileManagementDemo = async () => {\n    setIsUploading(true);\n\n    try {\n      const formData = new FormData();\n      formData.append('description', '文件管理界面功能分析 - 基于提供的截图进行专项分析');\n\n      const response = await fetch('http://localhost:8001/api/v1/analyze/file-management', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      onUploadSuccess(result);\n\n    } catch (error) {\n      console.error('File management demo error:', error);\n      onUploadError(error.message || '文件管理界面分析失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  return (\n    <div style={{ \n      background: 'white', \n      padding: '24px', \n      borderRadius: '12px', \n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n      marginBottom: '24px'\n    }}>\n      <h3>📁 上传UI界面截图</h3>\n      <form onSubmit={handleSubmit}>\n        <div style={{ marginBottom: '24px' }}>\n          <label style={{ display: 'block', marginBottom: '12px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n            📁 上传UI界面截图\n          </label>\n          <div\n            className=\"file-upload-area\"\n            onClick={() => document.getElementById('file-input').click()}\n            style={{\n              height: '240px', // 调高3倍（原来约80px）\n              border: '2px dashed #667eea',\n              borderRadius: '12px',\n              background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.3s ease',\n              position: 'relative',\n              overflow: 'hidden'\n            }}\n            onDragOver={(e) => {\n              e.preventDefault();\n              e.currentTarget.style.borderColor = '#4f46e5';\n              e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';\n            }}\n            onDragLeave={(e) => {\n              e.currentTarget.style.borderColor = '#667eea';\n              e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';\n            }}\n            onDrop={(e) => {\n              e.preventDefault();\n              const files = e.dataTransfer.files;\n              if (files.length > 0) {\n                setSelectedFile(files[0]);\n              }\n              e.currentTarget.style.borderColor = '#667eea';\n              e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';\n            }}\n          >\n            <input\n              id=\"file-input\"\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={handleFileSelect}\n              style={{ display: 'none' }}\n            />\n\n            {selectedFile ? (\n              <div style={{ textAlign: 'center', padding: '20px' }}>\n                <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>✅</div>\n                <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>文件已选择</h3>\n                <p style={{ margin: '0 0 8px 0', color: '#666', fontSize: '14px', fontWeight: '500' }}>\n                  {selectedFile.name}\n                </p>\n                <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>\n                  大小: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB\n                </p>\n                <button\n                  type=\"button\"\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    setSelectedFile(null);\n                    document.getElementById('file-input').value = '';\n                  }}\n                  style={{\n                    marginTop: '12px',\n                    background: 'rgba(239, 68, 68, 0.1)',\n                    color: '#ef4444',\n                    border: '1px solid rgba(239, 68, 68, 0.3)',\n                    padding: '6px 12px',\n                    borderRadius: '6px',\n                    fontSize: '12px',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseOver={(e) => {\n                    e.target.style.background = 'rgba(239, 68, 68, 0.2)';\n                  }}\n                  onMouseOut={(e) => {\n                    e.target.style.background = 'rgba(239, 68, 68, 0.1)';\n                  }}\n                >\n                  重新选择\n                </button>\n              </div>\n            ) : (\n              <div style={{ textAlign: 'center', padding: '20px' }}>\n                <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>📤</div>\n                <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>点击或拖拽上传图片</h3>\n                <p style={{ margin: '0 0 4px 0', color: '#666', fontSize: '14px' }}>\n                  支持 PNG、JPG、JPEG、GIF 格式\n                </p>\n                <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>\n                  文件大小不超过 10MB\n                </p>\n                <div style={{\n                  marginTop: '16px',\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  color: 'white',\n                  padding: '8px 20px',\n                  borderRadius: '20px',\n                  fontSize: '14px',\n                  fontWeight: '500',\n                  display: 'inline-block'\n                }}>\n                  选择文件\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* 界面功能描述 */}\n        <div style={{ marginBottom: '20px' }}>\n          <label htmlFor=\"description\" style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n            🎯 界面功能描述\n          </label>\n          <textarea\n            id=\"description\"\n            value={description}\n            onChange={(e) => setDescription(e.target.value)}\n            placeholder=\"请详细描述这个界面的主要功能，例如：这是一个登录页面，包含用户名密码输入框和登录按钮...\"\n            rows={4}\n            style={{\n              width: '100%',\n              padding: '12px',\n              border: '2px solid #e5e7eb',\n              borderRadius: '8px',\n              resize: 'vertical',\n              fontFamily: 'inherit',\n              fontSize: '14px',\n              lineHeight: '1.5',\n              transition: 'border-color 0.2s ease',\n              background: '#fafafa'\n            }}\n            onFocus={(e) => {\n              e.target.style.borderColor = '#667eea';\n              e.target.style.background = '#ffffff';\n            }}\n            onBlur={(e) => {\n              e.target.style.borderColor = '#e5e7eb';\n              e.target.style.background = '#fafafa';\n            }}\n            required\n          />\n          <div style={{ textAlign: 'right', fontSize: '12px', color: '#666', marginTop: '4px' }}>\n            {description.length}/500\n          </div>\n        </div>\n\n        {/* 项目信息 */}\n        <div style={{ marginBottom: '20px' }}>\n          <label style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n            📋 项目信息\n          </label>\n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>\n            <div>\n              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', color: '#555' }}>\n                项目名称\n              </label>\n              <input\n                type=\"text\"\n                value={additionalInfo.projectName}\n                onChange={(e) => setAdditionalInfo(prev => ({ ...prev, projectName: e.target.value }))}\n                placeholder=\"输入项目名称\"\n                style={{\n                  width: '100%',\n                  padding: '10px 12px',\n                  border: '2px solid #e5e7eb',\n                  borderRadius: '6px',\n                  fontSize: '14px',\n                  transition: 'border-color 0.2s ease',\n                  background: '#fafafa'\n                }}\n                onFocus={(e) => {\n                  e.target.style.borderColor = '#667eea';\n                  e.target.style.background = '#ffffff';\n                }}\n                onBlur={(e) => {\n                  e.target.style.borderColor = '#e5e7eb';\n                  e.target.style.background = '#fafafa';\n                }}\n              />\n            </div>\n            <div>\n              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', color: '#555' }}>\n                测试类型\n              </label>\n              <select\n                value={additionalInfo.testType}\n                onChange={(e) => setAdditionalInfo(prev => ({ ...prev, testType: e.target.value }))}\n                style={{\n                  width: '100%',\n                  padding: '10px 12px',\n                  border: '2px solid #e5e7eb',\n                  borderRadius: '6px',\n                  fontSize: '14px',\n                  background: '#fafafa',\n                  cursor: 'pointer'\n                }}\n              >\n                <option value=\"functional\">功能测试</option>\n                <option value=\"ui\">UI测试</option>\n                <option value=\"integration\">集成测试</option>\n                <option value=\"regression\">回归测试</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* 测试优先级和预期元素 */}\n        <div style={{ marginBottom: '20px' }}>\n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>\n            <div>\n              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', color: '#555' }}>\n                优先级\n              </label>\n              <select\n                value={additionalInfo.priority}\n                onChange={(e) => setAdditionalInfo(prev => ({ ...prev, priority: e.target.value }))}\n                style={{\n                  width: '100%',\n                  padding: '10px 12px',\n                  border: '2px solid #e5e7eb',\n                  borderRadius: '6px',\n                  fontSize: '14px',\n                  background: '#fafafa',\n                  cursor: 'pointer'\n                }}\n              >\n                <option value=\"low\">低</option>\n                <option value=\"medium\">中</option>\n                <option value=\"high\">高</option>\n                <option value=\"critical\">紧急</option>\n              </select>\n            </div>\n            <div>\n              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', color: '#555' }}>\n                预期元素数量\n              </label>\n              <input\n                type=\"text\"\n                value={additionalInfo.expectedElements}\n                onChange={(e) => setAdditionalInfo(prev => ({ ...prev, expectedElements: e.target.value }))}\n                placeholder=\"例如：5-10个按钮，2个输入框\"\n                style={{\n                  width: '100%',\n                  padding: '10px 12px',\n                  border: '2px solid #e5e7eb',\n                  borderRadius: '6px',\n                  fontSize: '14px',\n                  transition: 'border-color 0.2s ease',\n                  background: '#fafafa'\n                }}\n                onFocus={(e) => {\n                  e.target.style.borderColor = '#667eea';\n                  e.target.style.background = '#ffffff';\n                }}\n                onBlur={(e) => {\n                  e.target.style.borderColor = '#e5e7eb';\n                  e.target.style.background = '#fafafa';\n                }}\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* 特殊要求 */}\n        <div style={{ marginBottom: '24px' }}>\n          <label style={{ display: 'block', marginBottom: '8px', fontSize: '14px', color: '#555' }}>\n            特殊要求或注意事项\n          </label>\n          <textarea\n            value={additionalInfo.specialRequirements}\n            onChange={(e) => setAdditionalInfo(prev => ({ ...prev, specialRequirements: e.target.value }))}\n            placeholder=\"例如：需要特别关注响应式设计、无障碍访问、特定浏览器兼容性等...\"\n            rows={3}\n            style={{\n              width: '100%',\n              padding: '12px',\n              border: '2px solid #e5e7eb',\n              borderRadius: '8px',\n              resize: 'vertical',\n              fontFamily: 'inherit',\n              fontSize: '14px',\n              lineHeight: '1.5',\n              transition: 'border-color 0.2s ease',\n              background: '#fafafa'\n            }}\n            onFocus={(e) => {\n              e.target.style.borderColor = '#667eea';\n              e.target.style.background = '#ffffff';\n            }}\n            onBlur={(e) => {\n              e.target.style.borderColor = '#e5e7eb';\n              e.target.style.background = '#fafafa';\n            }}\n          />\n        </div>\n\n        <button\n          type=\"submit\"\n          disabled={isUploading || !selectedFile || !description.trim()}\n          style={{\n            background: isUploading ? '#6c757d' : '#007bff',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            fontSize: '16px',\n            fontWeight: '500',\n            cursor: isUploading ? 'not-allowed' : 'pointer',\n            transition: 'all 0.3s ease',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          }}\n        >\n          {isUploading ? (\n            <>\n              <span style={{\n                width: '16px',\n                height: '16px',\n                border: '2px solid transparent',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }}></span>\n              分析中...\n            </>\n          ) : (\n            '🚀 开始分析'\n          )}\n        </button>\n      </form>\n\n      {/* 分隔线 */}\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        margin: '24px 0',\n        color: '#666',\n        fontSize: '14px'\n      }}>\n        <div style={{ flex: 1, height: '1px', background: '#e9ecef' }}></div>\n        <span style={{ padding: '0 16px' }}>或者</span>\n        <div style={{ flex: 1, height: '1px', background: '#e9ecef' }}></div>\n      </div>\n\n      {/* 文件管理界面演示按钮 */}\n      <div style={{ textAlign: 'center' }}>\n        <h4 style={{ margin: '0 0 12px 0', color: '#333', fontSize: '16px' }}>\n          📁 专项分析演示\n        </h4>\n        <p style={{ margin: '0 0 16px 0', color: '#666', fontSize: '14px' }}>\n          基于您提供的文件管理界面截图，生成专门的UI自动化测试脚本\n        </p>\n        <button\n          type=\"button\"\n          onClick={handleFileManagementDemo}\n          disabled={isUploading}\n          style={{\n            background: isUploading ? '#6c757d' : '#28a745',\n            color: 'white',\n            border: 'none',\n            padding: '12px 32px',\n            borderRadius: '6px',\n            fontSize: '16px',\n            fontWeight: '500',\n            cursor: isUploading ? 'not-allowed' : 'pointer',\n            transition: 'all 0.3s ease',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px',\n            margin: '0 auto'\n          }}\n        >\n          {isUploading ? (\n            <>\n              <span style={{\n                width: '16px',\n                height: '16px',\n                border: '2px solid transparent',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }}></span>\n              分析中...\n            </>\n          ) : (\n            <>\n              📊 分析文件管理界面\n            </>\n          )}\n        </button>\n      </div>\n\n      <style jsx>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default SimpleUpload;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,YAAY,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC;IACnDiB,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,YAAY;IACtBC,QAAQ,EAAE,QAAQ;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,mBAAmB,EAAE;EACvB,CAAC,CAAC;EAEF,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACRd,eAAe,CAACc,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOJ,KAAK,IAAK;IACpCA,KAAK,CAACK,cAAc,CAAC,CAAC;IAEtB,IAAI,CAACnB,YAAY,EAAE;MACjBF,aAAa,CAAC,SAAS,CAAC;MACxB;IACF;IAEA,IAAI,CAACI,WAAW,CAACkB,IAAI,CAAC,CAAC,EAAE;MACvBtB,aAAa,CAAC,WAAW,CAAC;MAC1B;IACF;IAEAO,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,MAAMgB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEvB,YAAY,CAAC;MAC3CqB,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAErB,WAAW,CAACkB,IAAI,CAAC,CAAC,CAAC;MAElD,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,qCAAqC,EAAE;QAClEC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEN;MACR,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,uBAAuBR,QAAQ,CAACS,MAAM,EAAE,CAAC;MAC/E;MAEA,MAAMC,MAAM,GAAG,MAAMV,QAAQ,CAACM,IAAI,CAAC,CAAC;MACpCjC,eAAe,CAACqC,MAAM,CAAC;;MAEvB;MACAjC,eAAe,CAAC,IAAI,CAAC;MACrBE,cAAc,CAAC,EAAE,CAAC;MAClBW,KAAK,CAACE,MAAM,CAACmB,KAAK,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCtC,aAAa,CAACsC,KAAK,CAACE,OAAO,IAAI,UAAU,CAAC;IAC5C,CAAC,SAAS;MACRjC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMkC,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3ClC,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,MAAMgB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE,4BAA4B,CAAC;MAE5D,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,sDAAsD,EAAE;QACnFC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEN;MACR,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,uBAAuBR,QAAQ,CAACS,MAAM,EAAE,CAAC;MAC/E;MAEA,MAAMC,MAAM,GAAG,MAAMV,QAAQ,CAACM,IAAI,CAAC,CAAC;MACpCjC,eAAe,CAACqC,MAAM,CAAC;IAEzB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDtC,aAAa,CAACsC,KAAK,CAACE,OAAO,IAAI,gBAAgB,CAAC;IAClD,CAAC,SAAS;MACRjC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,oBACEZ,OAAA;IAAK+C,KAAK,EAAE;MACVC,UAAU,EAAE,OAAO;MACnBC,OAAO,EAAE,MAAM;MACfC,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,4BAA4B;MACvCC,YAAY,EAAE;IAChB,CAAE;IAAAC,QAAA,gBACArD,OAAA;MAAAqD,QAAA,EAAI;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpBzD,OAAA;MAAM0D,QAAQ,EAAEjC,YAAa;MAAA4B,QAAA,gBAC3BrD,OAAA;QAAK+C,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnCrD,OAAA;UAAO+C,KAAK,EAAE;YAAEY,OAAO,EAAE,OAAO;YAAEP,YAAY,EAAE,MAAM;YAAEQ,UAAU,EAAE,KAAK;YAAEC,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE9G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRzD,OAAA;UACE+D,SAAS,EAAC,kBAAkB;UAC5BC,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAACC,KAAK,CAAC,CAAE;UAC7DpB,KAAK,EAAE;YACLqB,MAAM,EAAE,OAAO;YAAE;YACjBC,MAAM,EAAE,oBAAoB;YAC5BnB,YAAY,EAAE,MAAM;YACpBF,UAAU,EAAEzC,YAAY,GAAG,mDAAmD,GAAG,mDAAmD;YACpIoD,OAAO,EAAE,MAAM;YACfW,aAAa,EAAE,QAAQ;YACvBC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,MAAM,EAAE,SAAS;YACjBC,UAAU,EAAE,eAAe;YAC3BC,QAAQ,EAAE,UAAU;YACpBC,QAAQ,EAAE;UACZ,CAAE;UACFC,UAAU,EAAGC,CAAC,IAAK;YACjBA,CAAC,CAACpD,cAAc,CAAC,CAAC;YAClBoD,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACiC,WAAW,GAAG,SAAS;YAC7CF,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACC,UAAU,GAAG,mDAAmD;UACxF,CAAE;UACFiC,WAAW,EAAGH,CAAC,IAAK;YAClBA,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACiC,WAAW,GAAG,SAAS;YAC7CF,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACC,UAAU,GAAGzC,YAAY,GAAG,mDAAmD,GAAG,mDAAmD;UAC7J,CAAE;UACF2E,MAAM,EAAGJ,CAAC,IAAK;YACbA,CAAC,CAACpD,cAAc,CAAC,CAAC;YAClB,MAAMF,KAAK,GAAGsD,CAAC,CAACK,YAAY,CAAC3D,KAAK;YAClC,IAAIA,KAAK,CAAC4D,MAAM,GAAG,CAAC,EAAE;cACpB5E,eAAe,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B;YACAsD,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACiC,WAAW,GAAG,SAAS;YAC7CF,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACC,UAAU,GAAG,mDAAmD;UACxF,CAAE;UAAAK,QAAA,gBAEFrD,OAAA;YACEqF,EAAE,EAAC,YAAY;YACfC,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,SAAS;YAChBC,QAAQ,EAAEpE,gBAAiB;YAC3B2B,KAAK,EAAE;cAAEY,OAAO,EAAE;YAAO;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,EAEDlD,YAAY,gBACXP,OAAA;YAAK+C,KAAK,EAAE;cAAE0C,SAAS,EAAE,QAAQ;cAAExC,OAAO,EAAE;YAAO,CAAE;YAAAI,QAAA,gBACnDrD,OAAA;cAAK+C,KAAK,EAAE;gBAAEe,QAAQ,EAAE,MAAM;gBAAEV,YAAY,EAAE,MAAM;gBAAES,KAAK,EAAE;cAAU,CAAE;cAAAR,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjFzD,OAAA;cAAI+C,KAAK,EAAE;gBAAE2C,MAAM,EAAE,WAAW;gBAAE7B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/EzD,OAAA;cAAG+C,KAAK,EAAE;gBAAE2C,MAAM,EAAE,WAAW;gBAAE7B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE,MAAM;gBAAEF,UAAU,EAAE;cAAM,CAAE;cAAAP,QAAA,EACnF9C,YAAY,CAACoF;YAAI;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACJzD,OAAA;cAAG+C,KAAK,EAAE;gBAAE2C,MAAM,EAAE,GAAG;gBAAE7B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,GAAC,gBACtD,EAAC,CAAC9C,YAAY,CAACqF,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KACpD;YAAA;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJzD,OAAA;cACEsF,IAAI,EAAC,QAAQ;cACbtB,OAAO,EAAGc,CAAC,IAAK;gBACdA,CAAC,CAACgB,eAAe,CAAC,CAAC;gBACnBtF,eAAe,CAAC,IAAI,CAAC;gBACrByD,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAAC6B,KAAK,GAAG,EAAE;cAClD,CAAE;cACFhD,KAAK,EAAE;gBACLiD,SAAS,EAAE,MAAM;gBACjBhD,UAAU,EAAE,wBAAwB;gBACpCa,KAAK,EAAE,SAAS;gBAChBQ,MAAM,EAAE,kCAAkC;gBAC1CpB,OAAO,EAAE,UAAU;gBACnBC,YAAY,EAAE,KAAK;gBACnBY,QAAQ,EAAE,MAAM;gBAChBW,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE;cACd,CAAE;cACFuB,WAAW,EAAGnB,CAAC,IAAK;gBAClBA,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACC,UAAU,GAAG,wBAAwB;cACtD,CAAE;cACFkD,UAAU,EAAGpB,CAAC,IAAK;gBACjBA,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACC,UAAU,GAAG,wBAAwB;cACtD,CAAE;cAAAK,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAENzD,OAAA;YAAK+C,KAAK,EAAE;cAAE0C,SAAS,EAAE,QAAQ;cAAExC,OAAO,EAAE;YAAO,CAAE;YAAAI,QAAA,gBACnDrD,OAAA;cAAK+C,KAAK,EAAE;gBAAEe,QAAQ,EAAE,MAAM;gBAAEV,YAAY,EAAE,MAAM;gBAAES,KAAK,EAAE;cAAU,CAAE;cAAAR,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClFzD,OAAA;cAAI+C,KAAK,EAAE;gBAAE2C,MAAM,EAAE,WAAW;gBAAE7B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnFzD,OAAA;cAAG+C,KAAK,EAAE;gBAAE2C,MAAM,EAAE,WAAW;gBAAE7B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJzD,OAAA;cAAG+C,KAAK,EAAE;gBAAE2C,MAAM,EAAE,GAAG;gBAAE7B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAC;YAE5D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJzD,OAAA;cAAK+C,KAAK,EAAE;gBACViD,SAAS,EAAE,MAAM;gBACjBhD,UAAU,EAAE,mDAAmD;gBAC/Da,KAAK,EAAE,OAAO;gBACdZ,OAAO,EAAE,UAAU;gBACnBC,YAAY,EAAE,MAAM;gBACpBY,QAAQ,EAAE,MAAM;gBAChBF,UAAU,EAAE,KAAK;gBACjBD,OAAO,EAAE;cACX,CAAE;cAAAN,QAAA,EAAC;YAEH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzD,OAAA;QAAK+C,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnCrD,OAAA;UAAOmG,OAAO,EAAC,aAAa;UAACpD,KAAK,EAAE;YAAEY,OAAO,EAAE,OAAO;YAAEP,YAAY,EAAE,KAAK;YAAEQ,UAAU,EAAE,KAAK;YAAEC,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAEnI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRzD,OAAA;UACEqF,EAAE,EAAC,aAAa;UAChBU,KAAK,EAAEtF,WAAY;UACnB+E,QAAQ,EAAGV,CAAC,IAAKpE,cAAc,CAACoE,CAAC,CAACvD,MAAM,CAACwE,KAAK,CAAE;UAChDK,WAAW,EAAC,iQAA+C;UAC3DC,IAAI,EAAE,CAAE;UACRtD,KAAK,EAAE;YACLuD,KAAK,EAAE,MAAM;YACbrD,OAAO,EAAE,MAAM;YACfoB,MAAM,EAAE,mBAAmB;YAC3BnB,YAAY,EAAE,KAAK;YACnBqD,MAAM,EAAE,UAAU;YAClBC,UAAU,EAAE,SAAS;YACrB1C,QAAQ,EAAE,MAAM;YAChB2C,UAAU,EAAE,KAAK;YACjB/B,UAAU,EAAE,wBAAwB;YACpC1B,UAAU,EAAE;UACd,CAAE;UACF0D,OAAO,EAAG5B,CAAC,IAAK;YACdA,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACiC,WAAW,GAAG,SAAS;YACtCF,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACC,UAAU,GAAG,SAAS;UACvC,CAAE;UACF2D,MAAM,EAAG7B,CAAC,IAAK;YACbA,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACiC,WAAW,GAAG,SAAS;YACtCF,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACC,UAAU,GAAG,SAAS;UACvC,CAAE;UACF4D,QAAQ;QAAA;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFzD,OAAA;UAAK+C,KAAK,EAAE;YAAE0C,SAAS,EAAE,OAAO;YAAE3B,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE,MAAM;YAAEmC,SAAS,EAAE;UAAM,CAAE;UAAA3C,QAAA,GACnF5C,WAAW,CAAC2E,MAAM,EAAC,MACtB;QAAA;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzD,OAAA;QAAK+C,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnCrD,OAAA;UAAO+C,KAAK,EAAE;YAAEY,OAAO,EAAE,OAAO;YAAEP,YAAY,EAAE,KAAK;YAAEQ,UAAU,EAAE,KAAK;YAAEC,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE7G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRzD,OAAA;UAAK+C,KAAK,EAAE;YAAEY,OAAO,EAAE,MAAM;YAAEkD,mBAAmB,EAAE,SAAS;YAAEC,GAAG,EAAE,MAAM;YAAE1D,YAAY,EAAE;UAAO,CAAE;UAAAC,QAAA,gBACjGrD,OAAA;YAAAqD,QAAA,gBACErD,OAAA;cAAO+C,KAAK,EAAE;gBAAEY,OAAO,EAAE,OAAO;gBAAEP,YAAY,EAAE,KAAK;gBAAEU,QAAQ,EAAE,MAAM;gBAAED,KAAK,EAAE;cAAO,CAAE;cAAAR,QAAA,EAAC;YAE1F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzD,OAAA;cACEsF,IAAI,EAAC,MAAM;cACXS,KAAK,EAAElF,cAAc,CAACE,WAAY;cAClCyE,QAAQ,EAAGV,CAAC,IAAKhE,iBAAiB,CAACiG,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEhG,WAAW,EAAE+D,CAAC,CAACvD,MAAM,CAACwE;cAAM,CAAC,CAAC,CAAE;cACvFK,WAAW,EAAC,sCAAQ;cACpBrD,KAAK,EAAE;gBACLuD,KAAK,EAAE,MAAM;gBACbrD,OAAO,EAAE,WAAW;gBACpBoB,MAAM,EAAE,mBAAmB;gBAC3BnB,YAAY,EAAE,KAAK;gBACnBY,QAAQ,EAAE,MAAM;gBAChBY,UAAU,EAAE,wBAAwB;gBACpC1B,UAAU,EAAE;cACd,CAAE;cACF0D,OAAO,EAAG5B,CAAC,IAAK;gBACdA,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACiC,WAAW,GAAG,SAAS;gBACtCF,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACC,UAAU,GAAG,SAAS;cACvC,CAAE;cACF2D,MAAM,EAAG7B,CAAC,IAAK;gBACbA,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACiC,WAAW,GAAG,SAAS;gBACtCF,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACC,UAAU,GAAG,SAAS;cACvC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzD,OAAA;YAAAqD,QAAA,gBACErD,OAAA;cAAO+C,KAAK,EAAE;gBAAEY,OAAO,EAAE,OAAO;gBAAEP,YAAY,EAAE,KAAK;gBAAEU,QAAQ,EAAE,MAAM;gBAAED,KAAK,EAAE;cAAO,CAAE;cAAAR,QAAA,EAAC;YAE1F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzD,OAAA;cACE+F,KAAK,EAAElF,cAAc,CAACG,QAAS;cAC/BwE,QAAQ,EAAGV,CAAC,IAAKhE,iBAAiB,CAACiG,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE/F,QAAQ,EAAE8D,CAAC,CAACvD,MAAM,CAACwE;cAAM,CAAC,CAAC,CAAE;cACpFhD,KAAK,EAAE;gBACLuD,KAAK,EAAE,MAAM;gBACbrD,OAAO,EAAE,WAAW;gBACpBoB,MAAM,EAAE,mBAAmB;gBAC3BnB,YAAY,EAAE,KAAK;gBACnBY,QAAQ,EAAE,MAAM;gBAChBd,UAAU,EAAE,SAAS;gBACrByB,MAAM,EAAE;cACV,CAAE;cAAApB,QAAA,gBAEFrD,OAAA;gBAAQ+F,KAAK,EAAC,YAAY;gBAAA1C,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCzD,OAAA;gBAAQ+F,KAAK,EAAC,IAAI;gBAAA1C,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChCzD,OAAA;gBAAQ+F,KAAK,EAAC,aAAa;gBAAA1C,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzCzD,OAAA;gBAAQ+F,KAAK,EAAC,YAAY;gBAAA1C,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzD,OAAA;QAAK+C,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,eACnCrD,OAAA;UAAK+C,KAAK,EAAE;YAAEY,OAAO,EAAE,MAAM;YAAEkD,mBAAmB,EAAE,SAAS;YAAEC,GAAG,EAAE,MAAM;YAAE1D,YAAY,EAAE;UAAO,CAAE;UAAAC,QAAA,gBACjGrD,OAAA;YAAAqD,QAAA,gBACErD,OAAA;cAAO+C,KAAK,EAAE;gBAAEY,OAAO,EAAE,OAAO;gBAAEP,YAAY,EAAE,KAAK;gBAAEU,QAAQ,EAAE,MAAM;gBAAED,KAAK,EAAE;cAAO,CAAE;cAAAR,QAAA,EAAC;YAE1F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzD,OAAA;cACE+F,KAAK,EAAElF,cAAc,CAACI,QAAS;cAC/BuE,QAAQ,EAAGV,CAAC,IAAKhE,iBAAiB,CAACiG,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE9F,QAAQ,EAAE6D,CAAC,CAACvD,MAAM,CAACwE;cAAM,CAAC,CAAC,CAAE;cACpFhD,KAAK,EAAE;gBACLuD,KAAK,EAAE,MAAM;gBACbrD,OAAO,EAAE,WAAW;gBACpBoB,MAAM,EAAE,mBAAmB;gBAC3BnB,YAAY,EAAE,KAAK;gBACnBY,QAAQ,EAAE,MAAM;gBAChBd,UAAU,EAAE,SAAS;gBACrByB,MAAM,EAAE;cACV,CAAE;cAAApB,QAAA,gBAEFrD,OAAA;gBAAQ+F,KAAK,EAAC,KAAK;gBAAA1C,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BzD,OAAA;gBAAQ+F,KAAK,EAAC,QAAQ;gBAAA1C,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACjCzD,OAAA;gBAAQ+F,KAAK,EAAC,MAAM;gBAAA1C,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/BzD,OAAA;gBAAQ+F,KAAK,EAAC,UAAU;gBAAA1C,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNzD,OAAA;YAAAqD,QAAA,gBACErD,OAAA;cAAO+C,KAAK,EAAE;gBAAEY,OAAO,EAAE,OAAO;gBAAEP,YAAY,EAAE,KAAK;gBAAEU,QAAQ,EAAE,MAAM;gBAAED,KAAK,EAAE;cAAO,CAAE;cAAAR,QAAA,EAAC;YAE1F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzD,OAAA;cACEsF,IAAI,EAAC,MAAM;cACXS,KAAK,EAAElF,cAAc,CAACK,gBAAiB;cACvCsE,QAAQ,EAAGV,CAAC,IAAKhE,iBAAiB,CAACiG,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE7F,gBAAgB,EAAE4D,CAAC,CAACvD,MAAM,CAACwE;cAAM,CAAC,CAAC,CAAE;cAC5FK,WAAW,EAAC,yEAAkB;cAC9BrD,KAAK,EAAE;gBACLuD,KAAK,EAAE,MAAM;gBACbrD,OAAO,EAAE,WAAW;gBACpBoB,MAAM,EAAE,mBAAmB;gBAC3BnB,YAAY,EAAE,KAAK;gBACnBY,QAAQ,EAAE,MAAM;gBAChBY,UAAU,EAAE,wBAAwB;gBACpC1B,UAAU,EAAE;cACd,CAAE;cACF0D,OAAO,EAAG5B,CAAC,IAAK;gBACdA,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACiC,WAAW,GAAG,SAAS;gBACtCF,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACC,UAAU,GAAG,SAAS;cACvC,CAAE;cACF2D,MAAM,EAAG7B,CAAC,IAAK;gBACbA,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACiC,WAAW,GAAG,SAAS;gBACtCF,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACC,UAAU,GAAG,SAAS;cACvC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzD,OAAA;QAAK+C,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnCrD,OAAA;UAAO+C,KAAK,EAAE;YAAEY,OAAO,EAAE,OAAO;YAAEP,YAAY,EAAE,KAAK;YAAEU,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE;UAAO,CAAE;UAAAR,QAAA,EAAC;QAE1F;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRzD,OAAA;UACE+F,KAAK,EAAElF,cAAc,CAACM,mBAAoB;UAC1CqE,QAAQ,EAAGV,CAAC,IAAKhE,iBAAiB,CAACiG,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE5F,mBAAmB,EAAE2D,CAAC,CAACvD,MAAM,CAACwE;UAAM,CAAC,CAAC,CAAE;UAC/FK,WAAW,EAAC,yLAAmC;UAC/CC,IAAI,EAAE,CAAE;UACRtD,KAAK,EAAE;YACLuD,KAAK,EAAE,MAAM;YACbrD,OAAO,EAAE,MAAM;YACfoB,MAAM,EAAE,mBAAmB;YAC3BnB,YAAY,EAAE,KAAK;YACnBqD,MAAM,EAAE,UAAU;YAClBC,UAAU,EAAE,SAAS;YACrB1C,QAAQ,EAAE,MAAM;YAChB2C,UAAU,EAAE,KAAK;YACjB/B,UAAU,EAAE,wBAAwB;YACpC1B,UAAU,EAAE;UACd,CAAE;UACF0D,OAAO,EAAG5B,CAAC,IAAK;YACdA,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACiC,WAAW,GAAG,SAAS;YACtCF,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACC,UAAU,GAAG,SAAS;UACvC,CAAE;UACF2D,MAAM,EAAG7B,CAAC,IAAK;YACbA,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACiC,WAAW,GAAG,SAAS;YACtCF,CAAC,CAACvD,MAAM,CAACwB,KAAK,CAACC,UAAU,GAAG,SAAS;UACvC;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENzD,OAAA;QACEsF,IAAI,EAAC,QAAQ;QACb0B,QAAQ,EAAErG,WAAW,IAAI,CAACJ,YAAY,IAAI,CAACE,WAAW,CAACkB,IAAI,CAAC,CAAE;QAC9DoB,KAAK,EAAE;UACLC,UAAU,EAAErC,WAAW,GAAG,SAAS,GAAG,SAAS;UAC/CkD,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdpB,OAAO,EAAE,WAAW;UACpBC,YAAY,EAAE,KAAK;UACnBY,QAAQ,EAAE,MAAM;UAChBF,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAE9D,WAAW,GAAG,aAAa,GAAG,SAAS;UAC/C+D,UAAU,EAAE,eAAe;UAC3Bf,OAAO,EAAE,MAAM;UACfY,UAAU,EAAE,QAAQ;UACpBuC,GAAG,EAAE;QACP,CAAE;QAAAzD,QAAA,EAED1C,WAAW,gBACVX,OAAA,CAAAE,SAAA;UAAAmD,QAAA,gBACErD,OAAA;YAAM+C,KAAK,EAAE;cACXuD,KAAK,EAAE,MAAM;cACblC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,uBAAuB;cAC/B4C,SAAS,EAAE,iBAAiB;cAC5B/D,YAAY,EAAE,KAAK;cACnBgE,SAAS,EAAE;YACb;UAAE;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,yBAEZ;QAAA,eAAE,CAAC,GAEH;MACD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGPzD,OAAA;MAAK+C,KAAK,EAAE;QACVY,OAAO,EAAE,MAAM;QACfY,UAAU,EAAE,QAAQ;QACpBmB,MAAM,EAAE,QAAQ;QAChB7B,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE;MACZ,CAAE;MAAAT,QAAA,gBACArD,OAAA;QAAK+C,KAAK,EAAE;UAAEoE,IAAI,EAAE,CAAC;UAAE/C,MAAM,EAAE,KAAK;UAAEpB,UAAU,EAAE;QAAU;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrEzD,OAAA;QAAM+C,KAAK,EAAE;UAAEE,OAAO,EAAE;QAAS,CAAE;QAAAI,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7CzD,OAAA;QAAK+C,KAAK,EAAE;UAAEoE,IAAI,EAAE,CAAC;UAAE/C,MAAM,EAAE,KAAK;UAAEpB,UAAU,EAAE;QAAU;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE,CAAC,eAGNzD,OAAA;MAAK+C,KAAK,EAAE;QAAE0C,SAAS,EAAE;MAAS,CAAE;MAAApC,QAAA,gBAClCrD,OAAA;QAAI+C,KAAK,EAAE;UAAE2C,MAAM,EAAE,YAAY;UAAE7B,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAEtE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLzD,OAAA;QAAG+C,KAAK,EAAE;UAAE2C,MAAM,EAAE,YAAY;UAAE7B,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAErE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJzD,OAAA;QACEsF,IAAI,EAAC,QAAQ;QACbtB,OAAO,EAAElB,wBAAyB;QAClCkE,QAAQ,EAAErG,WAAY;QACtBoC,KAAK,EAAE;UACLC,UAAU,EAAErC,WAAW,GAAG,SAAS,GAAG,SAAS;UAC/CkD,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdpB,OAAO,EAAE,WAAW;UACpBC,YAAY,EAAE,KAAK;UACnBY,QAAQ,EAAE,MAAM;UAChBF,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAE9D,WAAW,GAAG,aAAa,GAAG,SAAS;UAC/C+D,UAAU,EAAE,eAAe;UAC3Bf,OAAO,EAAE,MAAM;UACfY,UAAU,EAAE,QAAQ;UACpBuC,GAAG,EAAE,KAAK;UACVpB,MAAM,EAAE;QACV,CAAE;QAAArC,QAAA,EAED1C,WAAW,gBACVX,OAAA,CAAAE,SAAA;UAAAmD,QAAA,gBACErD,OAAA;YAAM+C,KAAK,EAAE;cACXuD,KAAK,EAAE,MAAM;cACblC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,uBAAuB;cAC/B4C,SAAS,EAAE,iBAAiB;cAC5B/D,YAAY,EAAE,KAAK;cACnBgE,SAAS,EAAE;YACb;UAAE;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,yBAEZ;QAAA,eAAE,CAAC,gBAEHzD,OAAA,CAAAE,SAAA;UAAAmD,QAAA,EAAE;QAEF,gBAAE;MACH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENzD,OAAA;MAAOoH,GAAG;MAAA/D,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACnD,EAAA,CA5fIH,YAAY;AAAAkH,EAAA,GAAZlH,YAAY;AA8flB,eAAeA,YAAY;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}