/**
 * UI自动化分析平台 - 主应用组件
 */
import React, { useState } from 'react';
import SimpleUpload from './components/SimpleUpload';
import SimpleResults from './components/SimpleResults';
import RealTimeAnalysis from './components/RealTimeAnalysis';
import './App.css';

function App() {
  const [appState, setAppState] = useState('upload'); // 'upload' | 'analyzing' | 'results'
  const [currentTaskId, setCurrentTaskId] = useState('');
  const [analysisResult, setAnalysisResult] = useState(null);
  const [error, setError] = useState('');

  const handleUploadSuccess = (result) => {
    if (result.task_id) {
      // 如果有任务ID，进入实时分析模式
      setCurrentTaskId(result.task_id);
      setAppState('analyzing');
    } else {
      // 如果直接返回结果，进入结果展示模式
      setAnalysisResult(result);
      setAppState('results');
    }
    setError('');
  };

  const handleUploadError = (errorMessage) => {
    setError(errorMessage);
  };

  const handleAnalysisComplete = (taskData = null) => {
    // 实时分析完成，设置最终结果
    setAppState('results');

    if (taskData) {
      // 使用从API获取的真实数据
      setAnalysisResult({
        task_id: currentTaskId,
        message: "分析完成",
        result: taskData
      });
    } else {
      // 使用默认数据
      setAnalysisResult({
        task_id: currentTaskId,
        message: "分析完成",
        result: {
          status: "completed",
          elements: [],
          flows: [],
          automation_scripts: []
        }
      });
    }
  };

  const handleAnalysisError = (errorMessage) => {
    setError(errorMessage);
    setAppState('upload');
  };

  const handleReset = () => {
    setAppState('upload');
    setCurrentTaskId('');
    setAnalysisResult(null);
    setError('');
  };

  return (
    <div className="app">
      <header className="app-header">
        <div className="container">
          <h1>🤖 UI自动化分析平台</h1>
          <p>基于AI智能体的UI界面分析和自动化测试脚本生成</p>
          {appState !== 'upload' && (
            <button className="reset-button" onClick={handleReset}>
              ← 重新开始
            </button>
          )}
        </div>
      </header>

      <main className="app-main">
        <div className="container">
          {error && (
            <div className="error-banner">
              <span className="error-icon">⚠️</span>
              <span className="error-text">{error}</span>
              <button className="error-close" onClick={() => setError('')}>
                ✕
              </button>
            </div>
          )}

          {/* 实际组件 */}
          {appState === 'upload' && (
            <SimpleUpload
              onUploadSuccess={handleUploadSuccess}
              onUploadError={handleUploadError}
            />
          )}

          {appState === 'analyzing' && currentTaskId && (
            <RealTimeAnalysis
              taskId={currentTaskId}
              onAnalysisComplete={handleAnalysisComplete}
              onAnalysisError={handleAnalysisError}
            />
          )}

          {appState === 'results' && analysisResult && (
            <SimpleResults result={analysisResult} />
          )}
        </div>
      </main>

      <footer className="app-footer">
        <div className="container">
          <p>© 2024 UI自动化分析平台 - 基于FastAPI + React + AI智能体</p>
        </div>
      </footer>
    </div>
  );
}

export default App;
