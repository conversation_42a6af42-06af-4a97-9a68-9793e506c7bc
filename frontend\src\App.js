/**
 * UI自动化分析平台 - 主应用组件
 */
import React, { useState } from 'react';
import Sidebar from './components/Sidebar';
import AnalysisPage from './pages/AnalysisPage';
import HistoryPage from './pages/HistoryPage';
import SettingsPage from './pages/SettingsPage';
import AboutPage from './pages/AboutPage';
import './App.css';

function App() {
  const [currentPage, setCurrentPage] = useState('analysis');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'analysis':
        return <AnalysisPage />;
      case 'history':
        return <HistoryPage />;
      case 'settings':
        return <SettingsPage />;
      case 'about':
        return <AboutPage />;
      default:
        return <AnalysisPage />;
    }
  };

  return (
    <div className="app">
      <Sidebar
        currentPage={currentPage}
        onPageChange={setCurrentPage}
        collapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
      />

      <main className={`app-main ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
        <div className="page-container">
          {renderCurrentPage()}
        </div>
      </main>
    </div>
  );
}

export default App;
