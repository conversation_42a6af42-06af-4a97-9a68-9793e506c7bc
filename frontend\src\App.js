/**
 * UI自动化分析平台 - 主应用组件
 */
import React, { useState } from 'react';
import SimpleUpload from './components/SimpleUpload';
import SimpleResults from './components/SimpleResults';
import './App.css';

function App() {
  const [appState, setAppState] = useState('upload'); // 'upload' | 'results'
  const [analysisResult, setAnalysisResult] = useState(null);
  const [error, setError] = useState('');

  const handleUploadSuccess = (result) => {
    setAnalysisResult(result);
    setAppState('results');
    setError('');
  };

  const handleUploadError = (errorMessage) => {
    setError(errorMessage);
  };

  const handleReset = () => {
    setAppState('upload');
    setAnalysisResult(null);
    setError('');
  };

  return (
    <div className="app">
      <header className="app-header">
        <div className="container">
          <h1>🤖 UI自动化分析平台</h1>
          <p>基于AI智能体的UI界面分析和自动化测试用例生成</p>
          {appState !== 'upload' && (
            <button className="reset-button" onClick={handleReset}>
              ← 重新开始
            </button>
          )}
        </div>
      </header>

      <main className="app-main">
        <div className="container">
          {error && (
            <div className="error-banner">
              <span className="error-icon">⚠️</span>
              <span className="error-text">{error}</span>
              <button className="error-close" onClick={() => setError('')}>
                ✕
              </button>
            </div>
          )}

          {/* 实际组件 */}
          {appState === 'upload' && (
            <SimpleUpload
              onUploadSuccess={handleUploadSuccess}
              onUploadError={handleUploadError}
            />
          )}

          {appState === 'results' && analysisResult && (
            <SimpleResults result={analysisResult} />
          )}
        </div>
      </main>

      <footer className="app-footer">
        <div className="container">
          <p>© 2024 UI自动化分析平台 - 基于FastAPI + React + AI智能体</p>
        </div>
      </footer>
    </div>
  );
}

export default App;
