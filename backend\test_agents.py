#!/usr/bin/env python3
"""
测试智能体功能的脚本
"""
import os
import sys
from pathlib import Path
from PIL import Image

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_agents_mock():
    """测试智能体（模拟模式）"""
    print("🤖 测试智能体（模拟模式）...")
    
    try:
        from app.agents.element_detection.agent import ElementDetectionAgent
        from app.agents.interaction_analysis.agent import InteractionAnalysisAgent
        from app.agents.test_generation.agent import TestGenerationAgent
        
        # 创建测试图片
        test_image = "test.png"
        img = Image.new('RGB', (800, 600), color='white')
        img.save(test_image)
        print(f"  📷 创建测试图片: {test_image}")
        
        # 1. 元素识别（模拟模式）
        print("  🔍 步骤1: 元素识别...")
        element_agent = ElementDetectionAgent(api_key="")  # 空密钥使用模拟模式
        elements = element_agent.analyze(test_image, "测试界面")
        print(f"    ✅ 元素识别: {len(elements)} 个元素")
        for elem in elements:
            print(f"      - {elem.get('name', 'Unknown')} ({elem.get('element_type', 'unknown')})")
        
        # 2. 交互分析（模拟模式）
        print("  🔄 步骤2: 交互分析...")
        interaction_agent = InteractionAnalysisAgent(api_key="")
        flows = interaction_agent.analyze(elements, "测试界面")
        print(f"    ✅ 交互分析: {len(flows)} 个流程")
        for flow in flows:
            print(f"      - {flow.get('flow_name', 'Unknown')} ({len(flow.get('steps', []))}个步骤)")
        
        # 3. 脚本生成（模拟模式）
        print("  📝 步骤3: 脚本生成...")
        script_agent = TestGenerationAgent(api_key="")
        scripts = script_agent.analyze(elements, flows, "测试界面")
        print(f"    ✅ 脚本生成: {len(scripts)} 个脚本")
        for script in scripts:
            print(f"      - {script.get('script_name', 'Unknown')} ({len(script.get('test_steps', []))}个测试步骤)")
        
        # 清理
        os.remove(test_image)
        print("  🧹 清理测试文件")
        
        print("  🎉 智能体测试完成!")
        return True
        
    except Exception as e:
        print(f"  ❌ 智能体测试失败: {e}")
        # 清理测试文件
        if os.path.exists("test.png"):
            os.remove("test.png")
        return False

def test_agents_with_real_api():
    """测试智能体（真实API模式）"""
    print("🤖 测试智能体（真实API模式）...")
    
    # 检查API配置
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("  ⚠️ 未配置OPENAI_API_KEY，跳过真实API测试")
        return True
    
    try:
        from app.agents.element_detection.agent import ElementDetectionAgent
        from app.agents.interaction_analysis.agent import InteractionAnalysisAgent
        from app.agents.test_generation.agent import TestGenerationAgent
        
        # 创建测试图片
        test_image = "test_real.png"
        img = Image.new('RGB', (800, 600), color='lightblue')
        img.save(test_image)
        print(f"  📷 创建测试图片: {test_image}")
        
        # 1. 元素识别（真实API）
        print("  🔍 步骤1: 元素识别（真实API）...")
        element_agent = ElementDetectionAgent(api_key=api_key)
        elements = element_agent.analyze(test_image, "文件管理界面")
        print(f"    ✅ 元素识别: {len(elements)} 个元素")
        
        # 2. 交互分析（真实API）
        print("  🔄 步骤2: 交互分析（真实API）...")
        interaction_agent = InteractionAnalysisAgent(api_key=api_key)
        flows = interaction_agent.analyze(elements, "文件管理界面")
        print(f"    ✅ 交互分析: {len(flows)} 个流程")
        
        # 3. 脚本生成（真实API）
        print("  📝 步骤3: 脚本生成（真实API）...")
        script_agent = TestGenerationAgent(api_key=api_key)
        scripts = script_agent.analyze(elements, flows, "文件管理界面")
        print(f"    ✅ 脚本生成: {len(scripts)} 个脚本")
        
        # 清理
        os.remove(test_image)
        print("  🧹 清理测试文件")
        
        print("  🎉 真实API测试完成!")
        return True
        
    except Exception as e:
        print(f"  ❌ 真实API测试失败: {e}")
        # 清理测试文件
        if os.path.exists("test_real.png"):
            os.remove("test_real.png")
        return False

def main():
    """主函数"""
    print("🚀 开始智能体功能测试...")
    print("=" * 50)
    
    # 测试模拟模式
    mock_success = test_agents_mock()
    print()
    
    # 测试真实API模式
    real_success = test_agents_with_real_api()
    print()
    
    # 总结
    print("=" * 50)
    print("📊 测试结果总结:")
    print(f"  - 模拟模式: {'✅ 通过' if mock_success else '❌ 失败'}")
    print(f"  - 真实API: {'✅ 通过' if real_success else '❌ 失败'}")
    
    if mock_success and real_success:
        print("🎉 所有测试通过!")
        return 0
    else:
        print("❌ 部分测试失败!")
        return 1

if __name__ == "__main__":
    exit(main())
