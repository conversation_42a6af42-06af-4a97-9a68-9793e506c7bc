{"ast": null, "code": "import _objectSpread from\"E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";/**\n * 设置页面\n */import React,{useState}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SettingsPage=()=>{const[settings,setSettings]=useState({// API设置\napiEndpoint:'http://localhost:8001',timeout:30,// 分析设置\nmaxFileSize:10,supportedFormats:['png','jpg','jpeg','gif'],autoSave:true,// 智能体设置\nenableElementDetection:true,enableInteractionAnalysis:true,enableScriptGeneration:true,// 界面设置\ntheme:'light',language:'zh-CN',showNotifications:true});const[saved,setSaved]=useState(false);const handleSettingChange=(key,value)=>{setSettings(prev=>_objectSpread(_objectSpread({},prev),{},{[key]:value}));};const handleArraySettingChange=(key,index,value)=>{setSettings(prev=>_objectSpread(_objectSpread({},prev),{},{[key]:prev[key].map((item,i)=>i===index?value:item)}));};const handleSaveSettings=()=>{// 模拟保存设置\nlocalStorage.setItem('ui-automation-settings',JSON.stringify(settings));setSaved(true);setTimeout(()=>setSaved(false),3000);};const handleResetSettings=()=>{if(window.confirm('确定要重置所有设置吗？')){setSettings({apiEndpoint:'http://localhost:8001',timeout:30,maxFileSize:10,supportedFormats:['png','jpg','jpeg','gif'],autoSave:true,enableElementDetection:true,enableInteractionAnalysis:true,enableScriptGeneration:true,theme:'light',language:'zh-CN',showNotifications:true});}};return/*#__PURE__*/_jsxs(\"div\",{className:\"settings-page\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"page-header\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"\\u2699\\uFE0F \\u7CFB\\u7EDF\\u8BBE\\u7F6E\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u914D\\u7F6E\\u7CFB\\u7EDF\\u53C2\\u6570\\u548C\\u4E2A\\u4EBA\\u504F\\u597D\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"page-content\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"settings-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"settings-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83D\\uDD17 API\\u8BBE\\u7F6E\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"API\\u7AEF\\u70B9\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:settings.apiEndpoint,onChange:e=>handleSettingChange('apiEndpoint',e.target.value),placeholder:\"http://localhost:8001\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u8BF7\\u6C42\\u8D85\\u65F6 (\\u79D2)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:settings.timeout,onChange:e=>handleSettingChange('timeout',parseInt(e.target.value)),min:\"5\",max:\"300\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83D\\uDD0D \\u5206\\u6790\\u8BBE\\u7F6E\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u6700\\u5927\\u6587\\u4EF6\\u5927\\u5C0F (MB)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:settings.maxFileSize,onChange:e=>handleSettingChange('maxFileSize',parseInt(e.target.value)),min:\"1\",max:\"100\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u81EA\\u52A8\\u4FDD\\u5B58\\u7ED3\\u679C\"}),/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:settings.autoSave,onChange:e=>handleSettingChange('autoSave',e.target.checked)})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83E\\uDD16 \\u667A\\u80FD\\u4F53\\u8BBE\\u7F6E\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u542F\\u7528\\u5143\\u7D20\\u8BC6\\u522B\\u667A\\u80FD\\u4F53\"}),/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:settings.enableElementDetection,onChange:e=>handleSettingChange('enableElementDetection',e.target.checked)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u542F\\u7528\\u4EA4\\u4E92\\u5206\\u6790\\u667A\\u80FD\\u4F53\"}),/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:settings.enableInteractionAnalysis,onChange:e=>handleSettingChange('enableInteractionAnalysis',e.target.checked)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u542F\\u7528\\u811A\\u672C\\u751F\\u6210\\u667A\\u80FD\\u4F53\"}),/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:settings.enableScriptGeneration,onChange:e=>handleSettingChange('enableScriptGeneration',e.target.checked)})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83C\\uDFA8 \\u754C\\u9762\\u8BBE\\u7F6E\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u4E3B\\u9898\"}),/*#__PURE__*/_jsxs(\"select\",{value:settings.theme,onChange:e=>handleSettingChange('theme',e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"light\",children:\"\\u6D45\\u8272\\u4E3B\\u9898\"}),/*#__PURE__*/_jsx(\"option\",{value:\"dark\",children:\"\\u6DF1\\u8272\\u4E3B\\u9898\"}),/*#__PURE__*/_jsx(\"option\",{value:\"auto\",children:\"\\u8DDF\\u968F\\u7CFB\\u7EDF\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u8BED\\u8A00\"}),/*#__PURE__*/_jsxs(\"select\",{value:settings.language,onChange:e=>handleSettingChange('language',e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"zh-CN\",children:\"\\u7B80\\u4F53\\u4E2D\\u6587\"}),/*#__PURE__*/_jsx(\"option\",{value:\"en-US\",children:\"English\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"setting-item\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u663E\\u793A\\u901A\\u77E5\"}),/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:settings.showNotifications,onChange:e=>handleSettingChange('showNotifications',e.target.checked)})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-actions\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"btn-primary\",onClick:handleSaveSettings,children:saved?'✅ 已保存':'💾 保存设置'}),/*#__PURE__*/_jsx(\"button\",{className:\"btn-secondary\",onClick:handleResetSettings,children:\"\\uD83D\\uDD04 \\u91CD\\u7F6E\\u8BBE\\u7F6E\"})]})]})}),/*#__PURE__*/_jsx(\"style\",{children:\"\\n        .settings-page {\\n          min-height: 100vh;\\n          background: #f8f9fa;\\n        }\\n\\n        .page-header {\\n          background: white;\\n          border-bottom: 1px solid #e9ecef;\\n          padding: 24px 0;\\n          margin-bottom: 24px;\\n        }\\n\\n        .page-header h1 {\\n          margin: 0 0 8px 0;\\n          color: #333;\\n          font-size: 28px;\\n          font-weight: 600;\\n          max-width: 1200px;\\n          margin-left: auto;\\n          margin-right: auto;\\n          padding: 0 24px;\\n        }\\n\\n        .page-header p {\\n          margin: 0;\\n          color: #666;\\n          font-size: 16px;\\n          max-width: 1200px;\\n          margin-left: auto;\\n          margin-right: auto;\\n          padding: 0 24px;\\n        }\\n\\n        .page-content {\\n          max-width: 800px;\\n          margin: 0 auto;\\n          padding: 0 24px;\\n        }\\n\\n        .settings-container {\\n          background: white;\\n          border-radius: 12px;\\n          padding: 24px;\\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\\n        }\\n\\n        .settings-section {\\n          margin-bottom: 32px;\\n          padding-bottom: 24px;\\n          border-bottom: 1px solid #e9ecef;\\n        }\\n\\n        .settings-section:last-of-type {\\n          border-bottom: none;\\n          margin-bottom: 24px;\\n        }\\n\\n        .settings-section h3 {\\n          margin: 0 0 20px 0;\\n          color: #333;\\n          font-size: 18px;\\n          font-weight: 600;\\n        }\\n\\n        .setting-item {\\n          display: flex;\\n          justify-content: space-between;\\n          align-items: center;\\n          margin-bottom: 16px;\\n          padding: 12px 0;\\n        }\\n\\n        .setting-item:last-child {\\n          margin-bottom: 0;\\n        }\\n\\n        .setting-item label {\\n          font-weight: 500;\\n          color: #333;\\n          flex: 1;\\n        }\\n\\n        .setting-item input,\\n        .setting-item select {\\n          width: 200px;\\n          padding: 8px 12px;\\n          border: 1px solid #ddd;\\n          border-radius: 6px;\\n          font-size: 14px;\\n        }\\n\\n        .setting-item input[type=\\\"checkbox\\\"] {\\n          width: auto;\\n          transform: scale(1.2);\\n        }\\n\\n        .setting-item input:focus,\\n        .setting-item select:focus {\\n          outline: none;\\n          border-color: #007bff;\\n          box-shadow: 0 0 0 2px rgba(0,123,255,0.25);\\n        }\\n\\n        .settings-actions {\\n          display: flex;\\n          gap: 12px;\\n          justify-content: center;\\n          padding-top: 24px;\\n          border-top: 1px solid #e9ecef;\\n        }\\n\\n        .btn-primary,\\n        .btn-secondary {\\n          border: none;\\n          padding: 12px 24px;\\n          border-radius: 6px;\\n          font-size: 16px;\\n          font-weight: 500;\\n          cursor: pointer;\\n          transition: all 0.2s ease;\\n          min-width: 120px;\\n        }\\n\\n        .btn-primary {\\n          background: #007bff;\\n          color: white;\\n        }\\n\\n        .btn-primary:hover {\\n          background: #0056b3;\\n        }\\n\\n        .btn-secondary {\\n          background: #6c757d;\\n          color: white;\\n        }\\n\\n        .btn-secondary:hover {\\n          background: #5a6268;\\n        }\\n\\n        /* \\u54CD\\u5E94\\u5F0F\\u8BBE\\u8BA1 */\\n        @media (max-width: 768px) {\\n          .page-header h1,\\n          .page-header p,\\n          .page-content {\\n            padding: 0 16px;\\n          }\\n\\n          .settings-container {\\n            padding: 16px;\\n          }\\n\\n          .setting-item {\\n            flex-direction: column;\\n            align-items: flex-start;\\n            gap: 8px;\\n          }\\n\\n          .setting-item input,\\n          .setting-item select {\\n            width: 100%;\\n          }\\n\\n          .settings-actions {\\n            flex-direction: column;\\n          }\\n        }\\n      \"})]});};export default SettingsPage;", "map": {"version": 3, "names": ["React", "useState", "jsx", "_jsx", "jsxs", "_jsxs", "SettingsPage", "settings", "setSettings", "apiEndpoint", "timeout", "maxFileSize", "supportedFormats", "autoSave", "enableElementDetection", "enableInteractionAnalysis", "enableScriptGeneration", "theme", "language", "showNotifications", "saved", "setSaved", "handleSettingChange", "key", "value", "prev", "_objectSpread", "handleArraySettingChange", "index", "map", "item", "i", "handleSaveSettings", "localStorage", "setItem", "JSON", "stringify", "setTimeout", "handleResetSettings", "window", "confirm", "className", "children", "type", "onChange", "e", "target", "placeholder", "parseInt", "min", "max", "checked", "onClick"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/pages/SettingsPage.js"], "sourcesContent": ["/**\n * 设置页面\n */\nimport React, { useState } from 'react';\n\nconst SettingsPage = () => {\n  const [settings, setSettings] = useState({\n    // API设置\n    apiEndpoint: 'http://localhost:8001',\n    timeout: 30,\n    \n    // 分析设置\n    maxFileSize: 10,\n    supportedFormats: ['png', 'jpg', 'jpeg', 'gif'],\n    autoSave: true,\n    \n    // 智能体设置\n    enableElementDetection: true,\n    enableInteractionAnalysis: true,\n    enableScriptGeneration: true,\n    \n    // 界面设置\n    theme: 'light',\n    language: 'zh-CN',\n    showNotifications: true\n  });\n\n  const [saved, setSaved] = useState(false);\n\n  const handleSettingChange = (key, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n\n  const handleArraySettingChange = (key, index, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [key]: prev[key].map((item, i) => i === index ? value : item)\n    }));\n  };\n\n  const handleSaveSettings = () => {\n    // 模拟保存设置\n    localStorage.setItem('ui-automation-settings', JSON.stringify(settings));\n    setSaved(true);\n    setTimeout(() => setSaved(false), 3000);\n  };\n\n  const handleResetSettings = () => {\n    if (window.confirm('确定要重置所有设置吗？')) {\n      setSettings({\n        apiEndpoint: 'http://localhost:8001',\n        timeout: 30,\n        maxFileSize: 10,\n        supportedFormats: ['png', 'jpg', 'jpeg', 'gif'],\n        autoSave: true,\n        enableElementDetection: true,\n        enableInteractionAnalysis: true,\n        enableScriptGeneration: true,\n        theme: 'light',\n        language: 'zh-CN',\n        showNotifications: true\n      });\n    }\n  };\n\n  return (\n    <div className=\"settings-page\">\n      <div className=\"page-header\">\n        <h1>⚙️ 系统设置</h1>\n        <p>配置系统参数和个人偏好</p>\n      </div>\n\n      <div className=\"page-content\">\n        <div className=\"settings-container\">\n          {/* API设置 */}\n          <div className=\"settings-section\">\n            <h3>🔗 API设置</h3>\n            <div className=\"setting-item\">\n              <label>API端点</label>\n              <input\n                type=\"text\"\n                value={settings.apiEndpoint}\n                onChange={(e) => handleSettingChange('apiEndpoint', e.target.value)}\n                placeholder=\"http://localhost:8001\"\n              />\n            </div>\n            <div className=\"setting-item\">\n              <label>请求超时 (秒)</label>\n              <input\n                type=\"number\"\n                value={settings.timeout}\n                onChange={(e) => handleSettingChange('timeout', parseInt(e.target.value))}\n                min=\"5\"\n                max=\"300\"\n              />\n            </div>\n          </div>\n\n          {/* 分析设置 */}\n          <div className=\"settings-section\">\n            <h3>🔍 分析设置</h3>\n            <div className=\"setting-item\">\n              <label>最大文件大小 (MB)</label>\n              <input\n                type=\"number\"\n                value={settings.maxFileSize}\n                onChange={(e) => handleSettingChange('maxFileSize', parseInt(e.target.value))}\n                min=\"1\"\n                max=\"100\"\n              />\n            </div>\n            <div className=\"setting-item\">\n              <label>自动保存结果</label>\n              <input\n                type=\"checkbox\"\n                checked={settings.autoSave}\n                onChange={(e) => handleSettingChange('autoSave', e.target.checked)}\n              />\n            </div>\n          </div>\n\n          {/* 智能体设置 */}\n          <div className=\"settings-section\">\n            <h3>🤖 智能体设置</h3>\n            <div className=\"setting-item\">\n              <label>启用元素识别智能体</label>\n              <input\n                type=\"checkbox\"\n                checked={settings.enableElementDetection}\n                onChange={(e) => handleSettingChange('enableElementDetection', e.target.checked)}\n              />\n            </div>\n            <div className=\"setting-item\">\n              <label>启用交互分析智能体</label>\n              <input\n                type=\"checkbox\"\n                checked={settings.enableInteractionAnalysis}\n                onChange={(e) => handleSettingChange('enableInteractionAnalysis', e.target.checked)}\n              />\n            </div>\n            <div className=\"setting-item\">\n              <label>启用脚本生成智能体</label>\n              <input\n                type=\"checkbox\"\n                checked={settings.enableScriptGeneration}\n                onChange={(e) => handleSettingChange('enableScriptGeneration', e.target.checked)}\n              />\n            </div>\n          </div>\n\n          {/* 界面设置 */}\n          <div className=\"settings-section\">\n            <h3>🎨 界面设置</h3>\n            <div className=\"setting-item\">\n              <label>主题</label>\n              <select\n                value={settings.theme}\n                onChange={(e) => handleSettingChange('theme', e.target.value)}\n              >\n                <option value=\"light\">浅色主题</option>\n                <option value=\"dark\">深色主题</option>\n                <option value=\"auto\">跟随系统</option>\n              </select>\n            </div>\n            <div className=\"setting-item\">\n              <label>语言</label>\n              <select\n                value={settings.language}\n                onChange={(e) => handleSettingChange('language', e.target.value)}\n              >\n                <option value=\"zh-CN\">简体中文</option>\n                <option value=\"en-US\">English</option>\n              </select>\n            </div>\n            <div className=\"setting-item\">\n              <label>显示通知</label>\n              <input\n                type=\"checkbox\"\n                checked={settings.showNotifications}\n                onChange={(e) => handleSettingChange('showNotifications', e.target.checked)}\n              />\n            </div>\n          </div>\n\n          {/* 操作按钮 */}\n          <div className=\"settings-actions\">\n            <button className=\"btn-primary\" onClick={handleSaveSettings}>\n              {saved ? '✅ 已保存' : '💾 保存设置'}\n            </button>\n            <button className=\"btn-secondary\" onClick={handleResetSettings}>\n              🔄 重置设置\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <style>{`\n        .settings-page {\n          min-height: 100vh;\n          background: #f8f9fa;\n        }\n\n        .page-header {\n          background: white;\n          border-bottom: 1px solid #e9ecef;\n          padding: 24px 0;\n          margin-bottom: 24px;\n        }\n\n        .page-header h1 {\n          margin: 0 0 8px 0;\n          color: #333;\n          font-size: 28px;\n          font-weight: 600;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-header p {\n          margin: 0;\n          color: #666;\n          font-size: 16px;\n          max-width: 1200px;\n          margin-left: auto;\n          margin-right: auto;\n          padding: 0 24px;\n        }\n\n        .page-content {\n          max-width: 800px;\n          margin: 0 auto;\n          padding: 0 24px;\n        }\n\n        .settings-container {\n          background: white;\n          border-radius: 12px;\n          padding: 24px;\n          box-shadow: 0 2px 12px rgba(0,0,0,0.1);\n        }\n\n        .settings-section {\n          margin-bottom: 32px;\n          padding-bottom: 24px;\n          border-bottom: 1px solid #e9ecef;\n        }\n\n        .settings-section:last-of-type {\n          border-bottom: none;\n          margin-bottom: 24px;\n        }\n\n        .settings-section h3 {\n          margin: 0 0 20px 0;\n          color: #333;\n          font-size: 18px;\n          font-weight: 600;\n        }\n\n        .setting-item {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 16px;\n          padding: 12px 0;\n        }\n\n        .setting-item:last-child {\n          margin-bottom: 0;\n        }\n\n        .setting-item label {\n          font-weight: 500;\n          color: #333;\n          flex: 1;\n        }\n\n        .setting-item input,\n        .setting-item select {\n          width: 200px;\n          padding: 8px 12px;\n          border: 1px solid #ddd;\n          border-radius: 6px;\n          font-size: 14px;\n        }\n\n        .setting-item input[type=\"checkbox\"] {\n          width: auto;\n          transform: scale(1.2);\n        }\n\n        .setting-item input:focus,\n        .setting-item select:focus {\n          outline: none;\n          border-color: #007bff;\n          box-shadow: 0 0 0 2px rgba(0,123,255,0.25);\n        }\n\n        .settings-actions {\n          display: flex;\n          gap: 12px;\n          justify-content: center;\n          padding-top: 24px;\n          border-top: 1px solid #e9ecef;\n        }\n\n        .btn-primary,\n        .btn-secondary {\n          border: none;\n          padding: 12px 24px;\n          border-radius: 6px;\n          font-size: 16px;\n          font-weight: 500;\n          cursor: pointer;\n          transition: all 0.2s ease;\n          min-width: 120px;\n        }\n\n        .btn-primary {\n          background: #007bff;\n          color: white;\n        }\n\n        .btn-primary:hover {\n          background: #0056b3;\n        }\n\n        .btn-secondary {\n          background: #6c757d;\n          color: white;\n        }\n\n        .btn-secondary:hover {\n          background: #5a6268;\n        }\n\n        /* 响应式设计 */\n        @media (max-width: 768px) {\n          .page-header h1,\n          .page-header p,\n          .page-content {\n            padding: 0 16px;\n          }\n\n          .settings-container {\n            padding: 16px;\n          }\n\n          .setting-item {\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 8px;\n          }\n\n          .setting-item input,\n          .setting-item select {\n            width: 100%;\n          }\n\n          .settings-actions {\n            flex-direction: column;\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default SettingsPage;\n"], "mappings": "wJAAA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGP,QAAQ,CAAC,CACvC;AACAQ,WAAW,CAAE,uBAAuB,CACpCC,OAAO,CAAE,EAAE,CAEX;AACAC,WAAW,CAAE,EAAE,CACfC,gBAAgB,CAAE,CAAC,KAAK,CAAE,KAAK,CAAE,MAAM,CAAE,KAAK,CAAC,CAC/CC,QAAQ,CAAE,IAAI,CAEd;AACAC,sBAAsB,CAAE,IAAI,CAC5BC,yBAAyB,CAAE,IAAI,CAC/BC,sBAAsB,CAAE,IAAI,CAE5B;AACAC,KAAK,CAAE,OAAO,CACdC,QAAQ,CAAE,OAAO,CACjBC,iBAAiB,CAAE,IACrB,CAAC,CAAC,CAEF,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CAEzC,KAAM,CAAAqB,mBAAmB,CAAGA,CAACC,GAAG,CAAEC,KAAK,GAAK,CAC1ChB,WAAW,CAACiB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACXD,IAAI,MACP,CAACF,GAAG,EAAGC,KAAK,EACZ,CAAC,CACL,CAAC,CAED,KAAM,CAAAG,wBAAwB,CAAGA,CAACJ,GAAG,CAAEK,KAAK,CAAEJ,KAAK,GAAK,CACtDhB,WAAW,CAACiB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACXD,IAAI,MACP,CAACF,GAAG,EAAGE,IAAI,CAACF,GAAG,CAAC,CAACM,GAAG,CAAC,CAACC,IAAI,CAAEC,CAAC,GAAKA,CAAC,GAAKH,KAAK,CAAGJ,KAAK,CAAGM,IAAI,CAAC,EAC7D,CAAC,CACL,CAAC,CAED,KAAM,CAAAE,kBAAkB,CAAGA,CAAA,GAAM,CAC/B;AACAC,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAEC,IAAI,CAACC,SAAS,CAAC7B,QAAQ,CAAC,CAAC,CACxEc,QAAQ,CAAC,IAAI,CAAC,CACdgB,UAAU,CAAC,IAAMhB,QAAQ,CAAC,KAAK,CAAC,CAAE,IAAI,CAAC,CACzC,CAAC,CAED,KAAM,CAAAiB,mBAAmB,CAAGA,CAAA,GAAM,CAChC,GAAIC,MAAM,CAACC,OAAO,CAAC,aAAa,CAAC,CAAE,CACjChC,WAAW,CAAC,CACVC,WAAW,CAAE,uBAAuB,CACpCC,OAAO,CAAE,EAAE,CACXC,WAAW,CAAE,EAAE,CACfC,gBAAgB,CAAE,CAAC,KAAK,CAAE,KAAK,CAAE,MAAM,CAAE,KAAK,CAAC,CAC/CC,QAAQ,CAAE,IAAI,CACdC,sBAAsB,CAAE,IAAI,CAC5BC,yBAAyB,CAAE,IAAI,CAC/BC,sBAAsB,CAAE,IAAI,CAC5BC,KAAK,CAAE,OAAO,CACdC,QAAQ,CAAE,OAAO,CACjBC,iBAAiB,CAAE,IACrB,CAAC,CAAC,CACJ,CACF,CAAC,CAED,mBACEd,KAAA,QAAKoC,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BrC,KAAA,QAAKoC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BvC,IAAA,OAAAuC,QAAA,CAAI,uCAAO,CAAI,CAAC,cAChBvC,IAAA,MAAAuC,QAAA,CAAG,oEAAW,CAAG,CAAC,EACf,CAAC,cAENvC,IAAA,QAAKsC,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3BrC,KAAA,QAAKoC,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eAEjCrC,KAAA,QAAKoC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BvC,IAAA,OAAAuC,QAAA,CAAI,8BAAQ,CAAI,CAAC,cACjBrC,KAAA,QAAKoC,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BvC,IAAA,UAAAuC,QAAA,CAAO,iBAAK,CAAO,CAAC,cACpBvC,IAAA,UACEwC,IAAI,CAAC,MAAM,CACXnB,KAAK,CAAEjB,QAAQ,CAACE,WAAY,CAC5BmC,QAAQ,CAAGC,CAAC,EAAKvB,mBAAmB,CAAC,aAAa,CAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE,CACpEuB,WAAW,CAAC,uBAAuB,CACpC,CAAC,EACC,CAAC,cACN1C,KAAA,QAAKoC,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BvC,IAAA,UAAAuC,QAAA,CAAO,mCAAQ,CAAO,CAAC,cACvBvC,IAAA,UACEwC,IAAI,CAAC,QAAQ,CACbnB,KAAK,CAAEjB,QAAQ,CAACG,OAAQ,CACxBkC,QAAQ,CAAGC,CAAC,EAAKvB,mBAAmB,CAAC,SAAS,CAAE0B,QAAQ,CAACH,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAC,CAAE,CAC1EyB,GAAG,CAAC,GAAG,CACPC,GAAG,CAAC,KAAK,CACV,CAAC,EACC,CAAC,EACH,CAAC,cAGN7C,KAAA,QAAKoC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BvC,IAAA,OAAAuC,QAAA,CAAI,uCAAO,CAAI,CAAC,cAChBrC,KAAA,QAAKoC,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BvC,IAAA,UAAAuC,QAAA,CAAO,2CAAW,CAAO,CAAC,cAC1BvC,IAAA,UACEwC,IAAI,CAAC,QAAQ,CACbnB,KAAK,CAAEjB,QAAQ,CAACI,WAAY,CAC5BiC,QAAQ,CAAGC,CAAC,EAAKvB,mBAAmB,CAAC,aAAa,CAAE0B,QAAQ,CAACH,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAC,CAAE,CAC9EyB,GAAG,CAAC,GAAG,CACPC,GAAG,CAAC,KAAK,CACV,CAAC,EACC,CAAC,cACN7C,KAAA,QAAKoC,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BvC,IAAA,UAAAuC,QAAA,CAAO,sCAAM,CAAO,CAAC,cACrBvC,IAAA,UACEwC,IAAI,CAAC,UAAU,CACfQ,OAAO,CAAE5C,QAAQ,CAACM,QAAS,CAC3B+B,QAAQ,CAAGC,CAAC,EAAKvB,mBAAmB,CAAC,UAAU,CAAEuB,CAAC,CAACC,MAAM,CAACK,OAAO,CAAE,CACpE,CAAC,EACC,CAAC,EACH,CAAC,cAGN9C,KAAA,QAAKoC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BvC,IAAA,OAAAuC,QAAA,CAAI,6CAAQ,CAAI,CAAC,cACjBrC,KAAA,QAAKoC,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BvC,IAAA,UAAAuC,QAAA,CAAO,wDAAS,CAAO,CAAC,cACxBvC,IAAA,UACEwC,IAAI,CAAC,UAAU,CACfQ,OAAO,CAAE5C,QAAQ,CAACO,sBAAuB,CACzC8B,QAAQ,CAAGC,CAAC,EAAKvB,mBAAmB,CAAC,wBAAwB,CAAEuB,CAAC,CAACC,MAAM,CAACK,OAAO,CAAE,CAClF,CAAC,EACC,CAAC,cACN9C,KAAA,QAAKoC,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BvC,IAAA,UAAAuC,QAAA,CAAO,wDAAS,CAAO,CAAC,cACxBvC,IAAA,UACEwC,IAAI,CAAC,UAAU,CACfQ,OAAO,CAAE5C,QAAQ,CAACQ,yBAA0B,CAC5C6B,QAAQ,CAAGC,CAAC,EAAKvB,mBAAmB,CAAC,2BAA2B,CAAEuB,CAAC,CAACC,MAAM,CAACK,OAAO,CAAE,CACrF,CAAC,EACC,CAAC,cACN9C,KAAA,QAAKoC,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BvC,IAAA,UAAAuC,QAAA,CAAO,wDAAS,CAAO,CAAC,cACxBvC,IAAA,UACEwC,IAAI,CAAC,UAAU,CACfQ,OAAO,CAAE5C,QAAQ,CAACS,sBAAuB,CACzC4B,QAAQ,CAAGC,CAAC,EAAKvB,mBAAmB,CAAC,wBAAwB,CAAEuB,CAAC,CAACC,MAAM,CAACK,OAAO,CAAE,CAClF,CAAC,EACC,CAAC,EACH,CAAC,cAGN9C,KAAA,QAAKoC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BvC,IAAA,OAAAuC,QAAA,CAAI,uCAAO,CAAI,CAAC,cAChBrC,KAAA,QAAKoC,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BvC,IAAA,UAAAuC,QAAA,CAAO,cAAE,CAAO,CAAC,cACjBrC,KAAA,WACEmB,KAAK,CAAEjB,QAAQ,CAACU,KAAM,CACtB2B,QAAQ,CAAGC,CAAC,EAAKvB,mBAAmB,CAAC,OAAO,CAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE,CAAAkB,QAAA,eAE9DvC,IAAA,WAAQqB,KAAK,CAAC,OAAO,CAAAkB,QAAA,CAAC,0BAAI,CAAQ,CAAC,cACnCvC,IAAA,WAAQqB,KAAK,CAAC,MAAM,CAAAkB,QAAA,CAAC,0BAAI,CAAQ,CAAC,cAClCvC,IAAA,WAAQqB,KAAK,CAAC,MAAM,CAAAkB,QAAA,CAAC,0BAAI,CAAQ,CAAC,EAC5B,CAAC,EACN,CAAC,cACNrC,KAAA,QAAKoC,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BvC,IAAA,UAAAuC,QAAA,CAAO,cAAE,CAAO,CAAC,cACjBrC,KAAA,WACEmB,KAAK,CAAEjB,QAAQ,CAACW,QAAS,CACzB0B,QAAQ,CAAGC,CAAC,EAAKvB,mBAAmB,CAAC,UAAU,CAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE,CAAAkB,QAAA,eAEjEvC,IAAA,WAAQqB,KAAK,CAAC,OAAO,CAAAkB,QAAA,CAAC,0BAAI,CAAQ,CAAC,cACnCvC,IAAA,WAAQqB,KAAK,CAAC,OAAO,CAAAkB,QAAA,CAAC,SAAO,CAAQ,CAAC,EAChC,CAAC,EACN,CAAC,cACNrC,KAAA,QAAKoC,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BvC,IAAA,UAAAuC,QAAA,CAAO,0BAAI,CAAO,CAAC,cACnBvC,IAAA,UACEwC,IAAI,CAAC,UAAU,CACfQ,OAAO,CAAE5C,QAAQ,CAACY,iBAAkB,CACpCyB,QAAQ,CAAGC,CAAC,EAAKvB,mBAAmB,CAAC,mBAAmB,CAAEuB,CAAC,CAACC,MAAM,CAACK,OAAO,CAAE,CAC7E,CAAC,EACC,CAAC,EACH,CAAC,cAGN9C,KAAA,QAAKoC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BvC,IAAA,WAAQsC,SAAS,CAAC,aAAa,CAACW,OAAO,CAAEpB,kBAAmB,CAAAU,QAAA,CACzDtB,KAAK,CAAG,OAAO,CAAG,SAAS,CACtB,CAAC,cACTjB,IAAA,WAAQsC,SAAS,CAAC,eAAe,CAACW,OAAO,CAAEd,mBAAoB,CAAAI,QAAA,CAAC,uCAEhE,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,cAENvC,IAAA,UAAAuC,QAAA,u1HAyKS,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAApC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}