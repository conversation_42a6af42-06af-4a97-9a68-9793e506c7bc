<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>脚本名称格式测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }
        .script-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        .script-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        .original-name {
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
        }
        .description {
            color: #666;
            margin-bottom: 12px;
        }
        .meta-info {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #666;
        }
        .download-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 8px;
        }
        .download-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📝 UI自动化测试脚本名称格式示例</h1>
            <p>新的脚本名称格式：年月日+时分+脚本名称+spec.ts</p>
        </div>

        <div class="script-item">
            <div class="script-name">20241217-1030-用户登录.spec.ts</div>
            <div class="original-name">原始名称: 用户登录自动化脚本</div>
            <div class="description">验证用户登录界面的完整流程</div>
            <div class="meta-info">
                <span>优先级: high</span>
                <span>预计时长: 30秒</span>
                <span>创建时间: 2024-12-17 10:30:00</span>
            </div>
            <button class="download-btn" onclick="downloadScript('20241217-1030-用户登录.yaml')">📥 下载脚本</button>
        </div>

        <div class="script-item">
            <div class="script-name">20241217-1415-文件管理.spec.ts</div>
            <div class="original-name">原始名称: 文件管理界面功能自动化脚本</div>
            <div class="description">验证文件管理界面功能的完整流程</div>
            <div class="meta-info">
                <span>优先级: medium</span>
                <span>预计时长: 50秒</span>
                <span>创建时间: 2024-12-17 14:15:00</span>
            </div>
            <button class="download-btn" onclick="downloadScript('20241217-1415-文件管理.yaml')">📥 下载脚本</button>
        </div>

        <div class="script-item">
            <div class="script-name">20241217-1645-购物车操作.spec.ts</div>
            <div class="original-name">原始名称: 电商购物车界面自动化脚本</div>
            <div class="description">验证电商购物车界面的完整流程</div>
            <div class="meta-info">
                <span>优先级: medium</span>
                <span>预计时长: 40秒</span>
                <span>创建时间: 2024-12-17 16:45:00</span>
            </div>
            <button class="download-btn" onclick="downloadScript('20241217-1645-购物车操作.yaml')">📥 下载脚本</button>
        </div>

        <div class="script-item">
            <div class="script-name">20260618-1014-登录脚本.spec.ts</div>
            <div class="original-name">原始名称: 登录脚本自动化测试</div>
            <div class="description">按照您要求的格式示例：20260618-1014-登录脚本.spec.ts</div>
            <div class="meta-info">
                <span>优先级: high</span>
                <span>预计时长: 25秒</span>
                <span>创建时间: 2026-06-18 10:14:00</span>
            </div>
            <button class="download-btn" onclick="downloadScript('20260618-1014-登录脚本.yaml')">📥 下载脚本</button>
        </div>
    </div>

    <script>
        function downloadScript(filename) {
            const content = `# UI自动化测试脚本
# 脚本名称: ${filename}
# 生成时间: ${new Date().toISOString()}

name: ${filename.replace('.yaml', '')}
description: 自动化测试脚本

steps:
  - name: 示例步骤
    action: aiTap
    locate: 示例元素
    expect: 预期结果`;
            
            const blob = new Blob([content], { type: 'text/yaml' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
