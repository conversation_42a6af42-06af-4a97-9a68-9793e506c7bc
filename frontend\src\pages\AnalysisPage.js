/**
 * UI分析页面 - 主要的分析功能页面
 */
import React, { useState } from 'react';
import SimpleUpload from '../components/SimpleUpload';
import SimpleResults from '../components/SimpleResults';
import RealTimeAnalysis from '../components/RealTimeAnalysis';

const AnalysisPage = () => {
  const [appState, setAppState] = useState('upload'); // 'upload' | 'analyzing' | 'results'
  const [currentTaskId, setCurrentTaskId] = useState('');
  const [analysisResult, setAnalysisResult] = useState(null);
  const [error, setError] = useState('');

  const handleUploadSuccess = (result) => {
    if (result.task_id) {
      // 如果有任务ID，进入实时分析模式
      setCurrentTaskId(result.task_id);
      setAppState('analyzing');
    } else {
      // 如果直接返回结果，进入结果展示模式
      setAnalysisResult(result);
      setAppState('results');
    }
    setError('');
  };

  const handleUploadError = (errorMessage) => {
    setError(errorMessage);
  };

  const handleAnalysisComplete = (taskData = null) => {
    // 实时分析完成，设置最终结果
    setAppState('results');

    if (taskData) {
      // 使用从API获取的真实数据
      setAnalysisResult({
        task_id: currentTaskId,
        message: "分析完成",
        result: taskData
      });
    } else {
      // 使用默认数据
      setAnalysisResult({
        task_id: currentTaskId,
        message: "分析完成",
        result: {
          status: "completed",
          elements: [],
          flows: [],
          automation_scripts: []
        }
      });
    }
  };

  const handleAnalysisError = (errorMessage) => {
    setError(errorMessage);
    setAppState('upload');
  };

  const handleReset = () => {
    setAppState('upload');
    setCurrentTaskId('');
    setAnalysisResult(null);
    setError('');
  };

  return (
    <div className="analysis-page">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <h1>🔍 UI界面分析</h1>
          <p>上传UI界面截图，使用AI智能体进行自动化分析和测试脚本生成</p>
          {appState !== 'upload' && (
            <button className="reset-button" onClick={handleReset}>
              ← 重新开始分析
            </button>
          )}
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="error-banner">
          <span className="error-icon">⚠️</span>
          <span className="error-text">{error}</span>
          <button className="error-close" onClick={() => setError('')}>
            ✕
          </button>
        </div>
      )}

      {/* 主要内容区域 */}
      <div className="page-content">
        {appState === 'upload' && (
          <SimpleUpload
            onUploadSuccess={handleUploadSuccess}
            onUploadError={handleUploadError}
          />
        )}

        {appState === 'analyzing' && currentTaskId && (
          <RealTimeAnalysis
            taskId={currentTaskId}
            onAnalysisComplete={handleAnalysisComplete}
            onAnalysisError={handleAnalysisError}
          />
        )}

        {appState === 'results' && analysisResult && (
          <SimpleResults result={analysisResult} />
        )}
      </div>

      <style jsx>{`
        .analysis-page {
          min-height: 100vh;
          background: #f8f9fa;
        }

        .page-header {
          background: white;
          border-bottom: 1px solid #e9ecef;
          padding: 24px 0;
          margin-bottom: 24px;
        }

        .header-content {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 24px;
        }

        .header-content h1 {
          margin: 0 0 8px 0;
          color: #333;
          font-size: 28px;
          font-weight: 600;
        }

        .header-content p {
          margin: 0 0 16px 0;
          color: #666;
          font-size: 16px;
          line-height: 1.5;
        }

        .reset-button {
          background: #6c757d;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .reset-button:hover {
          background: #5a6268;
        }

        .error-banner {
          background: #f8d7da;
          color: #721c24;
          padding: 12px 24px;
          margin: 0 24px 24px 24px;
          border-radius: 6px;
          border: 1px solid #f5c6cb;
          display: flex;
          align-items: center;
          gap: 12px;
          max-width: 1200px;
          margin-left: auto;
          margin-right: auto;
        }

        .error-icon {
          font-size: 18px;
        }

        .error-text {
          flex: 1;
          font-weight: 500;
        }

        .error-close {
          background: none;
          border: none;
          color: #721c24;
          font-size: 18px;
          cursor: pointer;
          padding: 4px;
          border-radius: 4px;
          transition: all 0.2s ease;
        }

        .error-close:hover {
          background: rgba(114, 28, 36, 0.1);
        }

        .page-content {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 24px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .header-content {
            padding: 0 16px;
          }

          .header-content h1 {
            font-size: 24px;
          }

          .header-content p {
            font-size: 14px;
          }

          .page-content {
            padding: 0 16px;
          }

          .error-banner {
            margin: 0 16px 24px 16px;
          }
        }
      `}</style>
    </div>
  );
};

export default AnalysisPage;
