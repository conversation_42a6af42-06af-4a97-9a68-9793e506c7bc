/**
 * UI分析页面 - 主要的分析功能页面
 */
import React, { useState } from 'react';
import SimpleUpload from '../components/SimpleUpload';
import SimpleResults from '../components/SimpleResults';

const AnalysisPage = () => {
  const [analysisResult, setAnalysisResult] = useState(null);
  const [error, setError] = useState('');

  const handleUploadSuccess = (result) => {
    setAnalysisResult(result);
    setError('');
  };

  const handleUploadError = (errorMessage) => {
    setError(errorMessage);
  };

  return (
    <div style={{ height: '100vh', overflow: 'hidden' }}>
      <SimpleUpload
        onUploadSuccess={handleUploadSuccess}
        onUploadError={handleUploadError}
      />

      {/* 分析结果弹窗 */}
      {analysisResult && (
        <div style={{
          position: 'fixed',
          top: '0',
          left: '0',
          right: '0',
          bottom: '0',
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            background: 'white',
            padding: '24px',
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
            maxWidth: '800px',
            maxHeight: '80vh',
            overflow: 'auto',
            position: 'relative'
          }}>
            <SimpleResults result={analysisResult} />
            <button
              onClick={() => setAnalysisResult(null)}
              style={{
                position: 'absolute',
                top: '16px',
                right: '16px',
                background: 'none',
                border: 'none',
                fontSize: '20px',
                cursor: 'pointer',
                color: '#666'
              }}
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <div style={{
          position: 'fixed',
          top: '20px',
          right: '20px',
          background: '#fee',
          border: '1px solid #fcc',
          borderRadius: '8px',
          padding: '16px',
          color: '#c33',
          zIndex: 1001,
          maxWidth: '400px'
        }}>
          <strong>错误：</strong> {error}
          <button
            onClick={() => setError('')}
            style={{
              marginLeft: '12px',
              background: 'none',
              border: 'none',
              color: '#c33',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            ✕
          </button>
        </div>
      )}
    </div>
  );
};

export default AnalysisPage;
