<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI自动化分析平台 - 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
        .info {
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 UI自动化分析平台 - 功能测试</h1>
        <p>这个页面用于测试前后端连接和功能是否正常工作。</p>
    </div>

    <div class="container">
        <h3>🔗 连接测试</h3>
        <button onclick="testBackendConnection()">测试后端连接</button>
        <button onclick="testFileManagementAPI()">测试文件管理API</button>
        <button onclick="testSSEConnection()">测试SSE连接</button>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <div class="container">
        <h3>📋 测试日志</h3>
        <div id="log" class="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function testBackendConnection() {
            log('开始测试后端连接...', 'info');
            try {
                const response = await fetch('http://localhost:8001/health');
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 后端连接成功: ${data.message}`, 'success');
                } else {
                    log(`❌ 后端连接失败: HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ 后端连接错误: ${error.message}`, 'error');
            }
        }

        async function testFileManagementAPI() {
            log('开始测试文件管理API...', 'info');
            try {
                const formData = new FormData();
                formData.append('description', '测试文件管理界面功能');

                const response = await fetch('http://localhost:8001/api/v1/analyze/file-management', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 文件管理API调用成功: 任务ID ${data.task_id}`, 'success');
                    log(`📋 流式URL: ${data.stream_url}`, 'info');
                } else {
                    const errorData = await response.json();
                    log(`❌ 文件管理API失败: ${errorData.detail}`, 'error');
                }
            } catch (error) {
                log(`❌ 文件管理API错误: ${error.message}`, 'error');
            }
        }

        function testSSEConnection() {
            log('开始测试SSE连接...', 'info');
            const testTaskId = 'test-' + Date.now();
            
            try {
                const eventSource = new EventSource(`http://localhost:8001/api/v1/stream/${testTaskId}/test`);
                
                eventSource.onopen = () => {
                    log('✅ SSE连接已建立', 'success');
                };

                eventSource.addEventListener('connected', (event) => {
                    const data = JSON.parse(event.data);
                    log(`📡 连接确认: ${data.data.message}`, 'info');
                });

                eventSource.addEventListener('agent_progress', (event) => {
                    const data = JSON.parse(event.data);
                    log(`🔄 智能体进度: ${data.data.agent} - ${data.data.message}`, 'info');
                });

                eventSource.addEventListener('task_complete', (event) => {
                    const data = JSON.parse(event.data);
                    log(`✅ 任务完成: ${data.data.message}`, 'success');
                    eventSource.close();
                });

                eventSource.addEventListener('error', (event) => {
                    const data = JSON.parse(event.data);
                    log(`❌ SSE错误: ${data.data.message}`, 'error');
                    eventSource.close();
                });

                eventSource.onerror = (error) => {
                    log(`❌ SSE连接错误: ${error}`, 'error');
                    eventSource.close();
                };

                // 10秒后自动关闭连接
                setTimeout(() => {
                    if (eventSource.readyState !== EventSource.CLOSED) {
                        eventSource.close();
                        log('⏰ SSE连接测试超时，已关闭连接', 'info');
                    }
                }, 10000);

            } catch (error) {
                log(`❌ SSE连接创建失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动测试后端连接
        window.onload = () => {
            log('🚀 页面加载完成，开始自动测试...', 'info');
            testBackendConnection();
        };
    </script>
</body>
</html>
