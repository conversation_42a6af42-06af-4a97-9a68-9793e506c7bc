{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\components\\\\Sidebar.js\";\n/**\n * 侧边导航栏组件\n */\nimport React from 'react';\nimport './Sidebar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  currentPage,\n  onPageChange,\n  collapsed,\n  onToggleCollapse\n}) => {\n  const menuItems = [{\n    id: 'analysis',\n    name: 'UI分析',\n    icon: '🔍',\n    description: '上传界面截图进行AI分析'\n  }, {\n    id: 'history',\n    name: '历史记录',\n    icon: '📋',\n    description: '查看分析历史和结果'\n  }, {\n    id: 'settings',\n    name: '设置',\n    icon: '⚙️',\n    description: '系统配置和参数设置'\n  }, {\n    id: 'about',\n    name: '关于',\n    icon: 'ℹ️',\n    description: '平台信息和使用说明'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `sidebar ${collapsed ? 'collapsed' : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"logo-icon\",\n          children: \"\\uD83E\\uDD16\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"UI\\u81EA\\u52A8\\u5316\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u5206\\u6790\\u5E73\\u53F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"collapse-btn\",\n        onClick: onToggleCollapse,\n        title: collapsed ? '展开侧边栏' : '收起侧边栏',\n        children: collapsed ? '→' : '←'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"sidebar-nav\",\n      children: /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"nav-list\",\n        children: menuItems.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n          className: \"nav-item\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `nav-link ${currentPage === item.id ? 'active' : ''}`,\n            onClick: () => onPageChange(item.id),\n            title: collapsed ? item.name : item.description,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"nav-icon\",\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 17\n            }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"nav-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"nav-name\",\n                children: item.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"nav-desc\",\n                children: item.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this)\n        }, item.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"version-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u7248\\u672C v1.0.0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u57FA\\u4E8EAI\\u667A\\u80FD\\u4F53\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        .sidebar {\n          position: fixed;\n          left: 0;\n          top: 0;\n          height: 100vh;\n          width: 280px;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white;\n          transition: all 0.3s ease;\n          z-index: 1000;\n          display: flex;\n          flex-direction: column;\n          box-shadow: 2px 0 10px rgba(0,0,0,0.1);\n        }\n\n        .sidebar.collapsed {\n          width: 70px;\n        }\n\n        .sidebar-header {\n          padding: 20px;\n          border-bottom: 1px solid rgba(255,255,255,0.1);\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n        }\n\n        .logo {\n          display: flex;\n          align-items: center;\n          gap: 12px;\n        }\n\n        .logo-icon {\n          font-size: 32px;\n          line-height: 1;\n        }\n\n        .logo-text h2 {\n          margin: 0;\n          font-size: 18px;\n          font-weight: 600;\n          line-height: 1.2;\n        }\n\n        .logo-text p {\n          margin: 0;\n          font-size: 14px;\n          opacity: 0.8;\n          line-height: 1.2;\n        }\n\n        .collapse-btn {\n          background: rgba(255,255,255,0.1);\n          border: none;\n          color: white;\n          width: 30px;\n          height: 30px;\n          border-radius: 6px;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          transition: all 0.2s ease;\n          font-size: 14px;\n        }\n\n        .collapse-btn:hover {\n          background: rgba(255,255,255,0.2);\n        }\n\n        .sidebar-nav {\n          flex: 1;\n          padding: 20px 0;\n          overflow-y: auto;\n        }\n\n        .nav-list {\n          list-style: none;\n          margin: 0;\n          padding: 0;\n        }\n\n        .nav-item {\n          margin-bottom: 8px;\n        }\n\n        .nav-link {\n          width: 100%;\n          background: none;\n          border: none;\n          color: white;\n          padding: 16px 20px;\n          text-align: left;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          gap: 16px;\n          transition: all 0.2s ease;\n          border-radius: 0;\n          position: relative;\n        }\n\n        .nav-link:hover {\n          background: rgba(255,255,255,0.1);\n        }\n\n        .nav-link.active {\n          background: rgba(255,255,255,0.2);\n          border-right: 3px solid white;\n        }\n\n        .nav-icon {\n          font-size: 20px;\n          line-height: 1;\n          min-width: 20px;\n        }\n\n        .nav-content {\n          display: flex;\n          flex-direction: column;\n          gap: 2px;\n        }\n\n        .nav-name {\n          font-size: 16px;\n          font-weight: 500;\n          line-height: 1.2;\n        }\n\n        .nav-desc {\n          font-size: 12px;\n          opacity: 0.8;\n          line-height: 1.2;\n        }\n\n        .sidebar-footer {\n          padding: 20px;\n          border-top: 1px solid rgba(255,255,255,0.1);\n        }\n\n        .version-info p {\n          margin: 0;\n          font-size: 12px;\n          opacity: 0.7;\n          line-height: 1.4;\n        }\n\n        /* 响应式设计 */\n        @media (max-width: 768px) {\n          .sidebar {\n            width: 100%;\n            transform: translateX(-100%);\n          }\n          \n          .sidebar.collapsed {\n            width: 100%;\n            transform: translateX(-100%);\n          }\n          \n          .sidebar.mobile-open {\n            transform: translateX(0);\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Sidebar", "currentPage", "onPageChange", "collapsed", "onToggleCollapse", "menuItems", "id", "name", "icon", "description", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "title", "map", "item", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/components/Sidebar.js"], "sourcesContent": ["/**\n * 侧边导航栏组件\n */\nimport React from 'react';\nimport './Sidebar.css';\n\nconst Sidebar = ({ currentPage, onPageChange, collapsed, onToggleCollapse }) => {\n  const menuItems = [\n    {\n      id: 'analysis',\n      name: 'UI分析',\n      icon: '🔍',\n      description: '上传界面截图进行AI分析'\n    },\n    {\n      id: 'history',\n      name: '历史记录',\n      icon: '📋',\n      description: '查看分析历史和结果'\n    },\n    {\n      id: 'settings',\n      name: '设置',\n      icon: '⚙️',\n      description: '系统配置和参数设置'\n    },\n    {\n      id: 'about',\n      name: '关于',\n      icon: 'ℹ️',\n      description: '平台信息和使用说明'\n    }\n  ];\n\n  return (\n    <div className={`sidebar ${collapsed ? 'collapsed' : ''}`}>\n      {/* 头部 */}\n      <div className=\"sidebar-header\">\n        <div className=\"logo\">\n          <span className=\"logo-icon\">🤖</span>\n          {!collapsed && (\n            <div className=\"logo-text\">\n              <h2>UI自动化</h2>\n              <p>分析平台</p>\n            </div>\n          )}\n        </div>\n        <button \n          className=\"collapse-btn\"\n          onClick={onToggleCollapse}\n          title={collapsed ? '展开侧边栏' : '收起侧边栏'}\n        >\n          {collapsed ? '→' : '←'}\n        </button>\n      </div>\n\n      {/* 导航菜单 */}\n      <nav className=\"sidebar-nav\">\n        <ul className=\"nav-list\">\n          {menuItems.map((item) => (\n            <li key={item.id} className=\"nav-item\">\n              <button\n                className={`nav-link ${currentPage === item.id ? 'active' : ''}`}\n                onClick={() => onPageChange(item.id)}\n                title={collapsed ? item.name : item.description}\n              >\n                <span className=\"nav-icon\">{item.icon}</span>\n                {!collapsed && (\n                  <div className=\"nav-content\">\n                    <span className=\"nav-name\">{item.name}</span>\n                    <span className=\"nav-desc\">{item.description}</span>\n                  </div>\n                )}\n              </button>\n            </li>\n          ))}\n        </ul>\n      </nav>\n\n      {/* 底部信息 */}\n      {!collapsed && (\n        <div className=\"sidebar-footer\">\n          <div className=\"version-info\">\n            <p>版本 v1.0.0</p>\n            <p>基于AI智能体</p>\n          </div>\n        </div>\n      )}\n\n      <style>{`\n        .sidebar {\n          position: fixed;\n          left: 0;\n          top: 0;\n          height: 100vh;\n          width: 280px;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white;\n          transition: all 0.3s ease;\n          z-index: 1000;\n          display: flex;\n          flex-direction: column;\n          box-shadow: 2px 0 10px rgba(0,0,0,0.1);\n        }\n\n        .sidebar.collapsed {\n          width: 70px;\n        }\n\n        .sidebar-header {\n          padding: 20px;\n          border-bottom: 1px solid rgba(255,255,255,0.1);\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n        }\n\n        .logo {\n          display: flex;\n          align-items: center;\n          gap: 12px;\n        }\n\n        .logo-icon {\n          font-size: 32px;\n          line-height: 1;\n        }\n\n        .logo-text h2 {\n          margin: 0;\n          font-size: 18px;\n          font-weight: 600;\n          line-height: 1.2;\n        }\n\n        .logo-text p {\n          margin: 0;\n          font-size: 14px;\n          opacity: 0.8;\n          line-height: 1.2;\n        }\n\n        .collapse-btn {\n          background: rgba(255,255,255,0.1);\n          border: none;\n          color: white;\n          width: 30px;\n          height: 30px;\n          border-radius: 6px;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          transition: all 0.2s ease;\n          font-size: 14px;\n        }\n\n        .collapse-btn:hover {\n          background: rgba(255,255,255,0.2);\n        }\n\n        .sidebar-nav {\n          flex: 1;\n          padding: 20px 0;\n          overflow-y: auto;\n        }\n\n        .nav-list {\n          list-style: none;\n          margin: 0;\n          padding: 0;\n        }\n\n        .nav-item {\n          margin-bottom: 8px;\n        }\n\n        .nav-link {\n          width: 100%;\n          background: none;\n          border: none;\n          color: white;\n          padding: 16px 20px;\n          text-align: left;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          gap: 16px;\n          transition: all 0.2s ease;\n          border-radius: 0;\n          position: relative;\n        }\n\n        .nav-link:hover {\n          background: rgba(255,255,255,0.1);\n        }\n\n        .nav-link.active {\n          background: rgba(255,255,255,0.2);\n          border-right: 3px solid white;\n        }\n\n        .nav-icon {\n          font-size: 20px;\n          line-height: 1;\n          min-width: 20px;\n        }\n\n        .nav-content {\n          display: flex;\n          flex-direction: column;\n          gap: 2px;\n        }\n\n        .nav-name {\n          font-size: 16px;\n          font-weight: 500;\n          line-height: 1.2;\n        }\n\n        .nav-desc {\n          font-size: 12px;\n          opacity: 0.8;\n          line-height: 1.2;\n        }\n\n        .sidebar-footer {\n          padding: 20px;\n          border-top: 1px solid rgba(255,255,255,0.1);\n        }\n\n        .version-info p {\n          margin: 0;\n          font-size: 12px;\n          opacity: 0.7;\n          line-height: 1.4;\n        }\n\n        /* 响应式设计 */\n        @media (max-width: 768px) {\n          .sidebar {\n            width: 100%;\n            transform: translateX(-100%);\n          }\n          \n          .sidebar.collapsed {\n            width: 100%;\n            transform: translateX(-100%);\n          }\n          \n          .sidebar.mobile-open {\n            transform: translateX(0);\n          }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";AAAA;AACA;AACA;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAC;EAAEC,WAAW;EAAEC,YAAY;EAAEC,SAAS;EAAEC;AAAiB,CAAC,KAAK;EAC9E,MAAMC,SAAS,GAAG,CAChB;IACEC,EAAE,EAAE,UAAU;IACdC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE;EACf,CAAC,EACD;IACEH,EAAE,EAAE,SAAS;IACbC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE;EACf,CAAC,EACD;IACEH,EAAE,EAAE,UAAU;IACdC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE;EACf,CAAC,EACD;IACEH,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACEV,OAAA;IAAKW,SAAS,EAAE,WAAWP,SAAS,GAAG,WAAW,GAAG,EAAE,EAAG;IAAAQ,QAAA,gBAExDZ,OAAA;MAAKW,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BZ,OAAA;QAAKW,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBZ,OAAA;UAAMW,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACpC,CAACZ,SAAS,iBACTJ,OAAA;UAAKW,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBZ,OAAA;YAAAY,QAAA,EAAI;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACdhB,OAAA;YAAAY,QAAA,EAAG;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNhB,OAAA;QACEW,SAAS,EAAC,cAAc;QACxBM,OAAO,EAAEZ,gBAAiB;QAC1Ba,KAAK,EAAEd,SAAS,GAAG,OAAO,GAAG,OAAQ;QAAAQ,QAAA,EAEpCR,SAAS,GAAG,GAAG,GAAG;MAAG;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNhB,OAAA;MAAKW,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BZ,OAAA;QAAIW,SAAS,EAAC,UAAU;QAAAC,QAAA,EACrBN,SAAS,CAACa,GAAG,CAAEC,IAAI,iBAClBpB,OAAA;UAAkBW,SAAS,EAAC,UAAU;UAAAC,QAAA,eACpCZ,OAAA;YACEW,SAAS,EAAE,YAAYT,WAAW,KAAKkB,IAAI,CAACb,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;YACjEU,OAAO,EAAEA,CAAA,KAAMd,YAAY,CAACiB,IAAI,CAACb,EAAE,CAAE;YACrCW,KAAK,EAAEd,SAAS,GAAGgB,IAAI,CAACZ,IAAI,GAAGY,IAAI,CAACV,WAAY;YAAAE,QAAA,gBAEhDZ,OAAA;cAAMW,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAEQ,IAAI,CAACX;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC5C,CAACZ,SAAS,iBACTJ,OAAA;cAAKW,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BZ,OAAA;gBAAMW,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAEQ,IAAI,CAACZ;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7ChB,OAAA;gBAAMW,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAEQ,IAAI,CAACV;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC,GAbFI,IAAI,CAACb,EAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcZ,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGL,CAACZ,SAAS,iBACTJ,OAAA;MAAKW,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BZ,OAAA;QAAKW,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BZ,OAAA;UAAAY,QAAA,EAAG;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAChBhB,OAAA;UAAAY,QAAA,EAAG;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDhB,OAAA;MAAAY,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACK,EAAA,GA3PIpB,OAAO;AA6Pb,eAAeA,OAAO;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}