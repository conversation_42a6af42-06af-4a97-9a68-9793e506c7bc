{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\components\\\\SimpleUpload.js\",\n  _s = $RefreshSig$();\n/**\n * 简化的上传组件\n */\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SimpleUpload = ({\n  onUploadSuccess,\n  onUploadError\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const [showAnalysis, setShowAnalysis] = useState(false);\n  const [analysisProgress, setAnalysisProgress] = useState({\n    overall: 0,\n    currentStep: '准备开始分析...',\n    steps: [{\n      name: '初始化',\n      status: 'pending',\n      progress: 0\n    }, {\n      name: '元素分析和智能识别',\n      status: 'pending',\n      progress: 0\n    }, {\n      name: '生成自动化测试脚本',\n      status: 'pending',\n      progress: 0\n    }]\n  });\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n    setIsUploading(true);\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n      formData.append('project_name', additionalInfo.projectName);\n      formData.append('test_type', additionalInfo.testType);\n      formData.append('priority', additionalInfo.priority);\n      formData.append('expected_elements', additionalInfo.expectedElements);\n      formData.append('special_requirements', additionalInfo.specialRequirements);\n      const response = await fetch('http://localhost:8001/api/v1/upload', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n      const result = await response.json();\n      onUploadSuccess(result);\n\n      // 重置表单\n      setSelectedFile(null);\n      setDescription('');\n      event.target.reset();\n    } catch (error) {\n      console.error('Upload error:', error);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  const handleFileManagementDemo = async () => {\n    setIsUploading(true);\n    try {\n      const formData = new FormData();\n      formData.append('description', '文件管理界面功能分析 - 基于提供的截图进行专项分析');\n      const response = await fetch('http://localhost:8001/api/v1/analyze/file-management', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n      const result = await response.json();\n      onUploadSuccess(result);\n    } catch (error) {\n      console.error('File management demo error:', error);\n      onUploadError(error.message || '文件管理界面分析失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      background: 'white',\n      padding: '24px',\n      borderRadius: '12px',\n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n      marginBottom: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: 'block',\n            marginBottom: '12px',\n            fontWeight: '600',\n            color: '#333',\n            fontSize: '16px'\n          },\n          children: \"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"file-upload-area\",\n          onClick: () => document.getElementById('file-input').click(),\n          style: {\n            height: '240px',\n            // 调高3倍（原来约80px）\n            border: '2px dashed #667eea',\n            borderRadius: '12px',\n            background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            transition: 'all 0.3s ease',\n            position: 'relative',\n            overflow: 'hidden'\n          },\n          onDragOver: e => {\n            e.preventDefault();\n            e.currentTarget.style.borderColor = '#4f46e5';\n            e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';\n          },\n          onDragLeave: e => {\n            e.currentTarget.style.borderColor = '#667eea';\n            e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';\n          },\n          onDrop: e => {\n            e.preventDefault();\n            const files = e.dataTransfer.files;\n            if (files.length > 0) {\n              setSelectedFile(files[0]);\n            }\n            e.currentTarget.style.borderColor = '#667eea';\n            e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"file-input\",\n            type: \"file\",\n            accept: \"image/*\",\n            onChange: handleFileSelect,\n            style: {\n              display: 'none'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), selectedFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '16px',\n                color: '#667eea'\n              },\n              children: \"\\u2705\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: '0 0 8px 0',\n                color: '#333',\n                fontSize: '18px'\n              },\n              children: \"\\u6587\\u4EF6\\u5DF2\\u9009\\u62E9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0 0 8px 0',\n                color: '#666',\n                fontSize: '14px',\n                fontWeight: '500'\n              },\n              children: selectedFile.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0',\n                color: '#999',\n                fontSize: '12px'\n              },\n              children: [\"\\u5927\\u5C0F: \", (selectedFile.size / 1024 / 1024).toFixed(2), \" MB\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: e => {\n                e.stopPropagation();\n                setSelectedFile(null);\n                document.getElementById('file-input').value = '';\n              },\n              style: {\n                marginTop: '12px',\n                background: 'rgba(239, 68, 68, 0.1)',\n                color: '#ef4444',\n                border: '1px solid rgba(239, 68, 68, 0.3)',\n                padding: '6px 12px',\n                borderRadius: '6px',\n                fontSize: '12px',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              },\n              onMouseOver: e => {\n                e.target.style.background = 'rgba(239, 68, 68, 0.2)';\n              },\n              onMouseOut: e => {\n                e.target.style.background = 'rgba(239, 68, 68, 0.1)';\n              },\n              children: \"\\u91CD\\u65B0\\u9009\\u62E9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                marginBottom: '16px',\n                color: '#667eea'\n              },\n              children: \"\\uD83D\\uDCE4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: '0 0 8px 0',\n                color: '#333',\n                fontSize: '18px'\n              },\n              children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u4E0A\\u4F20\\u56FE\\u7247\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0 0 4px 0',\n                color: '#666',\n                fontSize: '14px'\n              },\n              children: \"\\u652F\\u6301 PNG\\u3001JPG\\u3001JPEG\\u3001GIF \\u683C\\u5F0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0',\n                color: '#999',\n                fontSize: '12px'\n              },\n              children: \"\\u6587\\u4EF6\\u5927\\u5C0F\\u4E0D\\u8D85\\u8FC7 10MB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '16px',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                color: 'white',\n                padding: '8px 20px',\n                borderRadius: '20px',\n                fontSize: '14px',\n                fontWeight: '500',\n                display: 'inline-block'\n              },\n              children: \"\\u9009\\u62E9\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"description\",\n          style: {\n            display: 'block',\n            marginBottom: '8px',\n            fontWeight: '600',\n            color: '#333',\n            fontSize: '16px'\n          },\n          children: \"\\uD83C\\uDFAF \\u754C\\u9762\\u529F\\u80FD\\u63CF\\u8FF0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          id: \"description\",\n          value: description,\n          onChange: e => setDescription(e.target.value),\n          placeholder: \"\\u8BF7\\u8BE6\\u7EC6\\u63CF\\u8FF0\\u8FD9\\u4E2A\\u754C\\u9762\\u7684\\u4E3B\\u8981\\u529F\\u80FD\\uFF0C\\u4F8B\\u5982\\uFF1A\\u8FD9\\u662F\\u4E00\\u4E2A\\u767B\\u5F55\\u9875\\u9762\\uFF0C\\u5305\\u542B\\u7528\\u6237\\u540D\\u5BC6\\u7801\\u8F93\\u5165\\u6846\\u548C\\u767B\\u5F55\\u6309\\u94AE...\",\n          rows: 4,\n          style: {\n            width: '100%',\n            padding: '12px',\n            border: '2px solid #e5e7eb',\n            borderRadius: '8px',\n            resize: 'vertical',\n            fontFamily: 'inherit',\n            fontSize: '14px',\n            lineHeight: '1.5',\n            transition: 'border-color 0.2s ease',\n            background: '#fafafa'\n          },\n          onFocus: e => {\n            e.target.style.borderColor = '#667eea';\n            e.target.style.background = '#ffffff';\n          },\n          onBlur: e => {\n            e.target.style.borderColor = '#e5e7eb';\n            e.target.style.background = '#fafafa';\n          },\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'right',\n            fontSize: '12px',\n            color: '#666',\n            marginTop: '4px'\n          },\n          children: [description.length, \"/500\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: 'block',\n            marginBottom: '8px',\n            fontWeight: '600',\n            color: '#333',\n            fontSize: '16px'\n          },\n          children: \"\\uD83D\\uDCCB \\u9879\\u76EE\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: '16px',\n            marginBottom: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '4px',\n                fontSize: '14px',\n                color: '#555'\n              },\n              children: \"\\u9879\\u76EE\\u540D\\u79F0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: additionalInfo.projectName,\n              onChange: e => setAdditionalInfo(prev => ({\n                ...prev,\n                projectName: e.target.value\n              })),\n              placeholder: \"\\u8F93\\u5165\\u9879\\u76EE\\u540D\\u79F0\",\n              style: {\n                width: '100%',\n                padding: '10px 12px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '6px',\n                fontSize: '14px',\n                transition: 'border-color 0.2s ease',\n                background: '#fafafa'\n              },\n              onFocus: e => {\n                e.target.style.borderColor = '#667eea';\n                e.target.style.background = '#ffffff';\n              },\n              onBlur: e => {\n                e.target.style.borderColor = '#e5e7eb';\n                e.target.style.background = '#fafafa';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '4px',\n                fontSize: '14px',\n                color: '#555'\n              },\n              children: \"\\u6D4B\\u8BD5\\u7C7B\\u578B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: additionalInfo.testType,\n              onChange: e => setAdditionalInfo(prev => ({\n                ...prev,\n                testType: e.target.value\n              })),\n              style: {\n                width: '100%',\n                padding: '10px 12px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '6px',\n                fontSize: '14px',\n                background: '#fafafa',\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"functional\",\n                children: \"\\u529F\\u80FD\\u6D4B\\u8BD5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"ui\",\n                children: \"UI\\u6D4B\\u8BD5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"integration\",\n                children: \"\\u96C6\\u6210\\u6D4B\\u8BD5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"regression\",\n                children: \"\\u56DE\\u5F52\\u6D4B\\u8BD5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: '1fr 1fr',\n            gap: '16px',\n            marginBottom: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '4px',\n                fontSize: '14px',\n                color: '#555'\n              },\n              children: \"\\u4F18\\u5148\\u7EA7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: additionalInfo.priority,\n              onChange: e => setAdditionalInfo(prev => ({\n                ...prev,\n                priority: e.target.value\n              })),\n              style: {\n                width: '100%',\n                padding: '10px 12px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '6px',\n                fontSize: '14px',\n                background: '#fafafa',\n                cursor: 'pointer'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"low\",\n                children: \"\\u4F4E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"medium\",\n                children: \"\\u4E2D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"high\",\n                children: \"\\u9AD8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"critical\",\n                children: \"\\u7D27\\u6025\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                marginBottom: '4px',\n                fontSize: '14px',\n                color: '#555'\n              },\n              children: \"\\u9884\\u671F\\u5143\\u7D20\\u6570\\u91CF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: additionalInfo.expectedElements,\n              onChange: e => setAdditionalInfo(prev => ({\n                ...prev,\n                expectedElements: e.target.value\n              })),\n              placeholder: \"\\u4F8B\\u5982\\uFF1A5-10\\u4E2A\\u6309\\u94AE\\uFF0C2\\u4E2A\\u8F93\\u5165\\u6846\",\n              style: {\n                width: '100%',\n                padding: '10px 12px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '6px',\n                fontSize: '14px',\n                transition: 'border-color 0.2s ease',\n                background: '#fafafa'\n              },\n              onFocus: e => {\n                e.target.style.borderColor = '#667eea';\n                e.target.style.background = '#ffffff';\n              },\n              onBlur: e => {\n                e.target.style.borderColor = '#e5e7eb';\n                e.target.style.background = '#fafafa';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: 'block',\n            marginBottom: '8px',\n            fontSize: '14px',\n            color: '#555'\n          },\n          children: \"\\u7279\\u6B8A\\u8981\\u6C42\\u6216\\u6CE8\\u610F\\u4E8B\\u9879\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: additionalInfo.specialRequirements,\n          onChange: e => setAdditionalInfo(prev => ({\n            ...prev,\n            specialRequirements: e.target.value\n          })),\n          placeholder: \"\\u4F8B\\u5982\\uFF1A\\u9700\\u8981\\u7279\\u522B\\u5173\\u6CE8\\u54CD\\u5E94\\u5F0F\\u8BBE\\u8BA1\\u3001\\u65E0\\u969C\\u788D\\u8BBF\\u95EE\\u3001\\u7279\\u5B9A\\u6D4F\\u89C8\\u5668\\u517C\\u5BB9\\u6027\\u7B49...\",\n          rows: 3,\n          style: {\n            width: '100%',\n            padding: '12px',\n            border: '2px solid #e5e7eb',\n            borderRadius: '8px',\n            resize: 'vertical',\n            fontFamily: 'inherit',\n            fontSize: '14px',\n            lineHeight: '1.5',\n            transition: 'border-color 0.2s ease',\n            background: '#fafafa'\n          },\n          onFocus: e => {\n            e.target.style.borderColor = '#667eea';\n            e.target.style.background = '#ffffff';\n          },\n          onBlur: e => {\n            e.target.style.borderColor = '#e5e7eb';\n            e.target.style.background = '#fafafa';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: isUploading || !selectedFile || !description.trim(),\n        style: {\n          background: isUploading ? '#6c757d' : '#007bff',\n          color: 'white',\n          border: 'none',\n          padding: '12px 24px',\n          borderRadius: '6px',\n          fontSize: '16px',\n          fontWeight: '500',\n          cursor: isUploading ? 'not-allowed' : 'pointer',\n          transition: 'all 0.3s ease',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        },\n        children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: '16px',\n              height: '16px',\n              border: '2px solid transparent',\n              borderTop: '2px solid white',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 15\n          }, this), \"\\u5206\\u6790\\u4E2D...\"]\n        }, void 0, true) : '🚀 开始分析'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        margin: '24px 0',\n        color: '#666',\n        fontSize: '14px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          height: '1px',\n          background: '#e9ecef'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          padding: '0 16px'\n        },\n        children: \"\\u6216\\u8005\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          height: '1px',\n          background: '#e9ecef'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 454,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        style: {\n          margin: '0 0 12px 0',\n          color: '#333',\n          fontSize: '16px'\n        },\n        children: \"\\uD83D\\uDCC1 \\u4E13\\u9879\\u5206\\u6790\\u6F14\\u793A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: '0 0 16px 0',\n          color: '#666',\n          fontSize: '14px'\n        },\n        children: \"\\u57FA\\u4E8E\\u60A8\\u63D0\\u4F9B\\u7684\\u6587\\u4EF6\\u7BA1\\u7406\\u754C\\u9762\\u622A\\u56FE\\uFF0C\\u751F\\u6210\\u4E13\\u95E8\\u7684UI\\u81EA\\u52A8\\u5316\\u6D4B\\u8BD5\\u811A\\u672C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: handleFileManagementDemo,\n        disabled: isUploading,\n        style: {\n          background: isUploading ? '#6c757d' : '#28a745',\n          color: 'white',\n          border: 'none',\n          padding: '12px 32px',\n          borderRadius: '6px',\n          fontSize: '16px',\n          fontWeight: '500',\n          cursor: isUploading ? 'not-allowed' : 'pointer',\n          transition: 'all 0.3s ease',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px',\n          margin: '0 auto'\n        },\n        children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              width: '16px',\n              height: '16px',\n              border: '2px solid transparent',\n              borderTop: '2px solid white',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 15\n          }, this), \"\\u5206\\u6790\\u4E2D...\"]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: \"\\uD83D\\uDCCA \\u5206\\u6790\\u6587\\u4EF6\\u7BA1\\u7406\\u754C\\u9762\"\n        }, void 0, false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 467,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 514,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleUpload, \"uXdqIXhBuTsq5cLMg12a1LPI6l0=\");\n_c = SimpleUpload;\nexport default SimpleUpload;\nvar _c;\n$RefreshReg$(_c, \"SimpleUpload\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SimpleUpload", "onUploadSuccess", "onUploadError", "_s", "selectedFile", "setSelectedFile", "description", "setDescription", "isUploading", "setIsUploading", "showAnalysis", "setShowAnalysis", "analysisProgress", "setAnalysisProgress", "overall", "currentStep", "steps", "name", "status", "progress", "handleFileSelect", "event", "file", "target", "files", "handleSubmit", "preventDefault", "trim", "formData", "FormData", "append", "additionalInfo", "projectName", "testType", "priority", "expectedElements", "specialRequirements", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "result", "reset", "error", "console", "message", "handleFileManagementDemo", "style", "background", "padding", "borderRadius", "boxShadow", "marginBottom", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "display", "fontWeight", "color", "fontSize", "className", "onClick", "document", "getElementById", "click", "height", "border", "flexDirection", "alignItems", "justifyContent", "cursor", "transition", "position", "overflow", "onDragOver", "e", "currentTarget", "borderColor", "onDragLeave", "onDrop", "dataTransfer", "length", "id", "type", "accept", "onChange", "textAlign", "margin", "size", "toFixed", "stopPropagation", "value", "marginTop", "onMouseOver", "onMouseOut", "htmlFor", "placeholder", "rows", "width", "resize", "fontFamily", "lineHeight", "onFocus", "onBlur", "required", "gridTemplateColumns", "gap", "setAdditionalInfo", "prev", "disabled", "borderTop", "animation", "flex", "jsx", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/components/SimpleUpload.js"], "sourcesContent": ["/**\n * 简化的上传组件\n */\nimport React, { useState } from 'react';\n\nconst SimpleUpload = ({ onUploadSuccess, onUploadError }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const [showAnalysis, setShowAnalysis] = useState(false);\n  const [analysisProgress, setAnalysisProgress] = useState({\n    overall: 0,\n    currentStep: '准备开始分析...',\n    steps: [\n      { name: '初始化', status: 'pending', progress: 0 },\n      { name: '元素分析和智能识别', status: 'pending', progress: 0 },\n      { name: '生成自动化测试脚本', status: 'pending', progress: 0 }\n    ]\n  });\n\n  const handleFileSelect = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n\n  const handleSubmit = async (event) => {\n    event.preventDefault();\n\n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n\n    setIsUploading(true);\n\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n      formData.append('project_name', additionalInfo.projectName);\n      formData.append('test_type', additionalInfo.testType);\n      formData.append('priority', additionalInfo.priority);\n      formData.append('expected_elements', additionalInfo.expectedElements);\n      formData.append('special_requirements', additionalInfo.specialRequirements);\n\n      const response = await fetch('http://localhost:8001/api/v1/upload', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      onUploadSuccess(result);\n\n      // 重置表单\n      setSelectedFile(null);\n      setDescription('');\n      event.target.reset();\n    } catch (error) {\n      console.error('Upload error:', error);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const handleFileManagementDemo = async () => {\n    setIsUploading(true);\n\n    try {\n      const formData = new FormData();\n      formData.append('description', '文件管理界面功能分析 - 基于提供的截图进行专项分析');\n\n      const response = await fetch('http://localhost:8001/api/v1/analyze/file-management', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      onUploadSuccess(result);\n\n    } catch (error) {\n      console.error('File management demo error:', error);\n      onUploadError(error.message || '文件管理界面分析失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  return (\n    <div style={{ \n      background: 'white', \n      padding: '24px', \n      borderRadius: '12px', \n      boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n      marginBottom: '24px'\n    }}>\n      <h3>📁 上传UI界面截图</h3>\n      <form onSubmit={handleSubmit}>\n        <div style={{ marginBottom: '24px' }}>\n          <label style={{ display: 'block', marginBottom: '12px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n            📁 上传UI界面截图\n          </label>\n          <div\n            className=\"file-upload-area\"\n            onClick={() => document.getElementById('file-input').click()}\n            style={{\n              height: '240px', // 调高3倍（原来约80px）\n              border: '2px dashed #667eea',\n              borderRadius: '12px',\n              background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.3s ease',\n              position: 'relative',\n              overflow: 'hidden'\n            }}\n            onDragOver={(e) => {\n              e.preventDefault();\n              e.currentTarget.style.borderColor = '#4f46e5';\n              e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';\n            }}\n            onDragLeave={(e) => {\n              e.currentTarget.style.borderColor = '#667eea';\n              e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';\n            }}\n            onDrop={(e) => {\n              e.preventDefault();\n              const files = e.dataTransfer.files;\n              if (files.length > 0) {\n                setSelectedFile(files[0]);\n              }\n              e.currentTarget.style.borderColor = '#667eea';\n              e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';\n            }}\n          >\n            <input\n              id=\"file-input\"\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={handleFileSelect}\n              style={{ display: 'none' }}\n            />\n\n            {selectedFile ? (\n              <div style={{ textAlign: 'center', padding: '20px' }}>\n                <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>✅</div>\n                <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>文件已选择</h3>\n                <p style={{ margin: '0 0 8px 0', color: '#666', fontSize: '14px', fontWeight: '500' }}>\n                  {selectedFile.name}\n                </p>\n                <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>\n                  大小: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB\n                </p>\n                <button\n                  type=\"button\"\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    setSelectedFile(null);\n                    document.getElementById('file-input').value = '';\n                  }}\n                  style={{\n                    marginTop: '12px',\n                    background: 'rgba(239, 68, 68, 0.1)',\n                    color: '#ef4444',\n                    border: '1px solid rgba(239, 68, 68, 0.3)',\n                    padding: '6px 12px',\n                    borderRadius: '6px',\n                    fontSize: '12px',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseOver={(e) => {\n                    e.target.style.background = 'rgba(239, 68, 68, 0.2)';\n                  }}\n                  onMouseOut={(e) => {\n                    e.target.style.background = 'rgba(239, 68, 68, 0.1)';\n                  }}\n                >\n                  重新选择\n                </button>\n              </div>\n            ) : (\n              <div style={{ textAlign: 'center', padding: '20px' }}>\n                <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>📤</div>\n                <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>点击或拖拽上传图片</h3>\n                <p style={{ margin: '0 0 4px 0', color: '#666', fontSize: '14px' }}>\n                  支持 PNG、JPG、JPEG、GIF 格式\n                </p>\n                <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>\n                  文件大小不超过 10MB\n                </p>\n                <div style={{\n                  marginTop: '16px',\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  color: 'white',\n                  padding: '8px 20px',\n                  borderRadius: '20px',\n                  fontSize: '14px',\n                  fontWeight: '500',\n                  display: 'inline-block'\n                }}>\n                  选择文件\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* 界面功能描述 */}\n        <div style={{ marginBottom: '20px' }}>\n          <label htmlFor=\"description\" style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n            🎯 界面功能描述\n          </label>\n          <textarea\n            id=\"description\"\n            value={description}\n            onChange={(e) => setDescription(e.target.value)}\n            placeholder=\"请详细描述这个界面的主要功能，例如：这是一个登录页面，包含用户名密码输入框和登录按钮...\"\n            rows={4}\n            style={{\n              width: '100%',\n              padding: '12px',\n              border: '2px solid #e5e7eb',\n              borderRadius: '8px',\n              resize: 'vertical',\n              fontFamily: 'inherit',\n              fontSize: '14px',\n              lineHeight: '1.5',\n              transition: 'border-color 0.2s ease',\n              background: '#fafafa'\n            }}\n            onFocus={(e) => {\n              e.target.style.borderColor = '#667eea';\n              e.target.style.background = '#ffffff';\n            }}\n            onBlur={(e) => {\n              e.target.style.borderColor = '#e5e7eb';\n              e.target.style.background = '#fafafa';\n            }}\n            required\n          />\n          <div style={{ textAlign: 'right', fontSize: '12px', color: '#666', marginTop: '4px' }}>\n            {description.length}/500\n          </div>\n        </div>\n\n        {/* 项目信息 */}\n        <div style={{ marginBottom: '20px' }}>\n          <label style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n            📋 项目信息\n          </label>\n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>\n            <div>\n              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', color: '#555' }}>\n                项目名称\n              </label>\n              <input\n                type=\"text\"\n                value={additionalInfo.projectName}\n                onChange={(e) => setAdditionalInfo(prev => ({ ...prev, projectName: e.target.value }))}\n                placeholder=\"输入项目名称\"\n                style={{\n                  width: '100%',\n                  padding: '10px 12px',\n                  border: '2px solid #e5e7eb',\n                  borderRadius: '6px',\n                  fontSize: '14px',\n                  transition: 'border-color 0.2s ease',\n                  background: '#fafafa'\n                }}\n                onFocus={(e) => {\n                  e.target.style.borderColor = '#667eea';\n                  e.target.style.background = '#ffffff';\n                }}\n                onBlur={(e) => {\n                  e.target.style.borderColor = '#e5e7eb';\n                  e.target.style.background = '#fafafa';\n                }}\n              />\n            </div>\n            <div>\n              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', color: '#555' }}>\n                测试类型\n              </label>\n              <select\n                value={additionalInfo.testType}\n                onChange={(e) => setAdditionalInfo(prev => ({ ...prev, testType: e.target.value }))}\n                style={{\n                  width: '100%',\n                  padding: '10px 12px',\n                  border: '2px solid #e5e7eb',\n                  borderRadius: '6px',\n                  fontSize: '14px',\n                  background: '#fafafa',\n                  cursor: 'pointer'\n                }}\n              >\n                <option value=\"functional\">功能测试</option>\n                <option value=\"ui\">UI测试</option>\n                <option value=\"integration\">集成测试</option>\n                <option value=\"regression\">回归测试</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* 测试优先级和预期元素 */}\n        <div style={{ marginBottom: '20px' }}>\n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>\n            <div>\n              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', color: '#555' }}>\n                优先级\n              </label>\n              <select\n                value={additionalInfo.priority}\n                onChange={(e) => setAdditionalInfo(prev => ({ ...prev, priority: e.target.value }))}\n                style={{\n                  width: '100%',\n                  padding: '10px 12px',\n                  border: '2px solid #e5e7eb',\n                  borderRadius: '6px',\n                  fontSize: '14px',\n                  background: '#fafafa',\n                  cursor: 'pointer'\n                }}\n              >\n                <option value=\"low\">低</option>\n                <option value=\"medium\">中</option>\n                <option value=\"high\">高</option>\n                <option value=\"critical\">紧急</option>\n              </select>\n            </div>\n            <div>\n              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px', color: '#555' }}>\n                预期元素数量\n              </label>\n              <input\n                type=\"text\"\n                value={additionalInfo.expectedElements}\n                onChange={(e) => setAdditionalInfo(prev => ({ ...prev, expectedElements: e.target.value }))}\n                placeholder=\"例如：5-10个按钮，2个输入框\"\n                style={{\n                  width: '100%',\n                  padding: '10px 12px',\n                  border: '2px solid #e5e7eb',\n                  borderRadius: '6px',\n                  fontSize: '14px',\n                  transition: 'border-color 0.2s ease',\n                  background: '#fafafa'\n                }}\n                onFocus={(e) => {\n                  e.target.style.borderColor = '#667eea';\n                  e.target.style.background = '#ffffff';\n                }}\n                onBlur={(e) => {\n                  e.target.style.borderColor = '#e5e7eb';\n                  e.target.style.background = '#fafafa';\n                }}\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* 特殊要求 */}\n        <div style={{ marginBottom: '24px' }}>\n          <label style={{ display: 'block', marginBottom: '8px', fontSize: '14px', color: '#555' }}>\n            特殊要求或注意事项\n          </label>\n          <textarea\n            value={additionalInfo.specialRequirements}\n            onChange={(e) => setAdditionalInfo(prev => ({ ...prev, specialRequirements: e.target.value }))}\n            placeholder=\"例如：需要特别关注响应式设计、无障碍访问、特定浏览器兼容性等...\"\n            rows={3}\n            style={{\n              width: '100%',\n              padding: '12px',\n              border: '2px solid #e5e7eb',\n              borderRadius: '8px',\n              resize: 'vertical',\n              fontFamily: 'inherit',\n              fontSize: '14px',\n              lineHeight: '1.5',\n              transition: 'border-color 0.2s ease',\n              background: '#fafafa'\n            }}\n            onFocus={(e) => {\n              e.target.style.borderColor = '#667eea';\n              e.target.style.background = '#ffffff';\n            }}\n            onBlur={(e) => {\n              e.target.style.borderColor = '#e5e7eb';\n              e.target.style.background = '#fafafa';\n            }}\n          />\n        </div>\n\n        <button\n          type=\"submit\"\n          disabled={isUploading || !selectedFile || !description.trim()}\n          style={{\n            background: isUploading ? '#6c757d' : '#007bff',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            fontSize: '16px',\n            fontWeight: '500',\n            cursor: isUploading ? 'not-allowed' : 'pointer',\n            transition: 'all 0.3s ease',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          }}\n        >\n          {isUploading ? (\n            <>\n              <span style={{\n                width: '16px',\n                height: '16px',\n                border: '2px solid transparent',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }}></span>\n              分析中...\n            </>\n          ) : (\n            '🚀 开始分析'\n          )}\n        </button>\n      </form>\n\n      {/* 分隔线 */}\n      <div style={{\n        display: 'flex',\n        alignItems: 'center',\n        margin: '24px 0',\n        color: '#666',\n        fontSize: '14px'\n      }}>\n        <div style={{ flex: 1, height: '1px', background: '#e9ecef' }}></div>\n        <span style={{ padding: '0 16px' }}>或者</span>\n        <div style={{ flex: 1, height: '1px', background: '#e9ecef' }}></div>\n      </div>\n\n      {/* 文件管理界面演示按钮 */}\n      <div style={{ textAlign: 'center' }}>\n        <h4 style={{ margin: '0 0 12px 0', color: '#333', fontSize: '16px' }}>\n          📁 专项分析演示\n        </h4>\n        <p style={{ margin: '0 0 16px 0', color: '#666', fontSize: '14px' }}>\n          基于您提供的文件管理界面截图，生成专门的UI自动化测试脚本\n        </p>\n        <button\n          type=\"button\"\n          onClick={handleFileManagementDemo}\n          disabled={isUploading}\n          style={{\n            background: isUploading ? '#6c757d' : '#28a745',\n            color: 'white',\n            border: 'none',\n            padding: '12px 32px',\n            borderRadius: '6px',\n            fontSize: '16px',\n            fontWeight: '500',\n            cursor: isUploading ? 'not-allowed' : 'pointer',\n            transition: 'all 0.3s ease',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px',\n            margin: '0 auto'\n          }}\n        >\n          {isUploading ? (\n            <>\n              <span style={{\n                width: '16px',\n                height: '16px',\n                border: '2px solid transparent',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }}></span>\n              分析中...\n            </>\n          ) : (\n            <>\n              📊 分析文件管理界面\n            </>\n          )}\n        </button>\n      </div>\n\n      <style jsx>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default SimpleUpload;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,YAAY,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlB,QAAQ,CAAC;IACvDmB,OAAO,EAAE,CAAC;IACVC,WAAW,EAAE,WAAW;IACxBC,KAAK,EAAE,CACL;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAE,CAAC,EAC/C;MAAEF,IAAI,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAE,CAAC,EACrD;MAAEF,IAAI,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAE,CAAC;EAEzD,CAAC,CAAC;EAEF,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACRjB,eAAe,CAACiB,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOJ,KAAK,IAAK;IACpCA,KAAK,CAACK,cAAc,CAAC,CAAC;IAEtB,IAAI,CAACtB,YAAY,EAAE;MACjBF,aAAa,CAAC,SAAS,CAAC;MACxB;IACF;IAEA,IAAI,CAACI,WAAW,CAACqB,IAAI,CAAC,CAAC,EAAE;MACvBzB,aAAa,CAAC,WAAW,CAAC;MAC1B;IACF;IAEAO,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,MAAMmB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE1B,YAAY,CAAC;MAC3CwB,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAExB,WAAW,CAACqB,IAAI,CAAC,CAAC,CAAC;MAClDC,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAEC,cAAc,CAACC,WAAW,CAAC;MAC3DJ,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEC,cAAc,CAACE,QAAQ,CAAC;MACrDL,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEC,cAAc,CAACG,QAAQ,CAAC;MACpDN,QAAQ,CAACE,MAAM,CAAC,mBAAmB,EAAEC,cAAc,CAACI,gBAAgB,CAAC;MACrEP,QAAQ,CAACE,MAAM,CAAC,sBAAsB,EAAEC,cAAc,CAACK,mBAAmB,CAAC;MAE3E,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,qCAAqC,EAAE;QAClEC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEZ;MACR,CAAC,CAAC;MAEF,IAAI,CAACS,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,uBAAuBR,QAAQ,CAACnB,MAAM,EAAE,CAAC;MAC/E;MAEA,MAAM4B,MAAM,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MACpC1C,eAAe,CAAC6C,MAAM,CAAC;;MAEvB;MACAzC,eAAe,CAAC,IAAI,CAAC;MACrBE,cAAc,CAAC,EAAE,CAAC;MAClBc,KAAK,CAACE,MAAM,CAACwB,KAAK,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC9C,aAAa,CAAC8C,KAAK,CAACE,OAAO,IAAI,UAAU,CAAC;IAC5C,CAAC,SAAS;MACRzC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAM0C,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C1C,cAAc,CAAC,IAAI,CAAC;IAEpB,IAAI;MACF,MAAMmB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE,4BAA4B,CAAC;MAE5D,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAAC,sDAAsD,EAAE;QACnFC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEZ;MACR,CAAC,CAAC;MAEF,IAAI,CAACS,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,uBAAuBR,QAAQ,CAACnB,MAAM,EAAE,CAAC;MAC/E;MAEA,MAAM4B,MAAM,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MACpC1C,eAAe,CAAC6C,MAAM,CAAC;IAEzB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD9C,aAAa,CAAC8C,KAAK,CAACE,OAAO,IAAI,gBAAgB,CAAC;IAClD,CAAC,SAAS;MACRzC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,oBACEZ,OAAA;IAAKuD,KAAK,EAAE;MACVC,UAAU,EAAE,OAAO;MACnBC,OAAO,EAAE,MAAM;MACfC,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,4BAA4B;MACvCC,YAAY,EAAE;IAChB,CAAE;IAAAC,QAAA,gBACA7D,OAAA;MAAA6D,QAAA,EAAI;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpBjE,OAAA;MAAMkE,QAAQ,EAAEtC,YAAa;MAAAiC,QAAA,gBAC3B7D,OAAA;QAAKuD,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnC7D,OAAA;UAAOuD,KAAK,EAAE;YAAEY,OAAO,EAAE,OAAO;YAAEP,YAAY,EAAE,MAAM;YAAEQ,UAAU,EAAE,KAAK;YAAEC,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE9G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRjE,OAAA;UACEuE,SAAS,EAAC,kBAAkB;UAC5BC,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAACC,KAAK,CAAC,CAAE;UAC7DpB,KAAK,EAAE;YACLqB,MAAM,EAAE,OAAO;YAAE;YACjBC,MAAM,EAAE,oBAAoB;YAC5BnB,YAAY,EAAE,MAAM;YACpBF,UAAU,EAAEjD,YAAY,GAAG,mDAAmD,GAAG,mDAAmD;YACpI4D,OAAO,EAAE,MAAM;YACfW,aAAa,EAAE,QAAQ;YACvBC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,MAAM,EAAE,SAAS;YACjBC,UAAU,EAAE,eAAe;YAC3BC,QAAQ,EAAE,UAAU;YACpBC,QAAQ,EAAE;UACZ,CAAE;UACFC,UAAU,EAAGC,CAAC,IAAK;YACjBA,CAAC,CAACzD,cAAc,CAAC,CAAC;YAClByD,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACiC,WAAW,GAAG,SAAS;YAC7CF,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACC,UAAU,GAAG,mDAAmD;UACxF,CAAE;UACFiC,WAAW,EAAGH,CAAC,IAAK;YAClBA,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACiC,WAAW,GAAG,SAAS;YAC7CF,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACC,UAAU,GAAGjD,YAAY,GAAG,mDAAmD,GAAG,mDAAmD;UAC7J,CAAE;UACFmF,MAAM,EAAGJ,CAAC,IAAK;YACbA,CAAC,CAACzD,cAAc,CAAC,CAAC;YAClB,MAAMF,KAAK,GAAG2D,CAAC,CAACK,YAAY,CAAChE,KAAK;YAClC,IAAIA,KAAK,CAACiE,MAAM,GAAG,CAAC,EAAE;cACpBpF,eAAe,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B;YACA2D,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACiC,WAAW,GAAG,SAAS;YAC7CF,CAAC,CAACC,aAAa,CAAChC,KAAK,CAACC,UAAU,GAAG,mDAAmD;UACxF,CAAE;UAAAK,QAAA,gBAEF7D,OAAA;YACE6F,EAAE,EAAC,YAAY;YACfC,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,SAAS;YAChBC,QAAQ,EAAEzE,gBAAiB;YAC3BgC,KAAK,EAAE;cAAEY,OAAO,EAAE;YAAO;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,EAED1D,YAAY,gBACXP,OAAA;YAAKuD,KAAK,EAAE;cAAE0C,SAAS,EAAE,QAAQ;cAAExC,OAAO,EAAE;YAAO,CAAE;YAAAI,QAAA,gBACnD7D,OAAA;cAAKuD,KAAK,EAAE;gBAAEe,QAAQ,EAAE,MAAM;gBAAEV,YAAY,EAAE,MAAM;gBAAES,KAAK,EAAE;cAAU,CAAE;cAAAR,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjFjE,OAAA;cAAIuD,KAAK,EAAE;gBAAE2C,MAAM,EAAE,WAAW;gBAAE7B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/EjE,OAAA;cAAGuD,KAAK,EAAE;gBAAE2C,MAAM,EAAE,WAAW;gBAAE7B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE,MAAM;gBAAEF,UAAU,EAAE;cAAM,CAAE;cAAAP,QAAA,EACnFtD,YAAY,CAACa;YAAI;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACJjE,OAAA;cAAGuD,KAAK,EAAE;gBAAE2C,MAAM,EAAE,GAAG;gBAAE7B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,GAAC,gBACtD,EAAC,CAACtD,YAAY,CAAC4F,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KACpD;YAAA;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJjE,OAAA;cACE8F,IAAI,EAAC,QAAQ;cACbtB,OAAO,EAAGc,CAAC,IAAK;gBACdA,CAAC,CAACe,eAAe,CAAC,CAAC;gBACnB7F,eAAe,CAAC,IAAI,CAAC;gBACrBiE,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAAC4B,KAAK,GAAG,EAAE;cAClD,CAAE;cACF/C,KAAK,EAAE;gBACLgD,SAAS,EAAE,MAAM;gBACjB/C,UAAU,EAAE,wBAAwB;gBACpCa,KAAK,EAAE,SAAS;gBAChBQ,MAAM,EAAE,kCAAkC;gBAC1CpB,OAAO,EAAE,UAAU;gBACnBC,YAAY,EAAE,KAAK;gBACnBY,QAAQ,EAAE,MAAM;gBAChBW,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE;cACd,CAAE;cACFsB,WAAW,EAAGlB,CAAC,IAAK;gBAClBA,CAAC,CAAC5D,MAAM,CAAC6B,KAAK,CAACC,UAAU,GAAG,wBAAwB;cACtD,CAAE;cACFiD,UAAU,EAAGnB,CAAC,IAAK;gBACjBA,CAAC,CAAC5D,MAAM,CAAC6B,KAAK,CAACC,UAAU,GAAG,wBAAwB;cACtD,CAAE;cAAAK,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAENjE,OAAA;YAAKuD,KAAK,EAAE;cAAE0C,SAAS,EAAE,QAAQ;cAAExC,OAAO,EAAE;YAAO,CAAE;YAAAI,QAAA,gBACnD7D,OAAA;cAAKuD,KAAK,EAAE;gBAAEe,QAAQ,EAAE,MAAM;gBAAEV,YAAY,EAAE,MAAM;gBAAES,KAAK,EAAE;cAAU,CAAE;cAAAR,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClFjE,OAAA;cAAIuD,KAAK,EAAE;gBAAE2C,MAAM,EAAE,WAAW;gBAAE7B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnFjE,OAAA;cAAGuD,KAAK,EAAE;gBAAE2C,MAAM,EAAE,WAAW;gBAAE7B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJjE,OAAA;cAAGuD,KAAK,EAAE;gBAAE2C,MAAM,EAAE,GAAG;gBAAE7B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAAT,QAAA,EAAC;YAE5D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJjE,OAAA;cAAKuD,KAAK,EAAE;gBACVgD,SAAS,EAAE,MAAM;gBACjB/C,UAAU,EAAE,mDAAmD;gBAC/Da,KAAK,EAAE,OAAO;gBACdZ,OAAO,EAAE,UAAU;gBACnBC,YAAY,EAAE,MAAM;gBACpBY,QAAQ,EAAE,MAAM;gBAChBF,UAAU,EAAE,KAAK;gBACjBD,OAAO,EAAE;cACX,CAAE;cAAAN,QAAA,EAAC;YAEH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjE,OAAA;QAAKuD,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnC7D,OAAA;UAAO0G,OAAO,EAAC,aAAa;UAACnD,KAAK,EAAE;YAAEY,OAAO,EAAE,OAAO;YAAEP,YAAY,EAAE,KAAK;YAAEQ,UAAU,EAAE,KAAK;YAAEC,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAEnI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRjE,OAAA;UACE6F,EAAE,EAAC,aAAa;UAChBS,KAAK,EAAE7F,WAAY;UACnBuF,QAAQ,EAAGV,CAAC,IAAK5E,cAAc,CAAC4E,CAAC,CAAC5D,MAAM,CAAC4E,KAAK,CAAE;UAChDK,WAAW,EAAC,iQAA+C;UAC3DC,IAAI,EAAE,CAAE;UACRrD,KAAK,EAAE;YACLsD,KAAK,EAAE,MAAM;YACbpD,OAAO,EAAE,MAAM;YACfoB,MAAM,EAAE,mBAAmB;YAC3BnB,YAAY,EAAE,KAAK;YACnBoD,MAAM,EAAE,UAAU;YAClBC,UAAU,EAAE,SAAS;YACrBzC,QAAQ,EAAE,MAAM;YAChB0C,UAAU,EAAE,KAAK;YACjB9B,UAAU,EAAE,wBAAwB;YACpC1B,UAAU,EAAE;UACd,CAAE;UACFyD,OAAO,EAAG3B,CAAC,IAAK;YACdA,CAAC,CAAC5D,MAAM,CAAC6B,KAAK,CAACiC,WAAW,GAAG,SAAS;YACtCF,CAAC,CAAC5D,MAAM,CAAC6B,KAAK,CAACC,UAAU,GAAG,SAAS;UACvC,CAAE;UACF0D,MAAM,EAAG5B,CAAC,IAAK;YACbA,CAAC,CAAC5D,MAAM,CAAC6B,KAAK,CAACiC,WAAW,GAAG,SAAS;YACtCF,CAAC,CAAC5D,MAAM,CAAC6B,KAAK,CAACC,UAAU,GAAG,SAAS;UACvC,CAAE;UACF2D,QAAQ;QAAA;UAAArD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFjE,OAAA;UAAKuD,KAAK,EAAE;YAAE0C,SAAS,EAAE,OAAO;YAAE3B,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE,MAAM;YAAEkC,SAAS,EAAE;UAAM,CAAE;UAAA1C,QAAA,GACnFpD,WAAW,CAACmF,MAAM,EAAC,MACtB;QAAA;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjE,OAAA;QAAKuD,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnC7D,OAAA;UAAOuD,KAAK,EAAE;YAAEY,OAAO,EAAE,OAAO;YAAEP,YAAY,EAAE,KAAK;YAAEQ,UAAU,EAAE,KAAK;YAAEC,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAE7G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRjE,OAAA;UAAKuD,KAAK,EAAE;YAAEY,OAAO,EAAE,MAAM;YAAEiD,mBAAmB,EAAE,SAAS;YAAEC,GAAG,EAAE,MAAM;YAAEzD,YAAY,EAAE;UAAO,CAAE;UAAAC,QAAA,gBACjG7D,OAAA;YAAA6D,QAAA,gBACE7D,OAAA;cAAOuD,KAAK,EAAE;gBAAEY,OAAO,EAAE,OAAO;gBAAEP,YAAY,EAAE,KAAK;gBAAEU,QAAQ,EAAE,MAAM;gBAAED,KAAK,EAAE;cAAO,CAAE;cAAAR,QAAA,EAAC;YAE1F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjE,OAAA;cACE8F,IAAI,EAAC,MAAM;cACXQ,KAAK,EAAEpE,cAAc,CAACC,WAAY;cAClC6D,QAAQ,EAAGV,CAAC,IAAKgC,iBAAiB,CAACC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEpF,WAAW,EAAEmD,CAAC,CAAC5D,MAAM,CAAC4E;cAAM,CAAC,CAAC,CAAE;cACvFK,WAAW,EAAC,sCAAQ;cACpBpD,KAAK,EAAE;gBACLsD,KAAK,EAAE,MAAM;gBACbpD,OAAO,EAAE,WAAW;gBACpBoB,MAAM,EAAE,mBAAmB;gBAC3BnB,YAAY,EAAE,KAAK;gBACnBY,QAAQ,EAAE,MAAM;gBAChBY,UAAU,EAAE,wBAAwB;gBACpC1B,UAAU,EAAE;cACd,CAAE;cACFyD,OAAO,EAAG3B,CAAC,IAAK;gBACdA,CAAC,CAAC5D,MAAM,CAAC6B,KAAK,CAACiC,WAAW,GAAG,SAAS;gBACtCF,CAAC,CAAC5D,MAAM,CAAC6B,KAAK,CAACC,UAAU,GAAG,SAAS;cACvC,CAAE;cACF0D,MAAM,EAAG5B,CAAC,IAAK;gBACbA,CAAC,CAAC5D,MAAM,CAAC6B,KAAK,CAACiC,WAAW,GAAG,SAAS;gBACtCF,CAAC,CAAC5D,MAAM,CAAC6B,KAAK,CAACC,UAAU,GAAG,SAAS;cACvC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNjE,OAAA;YAAA6D,QAAA,gBACE7D,OAAA;cAAOuD,KAAK,EAAE;gBAAEY,OAAO,EAAE,OAAO;gBAAEP,YAAY,EAAE,KAAK;gBAAEU,QAAQ,EAAE,MAAM;gBAAED,KAAK,EAAE;cAAO,CAAE;cAAAR,QAAA,EAAC;YAE1F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjE,OAAA;cACEsG,KAAK,EAAEpE,cAAc,CAACE,QAAS;cAC/B4D,QAAQ,EAAGV,CAAC,IAAKgC,iBAAiB,CAACC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEnF,QAAQ,EAAEkD,CAAC,CAAC5D,MAAM,CAAC4E;cAAM,CAAC,CAAC,CAAE;cACpF/C,KAAK,EAAE;gBACLsD,KAAK,EAAE,MAAM;gBACbpD,OAAO,EAAE,WAAW;gBACpBoB,MAAM,EAAE,mBAAmB;gBAC3BnB,YAAY,EAAE,KAAK;gBACnBY,QAAQ,EAAE,MAAM;gBAChBd,UAAU,EAAE,SAAS;gBACrByB,MAAM,EAAE;cACV,CAAE;cAAApB,QAAA,gBAEF7D,OAAA;gBAAQsG,KAAK,EAAC,YAAY;gBAAAzC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCjE,OAAA;gBAAQsG,KAAK,EAAC,IAAI;gBAAAzC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChCjE,OAAA;gBAAQsG,KAAK,EAAC,aAAa;gBAAAzC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACzCjE,OAAA;gBAAQsG,KAAK,EAAC,YAAY;gBAAAzC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjE,OAAA;QAAKuD,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,eACnC7D,OAAA;UAAKuD,KAAK,EAAE;YAAEY,OAAO,EAAE,MAAM;YAAEiD,mBAAmB,EAAE,SAAS;YAAEC,GAAG,EAAE,MAAM;YAAEzD,YAAY,EAAE;UAAO,CAAE;UAAAC,QAAA,gBACjG7D,OAAA;YAAA6D,QAAA,gBACE7D,OAAA;cAAOuD,KAAK,EAAE;gBAAEY,OAAO,EAAE,OAAO;gBAAEP,YAAY,EAAE,KAAK;gBAAEU,QAAQ,EAAE,MAAM;gBAAED,KAAK,EAAE;cAAO,CAAE;cAAAR,QAAA,EAAC;YAE1F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjE,OAAA;cACEsG,KAAK,EAAEpE,cAAc,CAACG,QAAS;cAC/B2D,QAAQ,EAAGV,CAAC,IAAKgC,iBAAiB,CAACC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAElF,QAAQ,EAAEiD,CAAC,CAAC5D,MAAM,CAAC4E;cAAM,CAAC,CAAC,CAAE;cACpF/C,KAAK,EAAE;gBACLsD,KAAK,EAAE,MAAM;gBACbpD,OAAO,EAAE,WAAW;gBACpBoB,MAAM,EAAE,mBAAmB;gBAC3BnB,YAAY,EAAE,KAAK;gBACnBY,QAAQ,EAAE,MAAM;gBAChBd,UAAU,EAAE,SAAS;gBACrByB,MAAM,EAAE;cACV,CAAE;cAAApB,QAAA,gBAEF7D,OAAA;gBAAQsG,KAAK,EAAC,KAAK;gBAAAzC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9BjE,OAAA;gBAAQsG,KAAK,EAAC,QAAQ;gBAAAzC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACjCjE,OAAA;gBAAQsG,KAAK,EAAC,MAAM;gBAAAzC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/BjE,OAAA;gBAAQsG,KAAK,EAAC,UAAU;gBAAAzC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNjE,OAAA;YAAA6D,QAAA,gBACE7D,OAAA;cAAOuD,KAAK,EAAE;gBAAEY,OAAO,EAAE,OAAO;gBAAEP,YAAY,EAAE,KAAK;gBAAEU,QAAQ,EAAE,MAAM;gBAAED,KAAK,EAAE;cAAO,CAAE;cAAAR,QAAA,EAAC;YAE1F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjE,OAAA;cACE8F,IAAI,EAAC,MAAM;cACXQ,KAAK,EAAEpE,cAAc,CAACI,gBAAiB;cACvC0D,QAAQ,EAAGV,CAAC,IAAKgC,iBAAiB,CAACC,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEjF,gBAAgB,EAAEgD,CAAC,CAAC5D,MAAM,CAAC4E;cAAM,CAAC,CAAC,CAAE;cAC5FK,WAAW,EAAC,yEAAkB;cAC9BpD,KAAK,EAAE;gBACLsD,KAAK,EAAE,MAAM;gBACbpD,OAAO,EAAE,WAAW;gBACpBoB,MAAM,EAAE,mBAAmB;gBAC3BnB,YAAY,EAAE,KAAK;gBACnBY,QAAQ,EAAE,MAAM;gBAChBY,UAAU,EAAE,wBAAwB;gBACpC1B,UAAU,EAAE;cACd,CAAE;cACFyD,OAAO,EAAG3B,CAAC,IAAK;gBACdA,CAAC,CAAC5D,MAAM,CAAC6B,KAAK,CAACiC,WAAW,GAAG,SAAS;gBACtCF,CAAC,CAAC5D,MAAM,CAAC6B,KAAK,CAACC,UAAU,GAAG,SAAS;cACvC,CAAE;cACF0D,MAAM,EAAG5B,CAAC,IAAK;gBACbA,CAAC,CAAC5D,MAAM,CAAC6B,KAAK,CAACiC,WAAW,GAAG,SAAS;gBACtCF,CAAC,CAAC5D,MAAM,CAAC6B,KAAK,CAACC,UAAU,GAAG,SAAS;cACvC;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjE,OAAA;QAAKuD,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAC,QAAA,gBACnC7D,OAAA;UAAOuD,KAAK,EAAE;YAAEY,OAAO,EAAE,OAAO;YAAEP,YAAY,EAAE,KAAK;YAAEU,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE;UAAO,CAAE;UAAAR,QAAA,EAAC;QAE1F;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRjE,OAAA;UACEsG,KAAK,EAAEpE,cAAc,CAACK,mBAAoB;UAC1CyD,QAAQ,EAAGV,CAAC,IAAKgC,iBAAiB,CAACC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEhF,mBAAmB,EAAE+C,CAAC,CAAC5D,MAAM,CAAC4E;UAAM,CAAC,CAAC,CAAE;UAC/FK,WAAW,EAAC,yLAAmC;UAC/CC,IAAI,EAAE,CAAE;UACRrD,KAAK,EAAE;YACLsD,KAAK,EAAE,MAAM;YACbpD,OAAO,EAAE,MAAM;YACfoB,MAAM,EAAE,mBAAmB;YAC3BnB,YAAY,EAAE,KAAK;YACnBoD,MAAM,EAAE,UAAU;YAClBC,UAAU,EAAE,SAAS;YACrBzC,QAAQ,EAAE,MAAM;YAChB0C,UAAU,EAAE,KAAK;YACjB9B,UAAU,EAAE,wBAAwB;YACpC1B,UAAU,EAAE;UACd,CAAE;UACFyD,OAAO,EAAG3B,CAAC,IAAK;YACdA,CAAC,CAAC5D,MAAM,CAAC6B,KAAK,CAACiC,WAAW,GAAG,SAAS;YACtCF,CAAC,CAAC5D,MAAM,CAAC6B,KAAK,CAACC,UAAU,GAAG,SAAS;UACvC,CAAE;UACF0D,MAAM,EAAG5B,CAAC,IAAK;YACbA,CAAC,CAAC5D,MAAM,CAAC6B,KAAK,CAACiC,WAAW,GAAG,SAAS;YACtCF,CAAC,CAAC5D,MAAM,CAAC6B,KAAK,CAACC,UAAU,GAAG,SAAS;UACvC;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENjE,OAAA;QACE8F,IAAI,EAAC,QAAQ;QACb0B,QAAQ,EAAE7G,WAAW,IAAI,CAACJ,YAAY,IAAI,CAACE,WAAW,CAACqB,IAAI,CAAC,CAAE;QAC9DyB,KAAK,EAAE;UACLC,UAAU,EAAE7C,WAAW,GAAG,SAAS,GAAG,SAAS;UAC/C0D,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdpB,OAAO,EAAE,WAAW;UACpBC,YAAY,EAAE,KAAK;UACnBY,QAAQ,EAAE,MAAM;UAChBF,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAEtE,WAAW,GAAG,aAAa,GAAG,SAAS;UAC/CuE,UAAU,EAAE,eAAe;UAC3Bf,OAAO,EAAE,MAAM;UACfY,UAAU,EAAE,QAAQ;UACpBsC,GAAG,EAAE;QACP,CAAE;QAAAxD,QAAA,EAEDlD,WAAW,gBACVX,OAAA,CAAAE,SAAA;UAAA2D,QAAA,gBACE7D,OAAA;YAAMuD,KAAK,EAAE;cACXsD,KAAK,EAAE,MAAM;cACbjC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,uBAAuB;cAC/B4C,SAAS,EAAE,iBAAiB;cAC5B/D,YAAY,EAAE,KAAK;cACnBgE,SAAS,EAAE;YACb;UAAE;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,yBAEZ;QAAA,eAAE,CAAC,GAEH;MACD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGPjE,OAAA;MAAKuD,KAAK,EAAE;QACVY,OAAO,EAAE,MAAM;QACfY,UAAU,EAAE,QAAQ;QACpBmB,MAAM,EAAE,QAAQ;QAChB7B,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE;MACZ,CAAE;MAAAT,QAAA,gBACA7D,OAAA;QAAKuD,KAAK,EAAE;UAAEoE,IAAI,EAAE,CAAC;UAAE/C,MAAM,EAAE,KAAK;UAAEpB,UAAU,EAAE;QAAU;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrEjE,OAAA;QAAMuD,KAAK,EAAE;UAAEE,OAAO,EAAE;QAAS,CAAE;QAAAI,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7CjE,OAAA;QAAKuD,KAAK,EAAE;UAAEoE,IAAI,EAAE,CAAC;UAAE/C,MAAM,EAAE,KAAK;UAAEpB,UAAU,EAAE;QAAU;MAAE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClE,CAAC,eAGNjE,OAAA;MAAKuD,KAAK,EAAE;QAAE0C,SAAS,EAAE;MAAS,CAAE;MAAApC,QAAA,gBAClC7D,OAAA;QAAIuD,KAAK,EAAE;UAAE2C,MAAM,EAAE,YAAY;UAAE7B,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAEtE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLjE,OAAA;QAAGuD,KAAK,EAAE;UAAE2C,MAAM,EAAE,YAAY;UAAE7B,KAAK,EAAE,MAAM;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAErE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJjE,OAAA;QACE8F,IAAI,EAAC,QAAQ;QACbtB,OAAO,EAAElB,wBAAyB;QAClCkE,QAAQ,EAAE7G,WAAY;QACtB4C,KAAK,EAAE;UACLC,UAAU,EAAE7C,WAAW,GAAG,SAAS,GAAG,SAAS;UAC/C0D,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,MAAM;UACdpB,OAAO,EAAE,WAAW;UACpBC,YAAY,EAAE,KAAK;UACnBY,QAAQ,EAAE,MAAM;UAChBF,UAAU,EAAE,KAAK;UACjBa,MAAM,EAAEtE,WAAW,GAAG,aAAa,GAAG,SAAS;UAC/CuE,UAAU,EAAE,eAAe;UAC3Bf,OAAO,EAAE,MAAM;UACfY,UAAU,EAAE,QAAQ;UACpBsC,GAAG,EAAE,KAAK;UACVnB,MAAM,EAAE;QACV,CAAE;QAAArC,QAAA,EAEDlD,WAAW,gBACVX,OAAA,CAAAE,SAAA;UAAA2D,QAAA,gBACE7D,OAAA;YAAMuD,KAAK,EAAE;cACXsD,KAAK,EAAE,MAAM;cACbjC,MAAM,EAAE,MAAM;cACdC,MAAM,EAAE,uBAAuB;cAC/B4C,SAAS,EAAE,iBAAiB;cAC5B/D,YAAY,EAAE,KAAK;cACnBgE,SAAS,EAAE;YACb;UAAE;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,yBAEZ;QAAA,eAAE,CAAC,gBAEHjE,OAAA,CAAAE,SAAA;UAAA2D,QAAA,EAAE;QAEF,gBAAE;MACH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENjE,OAAA;MAAO4H,GAAG;MAAA/D,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC3D,EAAA,CApgBIH,YAAY;AAAA0H,EAAA,GAAZ1H,YAAY;AAsgBlB,eAAeA,YAAY;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}