"""
交互分析智能体 - 分析用户交互流程
"""
import json
import os
import requests
import logging
from typing import List, Dict, Any

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InteractionAnalysisAgent:
    """交互分析智能体"""

    def __init__(self, api_key: str = None, base_url: str = None, model_name: str = None):
        self.prompt = self._load_prompt()
        self.api_key = api_key or os.getenv('OPENAI_API_KEY', 'sk-3b9cb1dbb58a421082ba5c9f0d6c07d6')
        self.base_url = base_url or os.getenv('OPENAI_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1')
        self.model_name = model_name or os.getenv('MIDSCENE_MODEL_NAME', 'qwen-vl-max-latest')

        # 验证配置
        if not self.api_key:
            logger.warning("未配置API密钥，将使用模拟数据")
            self.use_mock = True
        else:
            self.use_mock = False
    
    def _load_prompt(self) -> str:
        """加载提示词"""
        return """你是用户交互流程分析师，专门分析用户在界面上的操作流程，为自动化测试设计提供用户行为路径。

## 核心职责

### 1. 用户行为路径分析
- **主要流程**: 用户完成核心任务的标准路径
- **替代流程**: 用户可能采用的其他操作方式
- **异常流程**: 错误操作、网络异常等情况的处理
- **回退流程**: 用户撤销、返回等逆向操作

### 2. 交互节点识别
- **入口点**: 用户开始操作的位置
- **决策点**: 用户需要选择的关键节点
- **验证点**: 系统反馈和状态确认
- **出口点**: 流程完成或退出的位置

### 3. 操作序列设计
- **前置条件**: 执行操作前的必要状态
- **操作步骤**: 具体的用户动作序列
- **后置验证**: 操作完成后的状态检查
- **错误处理**: 异常情况的应对措施

### 4. 用户体验考量
- **操作便利性**: 符合用户习惯的操作方式
- **认知负荷**: 避免复杂的操作序列
- **反馈及时性**: 操作结果的即时反馈
- **容错性**: 允许用户纠错的机制

## 输出格式要求

请按照以下结构化格式输出交互流程：

```json
{
  "primary_flows": [
    {
      "flow_name": "用户登录流程",
      "description": "用户通过用户名密码登录系统",
      "steps": [
        {
          "step_id": 1,
          "action": "点击登录按钮",
          "target_element": "页面右上角蓝色登录按钮",
          "expected_result": "显示登录表单",
          "precondition": "用户未登录状态"
        },
        {
          "step_id": 2,
          "action": "输入用户名",
          "target_element": "用户名输入框",
          "expected_result": "输入框显示用户名",
          "validation": "检查输入格式"
        }
      ],
      "success_criteria": "成功登录并跳转到主页",
      "error_scenarios": ["用户名密码错误", "网络连接失败"]
    }
  ],
  "alternative_flows": [
    {
      "flow_name": "第三方登录流程",
      "trigger_condition": "用户选择第三方登录",
      "steps": []
    }
  ],
  "interaction_patterns": {
    "navigation_style": "顶部导航栏",
    "input_validation": "实时验证",
    "feedback_mechanism": "弹窗提示",
    "error_handling": "内联错误信息"
  }
}
```

## 分析维度

### 1. 流程完整性
- 覆盖所有主要用户场景
- 包含异常情况处理
- 考虑不同用户角色的需求

### 2. 操作可行性
- 每个步骤都有明确的触发元素
- 操作序列逻辑合理
- 符合界面实际布局

### 3. 测试友好性
- 每个步骤都可以自动化执行
- 包含明确的验证点
- 提供详细的元素定位信息
"""
    
    def analyze(self, elements: List[Dict[str, Any]], description: str) -> List[Dict[str, Any]]:
        """
        分析用户交互流程

        Args:
            elements: UI元素列表
            description: 界面功能描述

        Returns:
            交互流程列表
        """
        try:
            if self.use_mock:
                logger.info("使用模拟数据进行交互分析")
                return self._mock_analysis(elements, description)
            else:
                logger.info("使用AI模型进行交互分析")
                return self._ai_analysis(elements, description)

        except Exception as e:
            logger.error(f"交互分析失败: {e}")
            # 失败时回退到模拟数据
            return self._mock_analysis(elements, description)

    def _ai_analysis(self, elements: List[Dict[str, Any]], description: str) -> List[Dict[str, Any]]:
        """使用AI模型进行真实的交互分析"""
        try:
            # 构建请求
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            # 构建元素信息文本
            elements_text = json.dumps(elements, ensure_ascii=False, indent=2)

            # 构建消息
            messages = [
                {
                    "role": "user",
                    "content": f"{self.prompt}\n\n界面功能描述：{description}\n\nUI元素信息：\n{elements_text}\n\n请基于以上UI元素信息，分析用户交互流程并按照JSON格式输出。"
                }
            ]

            payload = {
                "model": self.model_name,
                "messages": messages,
                "max_tokens": 4000,
                "temperature": 0.1
            }

            # 发送请求
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=60
            )

            if response.status_code != 200:
                raise Exception(f"API请求失败: {response.status_code} - {response.text}")

            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')

            # 解析JSON结果
            flows = self._parse_ai_response(content)
            logger.info(f"AI模型分析出 {len(flows)} 个交互流程")
            return flows

        except Exception as e:
            logger.error(f"AI模型调用失败: {e}")
            raise e

    def _parse_ai_response(self, content: str) -> List[Dict[str, Any]]:
        """解析AI模型的响应内容"""
        try:
            # 尝试直接解析JSON
            if content.strip().startswith('['):
                return json.loads(content)

            # 如果包含代码块，提取JSON部分
            if '```json' in content:
                start = content.find('```json') + 7
                end = content.find('```', start)
                json_str = content[start:end].strip()
                return json.loads(json_str)

            # 如果包含普通代码块，提取内容
            if '```' in content:
                start = content.find('```') + 3
                end = content.find('```', start)
                json_str = content[start:end].strip()
                return json.loads(json_str)

            # 尝试查找JSON数组
            start = content.find('[')
            end = content.rfind(']') + 1
            if start >= 0 and end > start:
                json_str = content[start:end]
                return json.loads(json_str)

            logger.warning("无法解析AI响应，使用模拟数据")
            return []

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return []

    def _mock_analysis(self, elements: List[Dict[str, Any]], description: str) -> List[Dict[str, Any]]:
        """模拟交互分析结果"""
        # 基于元素和描述生成交互流程
        flows = []
        
        # 查找按钮和输入框
        buttons = [e for e in elements if e.get('element_type') == 'button']
        inputs = [e for e in elements if e.get('element_type') == 'input']
        
        # 生成主要流程
        if buttons and inputs:
            primary_flow = {
                "flow_name": "主要操作流程",
                "description": f"基于{description}的主要用户操作流程",
                "steps": [
                    {
                        "step_id": 1,
                        "action": "填写信息",
                        "target_element": inputs[0].get('name', '输入框'),
                        "expected_result": "信息输入完成",
                        "precondition": "页面已加载"
                    },
                    {
                        "step_id": 2,
                        "action": "提交操作",
                        "target_element": buttons[0].get('name', '操作按钮'),
                        "expected_result": "操作执行成功",
                        "validation": "检查操作结果"
                    }
                ],
                "success_criteria": "用户成功完成预期操作",
                "error_scenarios": ["输入验证失败", "网络请求超时"]
            }
            flows.append(primary_flow)
        
        # 如果有登录相关元素，生成登录流程
        login_elements = [e for e in elements if '登录' in e.get('name', '')]
        if login_elements or '登录' in description:
            login_flow = {
                "flow_name": "用户登录流程",
                "description": "用户登录系统的完整流程",
                "steps": [
                    {
                        "step_id": 1,
                        "action": "点击登录按钮",
                        "target_element": "登录按钮",
                        "expected_result": "显示登录表单",
                        "precondition": "用户未登录状态"
                    },
                    {
                        "step_id": 2,
                        "action": "输入用户名",
                        "target_element": "用户名输入框",
                        "expected_result": "用户名输入完成",
                        "validation": "检查用户名格式"
                    },
                    {
                        "step_id": 3,
                        "action": "输入密码",
                        "target_element": "密码输入框",
                        "expected_result": "密码输入完成",
                        "validation": "密码强度检查"
                    },
                    {
                        "step_id": 4,
                        "action": "提交登录",
                        "target_element": "登录提交按钮",
                        "expected_result": "登录成功，跳转到主页",
                        "validation": "检查登录状态"
                    }
                ],
                "success_criteria": "用户成功登录并进入系统",
                "error_scenarios": ["用户名或密码错误", "账户被锁定", "网络连接失败"]
            }
            flows.append(login_flow)
        
        # 生成导航流程
        nav_elements = [e for e in elements if e.get('element_type') == 'navigation']
        if nav_elements:
            nav_flow = {
                "flow_name": "页面导航流程",
                "description": "用户在不同页面间导航的流程",
                "steps": [
                    {
                        "step_id": 1,
                        "action": "点击导航菜单",
                        "target_element": "导航菜单项",
                        "expected_result": "页面跳转成功",
                        "precondition": "页面已加载完成"
                    }
                ],
                "success_criteria": "成功跳转到目标页面",
                "error_scenarios": ["页面加载失败", "权限不足"]
            }
            flows.append(nav_flow)
        
        return flows
