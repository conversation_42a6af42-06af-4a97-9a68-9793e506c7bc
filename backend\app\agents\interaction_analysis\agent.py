"""
交互分析智能体 - 分析用户交互流程
"""
import json
from typing import List, Dict, Any

class InteractionAnalysisAgent:
    """交互分析智能体"""
    
    def __init__(self):
        self.prompt = self._load_prompt()
    
    def _load_prompt(self) -> str:
        """加载提示词"""
        return """你是用户交互流程分析师，专门分析用户在界面上的操作流程，为自动化测试设计提供用户行为路径。

## 核心职责

### 1. 用户行为路径分析
- **主要流程**: 用户完成核心任务的标准路径
- **替代流程**: 用户可能采用的其他操作方式
- **异常流程**: 错误操作、网络异常等情况的处理
- **回退流程**: 用户撤销、返回等逆向操作

### 2. 交互节点识别
- **入口点**: 用户开始操作的位置
- **决策点**: 用户需要选择的关键节点
- **验证点**: 系统反馈和状态确认
- **出口点**: 流程完成或退出的位置

### 3. 操作序列设计
- **前置条件**: 执行操作前的必要状态
- **操作步骤**: 具体的用户动作序列
- **后置验证**: 操作完成后的状态检查
- **错误处理**: 异常情况的应对措施

### 4. 用户体验考量
- **操作便利性**: 符合用户习惯的操作方式
- **认知负荷**: 避免复杂的操作序列
- **反馈及时性**: 操作结果的即时反馈
- **容错性**: 允许用户纠错的机制

## 输出格式要求

请按照以下结构化格式输出交互流程：

```json
{
  "primary_flows": [
    {
      "flow_name": "用户登录流程",
      "description": "用户通过用户名密码登录系统",
      "steps": [
        {
          "step_id": 1,
          "action": "点击登录按钮",
          "target_element": "页面右上角蓝色登录按钮",
          "expected_result": "显示登录表单",
          "precondition": "用户未登录状态"
        },
        {
          "step_id": 2,
          "action": "输入用户名",
          "target_element": "用户名输入框",
          "expected_result": "输入框显示用户名",
          "validation": "检查输入格式"
        }
      ],
      "success_criteria": "成功登录并跳转到主页",
      "error_scenarios": ["用户名密码错误", "网络连接失败"]
    }
  ],
  "alternative_flows": [
    {
      "flow_name": "第三方登录流程",
      "trigger_condition": "用户选择第三方登录",
      "steps": []
    }
  ],
  "interaction_patterns": {
    "navigation_style": "顶部导航栏",
    "input_validation": "实时验证",
    "feedback_mechanism": "弹窗提示",
    "error_handling": "内联错误信息"
  }
}
```

## 分析维度

### 1. 流程完整性
- 覆盖所有主要用户场景
- 包含异常情况处理
- 考虑不同用户角色的需求

### 2. 操作可行性
- 每个步骤都有明确的触发元素
- 操作序列逻辑合理
- 符合界面实际布局

### 3. 测试友好性
- 每个步骤都可以自动化执行
- 包含明确的验证点
- 提供详细的元素定位信息
"""
    
    def analyze(self, elements: List[Dict[str, Any]], description: str) -> List[Dict[str, Any]]:
        """
        分析用户交互流程
        
        Args:
            elements: UI元素列表
            description: 界面功能描述
            
        Returns:
            交互流程列表
        """
        try:
            # 这里应该调用实际的AI模型API
            # 为了演示，我们返回模拟数据
            return self._mock_analysis(elements, description)
            
        except Exception as e:
            print(f"交互分析失败: {e}")
            return []
    
    def _mock_analysis(self, elements: List[Dict[str, Any]], description: str) -> List[Dict[str, Any]]:
        """模拟交互分析结果"""
        # 基于元素和描述生成交互流程
        flows = []
        
        # 查找按钮和输入框
        buttons = [e for e in elements if e.get('element_type') == 'button']
        inputs = [e for e in elements if e.get('element_type') == 'input']
        
        # 生成主要流程
        if buttons and inputs:
            primary_flow = {
                "flow_name": "主要操作流程",
                "description": f"基于{description}的主要用户操作流程",
                "steps": [
                    {
                        "step_id": 1,
                        "action": "填写信息",
                        "target_element": inputs[0].get('name', '输入框'),
                        "expected_result": "信息输入完成",
                        "precondition": "页面已加载"
                    },
                    {
                        "step_id": 2,
                        "action": "提交操作",
                        "target_element": buttons[0].get('name', '操作按钮'),
                        "expected_result": "操作执行成功",
                        "validation": "检查操作结果"
                    }
                ],
                "success_criteria": "用户成功完成预期操作",
                "error_scenarios": ["输入验证失败", "网络请求超时"]
            }
            flows.append(primary_flow)
        
        # 如果有登录相关元素，生成登录流程
        login_elements = [e for e in elements if '登录' in e.get('name', '')]
        if login_elements or '登录' in description:
            login_flow = {
                "flow_name": "用户登录流程",
                "description": "用户登录系统的完整流程",
                "steps": [
                    {
                        "step_id": 1,
                        "action": "点击登录按钮",
                        "target_element": "登录按钮",
                        "expected_result": "显示登录表单",
                        "precondition": "用户未登录状态"
                    },
                    {
                        "step_id": 2,
                        "action": "输入用户名",
                        "target_element": "用户名输入框",
                        "expected_result": "用户名输入完成",
                        "validation": "检查用户名格式"
                    },
                    {
                        "step_id": 3,
                        "action": "输入密码",
                        "target_element": "密码输入框",
                        "expected_result": "密码输入完成",
                        "validation": "密码强度检查"
                    },
                    {
                        "step_id": 4,
                        "action": "提交登录",
                        "target_element": "登录提交按钮",
                        "expected_result": "登录成功，跳转到主页",
                        "validation": "检查登录状态"
                    }
                ],
                "success_criteria": "用户成功登录并进入系统",
                "error_scenarios": ["用户名或密码错误", "账户被锁定", "网络连接失败"]
            }
            flows.append(login_flow)
        
        # 生成导航流程
        nav_elements = [e for e in elements if e.get('element_type') == 'navigation']
        if nav_elements:
            nav_flow = {
                "flow_name": "页面导航流程",
                "description": "用户在不同页面间导航的流程",
                "steps": [
                    {
                        "step_id": 1,
                        "action": "点击导航菜单",
                        "target_element": "导航菜单项",
                        "expected_result": "页面跳转成功",
                        "precondition": "页面已加载完成"
                    }
                ],
                "success_criteria": "成功跳转到目标页面",
                "error_scenarios": ["页面加载失败", "权限不足"]
            }
            flows.append(nav_flow)
        
        return flows
