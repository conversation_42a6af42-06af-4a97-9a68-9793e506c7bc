{"ast": null, "code": "/**\n * UI自动化分析平台 - 主应用组件\n */import React,{useState}from'react';import SimpleUpload from'./components/SimpleUpload';import SimpleResults from'./components/SimpleResults';import RealTimeAnalysis from'./components/RealTimeAnalysis';import'./App.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){const[appState,setAppState]=useState('upload');// 'upload' | 'analyzing' | 'results'\nconst[currentTaskId,setCurrentTaskId]=useState('');const[analysisResult,setAnalysisResult]=useState(null);const[error,setError]=useState('');const handleUploadSuccess=result=>{if(result.task_id){// 如果有任务ID，进入实时分析模式\nsetCurrentTaskId(result.task_id);setAppState('analyzing');}else{// 如果直接返回结果，进入结果展示模式\nsetAnalysisResult(result);setAppState('results');}setError('');};const handleUploadError=errorMessage=>{setError(errorMessage);};const handleAnalysisComplete=function(){let taskData=arguments.length>0&&arguments[0]!==undefined?arguments[0]:null;// 实时分析完成，设置最终结果\nsetAppState('results');if(taskData){// 使用从API获取的真实数据\nsetAnalysisResult({task_id:currentTaskId,message:\"分析完成\",result:taskData});}else{// 使用默认数据\nsetAnalysisResult({task_id:currentTaskId,message:\"分析完成\",result:{status:\"completed\",elements:[],flows:[],automation_scripts:[]}});}};const handleAnalysisError=errorMessage=>{setError(errorMessage);setAppState('upload');};const handleReset=()=>{setAppState('upload');setCurrentTaskId('');setAnalysisResult(null);setError('');};return/*#__PURE__*/_jsxs(\"div\",{className:\"app\",children:[/*#__PURE__*/_jsx(\"header\",{className:\"app-header\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"\\uD83E\\uDD16 UI\\u81EA\\u52A8\\u5316\\u5206\\u6790\\u5E73\\u53F0\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u57FA\\u4E8EAI\\u667A\\u80FD\\u4F53\\u7684UI\\u754C\\u9762\\u5206\\u6790\\u548C\\u81EA\\u52A8\\u5316\\u6D4B\\u8BD5\\u811A\\u672C\\u751F\\u6210\"}),appState!=='upload'&&/*#__PURE__*/_jsx(\"button\",{className:\"reset-button\",onClick:handleReset,children:\"\\u2190 \\u91CD\\u65B0\\u5F00\\u59CB\"})]})}),/*#__PURE__*/_jsx(\"main\",{className:\"app-main\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container\",children:[error&&/*#__PURE__*/_jsxs(\"div\",{className:\"error-banner\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"error-icon\",children:\"\\u26A0\\uFE0F\"}),/*#__PURE__*/_jsx(\"span\",{className:\"error-text\",children:error}),/*#__PURE__*/_jsx(\"button\",{className:\"error-close\",onClick:()=>setError(''),children:\"\\u2715\"})]}),appState==='upload'&&/*#__PURE__*/_jsx(SimpleUpload,{onUploadSuccess:handleUploadSuccess,onUploadError:handleUploadError}),appState==='analyzing'&&currentTaskId&&/*#__PURE__*/_jsx(RealTimeAnalysis,{taskId:currentTaskId,onAnalysisComplete:handleAnalysisComplete,onAnalysisError:handleAnalysisError}),appState==='results'&&analysisResult&&/*#__PURE__*/_jsx(SimpleResults,{result:analysisResult})]})}),/*#__PURE__*/_jsx(\"footer\",{className:\"app-footer\",children:/*#__PURE__*/_jsx(\"div\",{className:\"container\",children:/*#__PURE__*/_jsx(\"p\",{children:\"\\xA9 2024 UI\\u81EA\\u52A8\\u5316\\u5206\\u6790\\u5E73\\u53F0 - \\u57FA\\u4E8EFastAPI + React + AI\\u667A\\u80FD\\u4F53\"})})})]});}export default App;", "map": {"version": 3, "names": ["React", "useState", "SimpleUpload", "SimpleResults", "RealTimeAnalysis", "jsx", "_jsx", "jsxs", "_jsxs", "App", "appState", "setAppState", "currentTaskId", "setCurrentTaskId", "analysisResult", "setAnalysisResult", "error", "setError", "handleUploadSuccess", "result", "task_id", "handleUploadError", "errorMessage", "handleAnalysisComplete", "taskData", "arguments", "length", "undefined", "message", "status", "elements", "flows", "automation_scripts", "handleAnalysisError", "handleReset", "className", "children", "onClick", "onUploadSuccess", "onUploadError", "taskId", "onAnalysisComplete", "onAnalysisError"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/App.js"], "sourcesContent": ["/**\n * UI自动化分析平台 - 主应用组件\n */\nimport React, { useState } from 'react';\nimport SimpleUpload from './components/SimpleUpload';\nimport SimpleResults from './components/SimpleResults';\nimport RealTimeAnalysis from './components/RealTimeAnalysis';\nimport './App.css';\n\nfunction App() {\n  const [appState, setAppState] = useState('upload'); // 'upload' | 'analyzing' | 'results'\n  const [currentTaskId, setCurrentTaskId] = useState('');\n  const [analysisResult, setAnalysisResult] = useState(null);\n  const [error, setError] = useState('');\n\n  const handleUploadSuccess = (result) => {\n    if (result.task_id) {\n      // 如果有任务ID，进入实时分析模式\n      setCurrentTaskId(result.task_id);\n      setAppState('analyzing');\n    } else {\n      // 如果直接返回结果，进入结果展示模式\n      setAnalysisResult(result);\n      setAppState('results');\n    }\n    setError('');\n  };\n\n  const handleUploadError = (errorMessage) => {\n    setError(errorMessage);\n  };\n\n  const handleAnalysisComplete = (taskData = null) => {\n    // 实时分析完成，设置最终结果\n    setAppState('results');\n\n    if (taskData) {\n      // 使用从API获取的真实数据\n      setAnalysisResult({\n        task_id: currentTaskId,\n        message: \"分析完成\",\n        result: taskData\n      });\n    } else {\n      // 使用默认数据\n      setAnalysisResult({\n        task_id: currentTaskId,\n        message: \"分析完成\",\n        result: {\n          status: \"completed\",\n          elements: [],\n          flows: [],\n          automation_scripts: []\n        }\n      });\n    }\n  };\n\n  const handleAnalysisError = (errorMessage) => {\n    setError(errorMessage);\n    setAppState('upload');\n  };\n\n  const handleReset = () => {\n    setAppState('upload');\n    setCurrentTaskId('');\n    setAnalysisResult(null);\n    setError('');\n  };\n\n  return (\n    <div className=\"app\">\n      <header className=\"app-header\">\n        <div className=\"container\">\n          <h1>🤖 UI自动化分析平台</h1>\n          <p>基于AI智能体的UI界面分析和自动化测试脚本生成</p>\n          {appState !== 'upload' && (\n            <button className=\"reset-button\" onClick={handleReset}>\n              ← 重新开始\n            </button>\n          )}\n        </div>\n      </header>\n\n      <main className=\"app-main\">\n        <div className=\"container\">\n          {error && (\n            <div className=\"error-banner\">\n              <span className=\"error-icon\">⚠️</span>\n              <span className=\"error-text\">{error}</span>\n              <button className=\"error-close\" onClick={() => setError('')}>\n                ✕\n              </button>\n            </div>\n          )}\n\n          {/* 实际组件 */}\n          {appState === 'upload' && (\n            <SimpleUpload\n              onUploadSuccess={handleUploadSuccess}\n              onUploadError={handleUploadError}\n            />\n          )}\n\n          {appState === 'analyzing' && currentTaskId && (\n            <RealTimeAnalysis\n              taskId={currentTaskId}\n              onAnalysisComplete={handleAnalysisComplete}\n              onAnalysisError={handleAnalysisError}\n            />\n          )}\n\n          {appState === 'results' && analysisResult && (\n            <SimpleResults result={analysisResult} />\n          )}\n        </div>\n      </main>\n\n      <footer className=\"app-footer\">\n        <div className=\"container\">\n          <p>© 2024 UI自动化分析平台 - 基于FastAPI + React + AI智能体</p>\n        </div>\n      </footer>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA;AACA;AACA,GACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,YAAY,KAAM,2BAA2B,CACpD,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CACtD,MAAO,CAAAC,gBAAgB,KAAM,+BAA+B,CAC5D,MAAO,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnB,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGV,QAAQ,CAAC,QAAQ,CAAC,CAAE;AACpD,KAAM,CAACW,aAAa,CAAEC,gBAAgB,CAAC,CAAGZ,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACa,cAAc,CAAEC,iBAAiB,CAAC,CAAGd,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACe,KAAK,CAAEC,QAAQ,CAAC,CAAGhB,QAAQ,CAAC,EAAE,CAAC,CAEtC,KAAM,CAAAiB,mBAAmB,CAAIC,MAAM,EAAK,CACtC,GAAIA,MAAM,CAACC,OAAO,CAAE,CAClB;AACAP,gBAAgB,CAACM,MAAM,CAACC,OAAO,CAAC,CAChCT,WAAW,CAAC,WAAW,CAAC,CAC1B,CAAC,IAAM,CACL;AACAI,iBAAiB,CAACI,MAAM,CAAC,CACzBR,WAAW,CAAC,SAAS,CAAC,CACxB,CACAM,QAAQ,CAAC,EAAE,CAAC,CACd,CAAC,CAED,KAAM,CAAAI,iBAAiB,CAAIC,YAAY,EAAK,CAC1CL,QAAQ,CAACK,YAAY,CAAC,CACxB,CAAC,CAED,KAAM,CAAAC,sBAAsB,CAAG,QAAAA,CAAA,CAAqB,IAApB,CAAAC,QAAQ,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CAC7C;AACAd,WAAW,CAAC,SAAS,CAAC,CAEtB,GAAIa,QAAQ,CAAE,CACZ;AACAT,iBAAiB,CAAC,CAChBK,OAAO,CAAER,aAAa,CACtBgB,OAAO,CAAE,MAAM,CACfT,MAAM,CAAEK,QACV,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACAT,iBAAiB,CAAC,CAChBK,OAAO,CAAER,aAAa,CACtBgB,OAAO,CAAE,MAAM,CACfT,MAAM,CAAE,CACNU,MAAM,CAAE,WAAW,CACnBC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,kBAAkB,CAAE,EACtB,CACF,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAIX,YAAY,EAAK,CAC5CL,QAAQ,CAACK,YAAY,CAAC,CACtBX,WAAW,CAAC,QAAQ,CAAC,CACvB,CAAC,CAED,KAAM,CAAAuB,WAAW,CAAGA,CAAA,GAAM,CACxBvB,WAAW,CAAC,QAAQ,CAAC,CACrBE,gBAAgB,CAAC,EAAE,CAAC,CACpBE,iBAAiB,CAAC,IAAI,CAAC,CACvBE,QAAQ,CAAC,EAAE,CAAC,CACd,CAAC,CAED,mBACET,KAAA,QAAK2B,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClB9B,IAAA,WAAQ6B,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC5B5B,KAAA,QAAK2B,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB9B,IAAA,OAAA8B,QAAA,CAAI,2DAAY,CAAI,CAAC,cACrB9B,IAAA,MAAA8B,QAAA,CAAG,8HAAwB,CAAG,CAAC,CAC9B1B,QAAQ,GAAK,QAAQ,eACpBJ,IAAA,WAAQ6B,SAAS,CAAC,cAAc,CAACE,OAAO,CAAEH,WAAY,CAAAE,QAAA,CAAC,iCAEvD,CAAQ,CACT,EACE,CAAC,CACA,CAAC,cAET9B,IAAA,SAAM6B,SAAS,CAAC,UAAU,CAAAC,QAAA,cACxB5B,KAAA,QAAK2B,SAAS,CAAC,WAAW,CAAAC,QAAA,EACvBpB,KAAK,eACJR,KAAA,QAAK2B,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B9B,IAAA,SAAM6B,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,cACtC9B,IAAA,SAAM6B,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEpB,KAAK,CAAO,CAAC,cAC3CV,IAAA,WAAQ6B,SAAS,CAAC,aAAa,CAACE,OAAO,CAAEA,CAAA,GAAMpB,QAAQ,CAAC,EAAE,CAAE,CAAAmB,QAAA,CAAC,QAE7D,CAAQ,CAAC,EACN,CACN,CAGA1B,QAAQ,GAAK,QAAQ,eACpBJ,IAAA,CAACJ,YAAY,EACXoC,eAAe,CAAEpB,mBAAoB,CACrCqB,aAAa,CAAElB,iBAAkB,CAClC,CACF,CAEAX,QAAQ,GAAK,WAAW,EAAIE,aAAa,eACxCN,IAAA,CAACF,gBAAgB,EACfoC,MAAM,CAAE5B,aAAc,CACtB6B,kBAAkB,CAAElB,sBAAuB,CAC3CmB,eAAe,CAAET,mBAAoB,CACtC,CACF,CAEAvB,QAAQ,GAAK,SAAS,EAAII,cAAc,eACvCR,IAAA,CAACH,aAAa,EAACgB,MAAM,CAAEL,cAAe,CAAE,CACzC,EACE,CAAC,CACF,CAAC,cAEPR,IAAA,WAAQ6B,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC5B9B,IAAA,QAAK6B,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB9B,IAAA,MAAA8B,QAAA,CAAG,6GAA4C,CAAG,CAAC,CAChD,CAAC,CACA,CAAC,EACN,CAAC,CAEV,CAEA,cAAe,CAAA3B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}