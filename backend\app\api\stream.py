"""
SSE流式输出API路由
"""
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
import json
import asyncio
from app.core.task_manager import task_manager

router = APIRouter()

@router.get("/stream/{task_id}")
async def stream_progress(task_id: str):
    """
    SSE流式输出任务进度
    
    Args:
        task_id: 任务ID
    
    Returns:
        Server-Sent Events 流
    """
    # 检查任务是否存在
    task = task_manager.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    async def event_generator():
        """SSE事件生成器"""
        try:
            # 发送初始连接确认
            yield f"event: connected\ndata: {json.dumps({'message': '已连接到任务进度流', 'task_id': task_id})}\n\n"
            
            # 如果任务已完成，直接发送完成状态
            current_task = task_manager.get_task(task_id)
            if current_task and current_task.get('status') in ['completed', 'failed']:
                yield f"event: task_complete\ndata: {json.dumps(current_task)}\n\n"
                return
            
            # 订阅任务进度更新
            async for progress in task_manager.subscribe_progress(task_id):
                event_data = {
                    "agent": progress.agent,
                    "stage": progress.stage,
                    "message": progress.message,
                    "progress": progress.progress,
                    "data": progress.data,
                    "timestamp": progress.model_dump().get("timestamp")
                }
                
                yield f"event: agent_progress\ndata: {json.dumps(event_data)}\n\n"
                
                # 如果是完成状态，发送最终结果
                if progress.stage in ["completed", "failed"]:
                    final_task = task_manager.get_task(task_id)
                    if final_task:
                        yield f"event: task_complete\ndata: {json.dumps(final_task)}\n\n"
                    break
            
        except asyncio.CancelledError:
            # 客户端断开连接
            yield f"event: disconnected\ndata: {json.dumps({'message': '连接已断开'})}\n\n"
        except Exception as e:
            # 发送错误信息
            error_data = {
                "error": str(e),
                "message": "流式传输过程中发生错误"
            }
            yield f"event: error\ndata: {json.dumps(error_data)}\n\n"
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )

@router.get("/stream/{task_id}/test")
async def test_stream(task_id: str):
    """
    测试SSE流 - 发送模拟进度数据
    """
    async def test_generator():
        """测试事件生成器"""
        import time
        
        # 模拟智能体执行流程
        agents = [
            {"name": "元素识别", "duration": 3},
            {"name": "交互分析", "duration": 2}, 
            {"name": "用例生成", "duration": 4}
        ]
        
        yield f"event: connected\ndata: {json.dumps({'message': '测试连接已建立'})}\n\n"
        
        for i, agent in enumerate(agents):
            # 开始阶段
            start_data = {
                "agent": agent["name"],
                "stage": "processing",
                "message": f"{agent['name']}智能体开始工作...",
                "progress": 0
            }
            yield f"event: agent_progress\ndata: {json.dumps(start_data)}\n\n"
            
            # 进度更新
            for progress in range(0, 101, 20):
                await asyncio.sleep(0.1)  # 模拟处理时间
                progress_data = {
                    "agent": agent["name"],
                    "stage": "processing", 
                    "message": f"{agent['name']}处理中... {progress}%",
                    "progress": progress
                }
                yield f"event: agent_progress\ndata: {json.dumps(progress_data)}\n\n"
            
            # 完成阶段
            complete_data = {
                "agent": agent["name"],
                "stage": "completed",
                "message": f"{agent['name']}智能体完成工作",
                "progress": 100
            }
            yield f"event: agent_progress\ndata: {json.dumps(complete_data)}\n\n"
        
        # 最终完成
        final_data = {
            "message": "所有智能体执行完成",
            "status": "completed"
        }
        yield f"event: task_complete\ndata: {json.dumps(final_data)}\n\n"
    
    return StreamingResponse(
        test_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive"
        }
    )
