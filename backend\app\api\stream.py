"""
SSE流式输出API路由
"""
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import StreamingResponse
import asyncio
from app.core.sse_manager import sse_manager, AgentProgress, AgentStatus

router = APIRouter()

@router.get("/stream/{task_id}")
async def stream_progress(task_id: str, request: Request):
    """
    SSE流式输出任务进度

    Args:
        task_id: 任务ID
        request: FastAPI请求对象

    Returns:
        Server-Sent Events 流
    """
    # 创建SSE连接
    connection = sse_manager.create_connection(task_id)

    async def event_generator():
        """SSE事件生成器"""
        try:
            async for message in connection.get_messages():
                # 检查客户端是否断开连接
                if await request.is_disconnected():
                    break
                yield message

        except asyncio.CancelledError:
            pass
        finally:
            # 清理连接
            sse_manager.remove_connection(task_id, connection)

    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
            "X-Accel-Buffering": "no"  # 禁用Nginx缓冲
        }
    )

@router.get("/stream/{task_id}/test")
async def test_stream(task_id: str, request: Request):
    """
    测试SSE流 - 发送模拟进度数据
    """
    connection = sse_manager.create_connection(task_id)

    async def test_generator():
        """测试事件生成器"""
        try:
            # 模拟智能体执行流程
            agents = [
                {"name": "元素识别智能体", "duration": 3},
                {"name": "交互分析智能体", "duration": 2},
                {"name": "脚本生成智能体", "duration": 4}
            ]

            # 启动模拟进度任务
            progress_task = asyncio.create_task(simulate_progress(task_id, agents))

            async for message in connection.get_messages():
                if await request.is_disconnected():
                    break
                yield message

        except asyncio.CancelledError:
            pass
        finally:
            sse_manager.remove_connection(task_id, connection)

    return StreamingResponse(
        test_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )

async def simulate_progress(task_id: str, agents: list):
    """模拟智能体执行进度"""
    try:
        for i, agent in enumerate(agents):
            # 开始阶段
            await sse_manager.broadcast_progress(
                task_id,
                AgentProgress(
                    agent_name=agent["name"],
                    status=AgentStatus.PROCESSING,
                    message=f"{agent['name']}开始工作...",
                    progress=0.0
                )
            )

            # 进度更新
            for progress in range(0, 101, 20):
                await asyncio.sleep(0.2)  # 模拟处理时间
                await sse_manager.broadcast_progress(
                    task_id,
                    AgentProgress(
                        agent_name=agent["name"],
                        status=AgentStatus.PROCESSING,
                        message=f"{agent['name']}处理中... {progress}%",
                        progress=float(progress)
                    )
                )

            # 完成阶段
            await sse_manager.broadcast_progress(
                task_id,
                AgentProgress(
                    agent_name=agent["name"],
                    status=AgentStatus.COMPLETED,
                    message=f"{agent['name']}完成工作",
                    progress=100.0,
                    duration=agent["duration"]
                )
            )

        # 最终完成
        await sse_manager.broadcast_completion(
            task_id,
            {
                "message": "所有智能体执行完成",
                "elements_count": 5,
                "flows_count": 3,
                "scripts_count": 2
            }
        )

    except Exception as e:
        await sse_manager.broadcast_error(task_id, str(e))
