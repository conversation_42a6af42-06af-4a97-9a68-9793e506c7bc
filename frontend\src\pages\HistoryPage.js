/**
 * 历史记录页面
 */
import React, { useState, useEffect } from 'react';

// 生成标准脚本名称格式：年月日+时分+脚本名称+spec.ts
const generateScriptName = (createdAt, description) => {
  const date = new Date(createdAt);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');

  // 从描述中提取脚本名称，去掉"界面"、"分析"等后缀
  const scriptName = description
    .replace(/界面分析?$/, '')
    .replace(/功能分析?$/, '')
    .replace(/分析$/, '')
    .replace(/界面$/, '')
    .trim();

  return `${year}${month}${day}-${hour}${minute}-${scriptName}.spec.ts`;
};

const HistoryPage = () => {
  const [historyData, setHistoryData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedItem, setSelectedItem] = useState(null);

  useEffect(() => {
    // 从后端加载真实的脚本数据
    const loadScriptsData = async () => {
      try {
        const response = await fetch('http://localhost:8000/api/v1/scripts');
        if (response.ok) {
          const data = await response.json();
          const scripts = data.scripts || [];

          // 转换数据格式
          const processedData = scripts.map(script => ({
            id: script.id,
            taskId: script.id,
            fileName: script.original_name || script.script_name,
            description: script.description,
            status: 'completed',
            createdAt: script.created_at || new Date().toISOString(),
            elementsCount: 0, // 这些数据需要从分析结果中获取
            flowsCount: 0,
            scriptsCount: 1,
            scriptName: script.script_name,
            filePath: script.file_path,
            priority: script.priority,
            runCount: script.run_count || 0
          }));

          setHistoryData(processedData);
        } else {
          // 如果后端不可用，使用模拟数据
          const rawData = [
            {
              id: '1',
              taskId: 'task-001',
              fileName: '登录界面.png',
              description: '用户登录界面分析',
              status: 'completed',
              createdAt: '2024-12-17 10:30:00',
              elementsCount: 5,
              flowsCount: 2,
              scriptsCount: 1
            },
            {
              id: '2',
              taskId: 'task-002',
              fileName: '文件管理界面.png',
              description: '文件管理界面功能分析',
              status: 'completed',
              createdAt: '2024-12-17 14:15:00',
              elementsCount: 8,
              flowsCount: 3,
              scriptsCount: 2
            }
          ];

          // 为每个项目生成标准脚本名称
          const processedData = rawData.map(item => ({
            ...item,
            scriptName: generateScriptName(item.createdAt, item.description)
          }));

          setHistoryData(processedData);
        }
      } catch (error) {
        console.error('加载脚本数据失败:', error);
        // 使用模拟数据作为后备
        setHistoryData([]);
      } finally {
        setLoading(false);
      }
    };

    loadScriptsData();
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return '#28a745';
      case 'failed':
        return '#dc3545';
      case 'processing':
        return '#007bff';
      default:
        return '#6c757d';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'failed':
        return '失败';
      case 'processing':
        return '处理中';
      default:
        return '未知';
    }
  };

  const handleViewDetails = (item) => {
    setSelectedItem(item);
  };

  const handleRunScript = async (item) => {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/scripts/${item.id}/run`, {
        method: 'POST'
      });

      if (response.ok) {
        const result = await response.json();
        alert(`脚本开始运行: ${result.message}`);
        // 可以添加实时状态更新
      } else {
        const error = await response.json();
        alert(`运行失败: ${error.detail}`);
      }
    } catch (error) {
      console.error('运行脚本失败:', error);
      alert('运行脚本失败，请检查网络连接');
    }
  };

  const handleViewReport = async (item) => {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/scripts/${item.id}/report`);
      if (response.ok) {
        const result = await response.json();
        if (result.exists) {
          // 打开报告页面
          window.open(result.report_url, '_blank');
        } else {
          alert('报告文件不存在，请先运行脚本');
        }
      } else {
        alert('获取报告失败');
      }
    } catch (error) {
      console.error('查看报告失败:', error);
      alert('查看报告失败，请检查网络连接');
    }
  };

  const handleViewRunHistory = async (item) => {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/scripts/${item.id}/run-history`);
      if (response.ok) {
        const result = await response.json();
        // 显示运行历史
        const historyText = result.run_history.map(run =>
          `时间: ${run.timestamp}\n状态: ${run.status}\n${run.error ? '错误: ' + run.error : ''}`
        ).join('\n\n');
        alert(`运行历史 (共${result.total_runs}次):\n\n${historyText}`);
      } else {
        alert('获取运行历史失败');
      }
    } catch (error) {
      console.error('查看运行历史失败:', error);
      alert('查看运行历史失败，请检查网络连接');
    }
  };

  const handleDownloadScript = (item) => {
    // 模拟下载脚本
    const scriptContent = `# ${item.description}\n# 生成时间: ${item.createdAt}\n# 脚本名称: ${item.scriptName}\n\nname: ${item.description}\ndescription: 自动化测试脚本\n\nsteps:\n  - name: 示例步骤\n    action: aiTap\n    locate: 示例元素\n    expect: 预期结果`;

    const blob = new Blob([scriptContent], { type: 'text/yaml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    // 使用标准脚本名称格式，但保持.yaml扩展名用于下载
    a.download = item.scriptName.replace('.spec.ts', '.yaml');
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="history-page">
        <div className="page-header">
          <h1>📋 分析历史</h1>
          <p>查看所有UI分析记录和结果</p>
        </div>
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>加载历史记录中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="history-page">
      <div className="page-header">
        <h1>📋 分析历史</h1>
        <p>查看所有UI分析记录和结果</p>
      </div>

      <div className="page-content">
        {historyData.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">📝</div>
            <h3>暂无分析记录</h3>
            <p>开始您的第一次UI分析吧！</p>
          </div>
        ) : (
          <div className="history-list">
            {historyData.map((item) => (
              <div key={item.id} className="history-item">
                <div className="item-header">
                  <div className="item-info">
                    <h3>{item.scriptName}</h3>
                    <p>{item.description}</p>
                    <p style={{ fontSize: '12px', color: '#999', margin: '4px 0 0 0' }}>
                      原始文件: {item.fileName}
                    </p>
                  </div>
                  <div className="item-status">
                    <span 
                      className="status-badge"
                      style={{ backgroundColor: getStatusColor(item.status) }}
                    >
                      {getStatusText(item.status)}
                    </span>
                  </div>
                </div>
                
                <div className="item-stats">
                  <div className="stat">
                    <span className="stat-label">UI元素</span>
                    <span className="stat-value">{item.elementsCount}</span>
                  </div>
                  <div className="stat">
                    <span className="stat-label">交互流程</span>
                    <span className="stat-value">{item.flowsCount}</span>
                  </div>
                  <div className="stat">
                    <span className="stat-label">测试脚本</span>
                    <span className="stat-value">{item.scriptsCount}</span>
                  </div>
                </div>

                <div className="item-footer">
                  <span className="item-time">创建时间: {item.createdAt}</span>
                  <div className="item-actions">
                    <button
                      className="btn-secondary"
                      onClick={() => handleViewDetails(item)}
                    >
                      查看详情
                    </button>
                    {item.status === 'completed' && (
                      <>
                        <button
                          className="btn-success"
                          onClick={() => handleRunScript(item)}
                        >
                          运行脚本
                        </button>
                        <button
                          className="btn-info"
                          onClick={() => handleViewReport(item)}
                        >
                          查看报告
                        </button>
                        <button
                          className="btn-warning"
                          onClick={() => handleViewRunHistory(item)}
                        >
                          运行记录
                        </button>
                        <button
                          className="btn-primary"
                          onClick={() => handleDownloadScript(item)}
                        >
                          下载脚本
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <style>{`
        .history-page {
          min-height: 100vh;
          background: #f8f9fa;
        }

        .page-header {
          background: white;
          border-bottom: 1px solid #e9ecef;
          padding: 24px 0;
          margin-bottom: 24px;
        }

        .page-header h1 {
          margin: 0 0 8px 0;
          color: #333;
          font-size: 28px;
          font-weight: 600;
          max-width: 1200px;
          margin-left: auto;
          margin-right: auto;
          padding: 0 24px;
        }

        .page-header p {
          margin: 0;
          color: #666;
          font-size: 16px;
          max-width: 1200px;
          margin-left: auto;
          margin-right: auto;
          padding: 0 24px;
        }

        .page-content {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 24px;
        }

        .loading-container {
          text-align: center;
          padding: 60px 20px;
        }

        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 4px solid #e9ecef;
          border-top: 4px solid #007bff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 16px;
        }

        .empty-state {
          text-align: center;
          padding: 60px 20px;
          background: white;
          border-radius: 12px;
          box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }

        .empty-icon {
          font-size: 48px;
          margin-bottom: 16px;
        }

        .empty-state h3 {
          margin: 0 0 8px 0;
          color: #333;
          font-size: 20px;
        }

        .empty-state p {
          margin: 0;
          color: #666;
          font-size: 16px;
        }

        .history-list {
          display: grid;
          gap: 16px;
        }

        .history-item {
          background: white;
          border-radius: 12px;
          padding: 20px;
          box-shadow: 0 2px 12px rgba(0,0,0,0.1);
          transition: all 0.2s ease;
        }

        .history-item:hover {
          box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 16px;
        }

        .item-info h3 {
          margin: 0 0 4px 0;
          color: #333;
          font-size: 18px;
          font-weight: 600;
        }

        .item-info p {
          margin: 0;
          color: #666;
          font-size: 14px;
        }

        .status-badge {
          color: white;
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 500;
        }

        .item-stats {
          display: flex;
          gap: 24px;
          margin-bottom: 16px;
          padding: 16px 0;
          border-top: 1px solid #e9ecef;
          border-bottom: 1px solid #e9ecef;
        }

        .stat {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 4px;
        }

        .stat-label {
          font-size: 12px;
          color: #666;
        }

        .stat-value {
          font-size: 18px;
          font-weight: 600;
          color: #333;
        }

        .item-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .item-time {
          font-size: 12px;
          color: #999;
        }

        .item-actions {
          display: flex;
          gap: 8px;
        }

        .btn-primary, .btn-secondary, .btn-success, .btn-info, .btn-warning {
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.2s ease;
          margin-left: 8px;
        }

        .btn-primary {
          background: #007bff;
          color: white;
        }

        .btn-primary:hover {
          background: #0056b3;
        }

        .btn-secondary {
          background: #6c757d;
          color: white;
        }

        .btn-secondary:hover {
          background: #5a6268;
        }

        .btn-success {
          background: #28a745;
          color: white;
        }

        .btn-success:hover {
          background: #218838;
        }

        .btn-info {
          background: #17a2b8;
          color: white;
        }

        .btn-info:hover {
          background: #138496;
        }

        .btn-warning {
          background: #ffc107;
          color: #212529;
        }

        .btn-warning:hover {
          background: #e0a800;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .page-header h1, .page-header p, .page-content {
            padding: 0 16px;
          }

          .item-header {
            flex-direction: column;
            gap: 12px;
          }

          .item-stats {
            justify-content: space-around;
          }

          .item-footer {
            flex-direction: column;
            gap: 12px;
            align-items: flex-start;
          }
        }
      `}</style>
    </div>
  );
};

export default HistoryPage;
