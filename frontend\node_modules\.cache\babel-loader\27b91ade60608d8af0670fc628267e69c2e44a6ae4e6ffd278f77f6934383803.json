{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\components\\\\SimpleUpload.js\",\n  _s = $RefreshSig$();\n/**\n * 简化的上传组件 - 移除项目信息字段，添加实时分析界面\n */\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SimpleUpload = ({\n  onUploadSuccess,\n  onUploadError\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const [showAnalysis, setShowAnalysis] = useState(false);\n  const [analysisProgress, setAnalysisProgress] = useState({\n    overall: 0,\n    currentStep: '准备开始分析...',\n    steps: [{\n      name: '初始化',\n      status: 'pending',\n      progress: 0\n    }, {\n      name: '元素分析和智能识别',\n      status: 'pending',\n      progress: 0\n    }, {\n      name: '生成自动化测试脚本',\n      status: 'pending',\n      progress: 0\n    }]\n  });\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n    setIsUploading(true);\n    setShowAnalysis(true);\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n\n      // 开始模拟分析进度\n      simulateAnalysisProgress();\n      const response = await fetch('http://localhost:8001/api/v1/upload', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n      const result = await response.json();\n      onUploadSuccess(result);\n    } catch (error) {\n      console.error('Upload error:', error);\n      setShowAnalysis(false);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  const simulateAnalysisProgress = () => {\n    let step = 0;\n    const steps = ['初始化', '元素分析和智能识别', '生成自动化测试脚本'];\n    const updateProgress = () => {\n      if (step < steps.length) {\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: Math.round((step + 1) / steps.length * 100),\n          currentStep: `正在执行: ${steps[step]}`,\n          steps: prev.steps.map((s, index) => ({\n            ...s,\n            status: index < step ? 'completed' : index === step ? 'processing' : 'pending',\n            progress: index < step ? 100 : index === step ? Math.random() * 80 + 20 : 0\n          }))\n        }));\n        step++;\n        setTimeout(updateProgress, 2000 + Math.random() * 3000);\n      } else {\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: 100,\n          currentStep: '分析完成',\n          steps: prev.steps.map(s => ({\n            ...s,\n            status: 'completed',\n            progress: 100\n          }))\n        }));\n      }\n    };\n    setTimeout(updateProgress, 1000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '24px',\n      height: '100vh',\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: showAnalysis ? '0 0 400px' : '1',\n        background: 'white',\n        padding: '24px',\n        borderRadius: '12px',\n        boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n        transition: 'all 0.3s ease'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '24px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              marginBottom: '12px',\n              fontWeight: '600',\n              color: '#333',\n              fontSize: '16px'\n            },\n            children: \"\\uD83D\\uDCC1 \\u4E0A\\u4F20UI\\u754C\\u9762\\u622A\\u56FE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"file-upload-area\",\n            onClick: () => document.getElementById('file-input').click(),\n            style: {\n              height: '240px',\n              border: '2px dashed #667eea',\n              borderRadius: '12px',\n              background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.3s ease',\n              position: 'relative',\n              overflow: 'hidden'\n            },\n            onDragOver: e => {\n              e.preventDefault();\n              e.currentTarget.style.borderColor = '#4f46e5';\n              e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';\n            },\n            onDragLeave: e => {\n              e.currentTarget.style.borderColor = '#667eea';\n              e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';\n            },\n            onDrop: e => {\n              e.preventDefault();\n              const files = e.dataTransfer.files;\n              if (files.length > 0) {\n                setSelectedFile(files[0]);\n              }\n              e.currentTarget.style.borderColor = '#667eea';\n              e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              id: \"file-input\",\n              type: \"file\",\n              accept: \"image/*\",\n              onChange: handleFileSelect,\n              style: {\n                display: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), selectedFile ? /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: '20px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '48px',\n                  marginBottom: '16px',\n                  color: '#667eea'\n                },\n                children: \"\\u2705\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 8px 0',\n                  color: '#333',\n                  fontSize: '18px'\n                },\n                children: \"\\u6587\\u4EF6\\u5DF2\\u9009\\u62E9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0 0 8px 0',\n                  color: '#666',\n                  fontSize: '14px',\n                  fontWeight: '500'\n                },\n                children: selectedFile.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0',\n                  color: '#999',\n                  fontSize: '12px'\n                },\n                children: [\"\\u5927\\u5C0F: \", (selectedFile.size / 1024 / 1024).toFixed(2), \" MB\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: e => {\n                  e.stopPropagation();\n                  setSelectedFile(null);\n                  document.getElementById('file-input').value = '';\n                },\n                style: {\n                  marginTop: '12px',\n                  background: 'rgba(239, 68, 68, 0.1)',\n                  color: '#ef4444',\n                  border: '1px solid rgba(239, 68, 68, 0.3)',\n                  padding: '6px 12px',\n                  borderRadius: '6px',\n                  fontSize: '12px',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                },\n                children: \"\\u91CD\\u65B0\\u9009\\u62E9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: '20px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '48px',\n                  marginBottom: '16px',\n                  color: '#667eea'\n                },\n                children: \"\\uD83D\\uDCE4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: '0 0 8px 0',\n                  color: '#333',\n                  fontSize: '18px'\n                },\n                children: \"\\u70B9\\u51FB\\u6216\\u62D6\\u62FD\\u4E0A\\u4F20\\u56FE\\u7247\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0 0 4px 0',\n                  color: '#666',\n                  fontSize: '14px'\n                },\n                children: \"\\u652F\\u6301 PNG\\u3001JPG\\u3001JPEG\\u3001GIF \\u683C\\u5F0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '0',\n                  color: '#999',\n                  fontSize: '12px'\n                },\n                children: \"\\u6587\\u4EF6\\u5927\\u5C0F\\u4E0D\\u8D85\\u8FC7 10MB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: '16px',\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  color: 'white',\n                  padding: '8px 20px',\n                  borderRadius: '20px',\n                  fontSize: '14px',\n                  fontWeight: '500',\n                  display: 'inline-block'\n                },\n                children: \"\\u9009\\u62E9\\u6587\\u4EF6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"description\",\n            style: {\n              display: 'block',\n              marginBottom: '8px',\n              fontWeight: '600',\n              color: '#333',\n              fontSize: '16px'\n            },\n            children: \"\\uD83C\\uDFAF \\u754C\\u9762\\u529F\\u80FD\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"description\",\n            value: description,\n            onChange: e => setDescription(e.target.value),\n            placeholder: \"\\u8BF7\\u8BE6\\u7EC6\\u63CF\\u8FF0\\u8FD9\\u4E2A\\u754C\\u9762\\u7684\\u4E3B\\u8981\\u529F\\u80FD\\uFF0C\\u4F8B\\u5982\\uFF1A\\u8FD9\\u662F\\u4E00\\u4E2A\\u767B\\u5F55\\u9875\\u9762\\uFF0C\\u5305\\u542B\\u7528\\u6237\\u540D\\u5BC6\\u7801\\u8F93\\u5165\\u6846\\u548C\\u767B\\u5F55\\u6309\\u94AE...\",\n            rows: 4,\n            style: {\n              width: '100%',\n              padding: '12px',\n              border: '2px solid #e5e7eb',\n              borderRadius: '8px',\n              resize: 'vertical',\n              fontFamily: 'inherit',\n              fontSize: '14px',\n              lineHeight: '1.5',\n              transition: 'border-color 0.2s ease',\n              background: '#fafafa'\n            },\n            onFocus: e => {\n              e.target.style.borderColor = '#667eea';\n              e.target.style.background = '#ffffff';\n            },\n            onBlur: e => {\n              e.target.style.borderColor = '#e5e7eb';\n              e.target.style.background = '#fafafa';\n            },\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'right',\n              fontSize: '12px',\n              color: '#666',\n              marginTop: '4px'\n            },\n            children: [description.length, \"/500\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: isUploading || !selectedFile || !description.trim(),\n          style: {\n            background: isUploading ? '#6c757d' : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '8px',\n            fontSize: '16px',\n            fontWeight: '500',\n            cursor: isUploading ? 'not-allowed' : 'pointer',\n            transition: 'all 0.3s ease',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px',\n            width: '100%',\n            justifyContent: 'center'\n          },\n          children: isUploading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                width: '16px',\n                height: '16px',\n                border: '2px solid transparent',\n                borderTop: '2px solid white',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this), \"\\u5F00\\u59CB\\u5206\\u6790\\u4E2D...\"]\n          }, void 0, true) : '🚀 开始分析'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), showAnalysis && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: '1',\n        background: 'white',\n        padding: '24px',\n        borderRadius: '12px',\n        boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n        overflow: 'auto'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          marginBottom: '24px',\n          paddingBottom: '16px',\n          borderBottom: '2px solid #f1f3f4'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: 0,\n            color: '#333',\n            fontSize: '20px',\n            fontWeight: '600'\n          },\n          children: \"\\uD83D\\uDD0D \\u5B9E\\u65F6\\u5206\\u6790\\u8FDB\\u5EA6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f8f9fa',\n            padding: '8px 16px',\n            borderRadius: '20px',\n            fontSize: '14px',\n            fontWeight: '500',\n            color: '#666'\n          },\n          children: [analysisProgress.overall, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: '#f8f9ff',\n          border: '1px solid #e5e7eb',\n          borderRadius: '8px',\n          padding: '16px',\n          marginBottom: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '14px',\n            color: '#666',\n            marginBottom: '8px'\n          },\n          children: \"\\u5206\\u6790\\u72B6\\u6001\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '16px',\n            fontWeight: '600',\n            color: '#333'\n          },\n          children: analysisProgress.currentStep\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '24px'\n        },\n        children: analysisProgress.steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            padding: '16px',\n            marginBottom: '12px',\n            background: step.status === 'processing' ? '#f0f4ff' : '#fafafa',\n            border: `2px solid ${step.status === 'completed' ? '#28a745' : step.status === 'processing' ? '#667eea' : '#e9ecef'}`,\n            borderRadius: '8px',\n            transition: 'all 0.3s ease'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '32px',\n              height: '32px',\n              borderRadius: '50%',\n              background: step.status === 'completed' ? '#28a745' : step.status === 'processing' ? '#667eea' : '#e9ecef',\n              color: 'white',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontSize: '14px',\n              fontWeight: '600',\n              marginRight: '16px'\n            },\n            children: step.status === 'completed' ? '✓' : step.status === 'processing' ? '⚡' : index + 1\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: '600',\n                color: '#333',\n                marginBottom: '4px'\n              },\n              children: step.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '100%',\n                height: '6px',\n                background: '#e9ecef',\n                borderRadius: '3px',\n                overflow: 'hidden'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  height: '100%',\n                  width: `${step.progress}%`,\n                  background: step.status === 'completed' ? '#28a745' : step.status === 'processing' ? '#667eea' : '#e9ecef',\n                  transition: 'width 0.5s ease',\n                  borderRadius: '3px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleUpload, \"uXdqIXhBuTsq5cLMg12a1LPI6l0=\");\n_c = SimpleUpload;\nexport default SimpleUpload;\nvar _c;\n$RefreshReg$(_c, \"SimpleUpload\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SimpleUpload", "onUploadSuccess", "onUploadError", "_s", "selectedFile", "setSelectedFile", "description", "setDescription", "isUploading", "setIsUploading", "showAnalysis", "setShowAnalysis", "analysisProgress", "setAnalysisProgress", "overall", "currentStep", "steps", "name", "status", "progress", "handleFileSelect", "event", "file", "target", "files", "handleSubmit", "preventDefault", "trim", "formData", "FormData", "append", "simulateAnalysisProgress", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "result", "error", "console", "message", "step", "updateProgress", "length", "prev", "Math", "round", "map", "s", "index", "random", "setTimeout", "style", "display", "gap", "height", "padding", "children", "flex", "background", "borderRadius", "boxShadow", "transition", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "marginBottom", "fontWeight", "color", "fontSize", "className", "onClick", "document", "getElementById", "click", "border", "flexDirection", "alignItems", "justifyContent", "cursor", "position", "overflow", "onDragOver", "e", "currentTarget", "borderColor", "onDragLeave", "onDrop", "dataTransfer", "id", "type", "accept", "onChange", "textAlign", "margin", "size", "toFixed", "stopPropagation", "value", "marginTop", "htmlFor", "placeholder", "rows", "width", "resize", "fontFamily", "lineHeight", "onFocus", "onBlur", "required", "disabled", "borderTop", "animation", "paddingBottom", "borderBottom", "marginRight", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/components/SimpleUpload.js"], "sourcesContent": ["/**\n * 简化的上传组件 - 移除项目信息字段，添加实时分析界面\n */\nimport React, { useState } from 'react';\n\nconst SimpleUpload = ({ onUploadSuccess, onUploadError }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [description, setDescription] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n  const [showAnalysis, setShowAnalysis] = useState(false);\n  const [analysisProgress, setAnalysisProgress] = useState({\n    overall: 0,\n    currentStep: '准备开始分析...',\n    steps: [\n      { name: '初始化', status: 'pending', progress: 0 },\n      { name: '元素分析和智能识别', status: 'pending', progress: 0 },\n      { name: '生成自动化测试脚本', status: 'pending', progress: 0 }\n    ]\n  });\n\n  const handleFileSelect = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n    }\n  };\n\n  const handleSubmit = async (event) => {\n    event.preventDefault();\n\n    if (!selectedFile) {\n      onUploadError('请选择图片文件');\n      return;\n    }\n\n    if (!description.trim()) {\n      onUploadError('请输入界面功能描述');\n      return;\n    }\n\n    setIsUploading(true);\n    setShowAnalysis(true);\n\n    try {\n      const formData = new FormData();\n      formData.append('image_file', selectedFile);\n      formData.append('description', description.trim());\n\n      // 开始模拟分析进度\n      simulateAnalysisProgress();\n\n      const response = await fetch('http://localhost:8001/api/v1/upload', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      onUploadSuccess(result);\n\n    } catch (error) {\n      console.error('Upload error:', error);\n      setShowAnalysis(false);\n      onUploadError(error.message || '上传失败，请重试');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const simulateAnalysisProgress = () => {\n    let step = 0;\n    const steps = ['初始化', '元素分析和智能识别', '生成自动化测试脚本'];\n    \n    const updateProgress = () => {\n      if (step < steps.length) {\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: Math.round((step + 1) / steps.length * 100),\n          currentStep: `正在执行: ${steps[step]}`,\n          steps: prev.steps.map((s, index) => ({\n            ...s,\n            status: index < step ? 'completed' : index === step ? 'processing' : 'pending',\n            progress: index < step ? 100 : index === step ? Math.random() * 80 + 20 : 0\n          }))\n        }));\n        \n        step++;\n        setTimeout(updateProgress, 2000 + Math.random() * 3000);\n      } else {\n        setAnalysisProgress(prev => ({\n          ...prev,\n          overall: 100,\n          currentStep: '分析完成',\n          steps: prev.steps.map(s => ({ ...s, status: 'completed', progress: 100 }))\n        }));\n      }\n    };\n    \n    setTimeout(updateProgress, 1000);\n  };\n\n  return (\n    <div style={{ display: 'flex', gap: '24px', height: '100vh', padding: '24px' }}>\n      {/* 左侧上传区域 */}\n      <div style={{ \n        flex: showAnalysis ? '0 0 400px' : '1',\n        background: 'white', \n        padding: '24px', \n        borderRadius: '12px', \n        boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n        transition: 'all 0.3s ease'\n      }}>\n        <h3>📁 上传UI界面截图</h3>\n        <form onSubmit={handleSubmit}>\n          <div style={{ marginBottom: '24px' }}>\n            <label style={{ display: 'block', marginBottom: '12px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n              📁 上传UI界面截图\n            </label>\n            <div\n              className=\"file-upload-area\"\n              onClick={() => document.getElementById('file-input').click()}\n              style={{\n                height: '240px',\n                border: '2px dashed #667eea',\n                borderRadius: '12px',\n                background: selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                justifyContent: 'center',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease',\n                position: 'relative',\n                overflow: 'hidden'\n              }}\n              onDragOver={(e) => {\n                e.preventDefault();\n                e.currentTarget.style.borderColor = '#4f46e5';\n                e.currentTarget.style.background = 'linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%)';\n              }}\n              onDragLeave={(e) => {\n                e.currentTarget.style.borderColor = '#667eea';\n                e.currentTarget.style.background = selectedFile ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)' : 'linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%)';\n              }}\n              onDrop={(e) => {\n                e.preventDefault();\n                const files = e.dataTransfer.files;\n                if (files.length > 0) {\n                  setSelectedFile(files[0]);\n                }\n                e.currentTarget.style.borderColor = '#667eea';\n                e.currentTarget.style.background = 'linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%)';\n              }}\n            >\n              <input\n                id=\"file-input\"\n                type=\"file\"\n                accept=\"image/*\"\n                onChange={handleFileSelect}\n                style={{ display: 'none' }}\n              />\n\n              {selectedFile ? (\n                <div style={{ textAlign: 'center', padding: '20px' }}>\n                  <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>✅</div>\n                  <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>文件已选择</h3>\n                  <p style={{ margin: '0 0 8px 0', color: '#666', fontSize: '14px', fontWeight: '500' }}>\n                    {selectedFile.name}\n                  </p>\n                  <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>\n                    大小: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB\n                  </p>\n                  <button\n                    type=\"button\"\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      setSelectedFile(null);\n                      document.getElementById('file-input').value = '';\n                    }}\n                    style={{\n                      marginTop: '12px',\n                      background: 'rgba(239, 68, 68, 0.1)',\n                      color: '#ef4444',\n                      border: '1px solid rgba(239, 68, 68, 0.3)',\n                      padding: '6px 12px',\n                      borderRadius: '6px',\n                      fontSize: '12px',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    }}\n                  >\n                    重新选择\n                  </button>\n                </div>\n              ) : (\n                <div style={{ textAlign: 'center', padding: '20px' }}>\n                  <div style={{ fontSize: '48px', marginBottom: '16px', color: '#667eea' }}>📤</div>\n                  <h3 style={{ margin: '0 0 8px 0', color: '#333', fontSize: '18px' }}>点击或拖拽上传图片</h3>\n                  <p style={{ margin: '0 0 4px 0', color: '#666', fontSize: '14px' }}>\n                    支持 PNG、JPG、JPEG、GIF 格式\n                  </p>\n                  <p style={{ margin: '0', color: '#999', fontSize: '12px' }}>\n                    文件大小不超过 10MB\n                  </p>\n                  <div style={{\n                    marginTop: '16px',\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    color: 'white',\n                    padding: '8px 20px',\n                    borderRadius: '20px',\n                    fontSize: '14px',\n                    fontWeight: '500',\n                    display: 'inline-block'\n                  }}>\n                    选择文件\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* 界面功能描述 */}\n          <div style={{ marginBottom: '20px' }}>\n            <label htmlFor=\"description\" style={{ display: 'block', marginBottom: '8px', fontWeight: '600', color: '#333', fontSize: '16px' }}>\n              🎯 界面功能描述\n            </label>\n            <textarea\n              id=\"description\"\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              placeholder=\"请详细描述这个界面的主要功能，例如：这是一个登录页面，包含用户名密码输入框和登录按钮...\"\n              rows={4}\n              style={{\n                width: '100%',\n                padding: '12px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '8px',\n                resize: 'vertical',\n                fontFamily: 'inherit',\n                fontSize: '14px',\n                lineHeight: '1.5',\n                transition: 'border-color 0.2s ease',\n                background: '#fafafa'\n              }}\n              onFocus={(e) => {\n                e.target.style.borderColor = '#667eea';\n                e.target.style.background = '#ffffff';\n              }}\n              onBlur={(e) => {\n                e.target.style.borderColor = '#e5e7eb';\n                e.target.style.background = '#fafafa';\n              }}\n              required\n            />\n            <div style={{ textAlign: 'right', fontSize: '12px', color: '#666', marginTop: '4px' }}>\n              {description.length}/500\n            </div>\n          </div>\n\n          <button\n            type=\"submit\"\n            disabled={isUploading || !selectedFile || !description.trim()}\n            style={{\n              background: isUploading ? '#6c757d' : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '8px',\n              fontSize: '16px',\n              fontWeight: '500',\n              cursor: isUploading ? 'not-allowed' : 'pointer',\n              transition: 'all 0.3s ease',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px',\n              width: '100%',\n              justifyContent: 'center'\n            }}\n          >\n            {isUploading ? (\n              <>\n                <span style={{\n                  width: '16px',\n                  height: '16px',\n                  border: '2px solid transparent',\n                  borderTop: '2px solid white',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}></span>\n                开始分析中...\n              </>\n            ) : (\n              '🚀 开始分析'\n            )}\n          </button>\n        </form>\n      </div>\n\n      {/* 右侧实时分析界面 */}\n      {showAnalysis && (\n        <div style={{\n          flex: '1',\n          background: 'white',\n          padding: '24px',\n          borderRadius: '12px',\n          boxShadow: '0 2px 12px rgba(0,0,0,0.1)',\n          overflow: 'auto'\n        }}>\n          <div style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            marginBottom: '24px',\n            paddingBottom: '16px',\n            borderBottom: '2px solid #f1f3f4'\n          }}>\n            <h3 style={{ margin: 0, color: '#333', fontSize: '20px', fontWeight: '600' }}>\n              🔍 实时分析进度\n            </h3>\n            <div style={{\n              background: '#f8f9fa',\n              padding: '8px 16px',\n              borderRadius: '20px',\n              fontSize: '14px',\n              fontWeight: '500',\n              color: '#666'\n            }}>\n              {analysisProgress.overall}%\n            </div>\n          </div>\n\n          {/* 当前步骤 */}\n          <div style={{\n            background: '#f8f9ff',\n            border: '1px solid #e5e7eb',\n            borderRadius: '8px',\n            padding: '16px',\n            marginBottom: '24px'\n          }}>\n            <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>\n              分析状态\n            </div>\n            <div style={{ fontSize: '16px', fontWeight: '600', color: '#333' }}>\n              {analysisProgress.currentStep}\n            </div>\n          </div>\n\n          {/* 分析步骤 */}\n          <div style={{ marginBottom: '24px' }}>\n            {analysisProgress.steps.map((step, index) => (\n              <div key={index} style={{\n                display: 'flex',\n                alignItems: 'center',\n                padding: '16px',\n                marginBottom: '12px',\n                background: step.status === 'processing' ? '#f0f4ff' : '#fafafa',\n                border: `2px solid ${step.status === 'completed' ? '#28a745' :\n                                    step.status === 'processing' ? '#667eea' : '#e9ecef'}`,\n                borderRadius: '8px',\n                transition: 'all 0.3s ease'\n              }}>\n                <div style={{\n                  width: '32px',\n                  height: '32px',\n                  borderRadius: '50%',\n                  background: step.status === 'completed' ? '#28a745' :\n                             step.status === 'processing' ? '#667eea' : '#e9ecef',\n                  color: 'white',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '14px',\n                  fontWeight: '600',\n                  marginRight: '16px'\n                }}>\n                  {step.status === 'completed' ? '✓' :\n                   step.status === 'processing' ? '⚡' : index + 1}\n                </div>\n                <div style={{ flex: 1 }}>\n                  <div style={{ fontWeight: '600', color: '#333', marginBottom: '4px' }}>\n                    {step.name}\n                  </div>\n                  <div style={{\n                    width: '100%',\n                    height: '6px',\n                    background: '#e9ecef',\n                    borderRadius: '3px',\n                    overflow: 'hidden'\n                  }}>\n                    <div style={{\n                      height: '100%',\n                      width: `${step.progress}%`,\n                      background: step.status === 'completed' ? '#28a745' :\n                                 step.status === 'processing' ? '#667eea' : '#e9ecef',\n                      transition: 'width 0.5s ease',\n                      borderRadius: '3px'\n                    }}></div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      <style>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default SimpleUpload;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,YAAY,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlB,QAAQ,CAAC;IACvDmB,OAAO,EAAE,CAAC;IACVC,WAAW,EAAE,WAAW;IACxBC,KAAK,EAAE,CACL;MAAEC,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAE,CAAC,EAC/C;MAAEF,IAAI,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAE,CAAC,EACrD;MAAEF,IAAI,EAAE,WAAW;MAAEC,MAAM,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAE,CAAC;EAEzD,CAAC,CAAC;EAEF,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACRjB,eAAe,CAACiB,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOJ,KAAK,IAAK;IACpCA,KAAK,CAACK,cAAc,CAAC,CAAC;IAEtB,IAAI,CAACtB,YAAY,EAAE;MACjBF,aAAa,CAAC,SAAS,CAAC;MACxB;IACF;IAEA,IAAI,CAACI,WAAW,CAACqB,IAAI,CAAC,CAAC,EAAE;MACvBzB,aAAa,CAAC,WAAW,CAAC;MAC1B;IACF;IAEAO,cAAc,CAAC,IAAI,CAAC;IACpBE,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF,MAAMiB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE1B,YAAY,CAAC;MAC3CwB,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAExB,WAAW,CAACqB,IAAI,CAAC,CAAC,CAAC;;MAElD;MACAI,wBAAwB,CAAC,CAAC;MAE1B,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,qCAAqC,EAAE;QAClEC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEP;MACR,CAAC,CAAC;MAEF,IAAI,CAACI,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,uBAAuBR,QAAQ,CAACd,MAAM,EAAE,CAAC;MAC/E;MAEA,MAAMuB,MAAM,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MACpCrC,eAAe,CAACwC,MAAM,CAAC;IAEzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC/B,eAAe,CAAC,KAAK,CAAC;MACtBT,aAAa,CAACwC,KAAK,CAACE,OAAO,IAAI,UAAU,CAAC;IAC5C,CAAC,SAAS;MACRnC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMsB,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAIc,IAAI,GAAG,CAAC;IACZ,MAAM7B,KAAK,GAAG,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,CAAC;IAE/C,MAAM8B,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAID,IAAI,GAAG7B,KAAK,CAAC+B,MAAM,EAAE;QACvBlC,mBAAmB,CAACmC,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACPlC,OAAO,EAAEmC,IAAI,CAACC,KAAK,CAAC,CAACL,IAAI,GAAG,CAAC,IAAI7B,KAAK,CAAC+B,MAAM,GAAG,GAAG,CAAC;UACpDhC,WAAW,EAAE,SAASC,KAAK,CAAC6B,IAAI,CAAC,EAAE;UACnC7B,KAAK,EAAEgC,IAAI,CAAChC,KAAK,CAACmC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,MAAM;YACnC,GAAGD,CAAC;YACJlC,MAAM,EAAEmC,KAAK,GAAGR,IAAI,GAAG,WAAW,GAAGQ,KAAK,KAAKR,IAAI,GAAG,YAAY,GAAG,SAAS;YAC9E1B,QAAQ,EAAEkC,KAAK,GAAGR,IAAI,GAAG,GAAG,GAAGQ,KAAK,KAAKR,IAAI,GAAGI,IAAI,CAACK,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;UAC5E,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEHT,IAAI,EAAE;QACNU,UAAU,CAACT,cAAc,EAAE,IAAI,GAAGG,IAAI,CAACK,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC;MACzD,CAAC,MAAM;QACLzC,mBAAmB,CAACmC,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACPlC,OAAO,EAAE,GAAG;UACZC,WAAW,EAAE,MAAM;UACnBC,KAAK,EAAEgC,IAAI,CAAChC,KAAK,CAACmC,GAAG,CAACC,CAAC,KAAK;YAAE,GAAGA,CAAC;YAAElC,MAAM,EAAE,WAAW;YAAEC,QAAQ,EAAE;UAAI,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;MACL;IACF,CAAC;IAEDoC,UAAU,CAACT,cAAc,EAAE,IAAI,CAAC;EAClC,CAAC;EAED,oBACEjD,OAAA;IAAK2D,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,GAAG,EAAE,MAAM;MAAEC,MAAM,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAE7EhE,OAAA;MAAK2D,KAAK,EAAE;QACVM,IAAI,EAAEpD,YAAY,GAAG,WAAW,GAAG,GAAG;QACtCqD,UAAU,EAAE,OAAO;QACnBH,OAAO,EAAE,MAAM;QACfI,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE,4BAA4B;QACvCC,UAAU,EAAE;MACd,CAAE;MAAAL,QAAA,gBACAhE,OAAA;QAAAgE,QAAA,EAAI;MAAW;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpBzE,OAAA;QAAM0E,QAAQ,EAAE9C,YAAa;QAAAoC,QAAA,gBAC3BhE,OAAA;UAAK2D,KAAK,EAAE;YAAEgB,YAAY,EAAE;UAAO,CAAE;UAAAX,QAAA,gBACnChE,OAAA;YAAO2D,KAAK,EAAE;cAAEC,OAAO,EAAE,OAAO;cAAEe,YAAY,EAAE,MAAM;cAAEC,UAAU,EAAE,KAAK;cAAEC,KAAK,EAAE,MAAM;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAE9G;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRzE,OAAA;YACE+E,SAAS,EAAC,kBAAkB;YAC5BC,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAACC,KAAK,CAAC,CAAE;YAC7DxB,KAAK,EAAE;cACLG,MAAM,EAAE,OAAO;cACfsB,MAAM,EAAE,oBAAoB;cAC5BjB,YAAY,EAAE,MAAM;cACpBD,UAAU,EAAE3D,YAAY,GAAG,mDAAmD,GAAG,mDAAmD;cACpIqD,OAAO,EAAE,MAAM;cACfyB,aAAa,EAAE,QAAQ;cACvBC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,MAAM,EAAE,SAAS;cACjBnB,UAAU,EAAE,eAAe;cAC3BoB,QAAQ,EAAE,UAAU;cACpBC,QAAQ,EAAE;YACZ,CAAE;YACFC,UAAU,EAAGC,CAAC,IAAK;cACjBA,CAAC,CAAC/D,cAAc,CAAC,CAAC;cAClB+D,CAAC,CAACC,aAAa,CAAClC,KAAK,CAACmC,WAAW,GAAG,SAAS;cAC7CF,CAAC,CAACC,aAAa,CAAClC,KAAK,CAACO,UAAU,GAAG,mDAAmD;YACxF,CAAE;YACF6B,WAAW,EAAGH,CAAC,IAAK;cAClBA,CAAC,CAACC,aAAa,CAAClC,KAAK,CAACmC,WAAW,GAAG,SAAS;cAC7CF,CAAC,CAACC,aAAa,CAAClC,KAAK,CAACO,UAAU,GAAG3D,YAAY,GAAG,mDAAmD,GAAG,mDAAmD;YAC7J,CAAE;YACFyF,MAAM,EAAGJ,CAAC,IAAK;cACbA,CAAC,CAAC/D,cAAc,CAAC,CAAC;cAClB,MAAMF,KAAK,GAAGiE,CAAC,CAACK,YAAY,CAACtE,KAAK;cAClC,IAAIA,KAAK,CAACuB,MAAM,GAAG,CAAC,EAAE;gBACpB1C,eAAe,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC;cAC3B;cACAiE,CAAC,CAACC,aAAa,CAAClC,KAAK,CAACmC,WAAW,GAAG,SAAS;cAC7CF,CAAC,CAACC,aAAa,CAAClC,KAAK,CAACO,UAAU,GAAG,mDAAmD;YACxF,CAAE;YAAAF,QAAA,gBAEFhE,OAAA;cACEkG,EAAE,EAAC,YAAY;cACfC,IAAI,EAAC,MAAM;cACXC,MAAM,EAAC,SAAS;cAChBC,QAAQ,EAAE9E,gBAAiB;cAC3BoC,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAO;YAAE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,EAEDlE,YAAY,gBACXP,OAAA;cAAK2D,KAAK,EAAE;gBAAE2C,SAAS,EAAE,QAAQ;gBAAEvC,OAAO,EAAE;cAAO,CAAE;cAAAC,QAAA,gBACnDhE,OAAA;gBAAK2D,KAAK,EAAE;kBAAEmB,QAAQ,EAAE,MAAM;kBAAEH,YAAY,EAAE,MAAM;kBAAEE,KAAK,EAAE;gBAAU,CAAE;gBAAAb,QAAA,EAAC;cAAC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjFzE,OAAA;gBAAI2D,KAAK,EAAE;kBAAE4C,MAAM,EAAE,WAAW;kBAAE1B,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAd,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/EzE,OAAA;gBAAG2D,KAAK,EAAE;kBAAE4C,MAAM,EAAE,WAAW;kBAAE1B,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE,MAAM;kBAAEF,UAAU,EAAE;gBAAM,CAAE;gBAAAZ,QAAA,EACnFzD,YAAY,CAACa;cAAI;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACJzE,OAAA;gBAAG2D,KAAK,EAAE;kBAAE4C,MAAM,EAAE,GAAG;kBAAE1B,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAd,QAAA,GAAC,gBACtD,EAAC,CAACzD,YAAY,CAACiG,IAAI,GAAG,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KACpD;cAAA;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJzE,OAAA;gBACEmG,IAAI,EAAC,QAAQ;gBACbnB,OAAO,EAAGY,CAAC,IAAK;kBACdA,CAAC,CAACc,eAAe,CAAC,CAAC;kBACnBlG,eAAe,CAAC,IAAI,CAAC;kBACrByE,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC,CAACyB,KAAK,GAAG,EAAE;gBAClD,CAAE;gBACFhD,KAAK,EAAE;kBACLiD,SAAS,EAAE,MAAM;kBACjB1C,UAAU,EAAE,wBAAwB;kBACpCW,KAAK,EAAE,SAAS;kBAChBO,MAAM,EAAE,kCAAkC;kBAC1CrB,OAAO,EAAE,UAAU;kBACnBI,YAAY,EAAE,KAAK;kBACnBW,QAAQ,EAAE,MAAM;kBAChBU,MAAM,EAAE,SAAS;kBACjBnB,UAAU,EAAE;gBACd,CAAE;gBAAAL,QAAA,EACH;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,gBAENzE,OAAA;cAAK2D,KAAK,EAAE;gBAAE2C,SAAS,EAAE,QAAQ;gBAAEvC,OAAO,EAAE;cAAO,CAAE;cAAAC,QAAA,gBACnDhE,OAAA;gBAAK2D,KAAK,EAAE;kBAAEmB,QAAQ,EAAE,MAAM;kBAAEH,YAAY,EAAE,MAAM;kBAAEE,KAAK,EAAE;gBAAU,CAAE;gBAAAb,QAAA,EAAC;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClFzE,OAAA;gBAAI2D,KAAK,EAAE;kBAAE4C,MAAM,EAAE,WAAW;kBAAE1B,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAd,QAAA,EAAC;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnFzE,OAAA;gBAAG2D,KAAK,EAAE;kBAAE4C,MAAM,EAAE,WAAW;kBAAE1B,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAd,QAAA,EAAC;cAEpE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJzE,OAAA;gBAAG2D,KAAK,EAAE;kBAAE4C,MAAM,EAAE,GAAG;kBAAE1B,KAAK,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAd,QAAA,EAAC;cAE5D;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJzE,OAAA;gBAAK2D,KAAK,EAAE;kBACViD,SAAS,EAAE,MAAM;kBACjB1C,UAAU,EAAE,mDAAmD;kBAC/DW,KAAK,EAAE,OAAO;kBACdd,OAAO,EAAE,UAAU;kBACnBI,YAAY,EAAE,MAAM;kBACpBW,QAAQ,EAAE,MAAM;kBAChBF,UAAU,EAAE,KAAK;kBACjBhB,OAAO,EAAE;gBACX,CAAE;gBAAAI,QAAA,EAAC;cAEH;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzE,OAAA;UAAK2D,KAAK,EAAE;YAAEgB,YAAY,EAAE;UAAO,CAAE;UAAAX,QAAA,gBACnChE,OAAA;YAAO6G,OAAO,EAAC,aAAa;YAAClD,KAAK,EAAE;cAAEC,OAAO,EAAE,OAAO;cAAEe,YAAY,EAAE,KAAK;cAAEC,UAAU,EAAE,KAAK;cAAEC,KAAK,EAAE,MAAM;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAEnI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRzE,OAAA;YACEkG,EAAE,EAAC,aAAa;YAChBS,KAAK,EAAElG,WAAY;YACnB4F,QAAQ,EAAGT,CAAC,IAAKlF,cAAc,CAACkF,CAAC,CAAClE,MAAM,CAACiF,KAAK,CAAE;YAChDG,WAAW,EAAC,iQAA+C;YAC3DC,IAAI,EAAE,CAAE;YACRpD,KAAK,EAAE;cACLqD,KAAK,EAAE,MAAM;cACbjD,OAAO,EAAE,MAAM;cACfqB,MAAM,EAAE,mBAAmB;cAC3BjB,YAAY,EAAE,KAAK;cACnB8C,MAAM,EAAE,UAAU;cAClBC,UAAU,EAAE,SAAS;cACrBpC,QAAQ,EAAE,MAAM;cAChBqC,UAAU,EAAE,KAAK;cACjB9C,UAAU,EAAE,wBAAwB;cACpCH,UAAU,EAAE;YACd,CAAE;YACFkD,OAAO,EAAGxB,CAAC,IAAK;cACdA,CAAC,CAAClE,MAAM,CAACiC,KAAK,CAACmC,WAAW,GAAG,SAAS;cACtCF,CAAC,CAAClE,MAAM,CAACiC,KAAK,CAACO,UAAU,GAAG,SAAS;YACvC,CAAE;YACFmD,MAAM,EAAGzB,CAAC,IAAK;cACbA,CAAC,CAAClE,MAAM,CAACiC,KAAK,CAACmC,WAAW,GAAG,SAAS;cACtCF,CAAC,CAAClE,MAAM,CAACiC,KAAK,CAACO,UAAU,GAAG,SAAS;YACvC,CAAE;YACFoD,QAAQ;UAAA;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFzE,OAAA;YAAK2D,KAAK,EAAE;cAAE2C,SAAS,EAAE,OAAO;cAAExB,QAAQ,EAAE,MAAM;cAAED,KAAK,EAAE,MAAM;cAAE+B,SAAS,EAAE;YAAM,CAAE;YAAA5C,QAAA,GACnFvD,WAAW,CAACyC,MAAM,EAAC,MACtB;UAAA;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzE,OAAA;UACEmG,IAAI,EAAC,QAAQ;UACboB,QAAQ,EAAE5G,WAAW,IAAI,CAACJ,YAAY,IAAI,CAACE,WAAW,CAACqB,IAAI,CAAC,CAAE;UAC9D6B,KAAK,EAAE;YACLO,UAAU,EAAEvD,WAAW,GAAG,SAAS,GAAG,mDAAmD;YACzFkE,KAAK,EAAE,OAAO;YACdO,MAAM,EAAE,MAAM;YACdrB,OAAO,EAAE,WAAW;YACpBI,YAAY,EAAE,KAAK;YACnBW,QAAQ,EAAE,MAAM;YAChBF,UAAU,EAAE,KAAK;YACjBY,MAAM,EAAE7E,WAAW,GAAG,aAAa,GAAG,SAAS;YAC/C0D,UAAU,EAAE,eAAe;YAC3BT,OAAO,EAAE,MAAM;YACf0B,UAAU,EAAE,QAAQ;YACpBzB,GAAG,EAAE,KAAK;YACVmD,KAAK,EAAE,MAAM;YACbzB,cAAc,EAAE;UAClB,CAAE;UAAAvB,QAAA,EAEDrD,WAAW,gBACVX,OAAA,CAAAE,SAAA;YAAA8D,QAAA,gBACEhE,OAAA;cAAM2D,KAAK,EAAE;gBACXqD,KAAK,EAAE,MAAM;gBACblD,MAAM,EAAE,MAAM;gBACdsB,MAAM,EAAE,uBAAuB;gBAC/BoC,SAAS,EAAE,iBAAiB;gBAC5BrD,YAAY,EAAE,KAAK;gBACnBsD,SAAS,EAAE;cACb;YAAE;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,qCAEZ;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGL5D,YAAY,iBACXb,OAAA;MAAK2D,KAAK,EAAE;QACVM,IAAI,EAAE,GAAG;QACTC,UAAU,EAAE,OAAO;QACnBH,OAAO,EAAE,MAAM;QACfI,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE,4BAA4B;QACvCsB,QAAQ,EAAE;MACZ,CAAE;MAAA1B,QAAA,gBACAhE,OAAA;QAAK2D,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACf0B,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,eAAe;UAC/BZ,YAAY,EAAE,MAAM;UACpB+C,aAAa,EAAE,MAAM;UACrBC,YAAY,EAAE;QAChB,CAAE;QAAA3D,QAAA,gBACAhE,OAAA;UAAI2D,KAAK,EAAE;YAAE4C,MAAM,EAAE,CAAC;YAAE1B,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE,MAAM;YAAEF,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EAAC;QAE9E;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLzE,OAAA;UAAK2D,KAAK,EAAE;YACVO,UAAU,EAAE,SAAS;YACrBH,OAAO,EAAE,UAAU;YACnBI,YAAY,EAAE,MAAM;YACpBW,QAAQ,EAAE,MAAM;YAChBF,UAAU,EAAE,KAAK;YACjBC,KAAK,EAAE;UACT,CAAE;UAAAb,QAAA,GACCjD,gBAAgB,CAACE,OAAO,EAAC,GAC5B;QAAA;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzE,OAAA;QAAK2D,KAAK,EAAE;UACVO,UAAU,EAAE,SAAS;UACrBkB,MAAM,EAAE,mBAAmB;UAC3BjB,YAAY,EAAE,KAAK;UACnBJ,OAAO,EAAE,MAAM;UACfY,YAAY,EAAE;QAChB,CAAE;QAAAX,QAAA,gBACAhE,OAAA;UAAK2D,KAAK,EAAE;YAAEmB,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE,MAAM;YAAEF,YAAY,EAAE;UAAM,CAAE;UAAAX,QAAA,EAAC;QAEtE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNzE,OAAA;UAAK2D,KAAK,EAAE;YAAEmB,QAAQ,EAAE,MAAM;YAAEF,UAAU,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAb,QAAA,EAChEjD,gBAAgB,CAACG;QAAW;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzE,OAAA;QAAK2D,KAAK,EAAE;UAAEgB,YAAY,EAAE;QAAO,CAAE;QAAAX,QAAA,EAClCjD,gBAAgB,CAACI,KAAK,CAACmC,GAAG,CAAC,CAACN,IAAI,EAAEQ,KAAK,kBACtCxD,OAAA;UAAiB2D,KAAK,EAAE;YACtBC,OAAO,EAAE,MAAM;YACf0B,UAAU,EAAE,QAAQ;YACpBvB,OAAO,EAAE,MAAM;YACfY,YAAY,EAAE,MAAM;YACpBT,UAAU,EAAElB,IAAI,CAAC3B,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG,SAAS;YAChE+D,MAAM,EAAE,aAAapC,IAAI,CAAC3B,MAAM,KAAK,WAAW,GAAG,SAAS,GACxC2B,IAAI,CAAC3B,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG,SAAS,EAAE;YAC1E8C,YAAY,EAAE,KAAK;YACnBE,UAAU,EAAE;UACd,CAAE;UAAAL,QAAA,gBACAhE,OAAA;YAAK2D,KAAK,EAAE;cACVqD,KAAK,EAAE,MAAM;cACblD,MAAM,EAAE,MAAM;cACdK,YAAY,EAAE,KAAK;cACnBD,UAAU,EAAElB,IAAI,CAAC3B,MAAM,KAAK,WAAW,GAAG,SAAS,GACxC2B,IAAI,CAAC3B,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG,SAAS;cAC/DwD,KAAK,EAAE,OAAO;cACdjB,OAAO,EAAE,MAAM;cACf0B,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBT,QAAQ,EAAE,MAAM;cAChBF,UAAU,EAAE,KAAK;cACjBgD,WAAW,EAAE;YACf,CAAE;YAAA5D,QAAA,EACChB,IAAI,CAAC3B,MAAM,KAAK,WAAW,GAAG,GAAG,GACjC2B,IAAI,CAAC3B,MAAM,KAAK,YAAY,GAAG,GAAG,GAAGmC,KAAK,GAAG;UAAC;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACNzE,OAAA;YAAK2D,KAAK,EAAE;cAAEM,IAAI,EAAE;YAAE,CAAE;YAAAD,QAAA,gBACtBhE,OAAA;cAAK2D,KAAK,EAAE;gBAAEiB,UAAU,EAAE,KAAK;gBAAEC,KAAK,EAAE,MAAM;gBAAEF,YAAY,EAAE;cAAM,CAAE;cAAAX,QAAA,EACnEhB,IAAI,CAAC5B;YAAI;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACNzE,OAAA;cAAK2D,KAAK,EAAE;gBACVqD,KAAK,EAAE,MAAM;gBACblD,MAAM,EAAE,KAAK;gBACbI,UAAU,EAAE,SAAS;gBACrBC,YAAY,EAAE,KAAK;gBACnBuB,QAAQ,EAAE;cACZ,CAAE;cAAA1B,QAAA,eACAhE,OAAA;gBAAK2D,KAAK,EAAE;kBACVG,MAAM,EAAE,MAAM;kBACdkD,KAAK,EAAE,GAAGhE,IAAI,CAAC1B,QAAQ,GAAG;kBAC1B4C,UAAU,EAAElB,IAAI,CAAC3B,MAAM,KAAK,WAAW,GAAG,SAAS,GACxC2B,IAAI,CAAC3B,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG,SAAS;kBAC/DgD,UAAU,EAAE,iBAAiB;kBAC7BF,YAAY,EAAE;gBAChB;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAhDEjB,KAAK;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiDV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDzE,OAAA;MAAAgE,QAAA,EAAQ;AACd;AACA;AACA;AACA;AACA;IAAO;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACnE,EAAA,CA5ZIH,YAAY;AAAA0H,EAAA,GAAZ1H,YAAY;AA8ZlB,eAAeA,YAAY;AAAC,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}