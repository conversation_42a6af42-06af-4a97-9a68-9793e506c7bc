{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\n/**\n * UI自动化分析平台 - 主应用组件\n */\nimport React, { useState } from 'react';\nimport SimpleUpload from './components/SimpleUpload';\nimport SimpleResults from './components/SimpleResults';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [appState, setAppState] = useState('upload'); // 'upload' | 'results'\n  const [analysisResult, setAnalysisResult] = useState(null);\n  const [error, setError] = useState('');\n  const handleUploadSuccess = result => {\n    setAnalysisResult(result);\n    setAppState('results');\n    setError('');\n  };\n  const handleUploadError = errorMessage => {\n    setError(errorMessage);\n  };\n  const handleReset = () => {\n    setAppState('upload');\n    setAnalysisResult(null);\n    setError('');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"app-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83E\\uDD16 UI\\u81EA\\u52A8\\u5316\\u5206\\u6790\\u5E73\\u53F0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u57FA\\u4E8EAI\\u667A\\u80FD\\u4F53\\u7684UI\\u754C\\u9762\\u5206\\u6790\\u548C\\u81EA\\u52A8\\u5316\\u6D4B\\u8BD5\\u811A\\u672C\\u751F\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), appState !== 'upload' && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"reset-button\",\n          onClick: handleReset,\n          children: \"\\u2190 \\u91CD\\u65B0\\u5F00\\u59CB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"app-main\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-banner\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"error-icon\",\n            children: \"\\u26A0\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"error-text\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"error-close\",\n            onClick: () => setError(''),\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 13\n        }, this), appState === 'upload' && /*#__PURE__*/_jsxDEV(SimpleUpload, {\n          onUploadSuccess: handleUploadSuccess,\n          onUploadError: handleUploadError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 13\n        }, this), appState === 'results' && analysisResult && /*#__PURE__*/_jsxDEV(SimpleResults, {\n          result: analysisResult\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"app-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\xA9 2024 UI\\u81EA\\u52A8\\u5316\\u5206\\u6790\\u5E73\\u53F0 - \\u57FA\\u4E8EFastAPI + React + AI\\u667A\\u80FD\\u4F53\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"UorHJjy9UwMW5xFstElzlqwjJg8=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "SimpleUpload", "SimpleResults", "jsxDEV", "_jsxDEV", "App", "_s", "appState", "setAppState", "analysisResult", "setAnalysisResult", "error", "setError", "handleUploadSuccess", "result", "handleUploadError", "errorMessage", "handleReset", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onUploadSuccess", "onUploadError", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/App.js"], "sourcesContent": ["/**\n * UI自动化分析平台 - 主应用组件\n */\nimport React, { useState } from 'react';\nimport SimpleUpload from './components/SimpleUpload';\nimport SimpleResults from './components/SimpleResults';\nimport './App.css';\n\nfunction App() {\n  const [appState, setAppState] = useState('upload'); // 'upload' | 'results'\n  const [analysisResult, setAnalysisResult] = useState(null);\n  const [error, setError] = useState('');\n\n  const handleUploadSuccess = (result) => {\n    setAnalysisResult(result);\n    setAppState('results');\n    setError('');\n  };\n\n  const handleUploadError = (errorMessage) => {\n    setError(errorMessage);\n  };\n\n  const handleReset = () => {\n    setAppState('upload');\n    setAnalysisResult(null);\n    setError('');\n  };\n\n  return (\n    <div className=\"app\">\n      <header className=\"app-header\">\n        <div className=\"container\">\n          <h1>🤖 UI自动化分析平台</h1>\n          <p>基于AI智能体的UI界面分析和自动化测试脚本生成</p>\n          {appState !== 'upload' && (\n            <button className=\"reset-button\" onClick={handleReset}>\n              ← 重新开始\n            </button>\n          )}\n        </div>\n      </header>\n\n      <main className=\"app-main\">\n        <div className=\"container\">\n          {error && (\n            <div className=\"error-banner\">\n              <span className=\"error-icon\">⚠️</span>\n              <span className=\"error-text\">{error}</span>\n              <button className=\"error-close\" onClick={() => setError('')}>\n                ✕\n              </button>\n            </div>\n          )}\n\n          {/* 实际组件 */}\n          {appState === 'upload' && (\n            <SimpleUpload\n              onUploadSuccess={handleUploadSuccess}\n              onUploadError={handleUploadError}\n            />\n          )}\n\n          {appState === 'results' && analysisResult && (\n            <SimpleResults result={analysisResult} />\n          )}\n        </div>\n      </main>\n\n      <footer className=\"app-footer\">\n        <div className=\"container\">\n          <p>© 2024 UI自动化分析平台 - 基于FastAPI + React + AI智能体</p>\n        </div>\n      </footer>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACpD,MAAM,CAACS,cAAc,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMa,mBAAmB,GAAIC,MAAM,IAAK;IACtCJ,iBAAiB,CAACI,MAAM,CAAC;IACzBN,WAAW,CAAC,SAAS,CAAC;IACtBI,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMG,iBAAiB,GAAIC,YAAY,IAAK;IAC1CJ,QAAQ,CAACI,YAAY,CAAC;EACxB,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBT,WAAW,CAAC,QAAQ,CAAC;IACrBE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,oBACER,OAAA;IAAKc,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBf,OAAA;MAAQc,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC5Bf,OAAA;QAAKc,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBf,OAAA;UAAAe,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBnB,OAAA;UAAAe,QAAA,EAAG;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAC9BhB,QAAQ,KAAK,QAAQ,iBACpBH,OAAA;UAAQc,SAAS,EAAC,cAAc;UAACM,OAAO,EAAEP,WAAY;UAAAE,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETnB,OAAA;MAAMc,SAAS,EAAC,UAAU;MAAAC,QAAA,eACxBf,OAAA;QAAKc,SAAS,EAAC,WAAW;QAAAC,QAAA,GACvBR,KAAK,iBACJP,OAAA;UAAKc,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3Bf,OAAA;YAAMc,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtCnB,OAAA;YAAMc,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAER;UAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3CnB,OAAA;YAAQc,SAAS,EAAC,aAAa;YAACM,OAAO,EAAEA,CAAA,KAAMZ,QAAQ,CAAC,EAAE,CAAE;YAAAO,QAAA,EAAC;UAE7D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,EAGAhB,QAAQ,KAAK,QAAQ,iBACpBH,OAAA,CAACH,YAAY;UACXwB,eAAe,EAAEZ,mBAAoB;UACrCa,aAAa,EAAEX;QAAkB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CACF,EAEAhB,QAAQ,KAAK,SAAS,IAAIE,cAAc,iBACvCL,OAAA,CAACF,aAAa;UAACY,MAAM,EAAEL;QAAe;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACzC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEPnB,OAAA;MAAQc,SAAS,EAAC,YAAY;MAAAC,QAAA,eAC5Bf,OAAA;QAAKc,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBf,OAAA;UAAAe,QAAA,EAAG;QAA4C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACjB,EAAA,CApEQD,GAAG;AAAAsB,EAAA,GAAHtB,GAAG;AAsEZ,eAAeA,GAAG;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}