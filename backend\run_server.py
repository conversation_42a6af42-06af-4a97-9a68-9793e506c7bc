#!/usr/bin/env python3
"""
启动FastAPI服务器的脚本
"""
import uvicorn
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    print("正在启动FastAPI服务器...")
    print("服务器地址: http://localhost:8000")
    print("API文档: http://localhost:8000/docs")

    try:
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except Exception as e:
        print(f"启动服务器时出错: {e}")
        sys.exit(1)
