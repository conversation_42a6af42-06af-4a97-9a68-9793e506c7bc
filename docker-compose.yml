version: '3.8'

services:
  # Redis 服务
  redis:
    image: redis:7-alpine
    container_name: ui-automation-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    networks:
      - ui-automation-network

  # 后端 FastAPI 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ui-automation-backend
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379/0
      - PYTHONPATH=/app
    volumes:
      - ./backend:/app
      - uploads_data:/app/uploads
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - ui-automation-network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Celery Worker 服务
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ui-automation-celery
    environment:
      - REDIS_URL=redis://redis:6379/0
      - PYTHONPATH=/app
    volumes:
      - ./backend:/app
      - uploads_data:/app/uploads
    depends_on:
      - redis
      - backend
    restart: unless-stopped
    networks:
      - ui-automation-network
    command: celery -A app.core.celery_app worker --loglevel=info --concurrency=2

  # 前端 React 服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ui-automation-frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - ui-automation-network
    command: npm start

volumes:
  redis_data:
    driver: local
  uploads_data:
    driver: local

networks:
  ui-automation-network:
    driver: bridge
