{"ast": null, "code": "var _jsxFileName = \"E:\\\\WorkFile\\\\HuiCe\\\\testing-2025-06-14\\\\003demo\\\\ui-automation\\\\frontend\\\\src\\\\pages\\\\AnalysisPage.js\",\n  _s = $RefreshSig$();\n/**\n * UI分析页面 - 主要的分析功能页面\n */\nimport React, { useState } from 'react';\nimport SimpleUpload from '../components/SimpleUploadNew';\nimport SimpleResults from '../components/SimpleResults';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnalysisPage = () => {\n  _s();\n  const [analysisResult, setAnalysisResult] = useState(null);\n  const [error, setError] = useState('');\n  const handleUploadSuccess = result => {\n    setAnalysisResult(result);\n    setError('');\n  };\n  const handleUploadError = errorMessage => {\n    setError(errorMessage);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(SimpleUpload, {\n      onUploadSuccess: handleUploadSuccess,\n      onUploadError: handleUploadError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), analysisResult && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: '0',\n        left: '0',\n        right: '0',\n        bottom: '0',\n        background: 'rgba(0,0,0,0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          padding: '24px',\n          borderRadius: '12px',\n          boxShadow: '0 4px 20px rgba(0,0,0,0.15)',\n          maxWidth: '800px',\n          maxHeight: '80vh',\n          overflow: 'auto',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(SimpleResults, {\n          result: analysisResult\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setAnalysisResult(null),\n          style: {\n            position: 'absolute',\n            top: '16px',\n            right: '16px',\n            background: 'none',\n            border: 'none',\n            fontSize: '20px',\n            cursor: 'pointer',\n            color: '#666'\n          },\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: '20px',\n        right: '20px',\n        background: '#fee',\n        border: '1px solid #fcc',\n        borderRadius: '8px',\n        padding: '16px',\n        color: '#c33',\n        zIndex: 1001,\n        maxWidth: '400px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"\\u9519\\u8BEF\\uFF1A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 11\n      }, this), \" \", error, /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setError(''),\n        style: {\n          marginLeft: '12px',\n          background: 'none',\n          border: 'none',\n          color: '#c33',\n          cursor: 'pointer',\n          fontSize: '16px'\n        },\n        children: \"\\u2715\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_s(AnalysisPage, \"sf4VyFncAvmMT7UHRQoLXYjEAD8=\");\n_c = AnalysisPage;\nexport default AnalysisPage;\nvar _c;\n$RefreshReg$(_c, \"AnalysisPage\");", "map": {"version": 3, "names": ["React", "useState", "SimpleUpload", "SimpleResults", "jsxDEV", "_jsxDEV", "AnalysisPage", "_s", "analysisResult", "setAnalysisResult", "error", "setError", "handleUploadSuccess", "result", "handleUploadError", "errorMessage", "style", "height", "overflow", "children", "onUploadSuccess", "onUploadError", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "top", "left", "right", "bottom", "background", "display", "alignItems", "justifyContent", "zIndex", "padding", "borderRadius", "boxShadow", "max<PERSON><PERSON><PERSON>", "maxHeight", "onClick", "border", "fontSize", "cursor", "color", "marginLeft", "_c", "$RefreshReg$"], "sources": ["E:/WorkFile/HuiCe/testing-2025-06-14/003demo/ui-automation/frontend/src/pages/AnalysisPage.js"], "sourcesContent": ["/**\n * UI分析页面 - 主要的分析功能页面\n */\nimport React, { useState } from 'react';\nimport SimpleUpload from '../components/SimpleUploadNew';\nimport SimpleResults from '../components/SimpleResults';\n\nconst AnalysisPage = () => {\n  const [analysisResult, setAnalysisResult] = useState(null);\n  const [error, setError] = useState('');\n\n  const handleUploadSuccess = (result) => {\n    setAnalysisResult(result);\n    setError('');\n  };\n\n  const handleUploadError = (errorMessage) => {\n    setError(errorMessage);\n  };\n\n  return (\n    <div style={{ height: '100vh', overflow: 'hidden' }}>\n      <SimpleUpload\n        onUploadSuccess={handleUploadSuccess}\n        onUploadError={handleUploadError}\n      />\n\n      {/* 分析结果弹窗 */}\n      {analysisResult && (\n        <div style={{\n          position: 'fixed',\n          top: '0',\n          left: '0',\n          right: '0',\n          bottom: '0',\n          background: 'rgba(0,0,0,0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            background: 'white',\n            padding: '24px',\n            borderRadius: '12px',\n            boxShadow: '0 4px 20px rgba(0,0,0,0.15)',\n            maxWidth: '800px',\n            maxHeight: '80vh',\n            overflow: 'auto',\n            position: 'relative'\n          }}>\n            <SimpleResults result={analysisResult} />\n            <button\n              onClick={() => setAnalysisResult(null)}\n              style={{\n                position: 'absolute',\n                top: '16px',\n                right: '16px',\n                background: 'none',\n                border: 'none',\n                fontSize: '20px',\n                cursor: 'pointer',\n                color: '#666'\n              }}\n            >\n              ✕\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* 错误提示 */}\n      {error && (\n        <div style={{\n          position: 'fixed',\n          top: '20px',\n          right: '20px',\n          background: '#fee',\n          border: '1px solid #fcc',\n          borderRadius: '8px',\n          padding: '16px',\n          color: '#c33',\n          zIndex: 1001,\n          maxWidth: '400px'\n        }}>\n          <strong>错误：</strong> {error}\n          <button\n            onClick={() => setError('')}\n            style={{\n              marginLeft: '12px',\n              background: 'none',\n              border: 'none',\n              color: '#c33',\n              cursor: 'pointer',\n              fontSize: '16px'\n            }}\n          >\n            ✕\n          </button>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AnalysisPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,aAAa,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGR,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMW,mBAAmB,GAAIC,MAAM,IAAK;IACtCJ,iBAAiB,CAACI,MAAM,CAAC;IACzBF,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMG,iBAAiB,GAAIC,YAAY,IAAK;IAC1CJ,QAAQ,CAACI,YAAY,CAAC;EACxB,CAAC;EAED,oBACEV,OAAA;IAAKW,KAAK,EAAE;MAAEC,MAAM,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAClDd,OAAA,CAACH,YAAY;MACXkB,eAAe,EAAER,mBAAoB;MACrCS,aAAa,EAAEP;IAAkB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC,EAGDjB,cAAc,iBACbH,OAAA;MAAKW,KAAK,EAAE;QACVU,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,GAAG;QACRC,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE,iBAAiB;QAC7BC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,MAAM,EAAE;MACV,CAAE;MAAAhB,QAAA,eACAd,OAAA;QAAKW,KAAK,EAAE;UACVe,UAAU,EAAE,OAAO;UACnBK,OAAO,EAAE,MAAM;UACfC,YAAY,EAAE,MAAM;UACpBC,SAAS,EAAE,6BAA6B;UACxCC,QAAQ,EAAE,OAAO;UACjBC,SAAS,EAAE,MAAM;UACjBtB,QAAQ,EAAE,MAAM;UAChBQ,QAAQ,EAAE;QACZ,CAAE;QAAAP,QAAA,gBACAd,OAAA,CAACF,aAAa;UAACU,MAAM,EAAEL;QAAe;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCpB,OAAA;UACEoC,OAAO,EAAEA,CAAA,KAAMhC,iBAAiB,CAAC,IAAI,CAAE;UACvCO,KAAK,EAAE;YACLU,QAAQ,EAAE,UAAU;YACpBC,GAAG,EAAE,MAAM;YACXE,KAAK,EAAE,MAAM;YACbE,UAAU,EAAE,MAAM;YAClBW,MAAM,EAAE,MAAM;YACdC,QAAQ,EAAE,MAAM;YAChBC,MAAM,EAAE,SAAS;YACjBC,KAAK,EAAE;UACT,CAAE;UAAA1B,QAAA,EACH;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAf,KAAK,iBACJL,OAAA;MAAKW,KAAK,EAAE;QACVU,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,MAAM;QACXE,KAAK,EAAE,MAAM;QACbE,UAAU,EAAE,MAAM;QAClBW,MAAM,EAAE,gBAAgB;QACxBL,YAAY,EAAE,KAAK;QACnBD,OAAO,EAAE,MAAM;QACfS,KAAK,EAAE,MAAM;QACbV,MAAM,EAAE,IAAI;QACZI,QAAQ,EAAE;MACZ,CAAE;MAAApB,QAAA,gBACAd,OAAA;QAAAc,QAAA,EAAQ;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAACf,KAAK,eAC3BL,OAAA;QACEoC,OAAO,EAAEA,CAAA,KAAM9B,QAAQ,CAAC,EAAE,CAAE;QAC5BK,KAAK,EAAE;UACL8B,UAAU,EAAE,MAAM;UAClBf,UAAU,EAAE,MAAM;UAClBW,MAAM,EAAE,MAAM;UACdG,KAAK,EAAE,MAAM;UACbD,MAAM,EAAE,SAAS;UACjBD,QAAQ,EAAE;QACZ,CAAE;QAAAxB,QAAA,EACH;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClB,EAAA,CAhGID,YAAY;AAAAyC,EAAA,GAAZzC,YAAY;AAkGlB,eAAeA,YAAY;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}