"""
分析流水线 - 协调三个智能体的执行
"""
from celery import Task
from app.core.celery_app import celery_app
from app.core.task_manager import task_manager
from app.models.schemas import AgentProgress, AgentType, TaskStatus
import asyncio
import json
from datetime import datetime

class CallbackTask(Task):
    """带回调的Celery任务基类"""
    
    def on_success(self, retval, task_id, args, kwargs):
        """任务成功回调"""
        print(f"Task {task_id} succeeded with result: {retval}")
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """任务失败回调"""
        print(f"Task {task_id} failed with exception: {exc}")

@celery_app.task(base=CallbackTask, bind=True)
def start_analysis_pipeline(self, task_id: str, image_path: str, description: str):
    """
    启动分析流水线
    
    Args:
        task_id: 任务ID
        image_path: 图片文件路径
        description: 界面描述
    """
    try:
        # 更新任务状态为处理中
        task_manager.update_task_status(task_id, TaskStatus.PROCESSING)
        
        # 发送开始消息
        progress = AgentProgress(
            agent=AgentType.ELEMENT_DETECTION,
            stage=TaskStatus.PROCESSING,
            message="开始UI分析流水线...",
            progress=0.0
        )
        task_manager.publish_progress(task_id, progress)
        
        # 第一步：元素识别
        elements_result = execute_element_detection.delay(task_id, image_path, description)
        elements = elements_result.get(timeout=300)  # 5分钟超时
        
        if not elements:
            raise Exception("元素识别失败")
        
        # 保存元素识别结果
        task_manager.update_task_data(task_id, "elements", elements)
        
        # 第二步：交互分析
        flows_result = execute_interaction_analysis.delay(task_id, elements, description)
        flows = flows_result.get(timeout=300)
        
        if not flows:
            raise Exception("交互分析失败")
        
        # 保存交互分析结果
        task_manager.update_task_data(task_id, "flows", flows)
        
        # 第三步：用例生成
        scenarios_result = execute_test_generation.delay(task_id, elements, flows, description)
        scenarios = scenarios_result.get(timeout=300)
        
        if not scenarios:
            raise Exception("用例生成失败")
        
        # 保存测试用例结果
        task_manager.update_task_data(task_id, "test_scenarios", scenarios)
        
        # 更新任务状态为完成
        task_manager.update_task_status(task_id, TaskStatus.COMPLETED)
        
        # 发送完成消息
        final_progress = AgentProgress(
            agent=AgentType.TEST_GENERATION,
            stage=TaskStatus.COMPLETED,
            message="UI分析流水线执行完成！",
            progress=100.0,
            data={
                "elements_count": len(elements),
                "flows_count": len(flows),
                "scenarios_count": len(scenarios)
            }
        )
        task_manager.publish_progress(task_id, final_progress)
        
        return {
            "task_id": task_id,
            "status": "completed",
            "elements_count": len(elements),
            "flows_count": len(flows),
            "scenarios_count": len(scenarios)
        }
        
    except Exception as e:
        # 更新任务状态为失败
        task_manager.update_task_status(task_id, TaskStatus.FAILED)
        task_manager.update_task_data(task_id, "error_message", str(e))
        
        # 发送错误消息
        error_progress = AgentProgress(
            agent=AgentType.ELEMENT_DETECTION,
            stage=TaskStatus.FAILED,
            message=f"分析流水线执行失败: {str(e)}",
            progress=0.0
        )
        task_manager.publish_progress(task_id, error_progress)
        
        raise

@celery_app.task(bind=True)
def execute_element_detection(self, task_id: str, image_path: str, description: str):
    """执行元素识别"""
    try:
        # 发送开始消息
        progress = AgentProgress(
            agent=AgentType.ELEMENT_DETECTION,
            stage=TaskStatus.PROCESSING,
            message="正在识别UI元素...",
            progress=10.0
        )
        task_manager.publish_progress(task_id, progress)
        
        # 调用元素识别智能体
        from app.agents.element_detection.agent import ElementDetectionAgent
        agent = ElementDetectionAgent()
        elements = agent.analyze(image_path, description)
        
        # 发送完成消息
        complete_progress = AgentProgress(
            agent=AgentType.ELEMENT_DETECTION,
            stage=TaskStatus.COMPLETED,
            message=f"元素识别完成，识别到 {len(elements)} 个UI元素",
            progress=33.3,
            data={"elements_count": len(elements)}
        )
        task_manager.publish_progress(task_id, complete_progress)
        
        return elements
        
    except Exception as e:
        error_progress = AgentProgress(
            agent=AgentType.ELEMENT_DETECTION,
            stage=TaskStatus.FAILED,
            message=f"元素识别失败: {str(e)}",
            progress=0.0
        )
        task_manager.publish_progress(task_id, error_progress)
        raise

@celery_app.task(bind=True)
def execute_interaction_analysis(self, task_id: str, elements: list, description: str):
    """执行交互分析"""
    try:
        # 发送开始消息
        progress = AgentProgress(
            agent=AgentType.INTERACTION_ANALYSIS,
            stage=TaskStatus.PROCESSING,
            message="正在分析用户交互流程...",
            progress=40.0
        )
        task_manager.publish_progress(task_id, progress)
        
        # 调用交互分析智能体
        from app.agents.interaction_analysis.agent import InteractionAnalysisAgent
        agent = InteractionAnalysisAgent()
        flows = agent.analyze(elements, description)
        
        # 发送完成消息
        complete_progress = AgentProgress(
            agent=AgentType.INTERACTION_ANALYSIS,
            stage=TaskStatus.COMPLETED,
            message=f"交互分析完成，生成 {len(flows)} 个交互流程",
            progress=66.6,
            data={"flows_count": len(flows)}
        )
        task_manager.publish_progress(task_id, complete_progress)
        
        return flows
        
    except Exception as e:
        error_progress = AgentProgress(
            agent=AgentType.INTERACTION_ANALYSIS,
            stage=TaskStatus.FAILED,
            message=f"交互分析失败: {str(e)}",
            progress=40.0
        )
        task_manager.publish_progress(task_id, error_progress)
        raise

@celery_app.task(bind=True)
def execute_test_generation(self, task_id: str, elements: list, flows: list, description: str):
    """执行测试用例生成"""
    try:
        # 发送开始消息
        progress = AgentProgress(
            agent=AgentType.TEST_GENERATION,
            stage=TaskStatus.PROCESSING,
            message="正在生成MidScene.js测试用例...",
            progress=70.0
        )
        task_manager.publish_progress(task_id, progress)
        
        # 调用用例生成智能体
        from app.agents.test_generation.agent import TestGenerationAgent
        agent = TestGenerationAgent()
        scenarios = agent.analyze(elements, flows, description)
        
        # 发送完成消息
        complete_progress = AgentProgress(
            agent=AgentType.TEST_GENERATION,
            stage=TaskStatus.COMPLETED,
            message=f"测试用例生成完成，生成 {len(scenarios)} 个测试场景",
            progress=100.0,
            data={"scenarios_count": len(scenarios)}
        )
        task_manager.publish_progress(task_id, complete_progress)
        
        return scenarios
        
    except Exception as e:
        error_progress = AgentProgress(
            agent=AgentType.TEST_GENERATION,
            stage=TaskStatus.FAILED,
            message=f"测试用例生成失败: {str(e)}",
            progress=70.0
        )
        task_manager.publish_progress(task_id, error_progress)
        raise
