"""
任务管理器 - 管理分析任务的状态和结果
"""
import redis
import json
import asyncio
from typing import Dict, Any, Optional, AsyncGenerator
from datetime import datetime
from app.models.schemas import AgentProgress, TaskStatus, AnalysisResult

class TaskManager:
    """任务管理器"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379/0"):
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        self.pubsub = self.redis_client.pubsub()
    
    def get_task_key(self, task_id: str) -> str:
        """获取任务键名"""
        return f"task:{task_id}"
    
    def get_progress_channel(self, task_id: str) -> str:
        """获取进度频道名"""
        return f"progress:{task_id}"
    
    def create_task(self, task_id: str, image_path: str, description: str) -> None:
        """创建新任务"""
        task_data = {
            "task_id": task_id,
            "image_path": image_path,
            "description": description,
            "status": TaskStatus.PENDING.value,
            "created_at": datetime.now().isoformat(),
            "elements": [],
            "flows": [],
            "test_scenarios": []
        }
        self.redis_client.hset(
            self.get_task_key(task_id),
            mapping={k: json.dumps(v) if isinstance(v, (dict, list)) else v 
                    for k, v in task_data.items()}
        )
        # 设置过期时间为24小时
        self.redis_client.expire(self.get_task_key(task_id), 86400)
    
    def update_task_status(self, task_id: str, status: TaskStatus) -> None:
        """更新任务状态"""
        self.redis_client.hset(
            self.get_task_key(task_id),
            "status", status.value
        )
        if status == TaskStatus.COMPLETED:
            self.redis_client.hset(
                self.get_task_key(task_id),
                "completed_at", datetime.now().isoformat()
            )
    
    def update_task_data(self, task_id: str, field: str, data: Any) -> None:
        """更新任务数据"""
        self.redis_client.hset(
            self.get_task_key(task_id),
            field, json.dumps(data) if isinstance(data, (dict, list)) else data
        )
    
    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务信息"""
        task_data = self.redis_client.hgetall(self.get_task_key(task_id))
        if not task_data:
            return None
        
        # 解析JSON字段
        for key in ["elements", "flows", "test_scenarios"]:
            if key in task_data:
                try:
                    task_data[key] = json.loads(task_data[key])
                except json.JSONDecodeError:
                    task_data[key] = []
        
        return task_data
    
    def publish_progress(self, task_id: str, progress: AgentProgress) -> None:
        """发布进度消息"""
        channel = self.get_progress_channel(task_id)
        message = progress.model_dump_json()
        self.redis_client.publish(channel, message)
    
    async def subscribe_progress(self, task_id: str) -> AsyncGenerator[AgentProgress, None]:
        """订阅任务进度"""
        channel = self.get_progress_channel(task_id)
        pubsub = self.redis_client.pubsub()
        await asyncio.get_event_loop().run_in_executor(
            None, pubsub.subscribe, channel
        )
        
        try:
            while True:
                message = await asyncio.get_event_loop().run_in_executor(
                    None, pubsub.get_message, True, 1.0
                )
                if message and message['type'] == 'message':
                    try:
                        progress_data = json.loads(message['data'])
                        progress = AgentProgress(**progress_data)
                        yield progress
                    except (json.JSONDecodeError, ValueError):
                        continue
                
                # 检查任务是否完成
                task = self.get_task(task_id)
                if task and task.get('status') in [TaskStatus.COMPLETED.value, TaskStatus.FAILED.value]:
                    break
                    
        finally:
            pubsub.unsubscribe(channel)
            pubsub.close()

# 全局任务管理器实例
task_manager = TaskManager()
