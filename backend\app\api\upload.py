"""
文件上传API路由
"""
from fastapi import APIRouter, File, UploadFile, Form, HTTPException
import uuid
from pathlib import Path
from app.core.task_manager import task_manager
from app.core.celery_app import celery_app
from app.models.schemas import AnalyzeResponse

router = APIRouter()

# 上传目录
UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)

@router.post("/upload", response_model=AnalyzeResponse)
async def upload_and_analyze(
    image_file: UploadFile = File(..., description="UI界面截图"),
    description: str = Form(..., description="界面功能描述")
):
    """
    上传图片并启动分析
    
    Args:
        image_file: 上传的图片文件
        description: 界面功能的文字描述
    
    Returns:
        包含任务ID和流式输出URL的响应
    """
    # 验证文件类型
    if not image_file.content_type or not image_file.content_type.startswith('image/'):
        raise HTTPException(
            status_code=400, 
            detail="只支持图片文件 (jpg, png, gif, bmp, webp)"
        )
    
    # 验证文件大小 (最大10MB)
    if image_file.size and image_file.size > 10 * 1024 * 1024:
        raise HTTPException(
            status_code=400,
            detail="文件大小不能超过10MB"
        )
    
    # 生成唯一任务ID
    task_id = str(uuid.uuid4())
    
    try:
        # 保存上传的文件
        file_extension = Path(image_file.filename or "image.jpg").suffix
        file_path = UPLOAD_DIR / f"{task_id}{file_extension}"
        
        with open(file_path, "wb") as buffer:
            content = await image_file.read()
            buffer.write(content)
        
        # 创建任务记录
        task_manager.create_task(task_id, str(file_path), description)
        
        # 启动异步分析流水线
        celery_app.send_task(
            'app.core.analysis_pipeline.start_analysis_pipeline',
            args=[task_id, str(file_path), description],
            queue='analysis'
        )
        
        return AnalyzeResponse(
            task_id=task_id,
            message="分析任务已启动，请通过SSE接口获取实时进度",
            stream_url=f"/api/v1/stream/{task_id}"
        )
        
    except Exception as e:
        # 清理已上传的文件
        if 'file_path' in locals() and file_path.exists():
            file_path.unlink()
        
        raise HTTPException(
            status_code=500,
            detail=f"文件处理失败: {str(e)}"
        )

@router.get("/tasks/{task_id}")
async def get_task_status(task_id: str):
    """
    获取任务状态和结果
    
    Args:
        task_id: 任务ID
    
    Returns:
        任务的详细信息和分析结果
    """
    task = task_manager.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return task
