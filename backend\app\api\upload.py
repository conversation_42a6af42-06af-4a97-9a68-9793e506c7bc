"""
文件上传和分析API路由
"""
from fastapi import APIRouter, File, UploadFile, Form, HTTPException, BackgroundTasks
import uuid
import os
from pathlib import Path
from app.core.agent_pipeline import task_manager
from app.models.schemas import AnalyzeResponse

router = APIRouter()

# 上传目录
UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)

@router.post("/upload", response_model=AnalyzeResponse)
async def upload_and_analyze(
    background_tasks: BackgroundTasks,
    image_file: UploadFile = File(..., description="UI界面截图"),
    description: str = Form(..., description="界面功能描述")
):
    """
    上传图片并启动分析
    
    Args:
        image_file: 上传的图片文件
        description: 界面功能的文字描述
    
    Returns:
        包含任务ID和流式输出URL的响应
    """
    # 验证文件类型
    if not image_file.content_type or not image_file.content_type.startswith('image/'):
        raise HTTPException(
            status_code=400, 
            detail="只支持图片文件 (jpg, png, gif, bmp, webp)"
        )
    
    # 验证文件大小 (最大10MB)
    if image_file.size and image_file.size > 10 * 1024 * 1024:
        raise HTTPException(
            status_code=400,
            detail="文件大小不能超过10MB"
        )

    # 验证描述长度
    if len(description.strip()) < 5:
        raise HTTPException(
            status_code=400,
            detail="界面功能描述至少需要5个字符"
        )

    # 生成唯一任务ID
    task_id = str(uuid.uuid4())

    try:
        # 保存上传的文件
        file_extension = Path(image_file.filename or "image.jpg").suffix
        file_path = UPLOAD_DIR / f"{task_id}{file_extension}"

        with open(file_path, "wb") as buffer:
            content = await image_file.read()
            buffer.write(content)

        # 启动后台分析任务
        background_tasks.add_task(
            start_analysis_task,
            task_id,
            str(file_path),
            description.strip()
        )

        return AnalyzeResponse(
            task_id=task_id,
            message="文件上传成功，分析任务已启动",
            stream_url=f"/api/v1/stream/{task_id}"
        )
        
    except Exception as e:
        # 清理已上传的文件
        if 'file_path' in locals() and file_path.exists():
            file_path.unlink()
        
        raise HTTPException(
            status_code=500,
            detail=f"文件处理失败: {str(e)}"
        )

async def start_analysis_task(task_id: str, image_path: str, description: str):
    """启动分析任务（后台执行）"""
    try:
        await task_manager.start_analysis(task_id, image_path, description)
    except Exception as e:
        print(f"分析任务执行失败 {task_id}: {e}")

@router.get("/tasks/{task_id}")
async def get_task_status(task_id: str):
    """
    获取任务状态和结果

    Args:
        task_id: 任务ID

    Returns:
        任务的详细信息和分析结果
    """
    task = task_manager.get_task_status(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    return task

@router.get("/tasks")
async def get_active_tasks():
    """
    获取所有活跃任务

    Returns:
        活跃任务列表
    """
    active_tasks = task_manager.get_active_tasks()
    return {
        "active_tasks": active_tasks,
        "count": len(active_tasks)
    }

@router.delete("/tasks/{task_id}")
async def cancel_task(task_id: str):
    """
    取消任务

    Args:
        task_id: 任务ID

    Returns:
        取消结果
    """
    task = task_manager.get_task_status(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    # 清理任务
    task_manager.cleanup_task(task_id)

    # 清理上传的文件
    try:
        image_path = task.get("image_path")
        if image_path and os.path.exists(image_path):
            os.remove(image_path)
    except Exception as e:
        print(f"清理文件失败: {e}")

    return {"message": f"任务 {task_id} 已取消"}

@router.get("/pipeline/info")
async def get_pipeline_info():
    """
    获取分析流水线信息

    Returns:
        流水线配置信息
    """
    return task_manager.pipeline.get_pipeline_info()

@router.post("/analyze/demo")
async def demo_analyze(
    background_tasks: BackgroundTasks,
    description: str = Form(..., description="界面功能描述")
):
    """
    演示分析接口（不需要上传文件）

    Args:
        background_tasks: FastAPI后台任务
        description: 界面功能描述

    Returns:
        包含任务ID和流式输出URL的响应
    """
    # 生成唯一任务ID
    task_id = str(uuid.uuid4())

    # 使用模拟图片路径
    demo_image_path = "demo_image.jpg"

    # 启动后台分析任务
    background_tasks.add_task(
        start_analysis_task,
        task_id,
        demo_image_path,
        description.strip()
    )

    return AnalyzeResponse(
        task_id=task_id,
        message="演示分析任务已启动",
        stream_url=f"/api/v1/stream/{task_id}"
    )
