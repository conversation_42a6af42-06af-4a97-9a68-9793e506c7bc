"""
文件上传和分析API路由
"""
from fastapi import APIRouter, File, UploadFile, Form, HTTPException, BackgroundTasks
import uuid
import os
import asyncio
from pathlib import Path
from app.core.agent_pipeline import task_manager
from app.core.sse_manager import sse_manager
from app.models.schemas import AnalyzeResponse

router = APIRouter()

# 上传目录
UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)

@router.post("/upload", response_model=AnalyzeResponse)
async def upload_and_analyze(
    background_tasks: BackgroundTasks,
    image_file: UploadFile = File(..., description="UI界面截图"),
    description: str = Form(..., description="界面功能描述"),
    project_name: str = Form(default="", description="项目名称"),
    test_type: str = Form(default="functional", description="测试类型"),
    priority: str = Form(default="medium", description="优先级"),
    expected_elements: str = Form(default="", description="预期元素数量"),
    special_requirements: str = Form(default="", description="特殊要求")
):
    """
    上传图片并启动分析
    
    Args:
        image_file: 上传的图片文件
        description: 界面功能的文字描述
    
    Returns:
        包含任务ID和流式输出URL的响应
    """
    # 验证文件类型
    if not image_file.content_type or not image_file.content_type.startswith('image/'):
        raise HTTPException(
            status_code=400, 
            detail="只支持图片文件 (jpg, png, gif, bmp, webp)"
        )
    
    # 验证文件大小 (最大10MB)
    if image_file.size and image_file.size > 10 * 1024 * 1024:
        raise HTTPException(
            status_code=400,
            detail="文件大小不能超过10MB"
        )

    # 验证描述长度
    if len(description.strip()) < 5:
        raise HTTPException(
            status_code=400,
            detail="界面功能描述至少需要5个字符"
        )

    # 生成唯一任务ID
    task_id = str(uuid.uuid4())

    try:
        # 保存上传的文件
        file_extension = Path(image_file.filename or "image.jpg").suffix
        file_path = UPLOAD_DIR / f"{task_id}{file_extension}"

        with open(file_path, "wb") as buffer:
            content = await image_file.read()
            buffer.write(content)

        # 创建分析参数
        analysis_params = {
            "description": description.strip(),
            "project_name": project_name.strip() if project_name else "",
            "test_type": test_type,
            "priority": priority,
            "expected_elements": expected_elements.strip() if expected_elements else "",
            "special_requirements": special_requirements.strip() if special_requirements else ""
        }

        # 启动后台分析任务
        background_tasks.add_task(
            start_analysis_task,
            task_id,
            str(file_path),
            analysis_params
        )

        return AnalyzeResponse(
            task_id=task_id,
            message="文件上传成功，分析任务已启动",
            stream_url=f"/api/v1/stream/{task_id}"
        )
        
    except Exception as e:
        # 清理已上传的文件
        if 'file_path' in locals() and file_path.exists():
            file_path.unlink()
        
        raise HTTPException(
            status_code=500,
            detail=f"文件处理失败: {str(e)}"
        )

async def start_analysis_task(task_id: str, image_path: str, analysis_params: dict):
    """启动分析任务（后台执行）"""
    try:
        await task_manager.start_analysis(task_id, image_path, analysis_params)
    except Exception as e:
        print(f"分析任务执行失败 {task_id}: {e}")

@router.get("/tasks/{task_id}")
async def get_task_status(task_id: str):
    """
    获取任务状态和结果

    Args:
        task_id: 任务ID

    Returns:
        任务的详细信息和分析结果
    """
    task = task_manager.get_task_status(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    return task

@router.get("/tasks")
async def get_active_tasks():
    """
    获取所有活跃任务

    Returns:
        活跃任务列表
    """
    active_tasks = task_manager.get_active_tasks()
    return {
        "active_tasks": active_tasks,
        "count": len(active_tasks)
    }

@router.delete("/tasks/{task_id}")
async def cancel_task(task_id: str):
    """
    取消任务

    Args:
        task_id: 任务ID

    Returns:
        取消结果
    """
    task = task_manager.get_task_status(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    # 清理任务
    task_manager.cleanup_task(task_id)

    # 清理上传的文件
    try:
        image_path = task.get("image_path")
        if image_path and os.path.exists(image_path):
            os.remove(image_path)
    except Exception as e:
        print(f"清理文件失败: {e}")

    return {"message": f"任务 {task_id} 已取消"}

@router.get("/pipeline/info")
async def get_pipeline_info():
    """
    获取分析流水线信息

    Returns:
        流水线配置信息
    """
    return task_manager.pipeline.get_pipeline_info()

@router.post("/analyze/demo")
async def demo_analyze(
    background_tasks: BackgroundTasks,
    description: str = Form(..., description="界面功能描述")
):
    """
    演示分析接口（不需要上传文件）

    Args:
        background_tasks: FastAPI后台任务
        description: 界面功能描述

    Returns:
        包含任务ID和流式输出URL的响应
    """
    # 生成唯一任务ID
    task_id = str(uuid.uuid4())

    # 使用模拟图片路径
    demo_image_path = "demo_image.jpg"

    # 启动后台分析任务
    background_tasks.add_task(
        start_analysis_task,
        task_id,
        demo_image_path,
        description.strip()
    )

    return AnalyzeResponse(
        task_id=task_id,
        message="演示分析任务已启动",
        stream_url=f"/api/v1/stream/{task_id}"
    )

@router.post("/analyze/file-management")
async def analyze_file_management(
    background_tasks: BackgroundTasks,
    description: str = Form(default="文件管理界面功能分析", description="界面功能描述")
):
    """
    文件管理界面专项分析接口

    Args:
        background_tasks: FastAPI后台任务
        description: 界面功能描述

    Returns:
        包含任务ID和流式输出URL的响应
    """
    # 生成唯一任务ID
    task_id = str(uuid.uuid4())

    # 使用专门的文件管理界面分析
    demo_image_path = "file_management_demo.jpg"

    # 启动后台分析任务
    background_tasks.add_task(
        start_file_management_analysis,
        task_id,
        demo_image_path,
        description.strip()
    )

    return AnalyzeResponse(
        task_id=task_id,
        message="文件管理界面分析任务已启动",
        stream_url=f"/api/v1/stream/{task_id}"
    )

async def start_file_management_analysis(task_id: str, image_path: str, description: str):
    """启动文件管理界面分析任务（后台执行）"""
    try:
        # 模拟文件管理界面的分析结果
        await asyncio.sleep(1)  # 模拟分析时间

        # 创建专门的文件管理分析结果
        file_mgmt_result = {
            "task_id": task_id,
            "status": "completed",
            "elements": [
                {
                    "id": "upload_btn",
                    "name": "上传文件按钮",
                    "element_type": "button",
                    "description": "页面右上角的红色上传按钮",
                    "text_content": "新增文件",
                    "position": {
                        "area": "页面右上角",
                        "relative_to": "搜索框右侧"
                    },
                    "visual_features": {
                        "color": "红色背景，白色文字",
                        "shape": "圆角矩形",
                        "size": "中等"
                    },
                    "functionality": "文件上传入口",
                    "interaction_state": "可点击",
                    "confidence_score": 0.98
                },
                {
                    "id": "search_input",
                    "name": "搜索输入框",
                    "element_type": "input",
                    "description": "文件搜索输入框，支持按文件名搜索",
                    "text_content": "",
                    "position": {
                        "area": "页面右上角",
                        "relative_to": "上传按钮左侧"
                    },
                    "visual_features": {
                        "color": "白色背景，灰色边框",
                        "shape": "矩形",
                        "size": "中等"
                    },
                    "functionality": "搜索文件",
                    "interaction_state": "可输入",
                    "confidence_score": 0.95
                },
                {
                    "id": "file_table",
                    "name": "文件列表表格",
                    "element_type": "table",
                    "description": "显示文件信息的数据表格，包含文件名、大小、修改时间等",
                    "text_content": "",
                    "position": {
                        "area": "页面中央主要区域",
                        "relative_to": "导航栏下方"
                    },
                    "visual_features": {
                        "color": "白色背景，灰色边框",
                        "shape": "表格",
                        "size": "大"
                    },
                    "functionality": "展示文件列表和详细信息",
                    "interaction_state": "可交互",
                    "confidence_score": 0.97
                },
                {
                    "id": "file_actions",
                    "name": "文件操作按钮组",
                    "element_type": "button_group",
                    "description": "每行文件的操作按钮，包括编辑、下载、删除等",
                    "text_content": "编辑、下载、删除",
                    "position": {
                        "area": "文件列表右侧",
                        "relative_to": "每行文件末尾"
                    },
                    "visual_features": {
                        "color": "蓝色、绿色、红色",
                        "shape": "小按钮组",
                        "size": "小"
                    },
                    "functionality": "文件操作功能",
                    "interaction_state": "可点击",
                    "confidence_score": 0.93
                },
                {
                    "id": "pagination",
                    "name": "分页控件",
                    "element_type": "pagination",
                    "description": "文件列表的分页导航控件",
                    "text_content": "上一页、下一页、页码",
                    "position": {
                        "area": "文件列表底部",
                        "relative_to": "表格下方"
                    },
                    "visual_features": {
                        "color": "蓝色链接",
                        "shape": "分页按钮",
                        "size": "小"
                    },
                    "functionality": "分页导航",
                    "interaction_state": "可点击",
                    "confidence_score": 0.90
                }
            ],
            "flows": [
                {
                    "flow_name": "文件上传流程",
                    "description": "用户上传新文件的完整操作流程",
                    "steps": [
                        {
                            "step_id": 1,
                            "action": "点击上传按钮",
                            "target_element": "上传文件按钮",
                            "expected_result": "打开文件选择对话框",
                            "precondition": "用户已登录且有上传权限",
                            "validation": "检查文件选择对话框是否显示"
                        },
                        {
                            "step_id": 2,
                            "action": "选择文件",
                            "target_element": "文件选择对话框",
                            "expected_result": "文件被选中并显示文件名",
                            "precondition": "文件选择对话框已打开",
                            "validation": "检查文件是否被正确选择"
                        },
                        {
                            "step_id": 3,
                            "action": "确认上传",
                            "target_element": "确认上传按钮",
                            "expected_result": "文件开始上传并显示进度",
                            "precondition": "文件已选择",
                            "validation": "检查上传进度是否显示"
                        },
                        {
                            "step_id": 4,
                            "action": "验证上传结果",
                            "target_element": "文件列表",
                            "expected_result": "新文件出现在文件列表中",
                            "precondition": "文件上传完成",
                            "validation": "检查文件是否在列表中显示"
                        }
                    ],
                    "success_criteria": "文件成功上传并在列表中正确显示",
                    "error_scenarios": ["文件格式不支持", "文件大小超限", "网络上传失败", "存储空间不足"]
                }
            ],
            "automation_scripts": [
                {
                    "script_name": "文件管理界面自动化脚本",
                    "description": "验证文件管理界面的核心功能，包括文件上传、搜索、操作等",
                    "priority": "high",
                    "estimated_duration": "60秒",
                    "preconditions": ["页面已加载完成", "用户已登录", "具有文件操作权限"],
                    "test_steps": [
                        {
                            "step_id": 1,
                            "action_type": "点击",
                            "action_description": "点击上传文件按钮",
                            "visual_target": "页面右上角的红色'新增文件'按钮",
                            "expected_result": "打开文件选择对话框",
                            "validation_step": "检查文件选择对话框是否正确显示"
                        },
                        {
                            "step_id": 2,
                            "action_type": "输入",
                            "action_description": "在搜索框中输入文件名",
                            "visual_target": "页面右上角的搜索输入框，白色背景灰色边框",
                            "expected_result": "搜索关键词正确显示在输入框中",
                            "validation_step": "验证搜索框内容是否正确输入"
                        },
                        {
                            "step_id": 3,
                            "action_type": "点击",
                            "action_description": "点击文件操作按钮",
                            "visual_target": "文件列表中每行右侧的蓝色编辑按钮",
                            "expected_result": "显示文件编辑界面或操作菜单",
                            "validation_step": "检查操作是否正确执行"
                        },
                        {
                            "step_id": 4,
                            "action_type": "点击",
                            "action_description": "点击下载按钮",
                            "visual_target": "文件列表中每行右侧的绿色下载按钮",
                            "expected_result": "开始文件下载",
                            "validation_step": "检查下载是否开始"
                        },
                        {
                            "step_id": 5,
                            "action_type": "滚动",
                            "action_description": "滚动查看更多文件",
                            "visual_target": "文件列表区域",
                            "expected_result": "显示更多文件或分页",
                            "validation_step": "检查是否显示更多内容"
                        }
                    ],
                    "validation_points": [
                        "上传按钮点击响应正常",
                        "文件选择对话框正确显示",
                        "搜索功能工作正常",
                        "文件操作按钮响应正确",
                        "下载功能正常工作",
                        "分页或滚动功能正常"
                    ],
                    "yaml_content": """# 文件管理界面自动化脚本
# 验证文件管理界面的核心功能测试
# 生成时间: 2024-12-17T18:30:00
# 生成器: UI自动化分析平台

name: 文件管理界面自动化脚本
description: 验证文件管理界面的核心功能，包括文件上传、搜索、操作等

steps:
  # 文件上传功能测试
  - name: 点击上传文件按钮
    action: aiTap
    locate: 页面右上角的红色按钮，显示'新增文件'文字，位于搜索框右侧
    expect: 打开文件选择对话框或上传界面

  # 文件搜索功能测试
  - name: 测试文件搜索功能
    action: aiInput
    locate: 页面右上角的搜索输入框，白色背景灰色边框，占位符可能显示'搜索文件'
    value: test.spec
    expect: 搜索框显示输入的文件名

  - name: 执行搜索
    action: aiKeyboardPress
    locate: 搜索输入框
    value: Enter
    expect: 文件列表根据搜索关键词进行过滤

  # 文件列表操作测试
  - name: 点击文件编辑按钮
    action: aiTap
    locate: 文件列表中第一行右侧的蓝色'编辑'按钮，小尺寸圆角按钮
    expect: 打开文件编辑界面或显示编辑选项

  - name: 点击文件下载按钮
    action: aiTap
    locate: 文件列表中第一行右侧的绿色'下载'按钮，位于编辑按钮旁边
    expect: 开始文件下载或显示下载确认对话框

  - name: 测试文件删除功能
    action: aiTap
    locate: 文件列表中第一行右侧的红色'删除'按钮，位于下载按钮旁边
    expect: 显示删除确认对话框

  # 列表导航测试
  - name: 测试分页功能
    action: aiTap
    locate: 文件列表底部的分页控件，可能显示页码或'下一页'按钮
    expect: 切换到下一页或显示更多文件

  - name: 验证文件列表显示
    action: aiAssert
    locate: 页面中央的文件列表表格，显示文件名、大小、修改时间等信息
    expect: 文件列表正确显示，包含文件基本信息和操作按钮

  # 整体功能验证
  - name: 验证界面完整性
    action: aiQuery
    locate: 整个文件管理界面
    expect: 所有核心功能正常工作，界面响应流畅"""
                }
            ]
        }

        # 使用任务管理器处理结果
        task_manager.active_tasks[task_id] = file_mgmt_result

        # 广播完成消息
        await sse_manager.broadcast_completion(task_id, file_mgmt_result)

    except Exception as e:
        print(f"文件管理界面分析任务执行失败 {task_id}: {e}")
        await sse_manager.broadcast_error(task_id, str(e))
